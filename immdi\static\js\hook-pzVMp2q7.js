var c=(s,r,e)=>new Promise((o,u)=>{var t=n=>{try{a(e.next(n))}catch(l){u(l)}},i=n=>{try{a(e.throw(n))}catch(l){u(l)}},a=n=>n.done?o(n.value):Promise.resolve(n.value).then(t,i);a((e=e.apply(s,r)).next())});import{l as h,d as g}from"./system-DzNitOCO.js";import{aE as d,n as f,q as p}from"./index-BnxEuBzx.js";const m=s=>c(void 0,null,function*(){var r,e,o,u;try{const{meta:t,data:i}=yield s;if(t.status!==200){d(t.msg,{type:"error"});return}return{code:t.status,msg:t.msg,data:i}}catch(t){if(((r=t==null?void 0:t.response)==null?void 0:r.status)!==200){const i=(u=(o=(e=t.response)==null?void 0:e.data)==null?void 0:o.msg)!=null?u:t;d(i,{type:"error"})}else d(t,{type:"error"});return{code:-1,msg:"error",data:null}}});function M(s){const r=f([]),e=f(!0);function o(){return c(this,null,function*(){e.value=!0;const{data:a}=yield m(h());r.value=a,e.value=!1})}function u(a){s.value.fetchData(),a.id?s.value.showEdit(a):s.value.showEdit()}function t(a){a.id&&(s.value.fetchData(),s.value.showEditWithParent(a.id))}function i(a){return c(this,null,function*(){const{code:n}=yield m(g({id:a.id}));n===0&&(d("删除成功",{type:"success"}),o())})}return p(()=>{o()}),{dataList:r,loading:e,handleEdit:u,handleEditChild:t,handleDelete:i,fetchData:o}}export{m as requestHook,M as useSysMenuManagement};

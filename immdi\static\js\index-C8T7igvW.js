import{u as n}from"./prod-CmDsiAIL.js";import{useColumns as C}from"./columns-vX6yZjEI.js";import{d as b,r as t,o as k,c as x,e as s,b as a,h as r,f as S,u as o,ak as w,al as I,_ as y}from"./index-BnxEuBzx.js";import"./moment-C3TZ8gAF.js";import"./runlog-CNDP2hwV.js";import"./front-CiGk0t8u.js";import"./index-CA30dg9C.js";import"./editStateForm-BQH9QYuy.js";import"./dashboard-dtTxmf4X.js";import"./index.vue_vue_type_script_setup_true_lang-DEM1uiK0.js";import"./shift-DH35BNzV.js";const B=e=>(w("data-v-04548905"),e=e(),I(),e),N={class:"main"},P={class:"header"},V=B(()=>s("span",{style:{"margin-right":"10px"},class:"lightfont"},"设备运行及停机记录表",-1)),D={class:"content"},T=b({__name:"index",setup(e){const{changeState:c,autoCheckState:z,dataList:d,columns:l,loadingConfig:p,adaptiveConfig:_,loading:m,refreshData:u}=C();return(E,i)=>{const f=t("CirclePlus"),g=t("el-icon"),h=t("el-button"),v=t("pure-table");return k(),x("div",N,[s("div",P,[V,a(h,{type:"primary",onClick:i[0]||(i[0]=H=>{o(c)(o(n)().machineId,o(n)().selectedDate,o(n)().shift,void 0,void 0,void 0,void 0,"add",()=>{o(u)()})})},{default:r(()=>[a(g,{size:18},{default:r(()=>[a(f)]),_:1}),S("  插入状态 ")]),_:1})]),s("div",D,[a(v,{ref:"tableRef",border:"",stripe:"",adaptive:"",adaptiveConfig:o(_),"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:o(m),"loading-config":o(p),data:o(d),columns:o(l),height:"100%"},null,8,["adaptiveConfig","loading","loading-config","data","columns"])])])}}}),K=y(T,[["__scopeId","data-v-04548905"]]);export{K as default};

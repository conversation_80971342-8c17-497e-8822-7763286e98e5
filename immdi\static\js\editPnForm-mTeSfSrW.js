import{f as B,h as F}from"./dashboard-dtTxmf4X.js";import{g as M}from"./prod-CfeywgVC.js";import{d as S,n as o,q as z,r as s,o as r,c as m,e as l,b as c,h as n,F as g,t as b,g as k,y as f,_ as E}from"./index-BnxEuBzx.js";const I={class:"list_recently"},L={class:"card-header"},U=S({__name:"editPnForm",props:{PNData:{default:()=>({pn:"",machine_id:0})}},setup(x){const d=x,v=o([]),u=o(d.PNData),p=o([]),i=o(!1),h=o("");z(()=>{P(d.PNData.machine_id.toString())});const y=a=>{a?(i.value=!0,B({prefix:a}).then(t=>{p.value=t.data}),i.value=!1):p.value=[]},N=a=>{F({pn:a}).then(t=>{h.value=t.data})},P=a=>{M({machineid:a}).then(t=>{v.value=t.data})};return(a,t)=>{const w=s("el-option"),C=s("el-select"),_=s("el-form-item"),D=s("el-card"),V=s("el-form");return r(),m("div",null,[l("div",null,[c(V,{"label-width":"80px"},{default:n(()=>[c(_,{label:"料号"},{default:n(()=>[c(C,{modelValue:u.value.pn,"onUpdate:modelValue":t[0]||(t[0]=e=>u.value.pn=e),filterable:"",remote:"","reserve-keyword":"",placeholder:"输入生产料号","remote-show-suffix":"","remote-method":y,loading:i.value,size:"large",change:N(u.value.pn)},{default:n(()=>[(r(!0),m(g,null,b(p.value,e=>(r(),k(w,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","change"])]),_:1}),c(_,{label:"描述"},{default:n(()=>[l("div",null,[l("span",null,f(h.value),1)])]),_:1}),c(_,{label:"最近生产"},{default:n(()=>[l("div",I,[(r(!0),m(g,null,b(v.value,e=>(r(),k(D,{key:e.pn,onClick:$=>d.PNData.pn=e.pn},{header:n(()=>[l("div",L,[l("span",null,f(e.pn),1)])]),default:n(()=>[l("p",null,f(e.recorddate),1)]),_:2},1032,["onClick"]))),128))])]),_:1})]),_:1})])])}}}),G=E(U,[["__scopeId","data-v-455b4c90"]]);export{G as default};

from exchangelib import (
    Credentials,
    Account,
    DELEGATE,
    Configuration,
    FileAttachment,
    Message,
    HTMLBody
)
import threading
from config import config


class EmailLoader:
    def __init__(self, username, password, mailserver, address):
        self.username = username
        self.password = password
        self.mailserver = mailserver
        self.address = address
        self.account = self.connect_to_mailbox()

    def connect_to_mailbox(self):
        cred = Credentials(self.username, self.password)
        mailconfig = Configuration(server=self.mailserver, credentials=cred)
        account = Account(
            primary_smtp_address=self.address,
            config=mailconfig,
            autodiscover=False,
            access_type=DELEGATE,


        )
        return account

    def send_mail(self, toArr, subject, body, *args):
        m = Message(account=self.account, subject=subject, body=HTMLBody(body),
                    to_recipients=toArr)
        if len(args) == 1:
            for f in args[0]:
                filename = f[f.rfind('/')+1:]
                # print(filename)
                myfile = FileAttachment(name=filename, content=open(
                    f, 'rb').read())
                m.attach(myfile)
        m.send_and_save()


def sendMail(to, title, content):
    e = EmailLoader(config["default"].mailLoader[0], config["default"].mailLoader[1],
                    config["default"].mailLoader[2], config["default"].mailLoader[3])
    e.send_mail(to, title, content)


def sendMailwithAttachments(to, title, content, attachments):
    e = EmailLoader(config["default"].mailLoader[0], config["default"].mailLoader[1],
                    config["default"].mailLoader[2], config["default"].mailLoader[3])
    e.send_mail(to, title, content, attachments)


def sendMailTread(to, title, content):
    t = threading.Thread(target=sendMail, args=(to, title, content))
    t.setDaemon(True)
    t.start()


def sendMailwithAttachmentsTread(to, title, content, attachments):
    t = threading.Thread(target=sendMailwithAttachments, args=(to, title, content, attachments))
    t.setDaemon(True)
    t.start()


if __name__ == "__main__":
    sendMailwithAttachmentsTread(
        ['<EMAIL>'], 'test', 'test', ['E:/phpstudy_pro/WWW/receiving/uploads/CARS/9.xlsm'])

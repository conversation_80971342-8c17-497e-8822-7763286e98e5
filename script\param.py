import datetime
import time
from opcua import Client
from apscheduler.schedulers.blocking import BlockingScheduler
import pytz
import redis
from script_config import my_config

# 9号注塑机IOT采集器

client = Client(my_config['PARAM_IOT']['9#'], 3)

try:
    pool = redis.ConnectionPool.from_url(
        my_config['REDIS_URL'], db=0, decode_responses=True)
    r = redis.Redis(connection_pool=pool)
except Exception as message:
    print('连接服务器报错%s' % message)
else:
    print('redis服务连接成功')

# 连接OPCserver

opc_connected = False


def connectOpc():
    global opc_connected
    try:
        print("连接OPC......", time.strftime(
            "%Y-%m-%d %H:%M:%S", time.localtime()))
        client.connect()
        opc_connected = True
    except Exception:
        opc_connected = False

# 刷新机台基本信息


def baseInfo():
    global opc_connected
    try:
        # 读取模块基本信息
        info_data = {
            "controlerId": client.get_node("ns=1;i=210001").get_value(),
            "controllerType": client.get_node("ns=1;i=210002").get_value(),
            "version": client.get_node("ns=1;i=210003").get_value(),
            "model": client.get_node("ns=1;i=210004").get_value(),
            "ip": client.get_node("ns=1;i=210005").get_value(),
            "opMode": client.get_node("ns=1;i=210006").get_value(),
            "JobMode": client.get_node("ns=1;i=210007").get_value(),
            "updatetime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        }
        # 读取模具参数
        mold_data = {
            "Open1Pos": client.get_node("ns=1;i=212001").get_value(),
            "Open2Pos": client.get_node("ns=1;i=212002").get_value(),
            "Open3Pos": client.get_node("ns=1;i=212003").get_value(),
            "OpenFastPos": client.get_node("ns=1;i=212004").get_value(),
            "OpenSlowPos": client.get_node("ns=1;i=212005").get_value(),
            "Clamp1Pos": client.get_node("ns=1;i=212006").get_value(),
            "Clamp2Pos": client.get_node("ns=1;i=212007").get_value(),
            "Clamp3Pos": client.get_node("ns=1;i=212008").get_value(),
            "ClampLpPos": client.get_node("ns=1;i=212009").get_value(),
            "ClampHpPos": client.get_node("ns=1;i=212010").get_value(),
            "Open1Speed": client.get_node("ns=1;i=212011").get_value(),
            "Open2Speed": client.get_node("ns=1;i=212012").get_value(),
            "Open3Speed": client.get_node("ns=1;i=212013").get_value(),
            "OpenFastSpeed": client.get_node("ns=1;i=212014").get_value(),
            "OpenSlowSpeed": client.get_node("ns=1;i=212015").get_value(),
            "Clamp1Speed": client.get_node("ns=1;i=212016").get_value(),
            "Clamp2Speed": client.get_node("ns=1;i=212017").get_value(),
            "Clamp3Speed": client.get_node("ns=1;i=212018").get_value(),
            "ClampLpSpeed": client.get_node("ns=1;i=212019").get_value(),
            "ClampHpSpeed": client.get_node("ns=1;i=212020").get_value(),
            "Open1Pres": client.get_node("ns=1;i=212021").get_value(),
            "Open2Pres": client.get_node("ns=1;i=212022").get_value(),
            "Open3Pres": client.get_node("ns=1;i=212023").get_value(),
            "OpenFastPres": client.get_node("ns=1;i=212024").get_value(),
            "OpenSlowPres": client.get_node("ns=1;i=212025").get_value(),
            "Clamp1Pres": client.get_node("ns=1;i=212026").get_value(),
            "Clamp2Pres": client.get_node("ns=1;i=212027").get_value(),
            "Clamp3Pres": client.get_node("ns=1;i=212028").get_value(),
            "ClampLpPres": client.get_node("ns=1;i=212029").get_value(),
            "ClampHpPres": client.get_node("ns=1;i=212030").get_value(),
            "MaxClampLpTime": client.get_node("ns=1;i=212031").get_value(),
            "Timer38": client.get_node("ns=1;i=212032").get_value(),
            "Timer45": client.get_node("ns=1;i=212033").get_value(),
            "Timer76": client.get_node("ns=1;i=212034").get_value(),
            "OT017Speed": client.get_node("ns=1;i=212035").get_value(),
            "OT017Pres": client.get_node("ns=1;i=212036").get_value(),
            "OT018Speed": client.get_node("ns=1;i=212037").get_value(),
            "OT018Pres": client.get_node("ns=1;i=212038").get_value(),
            "HydraulicMoldAdj": client.get_node("ns=1;i=212039").get_value(),
            "Inject1Pos": client.get_node("ns=1;i=212040").get_value(),
            "Inject2Pos": client.get_node("ns=1;i=212041").get_value(),
            "Inject3Pos": client.get_node("ns=1;i=212042").get_value(),
            "Inject4Pos": client.get_node("ns=1;i=212043").get_value(),
            "Inject5Pos": client.get_node("ns=1;i=212044").get_value(),
            "Inject1Speed": client.get_node("ns=1;i=212045").get_value(),
            "Inject2Speed": client.get_node("ns=1;i=212046").get_value(),
            "Inject3Speed": client.get_node("ns=1;i=212047").get_value(),
            "Inject4Speed": client.get_node("ns=1;i=212048").get_value(),
            "Inject5Speed": client.get_node("ns=1;i=212049").get_value(),
            "Inject1Pres": client.get_node("ns=1;i=212050").get_value(),
            "Inject2Pres": client.get_node("ns=1;i=212051").get_value(),
            "Inject3Pres": client.get_node("ns=1;i=212052").get_value(),
            "Inject4Pres": client.get_node("ns=1;i=212053").get_value(),
            "Inject5Pres": client.get_node("ns=1;i=212054").get_value(),
            "HoldChange": client.get_node("ns=1;i=212055").get_value(),
            "MaxInjTime": client.get_node("ns=1;i=212056").get_value(),
            "HoldChangePos": client.get_node("ns=1;i=212057").get_value(),
            "InjectPres": client.get_node("ns=1;i=212058").get_value(),
            "Hold1Time": client.get_node("ns=1;i=212059").get_value(),
            "Hold2Time": client.get_node("ns=1;i=212060").get_value(),
            "Hold3Time": client.get_node("ns=1;i=212061").get_value(),
            "Hold4Time": client.get_node("ns=1;i=212062").get_value(),
            "Hold5Time": client.get_node("ns=1;i=212063").get_value(),
            "Hold1Speed": client.get_node("ns=1;i=212064").get_value(),
            "Hold2Speed": client.get_node("ns=1;i=212065").get_value(),
            "Hold3Speed": client.get_node("ns=1;i=212066").get_value(),
            "Hold4Speed": client.get_node("ns=1;i=212067").get_value(),
            "Hold5Speed": client.get_node("ns=1;i=212068").get_value(),
            "Hold1Pres": client.get_node("ns=1;i=212069").get_value(),
            "Hold2Pres": client.get_node("ns=1;i=212070").get_value(),
            "Hold3Pres": client.get_node("ns=1;i=212071").get_value(),
            "Hold4Pres": client.get_node("ns=1;i=212072").get_value(),
            "Hold5Pres": client.get_node("ns=1;i=212073").get_value(),
            "CoolingTime": client.get_node("ns=1;i=212074").get_value(),
            "SuckBack": client.get_node("ns=1;i=212075").get_value(),
            "DecompBefPos": client.get_node("ns=1;i=212076").get_value(),
            "Plast1Pos": client.get_node("ns=1;i=212077").get_value(),
            "Plast2Pos": client.get_node("ns=1;i=212078").get_value(),
            "Plast3Pos": client.get_node("ns=1;i=212079").get_value(),
            "Plast4Pos": client.get_node("ns=1;i=212080").get_value(),
            "Plast5Pos": client.get_node("ns=1;i=212081").get_value(),
            "Plast1Speed": client.get_node("ns=1;i=212082").get_value(),
            "Plast2Speed": client.get_node("ns=1;i=212083").get_value(),
            "Plast3Speed": client.get_node("ns=1;i=212084").get_value(),
            "Plast4Speed": client.get_node("ns=1;i=212085").get_value(),
            "Plast5Speed": client.get_node("ns=1;i=212086").get_value(),
            "Plast1Pres": client.get_node("ns=1;i=212087").get_value(),
            "Plast2Pres": client.get_node("ns=1;i=212088").get_value(),
            "Plast3Pres": client.get_node("ns=1;i=212089").get_value(),
            "Plast4Pres": client.get_node("ns=1;i=212090").get_value(),
            "Plast5Pres": client.get_node("ns=1;i=212091").get_value(),
            "Plast1BackPres": client.get_node("ns=1;i=212092").get_value(),
            "Plast2BackPres": client.get_node("ns=1;i=212093").get_value(),
            "Plast3BackPres": client.get_node("ns=1;i=212094").get_value(),
            "Plast4BackPres": client.get_node("ns=1;i=212095").get_value(),
            "Plast5BackPres": client.get_node("ns=1;i=212096").get_value(),
            "DecompPos": client.get_node("ns=1;i=212097").get_value(),
            "DecompSpeed": client.get_node("ns=1;i=212098").get_value(),
            "DecompPres": client.get_node("ns=1;i=212099").get_value(),
            "PlastDelay": client.get_node("ns=1;i=212100").get_value(),
            "NozzleFwdFastPos": client.get_node("ns=1;i=212101").get_value(),
            "NozzleFwdSlowPos": client.get_node("ns=1;i=212102").get_value(),
            "NozzleBackPos": client.get_node("ns=1;i=212103").get_value(),
            "NozzleFwdFastSpeed": client.get_node("ns=1;i=212104").get_value(),
            "NozzleFwdSlowSpeed": client.get_node("ns=1;i=212105").get_value(),
            "NozzleBackSpeed": client.get_node("ns=1;i=212106").get_value(),
            "NozzleFwdFastPres": client.get_node("ns=1;i=212107").get_value(),
            "NozzleFwdSlowPres": client.get_node("ns=1;i=212108").get_value(),
            "NozzleBackPres": client.get_node("ns=1;i=212109").get_value(),
            "NozzleFwdFastTime": client.get_node("ns=1;i=212110").get_value(),
            "NozzleBackTime": client.get_node("ns=1;i=212111").get_value(),
            "Timer70": client.get_node("ns=1;i=212113").get_value(),
            "Timer42": client.get_node("ns=1;i=212114").get_value(),
            "EjectOut1Pos": client.get_node("ns=1;i=212115").get_value(),
            "EjectOut2Pos": client.get_node("ns=1;i=212116").get_value(),
            "EjectIn1Pos": client.get_node("ns=1;i=212117").get_value(),
            "EjectIn2Pos": client.get_node("ns=1;i=212118").get_value(),
            "EjectOut1Speed": client.get_node("ns=1;i=212119").get_value(),
            "EjectOut2Speed": client.get_node("ns=1;i=212120").get_value(),
            "EjectIn1Speed": client.get_node("ns=1;i=212121").get_value(),
            "EjectIn2Speed": client.get_node("ns=1;i=212122").get_value(),
            "EjectOut1Pres": client.get_node("ns=1;i=212123").get_value(),
            "EjectOut2Pres": client.get_node("ns=1;i=212124").get_value(),
            "EjectIn1Pres": client.get_node("ns=1;i=212125").get_value(),
            "EjectIn2Pres": client.get_node("ns=1;i=212126").get_value(),
            "EjectMode": client.get_node("ns=1;i=212127").get_value(),
            "EjectCount": client.get_node("ns=1;i=212128").get_value(),
            "EjectVibrateCount": client.get_node("ns=1;i=212129").get_value(),
            "EjectTime": client.get_node("ns=1;i=212130").get_value(),
            "EjectOutStayTime": client.get_node("ns=1;i=212131").get_value(),
            "EjectStartPos": client.get_node("ns=1;i=212132").get_value(),
            "EjectConfirm": client.get_node("ns=1;i=212133").get_value(),
            "DelayBeforeEject": client.get_node("ns=1;i=212134").get_value(),
            "EjectStayInterval": client.get_node("ns=1;i=212135").get_value(),
            "AirBlow": client.get_node("ns=1;i=212136").get_value(),
            "Air2ModeAfterOpen": client.get_node("ns=1;i=212137").get_value(),
            "Air3ModeAfterOpen": client.get_node("ns=1;i=212138").get_value(),
            "Air1Pos": client.get_node("ns=1;i=212139").get_value(),
            "Air2Pos": client.get_node("ns=1;i=212140").get_value(),
            "Air3Pos": client.get_node("ns=1;i=212141").get_value(),
            "Air1Time": client.get_node("ns=1;i=212142").get_value(),
            "Air2Time": client.get_node("ns=1;i=212143").get_value(),
            "Air3Time": client.get_node("ns=1;i=212144").get_value(),
            "AIr1Delay": client.get_node("ns=1;i=212145").get_value(),
            "Air2Delay": client.get_node("ns=1;i=212146").get_value(),
            "Air3Delay": client.get_node("ns=1;i=212147").get_value(),
            "CoreAInMode": client.get_node("ns=1;i=212148").get_value(),
            "CoreAOutMode": client.get_node("ns=1;i=212149").get_value(),
            "CoreBInMode": client.get_node("ns=1;i=212150").get_value(),
            "CoreBOutMode": client.get_node("ns=1;i=212151").get_value(),
            "CoreCInMode": client.get_node("ns=1;i=212152").get_value(),
            "CoreCOutMode": client.get_node("ns=1;i=212153").get_value(),
            "CoreDInMode": client.get_node("ns=1;i=212154").get_value(),
            "CoreDOutMode": client.get_node("ns=1;i=212155").get_value(),
            "CoreAInOut": client.get_node("ns=1;i=212156").get_value(),
            "CoreBInOut": client.get_node("ns=1;i=212157").get_value(),
            "CoreCInOut": client.get_node("ns=1;i=212158").get_value(),
            "CoreDInOut": client.get_node("ns=1;i=212159").get_value(),
            "CoreAOut1Cycle": client.get_node("ns=1;i=212160").get_value(),
            "CoreBOut1Cycle": client.get_node("ns=1;i=212161").get_value(),
            "CoreCOut1Cycle": client.get_node("ns=1;i=212162").get_value(),
            "CoreDOut1Cycle": client.get_node("ns=1;i=212163").get_value(),
            "CoreAInPos": client.get_node("ns=1;i=212164").get_value(),
            "CoreAOutPos": client.get_node("ns=1;i=212165").get_value(),
            "CoreBInPos": client.get_node("ns=1;i=212166").get_value(),
            "CoreBOutPos": client.get_node("ns=1;i=212167").get_value(),
            "CoreCInPos": client.get_node("ns=1;i=212168").get_value(),
            "CoreCOutPos": client.get_node("ns=1;i=212169").get_value(),
            "CoreDInPos": client.get_node("ns=1;i=212170").get_value(),
            "CoreDOutPos": client.get_node("ns=1;i=212171").get_value(),
            "CoreAInLimit": client.get_node("ns=1;i=212172").get_value(),
            "CoreAOutLimit": client.get_node("ns=1;i=212173").get_value(),
            "CoreBInLimit": client.get_node("ns=1;i=212174").get_value(),
            "CoreBOutLimit": client.get_node("ns=1;i=212175").get_value(),
            "CoreCInLimit": client.get_node("ns=1;i=212176").get_value(),
            "CoreCOutLimit": client.get_node("ns=1;i=212177").get_value(),
            "CoreDInLimit": client.get_node("ns=1;i=212178").get_value(),
            "CoreDOutLimit": client.get_node("ns=1;i=212179").get_value(),
            "CoreAInSpeed": client.get_node("ns=1;i=212180").get_value(),
            "CoreAOutSpeed": client.get_node("ns=1;i=212181").get_value(),
            "CoreBInSpeed": client.get_node("ns=1;i=212182").get_value(),
            "CoreBOutSpeed": client.get_node("ns=1;i=212183").get_value(),
            "CoreCInSpeed": client.get_node("ns=1;i=212184").get_value(),
            "CoreCOutSpeed": client.get_node("ns=1;i=212185").get_value(),
            "CoreDInSpeed": client.get_node("ns=1;i=212186").get_value(),
            "CoreDOutSpeed": client.get_node("ns=1;i=212187").get_value(),
            "CoreAInPres": client.get_node("ns=1;i=212188").get_value(),
            "CoreAOutPres": client.get_node("ns=1;i=212189").get_value(),
            "CoreBInPres": client.get_node("ns=1;i=212190").get_value(),
            "CoreBOutPres": client.get_node("ns=1;i=212191").get_value(),
            "CoreCInPres": client.get_node("ns=1;i=212192").get_value(),
            "CoreCOutPres": client.get_node("ns=1;i=212193").get_value(),
            "CoreDInPres": client.get_node("ns=1;i=212194").get_value(),
            "CoreDOutPres": client.get_node("ns=1;i=212195").get_value(),
            "CoreAInTime": client.get_node("ns=1;i=212196").get_value(),
            "CoreAOutTIme": client.get_node("ns=1;i=212197").get_value(),
            "CoreBInTime": client.get_node("ns=1;i=212198").get_value(),
            "CoreBOutTime": client.get_node("ns=1;i=212199").get_value(),
            "CoreCInTime": client.get_node("ns=1;i=212200").get_value(),
            "CoreCOutTime": client.get_node("ns=1;i=212201").get_value(),
            "CoreDInTime": client.get_node("ns=1;i=212202").get_value(),
            "CoreDOutTime": client.get_node("ns=1;i=212203").get_value(),
            "Timer138": client.get_node("ns=1;i=212204").get_value(),
            "Timer139": client.get_node("ns=1;i=212205").get_value(),
            "Timer252": client.get_node("ns=1;i=212206").get_value(),
            "Timer253": client.get_node("ns=1;i=212207").get_value(),
            "Timer254": client.get_node("ns=1;i=212208").get_value(),
            "Timer255": client.get_node("ns=1;i=212209").get_value(),
            "Timer256": client.get_node("ns=1;i=212210").get_value(),
            "Timer257": client.get_node("ns=1;i=212211").get_value(),
            "CoreCInCount": client.get_node("ns=1;i=212212").get_value(),
            "CoreCOutCount": client.get_node("ns=1;i=212213").get_value(),
            "CoreInKeep": client.get_node("ns=1;i=212214").get_value(),
            "Counter22": client.get_node("ns=1;i=212215").get_value(),
            "Counter23": client.get_node("ns=1;i=212216").get_value(),
            "Counter24": client.get_node("ns=1;i=212217").get_value(),
            "Timer122": client.get_node("ns=1;i=212218").get_value(),
            "Timer123": client.get_node("ns=1;i=212219").get_value(),
            "Counter25": client.get_node("ns=1;i=212220").get_value(),
            "Counter26": client.get_node("ns=1;i=212221").get_value(),
            "Counter27": client.get_node("ns=1;i=212222").get_value(),
            "Timer124": client.get_node("ns=1;i=212223").get_value(),
            "Timer125": client.get_node("ns=1;i=212224").get_value(),
            "Lub1Cycle": client.get_node("ns=1;i=212225").get_value(),
            "Lub1Count": client.get_node("ns=1;i=212226").get_value(),
            "Lub1Time": client.get_node("ns=1;i=212227").get_value(),
            "Lub1AlarmTime": client.get_node("ns=1;i=212228").get_value(),
            "Timer117": client.get_node("ns=1;i=212229").get_value(),
            "Lub2Cycle": client.get_node("ns=1;i=212230").get_value(),
            "Lub2Count": client.get_node("ns=1;i=212231").get_value(),
            "Lub2Time": client.get_node("ns=1;i=212232").get_value(),
            "Lub2AlarmTime": client.get_node("ns=1;i=212233").get_value(),
            "Timer118": client.get_node("ns=1;i=212234").get_value(),
            "SetTempNozzle": client.get_node("ns=1;i=212235").get_value(),
            "MaxTempNozzle": client.get_node("ns=1;i=212236").get_value(),
            "MinTempNozzle": client.get_node("ns=1;i=212237").get_value(),
            "DutyNozzle": client.get_node("ns=1;i=212238").get_value(),
            "SetTempZ1": client.get_node("ns=1;i=212239").get_value(),
            "MinColdStartTempZ1": client.get_node("ns=1;i=212240").get_value(),
            "MaxTempZ1": client.get_node("ns=1;i=212241").get_value(),
            "MinTempZ1": client.get_node("ns=1;i=212242").get_value(),
            "DutyZ1": client.get_node("ns=1;i=212243").get_value(),
            "SetTempZ2": client.get_node("ns=1;i=212244").get_value(),
            "MinColdStartTempZ2": client.get_node("ns=1;i=212245").get_value(),
            "MaxTempZ2": client.get_node("ns=1;i=212246").get_value(),
            "MinTempZ2": client.get_node("ns=1;i=212247").get_value(),
            "DutyZ2": client.get_node("ns=1;i=212248").get_value(),
            "SetTempZ3": client.get_node("ns=1;i=212249").get_value(),
            "MinColdStartTempZ3": client.get_node("ns=1;i=212250").get_value(),
            "MaxTempZ3": client.get_node("ns=1;i=212251").get_value(),
            "MinTempZ3": client.get_node("ns=1;i=212252").get_value(),
            "DutyZ3": client.get_node("ns=1;i=212253").get_value(),
            "SetTempZ4": client.get_node("ns=1;i=212254").get_value(),
            "MinColdStartTempZ4": client.get_node("ns=1;i=212255").get_value(),
            "MaxTempZ4": client.get_node("ns=1;i=212256").get_value(),
            "MinTempZ4": client.get_node("ns=1;i=212257").get_value(),
            "DutyZ4": client.get_node("ns=1;i=212258").get_value(),
            "SetTempZ5": client.get_node("ns=1;i=212259").get_value(),
            "MinColdStartTempZ5": client.get_node("ns=1;i=212260").get_value(),
            "MaxTempZ5": client.get_node("ns=1;i=212261").get_value(),
            "MinTempZ5": client.get_node("ns=1;i=212262").get_value(),
            "DutyZ5": client.get_node("ns=1;i=212263").get_value(),
            "SetTempZ6": client.get_node("ns=1;i=212264").get_value(),
            "MinColdStartTempZ6": client.get_node("ns=1;i=212265").get_value(),
            "MaxTempZ6": client.get_node("ns=1;i=212266").get_value(),
            "MinTempZ6": client.get_node("ns=1;i=212267").get_value(),
            "DutyZ6": client.get_node("ns=1;i=212268").get_value(),
            "SetTempOil": client.get_node("ns=1;i=212269").get_value(),
            "MInColdStartTempZ7": client.get_node("ns=1;i=212270").get_value(),
            "MaxTempOil": client.get_node("ns=1;i=212271").get_value(),
            "MinTempOil": client.get_node("ns=1;i=212272").get_value(),
            "DutyOil": client.get_node("ns=1;i=212273").get_value(),
            "SetTempZ8": client.get_node("ns=1;i=212274").get_value(),
            "MInColdStartTempZ8": client.get_node("ns=1;i=212275").get_value(),
            "MaxTempZ8": client.get_node("ns=1;i=212276").get_value(),
            "MinTempZ8": client.get_node("ns=1;i=212277").get_value(),
            "DutyZ8": client.get_node("ns=1;i=212278").get_value(),
            "SetTempZ9": client.get_node("ns=1;i=212279").get_value(),
            "MinColdStartTempZ9": client.get_node("ns=1;i=212280").get_value(),
            "MaxTempZ9": client.get_node("ns=1;i=212281").get_value(),
            "MinTempZ9": client.get_node("ns=1;i=212282").get_value(),
            "DutyZ9": client.get_node("ns=1;i=212283").get_value(),
            "MinColdStartTime": client.get_node("ns=1;i=212284").get_value(),
            "Preheat": client.get_node("ns=1;i=212285").get_value(),
            "OpenStageSelect": client.get_node("ns=1;i=212286").get_value(),
            "ClampStageSelect": client.get_node("ns=1;i=212287").get_value(),
            "InjectStageSelect": client.get_node("ns=1;i=212288").get_value(),
            "HoldPresStageSelect": client.get_node("ns=1;i=212289").get_value(),
            "PlastStageSelect": client.get_node("ns=1;i=212290").get_value(),
            "Robot": client.get_node("ns=1;i=212291").get_value(),
            "EjectConfirm": client.get_node("ns=1;i=212292").get_value(),
            "SynAction": client.get_node("ns=1;i=212293").get_value(),
            "CycleGap": client.get_node("ns=1;i=212294").get_value(),
            "MaxCycleTime": client.get_node("ns=1;i=212295").get_value(),
            "ProductionCount": client.get_node("ns=1;i=212296").get_value(),
            "MaxProduction": client.get_node("ns=1;i=212297").get_value(),
            "ProductSensor": client.get_node("ns=1;i=212298").get_value(),
            "FastClamp": client.get_node("ns=1;i=212299").get_value(),
            "OT023Speed": client.get_node("ns=1;i=212300").get_value(),
            "OT023Pres": client.get_node("ns=1;i=212301").get_value(),
            "updatetime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        }
    except Exception:
        if not opc_connected:
            connectOpc()
    else:
        r.hmset("Param:9:Info", info_data)
        r.hmset("Param:9:MoldData", mold_data)


def alarm():
    global opc_connected
    # 读取Alarm警报信息
    try:
        alarm_data = {
            "alarmTime": str(client.get_node("ns=1;i=213001").get_value()),
            "AlarmName": str(client.get_node("ns=1;i=213002").get_value()),
            "AlarmCode": str(client.get_node("ns=1;i=213003").get_value()),
            "updatetime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        }
    except Exception:
        if not opc_connected:
            connectOpc()
    else:
        r.hmset("Param:9:Alarm", alarm_data)


def auditTrial():
    global opc_connected
    # 读取AuditTrial操作履历
    try:
        audit_trial = {
            "auditTime": str(client.get_node("ns=1;i=214001").get_value()),
            "Key": str(client.get_node("ns=1;i=214002").get_value()),
            "NewValue": str(client.get_node("ns=1;i=214003").get_value()),
            "OperatorId": str(client.get_node("ns=1;i=214004").get_value()),
            "updatetime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        }
    except Exception:
        if opc_connected:
            connectOpc()
    else:
        r.hmset("Param:9:AuditTrial", audit_trial)


def cycleData():
    global opc_connected
    # 读取CycleData
    try:
        cycle_data = {
            "Z_QDBCKPRS": client.get_node("ns=1;i=211001").get_value(),
            "Z_QDCOLTIM": client.get_node("ns=1;i=211002").get_value(),
            "Z_QDCYCTIM": client.get_node("ns=1;i=211003").get_value(),
            "Z_QDGODCNT": client.get_node("ns=1;i=211004").get_value(),
            "Z_QDHLDTIM": client.get_node("ns=1;i=211005").get_value(),
            "Z_QDINJENDPOS": client.get_node("ns=1;i=211006").get_value(),
            "Z_QDINJTIM": client.get_node("ns=1;i=211007").get_value(),
            "Z_QDMAXINJSPD": client.get_node("ns=1;i=211008").get_value(),
            "Z_QDMAXPLSRPM": client.get_node("ns=1;i=211009").get_value(),
            "Z_QDMLDCLSTIM": client.get_node("ns=1;i=211010").get_value(),
            "Z_QDMLDOPNENDPOS": client.get_node("ns=1;i=211011").get_value(),
            "Z_QDMLDOPNTIM": client.get_node("ns=1;i=211012").get_value(),
            "Z_QDNOZTEMP": client.get_node("ns=1;i=211013").get_value(),
            "Z_QDPLSENDPOS": client.get_node("ns=1;i=211014").get_value(),
            "Z_QDPLSTIM": client.get_node("ns=1;i=211015").get_value(),
            "Z_QDPRDCNT": client.get_node("ns=1;i=211016").get_value(),
            "Z_QDTEMPZ01": client.get_node("ns=1;i=211017").get_value(),
            "Z_QDTEMPZ02": client.get_node("ns=1;i=211018").get_value(),
            "Z_QDTEMPZ03": client.get_node("ns=1;i=211019").get_value(),
            "Z_QDTEMPZ04": client.get_node("ns=1;i=211020").get_value(),
            "Z_QDTEMPZ05": client.get_node("ns=1;i=211021").get_value(),
            "Z_QDTEMPZ06": client.get_node("ns=1;i=211022").get_value(),
            "Z_QDVPPOS": client.get_node("ns=1;i=211023").get_value(),
            "Z_QDFLAG": client.get_node("ns=1;i=211024").get_value(),
            "updatetime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        }
    except Exception:
        if opc_connected:
            connectOpc()
    else:
        r.hmset("Param:9:CycleData", cycle_data)


# 获取"Asia/Shanghai"时区
tz = pytz.timezone('Asia/Hong_Kong')
# 设置系统时区
datetime.datetime.now(tz).astimezone().tzinfo


# 创建调度器对象
scheduler = BlockingScheduler()

# 添加定时任务
scheduler.add_job(baseInfo, 'interval', seconds=30, coalesce=True)
scheduler.add_job(alarm, 'interval', seconds=2, coalesce=True)
scheduler.add_job(auditTrial, 'interval', seconds=2, coalesce=True)
scheduler.add_job(cycleData, 'interval', seconds=5, coalesce=True)
# 启动调度器
scheduler.start()

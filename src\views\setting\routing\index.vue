<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useColumns } from "./utils/routingColumns";
import { getpnlistbyprefix } from "@/api/dashboard";

const {
  search_condition,
  sql_count,
  redis_count,
  asyncRouting,
  openItem,
  loading,
  columns,
  loadingConfig,
  getRoutingList,
  pagination,
  dataList,
  adaptiveConfig,
  onCurrentChange
} = useColumns();

const tableRef = ref();

const formRef = ref();

const options = ref([]);

const routinglist = ref([]);

const pn = ref("");

const remoteMethod = (query: string) => {
  if (query) {
    getpnlistbyprefix({ prefix: query }).then((res: any) => {
      options.value = res.data;
    });
  } else {
    options.value = [];
  }
};



onMounted(() => {});
</script>

<template>
  <div>
    <el-form
      ref="formRef"
      :inline="true"
      :model="search_condition"
      class="search-form bg-bg_color"
    >
      <el-form-item label="料号：" prop="selecteddate">
        <el-select
          v-model="search_condition.pn"
          class="select_sku"
          filterable
          clearable
          remote
          reserve-keyword
          placeholder="输入生产料号"
          remote-show-suffix
          :remote-method="remoteMethod"
          :loading="loading"
          @change="getRoutingList"
        >
          <el-option
            v-for="item in options"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="status">
        <el-button type="primary" @click="openItem('add', {})"
          >+ 添加料号</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-dropdown split-button type="primary">
          批量上传
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>下载格式</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-form-item>
      <el-form-item>
        <el-tag class="ml-2" type="success" size="large"
          ><el-text size="default" type="primary"
            >MYSQL记录条数：{{ sql_count }}</el-text
          ></el-tag
        >
        <el-tag class="ml-2" type="success" size="large">
          <el-text type="warning"
            >缓存记录条数：{{ redis_count }}</el-text
          ></el-tag
        >
      </el-form-item>

      <el-form-item>
        <el-button
          v-show="sql_count != redis_count"
          type="danger"
          @click="asyncRouting"
          >同步缓存</el-button
        >
      </el-form-item>
    </el-form>

    <pure-table
      ref="tableRef"
      border
      adaptive
      :adaptiveConfig="adaptiveConfig"
      row-key="id"
      alignWhole="center"
      showOverflowTooltip
      :loading="loading"
      :pagination="pagination"
      :loading-config="loadingConfig"
      :data="
        dataList.slice(
          (pagination.currentPage - 1) * pagination.pageSize,
          pagination.currentPage * pagination.pageSize
        )
      "
      :columns="columns"
      @page-current-change="onCurrentChange"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 0;
}
:deep(td) {
  padding: 4px 0;
}

.search-form {
  margin-bottom: 6px;
  padding: 6px 0 6px 10px;
}
.el-form {
  display: flex;
}
.el-form-item {
  margin-bottom: 0;
  padding: 0;
}

.select_sku {
  width: 200px;
}
</style>

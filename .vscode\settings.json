{
  "editor.suggest.snippetsPreventQuickSuggestions": false,
  "python.linting.flake8Enabled": true,
  "python.linting.enabled": true,
  "python.linting.flake8Args": ["--max-line-length=200"],
  "python.formatting.provider": "autopep8",
  "python.formatting.autopep8Args": ["--max-line-length=100"],
  "files.exclude": {
    "**/build": true,
    "**/.idea": true,
    "**/venv": true,
    // "**/app/api": true,
    "node_modules": true,
    "**/__pycache__": true,
    "**/V300": true,
    "**/V301": true,
    "**/V302": true,
    "**/V305": true,
    "**/V306": true,
    "**/V307": true
    // "**/V304": true,
  }
}

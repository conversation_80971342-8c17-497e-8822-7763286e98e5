import{e as H}from"./dashboard-dtTxmf4X.js";import{_ as S}from"./index.vue_vue_type_script_setup_true_lang-DEM1uiK0.js";import{d as k,n as B,a2 as C,q as F,r,o as d,g as p,h as o,b as l,C as I,c,F as f,t as y,f as D,y as V,ak as N,al as q,e as U,_ as T}from"./index-BnxEuBzx.js";import"./shift-DH35BNzV.js";import"./moment-C3TZ8gAF.js";const j=u=>(N("data-v-458a4e2a"),u=u(),q(),u),z=j(()=>U("div",{style:{display:"flex","justify-content":"space-between","margin-left":"10px"}},null,-1)),E=k({__name:"editStateForm",props:{stateData:{default:()=>({machine_id:0,shift_date:"",shift:"",state_id:"0",start_time:"",end_time:"",row_id:0,action:""})}},setup(u){const _=u,s=B(_.stateData),v=C({data:[]});function x(t){const e={};for(const n of t){const{state_type:a}=n;e[a]?e[a].push(n):e[a]=[n]}return e}F(()=>{H().then(t=>{v.data=x(t.data)})});const b=t=>{if(t==1)return"正常运行状态";if(t==2)return"小停机状态";if(t==3)return"长时间故障状态";if(t==4)return"无计划状态"};return(t,e)=>{const n=r("el-date-picker"),a=r("el-form-item"),g=r("el-divider"),h=r("el-radio-button"),M=r("el-radio-group"),w=r("el-form");return d(),p(w,null,{default:o(()=>[l(a,{label:"开始时间","label-width":"100px"},{default:o(()=>[l(n,{modelValue:s.value.start_time,"onUpdate:modelValue":e[0]||(e[0]=m=>s.value.start_time=m),type:"datetime",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"填写状态开始时间"},null,8,["modelValue"]),z]),_:1}),_.stateData.action=="update"?(d(),p(a,{key:0,label:"结束时间","label-width":"100px"},{default:o(()=>[l(n,{modelValue:s.value.end_time,"onUpdate:modelValue":e[1]||(e[1]=m=>s.value.end_time=m),type:"datetime",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"填写状态开始时间"},null,8,["modelValue"])]),_:1})):I("",!0),l(a,{label:"当班节拍图","label-width":"100px"},{default:o(()=>[l(S,{query_data:_.stateData},null,8,["query_data"])]),_:1}),(d(!0),c(f,null,y(v.data,(m,Y)=>(d(),c(f,{key:Y},[l(g,null,{default:o(()=>[D(V(b(Y)),1)]),_:2},1024),l(M,{modelValue:s.value.state_id,"onUpdate:modelValue":e[2]||(e[2]=i=>s.value.state_id=i)},{default:o(()=>[(d(!0),c(f,null,y(m,i=>(d(),p(h,{value:i.id,size:"large",key:i.id},{default:o(()=>[D(V(i.state_name),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["modelValue"])],64))),128))]),_:1})}}}),K=T(E,[["__scopeId","data-v-458a4e2a"]]);export{K as default};

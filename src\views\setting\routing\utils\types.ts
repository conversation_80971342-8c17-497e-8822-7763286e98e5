interface FormItemProps {
  /** 料号 */
  pn: string;
  /** 料号描述 */
  pn_des: string;
  /** 模穴数 */
  cavity: number;
  /** 机台号 */
  machine: string;
  /** 成型周期 */
  moldingcycle: number;
  /** 上下料时间 */
  manualcycle: number;
  /** 自动下料 */
  unload: number;
  /** SAP系统工时 */
  sap_value: number;
}
interface FormProps {
  formInline: FormItemProps;
  ops: { action_type: number };
}

export type { FormItemProps, FormProps };

import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";
import { ref, onMounted, reactive, h } from "vue";
import { getmachinelist } from "@/api/dashboard";
import { changeState } from "@/views/components/editstate/index";
import { changePn } from "@/views/components/editpn/index";
import { addDialog } from "@/components/ReDialog";
import { show_param } from "@/views/components/param_monitor/index";
import moment, { now } from "moment";
import { current_shift } from "./functions";
import { watch } from "vue";
import { SuccessFilled, CircleCloseFilled } from "@element-plus/icons-vue";
import shiftdata from "@/views/components/hourlydata/index.vue";
import { dateEquals } from "element-plus";
export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);
  const is_current = ref(true);
  const columns: TableColumnList = [
    {
      label: "机台",
      prop: "id",
      width: "60px",
      cellRenderer: ({ row }) => <div>{row.id}#</div>
    },
    {
      label: "吨位",
      prop: "ton",
      width: "70px",
      cellRenderer: ({ row }) => <div>{row.ton}T</div>
    },
    {
      label: "计划停机时间",
      prop: "planned_stop",
      hide: true,
      width: "120px"
    },
    {
      label: "异常停机",
      prop: "downtimecount",
      hide: true,
      width: "120px"
    },
    {
      label: "有效时间",
      prop: "uptime",
      hide: true
    },
    {
      label: "标准时间",
      prop: "stdtime",
      hide: true
    },
    {
      label: "联机",
      prop: "conn",
      width: "60px",
      hide: () => !is_current.value,
      cellRenderer: ({ row }) => {
        if (row.conn) {
          return (
            <div style="display:flex;justify-content:space-around;color:green;">
              <el-icon
                onClick={() => {
                  show_param(row.id, row.pn);
                }}
                size={24}
              >
                <SuccessFilled />
              </el-icon>
            </div>
          );
        } else {
          return (
            <div style="display:flex;justify-content:space-around;color:grey;">
              <el-icon
                size="24"
                onClick={() => {
                  show_param(row.id, row.pn);
                }}
              >
                <CircleCloseFilled />
              </el-icon>
            </div>
          );
        }
      }
    },
    {
      label: "状态",
      width: "120px",
      hide: () => !is_current.value,
      cellRenderer: ({ row }) => {
        let backgroundColor = "";
        let color = "";
        let fontSize = "";

        if (row.state_type == 1) {
          backgroundColor = "green";
          fontSize = "12px";
          color = "white";
        } else if (row.state_type == 2) {
          backgroundColor = "yellow";
        } else if (row.state_type == 3) {
          backgroundColor = "red";
          color = "white";
        } else if (row.state_type == 4) {
          backgroundColor = "grey";
          color = "white";
        }

        return (
          <div
            style={{ backgroundColor, fontSize, color }}
            onClick={() =>
              changeState(
                row.id,
                search_condition.selecteddate,
                search_condition.shift,
                row.state_id,
                undefined,
                undefined,
                undefined,
                "add",
                () => {
                  refreshData();
                }
              )
            }
          >
            {row.state_name}
          </div>
        );
      }
    },
    {
      label: "料号",
      prop: "pn",
      width: "310px",
      cellRenderer: ({ row }) => {
        if (row.pn) {
          const splitArray = row.pn.split(/[/,@]/);
          return (
            <div
              style="display:flex;justify-content:space-around"
              onClick={() =>
                changePn(row.id, row.pn, () => {
                  refreshData();
                })
              }
            >
              {splitArray.map((element, index) => (
                <el-tag key={index}>{element}</el-tag>
              ))}
            </div>
          );
        }
      }
    },
    {
      label: "班产量",
      prop: "total_output",
      cellRenderer: ({ row }) => {
        return (
          <div
            onClick={() => {
              showhourlydata(row.id);
            }}
          >
            {row.total_output}
          </div>
        );
      }
    },
    {
      label: "不良",
      prop: "defect",
      hide: true
    },
    {
      label: "理论产量",
      prop: "std_output",
      hide: () => !is_current.value,
      formatter(row) {
        if (row.std_output > 0) {
          return row.std_output.toFixed(0);
        }
      }
    },
    {
      label: "最后动作",
      prop: "last_time",
      hide: () => !is_current.value,
      sortable: true,
      formatter(row) {
        if (row.last_time) {
          return moment(row.last_time).format("MM/DD HH:mm");
        }
      }
    },
    {
      label: "可用率",
      prop: "availability",
      formatter(row, column, cellValue) {
        if (!is_current.value) {
          if (row.stdtime > 0) {
            cellValue = 1;
          }
        }
        if (cellValue > 0) {
          return (cellValue * 100).toFixed(1) + "%";
        } else {
          return "";
        }
      }
    },
    {
      label: "性能",
      prop: "performance",
      cellRenderer: ({ row }) => {
        if (!is_current.value && row.uptime > 0) {
          row.performance = row.stdtime / row.uptime;
        }
        const formattedValue = (row.performance * 100).toFixed(1) + "%";
        if (row.performance > 1.2) {
          return <span style="color: red;">{formattedValue}</span>;
        } else if (row.performance == null) {
          return <span></span>;
        } else {
          return <span>{formattedValue}</span>;
        }
      }
    },
    {
      label: "良率",
      prop: "quality",
      formatter(row, column, cellValue) {
        if (!is_current.value && row.total_output > 0) {
          cellValue = 1 - row.defect / row.total_output;
        }
        if (cellValue > 0) {
          return (cellValue * 100).toFixed(1) + "%";
        }
      }
    },
    {
      label: "OEE",
      prop: "OEE",
      formatter(row) {
        let res = 0;
        if (!is_current.value && row.total_output > 0) {
          res =
            (row.stdtime / row.uptime) * (1 - row.defect / row.total_output);
        } else {
          res = row.availability * row.performance * row.quality;
        }

        if (res > 0) {
          return (res * 100).toFixed(1) + "%";
        } else {
          return "";
        }
      }
    }
  ];

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载生产采集数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  /** 撑满内容区自适应高度相关配置 */
  const adaptiveConfig: AdaptiveConfig = {
    /** 表格距离页面底部的偏移量，默认值为 `96` */
    offsetBottom: 12
    /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
    // fixHeader: true
    /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
    // timeout: 60
    /** 表头的 `z-index`，默认值为 `100` */
    // zIndex: 100
  };

  const showhourlydata = (machineid: any) => {
    addDialog({
      title: "查看" + machineid + "#机小时记录表",
      props: {
        search_condition: {
          selecteddate: search_condition.selecteddate,
          machine: machineid
        },
        shift: search_condition.shift
      },
      width: "60%",
      draggable: true,
      fullscreenIcon: false,
      closeOnClickModal: true,
      contentRenderer: () => h(shiftdata)
    });
  };

  function onSizeChange(val) {
    console.log("onSizeChange", val);
  }

  const search_condition = reactive({
    selecteddate:
      current_shift() == "C"
        ? moment().subtract(1, "days").format("YYYY-MM-DD")
        : moment().format("YYYY-MM-DD"),
    shift: current_shift(),
    status: 1
  });

  const reset_condition = () => {
    search_condition.selecteddate =
      current_shift() == "C"
        ? moment().subtract(1, "days").format("YYYY-MM-DD")
        : moment().format("YYYY-MM-DD");
    search_condition.shift = current_shift();
    search_condition.status = 2;
  };

  const startRefresh = () => {
    setInterval(() => {
      reset_condition();
      refreshData();
    }, 120000);
  };

  // 检测查询条的变化
  watch(search_condition, newValue => {
    loading.value = true;
    dataList.value = [];
    is_current.value = true;
    if (newValue.shift != current_shift()) {
      is_current.value = false;
    } else {
      if (newValue.shift == "C") {
        if (
          newValue.selecteddate !=
          moment().subtract(1, "days").format("YYYY-MM-DD")
        ) {
          is_current.value = false;
        }
      } else {
        if (newValue.selecteddate != moment().format("YYYY-MM-DD")) {
          is_current.value = false;
        }
      }
    }
    refreshData();
  });

  const refreshData = () => {
    loading.value = true;
    getmachinelist(search_condition).then((res: { data: any }) => {
      dataList.value = res.data;
      loading.value = false;
    });
  };

  onMounted(() => {
    refreshData();
    startRefresh();
  });

  return {
    refreshData,
    search_condition,
    reset_condition,
    is_current,
    loading,
    columns,
    dataList,
    loadingConfig,
    adaptiveConfig,
    onSizeChange
  };
}

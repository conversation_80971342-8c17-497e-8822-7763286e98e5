from flask import Blueprint, request
from app.cs.model.models_cs import User, Role, Permission
from extensions import db
import requests
from config import config, env
from sqlalchemy import func
from app.cs.functions import login_required, create_token, responseError, responsePost, responseGet, responseDelete, responsePut
from app.cs.schemas import users_schema, permissions_schema, roles_schema
api = Blueprint('cs/loginAPI', __name__)


@api.route('/', methods=['GET'])
# @login_required
def DD():
    return responseDelete("删除用户成功", {'data': 33})


@api.route('/userid/<int:uid>', methods=['DELETE'])
@login_required
def deleteUser(uid):
    user = db.session.query(User).filter(User.Id == uid).scalar()
    db.session.delete(user)
    db.session.commit()
    return responseDelete("删除用户成功")


@api.route('/resetPassword', methods=['PUT'])
@login_required
def resetPassword():
    res = request.json
    uid = res.get('id')
    user = db.session.query(User).filter(User.Id == uid).scalar()
    email = user.email
    r = requests.post(config[env].api_url+"public/resetPassword",
                      {'username': email})
    vdata = r.json()
    if vdata['meta']['status'] != 202:
        return responseError(vdata['meta']['msg'])
    return responsePut(vdata['meta']['msg'])


@api.route('/changePassword', methods=['PUT'])
@login_required
def changePassword():
    res = request.json
    email = res.get('email')
    oldpass = res.get('oldpass')
    newpass = res.get('newpass1')
    r = requests.post(config[env].api_url+"public/changePassword",
                      {'username': email, 'oldpass': oldpass, 'newpass': newpass})
    vdata = r.json()
    if vdata['meta']['status'] != 202:
        return responseError(vdata['meta']['msg'])
    return responsePut(vdata['meta']['msg'])


@api.route('/getUsers', methods=['GET'])
@login_required
def getUsers():
    res = request.args
    query = res.get('query')
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    users = db.session.query(User.Id, User.email, User.auth, User.isactive, Role.name.label(
        'rolename')).outerjoin(Role, User.roleid == Role.Id).filter(User.email.like('%{0}%'.format(query))).order_by(
        User.roleid).paginate(pagenum, pagesize, error_out=False).items
    total = db.session.query(func.count(User.Id)).filter(
        User.email.like('%{0}%'.format(query))).scalar()
    userlist = users_schema.dump(users)
    for u in userlist:
        if u['isactive']:
            u['isactive'] = True
        else:
            u['isactive'] = False
        auth = u['auth']
        if auth:
            myAuth = gemAuth(auth)
            u['children'] = myAuth
    data = {'total': total, 'userlist': userlist}
    return responseGet("获取用户列表成功", data)


@api.route('/getRoles', methods=['GET'])
@login_required
def getRoles():
    roles = db.session.query(Role).all()
    rolelist = roles_schema.dump(roles)
    return responseGet("获取用户列表成功", rolelist)


def gemAuth(auth):
    try:
        m = db.session.query(Permission).filter(func.find_in_set(Permission.Id, auth)).all()
        menu = permissions_schema.dump(m)
    except Exception:
        return responseError('获取菜单列表失败，请联系管理员！')
    dic = {}
    outArray = []
    for m in menu:
        if m['pid'] == 0:
            dic[m['Id']] = m
            dic[m['Id']]['children'] = []
    for m in menu:
        if m['pid'] in dic.keys():
            dic[m['pid']]['children'].append(m)
    for value in dic.values():
        outArray.append(value)
    # print(outArray)
    return outArray


@api.route('/addUser', methods=['POST'])
def addUser():
    res = request.json
    email = res.get("email").lower()
    eid = res.get("eid")
    roleid = res.get("roleid")
    auth = db.session.query(Role).filter(Role.Id == roleid).scalar()
    try:
        user1 = User(email=email,
                     password='123',
                     roleid=roleid,
                     auth=auth.default_auth,
                     isactive=1)
        print(user1)
        db.session.add(user1)
        r = requests.post(config[env].api_url+"/public/addUser",
                          {'email': email, 'eid': eid})
        vdata = r.json()
        print(vdata)
        if vdata['meta']['status'] != 201:
            return responseError(vdata['meta']['msg'])
        else:
            db.session.commit()
            return responsePost('成功建立新用户', {'data': 'success'})
    except Exception:
        return responseError('建立新用户失败！')


@api.route('/editRoles', methods=['POST'])
def editRoles():
    res = request.json
    id = res.get("id")
    auth = res.get("auth")
    try:
        user = db.session.query(User).filter(User.Id == id).scalar()
        user.auth = auth
        db.session.commit()
    except Exception:
        return responseError('修改用户权限失败！')
    return responsePost('成功修改权限', {'data': 'success'})


@api.route("/login", methods=["POST"])
def login():
    res_dir = request.json
    if res_dir is None:
        res_dir = request.form
    if not len(res_dir):
        return responseError('1请输入用户名密码')
    # 获取前端传过来的参数
    email = res_dir.get("email")
    password = res_dir.get("password")
    r = requests.post(config[env].api_url+"/public/login",
                      {'username': email, 'password': password})
    vdata = r.json()
    if vdata['meta']['status'] != 201:
        return responseError(vdata['meta']['msg'])
    else:
        email = vdata['data']['email']
    try:
        user = db.session.query(User).filter(User.isactive == 1).filter(User.email == email).first()
    except Exception:
        # traceback.print_exc()
        return responseError('查询错误，请联系管理员！')
    if user is None:
        return responseError('本系统中此账户不存在或被禁用，请联系管理员处理！')
    data = {'token': create_token(user.Id), 'auth': user.auth, 'email': email}
    return responsePost('登录成功', data)

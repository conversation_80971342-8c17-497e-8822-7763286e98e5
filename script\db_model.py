import sqlalchemy as db
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from script_config import my_config

Base = declarative_base()


class Connection:
    def __init__(self):
        # 连接字符串
        self.db_url = my_config['SQLALCHEMY_BINDS']['im']
        self.__engine = None
        self.__Session = None

    def __enter__(self):
        self.__engine = create_engine(self.db_url)
        self.__Session = scoped_session(sessionmaker(bind=self.__engine))
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.__Session is not None:
            self.__Session.remove()
        if self.__engine is not None:
            self.__engine.dispose()

    def get_session(self):
        return self.__Session()


# 定义数据库模型
class alarm_log(Base):

    __tablename__ = "alarm_log"
    id = db.Column(db.Integer, primary_key=True)
    machine_id = db.Column(db.SmallInteger)
    code = db.Column(db.SmallInteger)
    record_time = db.Column(db.DateTime())


class alarm_type(Base):

    __tablename__ = "alarm_type"
    code = db.Column(db.String(5), primary_key=True)
    des = db.Column(db.String(16))


class auth_group(Base):

    __tablename__ = "auth_group"
    Id = db.Column(db.Integer, primary_key=True)
    pid = db.Column(db.Integer)
    name = db.Column(db.String(32))
    rules = db.Column(db.Text())
    updatetime = db.Column(db.DateTime())


class defect(Base):

    __tablename__ = "defect"
    id = db.Column(db.Integer, primary_key=True)
    recorddate = db.Column(db.Date())
    hourid = db.Column(db.SmallInteger)
    machine_id = db.Column(db.SmallInteger)
    pn = db.Column(db.String(24))
    defect_type = db.Column(db.SmallInteger)
    quantity = db.Column(db.Float())
    unit = db.Column(db.String(2))
    update_time = db.Column(db.DateTime())


class defecttype(Base):

    __tablename__ = "defecttype"
    id = db.Column(db.SmallInteger, primary_key=True)
    des = db.Column(db.String(16))


class downtime(Base):

    __tablename__ = "downtime"
    id = db.Column(db.Integer, primary_key=True)
    machine_id = db.Column(db.SmallInteger)
    recorddate = db.Column(db.Date())
    shift = db.Column(db.String(1))
    stop_type = db.Column(db.SmallInteger)
    stop_time = db.Column(db.SmallInteger)
    update_time = db.Column(db.DateTime())


class hourly_output(Base):

    __tablename__ = "hourly_output"
    id = db.Column(db.Integer, primary_key=True)
    recorddate = db.Column(db.Date())
    hourid = db.Column(db.SmallInteger)
    machine_id = db.Column(db.SmallInteger)
    pn = db.Column(db.String(24))
    output = db.Column(db.SmallInteger)
    adjustion = db.Column(db.SmallInteger)
    routing = db.Column(db.Float())
    uptime = db.Column(db.Integer)
    confirm_state = db.Column(db.SmallInteger)
    confirm_time = db.Column(db.DateTime())
    recoder = db.Column(db.String(10))


class machine(Base):

    __tablename__ = "machine"
    id = db.Column(db.SmallInteger, primary_key=True)
    model = db.Column(db.String(20))
    manufacturer = db.Column(db.String(4))
    ton = db.Column(db.SmallInteger)
    arm = db.Column(db.SmallInteger)
    pn = db.Column(db.String(32))
    active = db.Column(db.SmallInteger)


class menu(Base):

    __tablename__ = "menu"
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(16))
    node_type = db.Column(db.SmallInteger)
    icon = db.Column(db.String(32))
    extra_icon = db.Column(db.String(32))
    path = db.Column(db.String(64))
    redirect = db.Column(db.String(64))
    parent_id = db.Column(db.SmallInteger)
    show_parent = db.Column(db.SmallInteger)
    show_link = db.Column(db.SmallInteger)
    hidden_tag = db.Column(db.SmallInteger)
    rank = db.Column(db.SmallInteger)
    title = db.Column(db.String(32))
    keep_alive = db.Column(db.SmallInteger)
    frame = db.Column(db.String(64))
    frame_loading = db.Column(db.SmallInteger)
    created_time = db.Column(db.DateTime())
    updated_time = db.Column(db.DateTime())


class mix_bom(Base):

    __tablename__ = "mix_bom"
    id = db.Column(db.Integer, primary_key=True)
    material = db.Column(db.String(18))
    desc = db.Column(db.String(40))
    component = db.Column(db.String(18))
    component_des = db.Column(db.String(40))
    component_type = db.Column(db.SmallInteger)
    usage = db.Column(db.Float())
    unit = db.Column(db.String(2))
    updatetime = db.Column(db.DateTime())


class pc(Base):

    __tablename__ = "pc"
    id = db.Column(db.SmallInteger, primary_key=True)
    ip_addr = db.Column(db.String(15))
    managed_machine = db.Column(db.String(80))
    is_active = db.Column(db.SmallInteger)


class permission(Base):

    __tablename__ = "permission"
    Id = db.Column(db.Integer, primary_key=True)
    ismenu = db.Column(db.SmallInteger)
    pid = db.Column(db.SmallInteger)
    name = db.Column(db.String(20))
    order = db.Column(db.SmallInteger)
    icon = db.Column(db.String(16))
    path = db.Column(db.String(20))


class plan_im(Base):
    __tablename__ = "plan_im"
    Id = db.Column(db.Integer, primary_key=True)
    machine = db.Column(db.String(50))
    pn = db.Column(db.String(50))
    remark = db.Column(db.String(255))
    d1 = db.Column(db.String(10))
    d2 = db.Column(db.String(10))
    d3 = db.Column(db.String(10))
    d4 = db.Column(db.String(10))
    d5 = db.Column(db.String(10))
    d6 = db.Column(db.String(10))
    d7 = db.Column(db.String(10))
    d8 = db.Column(db.String(10))
    d9 = db.Column(db.String(10))
    d10 = db.Column(db.String(10))
    d11 = db.Column(db.String(10))
    d12 = db.Column(db.String(10))
    d13 = db.Column(db.String(10))
    type = db.Column(db.String(10))


class plan_packing(Base):
    __tablename__ = "plan_packing"
    Id = db.Column(db.Integer, primary_key=True)
    mo = db.Column(db.String(10))
    sku = db.Column(db.String(50))
    qty = db.Column(db.String(10))
    due = db.Column(db.String(10))
    category = db.Column(db.String(50))
    lid = db.Column(db.String(50))
    sump = db.Column(db.String(50))
    hh = db.Column(db.String(10))
    pktime = db.Column(db.String(10))
    remark = db.Column(db.String(255))


class prod_history(Base):

    __tablename__ = "prod_history"
    id = db.Column(db.Integer, primary_key=True)
    proddate = db.Column(db.Date())
    shift = db.Column(db.String(1))
    machine_id = db.Column(db.SmallInteger)
    pn = db.Column(db.String(32))
    starttime = db.Column(db.DateTime())
    endtime = db.Column(db.DateTime())
    editor = db.Column(db.String(7))
    updatetime = db.Column(db.DateTime())


class role(Base):

    __tablename__ = "role"
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(32))
    auth = db.Column(db.String(255))
    rank = db.Column(db.SmallInteger)
    init_url = db.Column(db.SmallInteger)
    active = db.Column(db.SmallInteger)
    remark = db.Column(db.String(32))
    create_time = db.Column(db.DateTime())
    update_time = db.Column(db.DateTime())


class routing(Base):

    __tablename__ = "routing"
    id = db.Column(db.Integer, primary_key=True)
    pn = db.Column(db.String(32))
    pn_des = db.Column(db.String(64))
    cavity = db.Column(db.SmallInteger)
    machine = db.Column(db.SmallInteger)
    unload = db.Column(db.SmallInteger)
    moldingcycle = db.Column(db.Float())
    manualcycle = db.Column(db.Float())
    sap_value = db.Column(db.Float())
    create_time = db.Column(db.DateTime())
    update_time = db.Column(db.DateTime())


class routing_change_type(Base):

    __tablename__ = "routing_change_type"
    id = db.Column(db.SmallInteger, primary_key=True)
    reason = db.Column(db.String(16))
    active = db.Column(db.SmallInteger)


class routing_log(Base):

    __tablename__ = "routing_log"
    id = db.Column(db.Integer, primary_key=True)
    pn = db.Column(db.String(32))
    machine = db.Column(db.SmallInteger)
    moldingcycle = db.Column(db.Float())
    unloadtime = db.Column(db.Float())
    cavity = db.Column(db.SmallInteger)
    version = db.Column(db.SmallInteger)
    effective_time = db.Column(db.DateTime())
    change_type = db.Column(db.SmallInteger)
    update_time = db.Column(db.DateTime())


class shift_state_log(Base):

    __tablename__ = "shift_state_log"
    id = db.Column(db.Integer, primary_key=True)
    machine_id = db.Column(db.SmallInteger)
    shift_date = db.Column(db.Date())
    shift_code = db.Column(db.String(1))
    machine_state = db.Column(db.SmallInteger)
    machine_state_des = db.Column(db.SmallInteger)
    start_time = db.Column(db.DateTime())
    end_time = db.Column(db.DateTime())
    update_mode = db.Column(db.SmallInteger)
    update_time = db.Column(db.DateTime())


class shift_sku_log(Base):

    __tablename__ = "shift_sku_log"
    id = db.Column(db.Integer, primary_key=True)
    machine_id = db.Column(db.SmallInteger)
    sku = db.Column(db.String(32))
    shift_date = db.Column(db.Date())
    shift_code = db.Column(db.String(1))
    start_time = db.Column(db.DateTime())
    end_time = db.Column(db.DateTime())
    update_time = db.Column(db.DateTime())


class shift_type(Base):

    __tablename__ = "shift_type"
    id = db.Column(db.SmallInteger, primary_key=True)
    state_name = db.Column(db.String(16))
    state_type = db.Column(db.SmallInteger)
    is_scheduled_stop = db.Column(db.SmallInteger)
    is_active = db.Column(db.SmallInteger)
    is_output = db.Column(db.SmallInteger)


class shift_info(Base):

    __tablename__ = "shift_info"
    id = db.Column(db.Integer, primary_key=True)
    proddate = db.Column(db.Date())
    shift = db.Column(db.String(1))
    machine_id = db.Column(db.SmallInteger)
    planned_stop = db.Column(db.Integer)
    uptime = db.Column(db.Integer)
    stdtime = db.Column(db.Integer)
    total_output = db.Column(db.SmallInteger)
    defect = db.Column(db.SmallInteger)
    update_time = db.Column(db.DateTime())


class uptime_log(Base):

    __tablename__ = "uptime_log"
    id = db.Column(db.Integer, primary_key=True)
    machine = db.Column(db.SmallInteger)
    start_time = db.Column(db.DateTime())
    end_time = db.Column(db.DateTime())


class user(Base):

    __tablename__ = "user"
    eid = db.Column(db.String(8), primary_key=True)
    email = db.Column(db.String(32))
    role = db.Column(db.String(32))
    name = db.Column(db.String(16))
    cnname = db.Column(db.String(4))
    avatar = db.Column(db.String(32))
    auth = db.Column(db.String(255))
    is_active = db.Column(db.SmallInteger)
    phone = db.Column(db.String(11))
    last_login = db.Column(db.DateTime())

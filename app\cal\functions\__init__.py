from config import config, env
from app.voc.model.models_voc import Dvoc,  OptionVoc, Dsort
from extensions import db
from itsdangerous import TimedJSONWebSignatureSerializer as Serializer
from functools import wraps
from flask import request
import traceback
from sqlalchemy import func, or_
import os
import shutil
from app.public.functions import responseExpire, responseError
from ultils.log_helper import ProjectLogger
mylogger = ProjectLogger()


def getServer():  # 获取上传文件的访问路径
    urls = {
        'templatePath': config[env].localPath+"cal/workfolder/templates/",
        'templateUrl': config[env].base_url+'cal/workfolder/templates/',
        'uploadPath': config[env].localPath+"cal/workfolder/uploads/",
        'uploadUrl': config[env].base_url+'cal/workfolder/templates/',
    }
    return urls


def storeFile(id, new):
    tempFile = getServer()['uploadPath']+'temp/'+new
    storePath = getServer()['uploadPath']+'store/'+str(id)+'/'
    if not os.path.exists(storePath):
        os.mkdir(storePath)
    shutil.move(tempFile, storePath)


# def getOptions(name):
#     optionArr = []
#     options = db.session.query(Options).filter(Options.name == name).all()
#     for o in options:
#         dic = {
#             'label': o.listitem,
#             'value': o.listitem
#         }
#         optionArr.append(dic)
#     return optionArr


def getOptionsVoc(name):
    optionArrVoc = []
    options = db.session.query(OptionVoc).filter(OptionVoc.name == name).all()
    for o in options:
        dic = {
            'label': o.listitem,
            'value': o.listitem
        }
        optionArrVoc.append(dic)
    return optionArrVoc


def getDsortVoc(name):
    optionArrVoc = []
    options = db.session.query(Dsort).filter(Dsort.sorttype == name).all()
    for o in options:
        dic = {
            'label': o.sortitem,
            'value': o.sortitem
        }
        optionArrVoc.append(dic)
    return optionArrVoc


def getvendorcarVoc():
    optionArrVoc = []
    options = db.session.query(Dvoc.vendor).group_by(Dvoc.vendor).all()
    for o in options:
        dic = {
            'value': o.vendor
        }
        optionArrVoc.append(dic)
    return optionArrVoc


# def getUserlist(plant):
#     users = db.session.query(Role.name, User.email).outerjoin(
#         User, User.roleid == Role.Id).filter(Role.plant == plant).filter(User.isactive == 1).all()
#     dic = {}
#     outArr = []
#     for u in users:
#         if u.name in dic.keys():
#             dic[u.name]['children'].append({
#                 'label': u.email.split('@')[0],
#                 'value': u.email,
#             })
#         else:
#             dic[u.name] = {
#                 'label': u.name,
#                 'value': u.name,
#                 'children': [{
#                     'label': u.email.split('@')[0],
#                     'value': u.email,
#                 }]
#             }
#     for k, v in dic.items():
#         outArr.append(v)
#     return outArr


# def getMytasks(owner):
#     tquery = db.session.query(func.count(Funnellog.Id)).join(
#         Funnel, Funnellog.funnelid == Funnel.Id).filter(Funnel.owner == owner).filter(Funnellog.recorddate.is_(None)).scalar()
#     return tquery

def getMytasks(owner):
    mylogger.debug('getMytasks')
    count = db.session.query(Dvoc.Id).filter(Dvoc.qa == owner.split('@')[0]).filter(
        or_(Dvoc.needrccm == 1, Dvoc.needopl == 1)).filter(Dvoc.status != 'closed').count()
    return count


def getLastyear(dt):
    return str(int(dt.split('-')[0])-1)+'-'+dt.split('-')[1]+'-'+dt.split('-')[2]


def login_required(view_func):  # 通过一个装饰函数来实现登录验证
    @wraps(view_func)
    def verify_token(*args, **kwargs):
        mylogger.info("*"*20+"verify_token start"+"*"*20)
        try:
            email = request.headers["email"]
            auth = request.headers["auth"]
            # print("ddd", auth, email)
            mylogger.debug(auth)
            # user = db.session.query(User).filter(User.email == email).first()
            # myAuth = db.session.query(Permission).filter(
            #     Permission.Id.in_(user.auth.split(','))).all()
            # authArr = []
            # for m in myAuth:
            #     authArr.append(m.name)
            # if(','.join(authArr) != auth):
            #     return responseError('登录校验失败，请重新登陆！')
            # 在请求头上拿到token
            token = request.headers["token"]
        except Exception:
            traceback.print_exc()
            return responseError('登录失败！')

        s = Serializer(config[env].SECRET_KEY)
        try:
            s.loads(token)
        except Exception:
            return responseExpire('登录已过期！请重新登录！')

        return view_func(*args, **kwargs)

    return verify_token

/**
 * 运费数据管理 Composable
 */
import { reactive, computed } from 'vue';
import type { FeeFormData, FreightOption, BestFreightOption, RowData } from '../types';

export function useFeeData() {
  // 运费表单数据
  const feeFormData = reactive<FeeFormData>({
    freight_fee: 0,
    fee_remark: "",
    freight_calc_method: 0,
    freight_unit_price: 0,
    freight_adjust: 0,
    freight_adjust_reason: "",
    logistic_remark: "",
    logistic_company: "",
    freight_total_fee: 0
  });

  // 计算总运费
  const totalFreight = computed(() => {
    return feeFormData.freight_fee + feeFormData.freight_adjust;
  });

  // 重置表单数据
  const resetFeeData = () => {
    Object.assign(feeFormData, {
      freight_fee: 0,
      fee_remark: "",
      freight_calc_method: 0,
      freight_unit_price: 0,
      freight_adjust: 0,
      freight_adjust_reason: "",
      logistic_remark: "",
      logistic_company: "",
      freight_total_fee: 0
    });
  };

  // 初始化表单数据
  const initFeeData = (rowData: RowData) => {
    feeFormData.freight_fee = rowData.freight_fee || 0;
    feeFormData.fee_remark = rowData.fee_remark || "";
    feeFormData.freight_calc_method = rowData.freight_calc_method || 0;
    feeFormData.freight_unit_price = rowData.freight_unit_price || 0;
    feeFormData.freight_adjust = rowData.freight_adjust || 0;
    feeFormData.freight_adjust_reason = rowData.freight_adjust_reason || "";
    feeFormData.logistic_remark = rowData.logistic_remark || "";
    feeFormData.logistic_company = rowData.logistic_company || "";
  };

  // 更新运费数据
  const updateFeeData = (option: FreightOption) => {
    feeFormData.logistic_company = option.template;
    feeFormData.freight_unit_price = option.unitprice;
    feeFormData.freight_fee = option.freight_fee;
    feeFormData.freight_calc_method = option.calc_method;
    feeFormData.freight_total_fee = totalFreight.value;
  };

  // 获取表单数据副本
  const getFeeDataCopy = (): FeeFormData => {
    return {
      freight_fee: feeFormData.freight_fee,
      fee_remark: feeFormData.fee_remark,
      freight_calc_method: feeFormData.freight_calc_method,
      freight_unit_price: feeFormData.freight_unit_price,
      freight_adjust: feeFormData.freight_adjust,
      freight_adjust_reason: feeFormData.freight_adjust_reason,
      logistic_remark: feeFormData.logistic_remark,
      logistic_company: feeFormData.logistic_company,
      freight_total_fee: totalFreight.value
    };
  };

  // 验证表单数据
  const validateFeeData = (): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!feeFormData.logistic_company) {
      errors.push('请选择物流公司');
    }

    if (feeFormData.freight_fee <= 0) {
      errors.push('运费金额必须大于0');
    }

    if (!feeFormData.freight_calc_method) {
      errors.push('请选择计费方式');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  };

  // 计算最优运费方案
  const calculateBestOption = (
    weightOptions: any[],
    volumeOptions: any[],
    rowData: RowData
  ): BestFreightOption | null => {
    let bestOption: BestFreightOption | null = null;
    let lowestPrice = Infinity;

    // 检查重量计费选项
    weightOptions.forEach((item, index) => {
      const totalPrice = (rowData.weight || 0) * item.unitprice;
      if (totalPrice < lowestPrice) {
        lowestPrice = totalPrice;
        bestOption = {
          option: {
            template: item.template,
            unitprice: item.unitprice,
            freight_fee: totalPrice,
            lowerlimit: item.lowerlimit,
            upperlimit: item.upperlimit,
            calc_method: 1
          },
          type: 'weight',
          index,
          totalPrice
        };
      }
    });

    // 检查体积计费选项
    volumeOptions.forEach((item, index) => {
      const totalPrice = (rowData.volume || 0) * item.unitprice;
      if (totalPrice < lowestPrice) {
        lowestPrice = totalPrice;
        bestOption = {
          option: {
            template: item.template,
            unitprice: item.unitprice,
            freight_fee: totalPrice,
            lowerlimit: item.lowerlimit,
            upperlimit: item.upperlimit,
            calc_method: 2
          },
          type: 'volume',
          index,
          totalPrice
        };
      }
    });

    return bestOption;
  };

  return {
    feeFormData,
    totalFreight,
    resetFeeData,
    initFeeData,
    updateFeeData,
    getFeeDataCopy,
    validateFeeData,
    calculateBestOption
  };
}

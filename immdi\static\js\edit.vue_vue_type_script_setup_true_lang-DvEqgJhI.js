var g=(w,v,n)=>new Promise((b,u)=>{var c=t=>{try{e(n.next(t))}catch(m){u(m)}},i=t=>{try{e(n.throw(t))}catch(m){u(m)}},e=t=>t.done?b(t.value):Promise.resolve(t.value).then(c,i);e((n=n.apply(w,v)).next())});import{a2 as E,d as M,n as s,b2 as D,G as A,r as d,o as B,g as N,h as r,b as o,u as k,f as C,aE as z}from"./index-BnxEuBzx.js";import{R as G}from"./index-8CyAUJH-.js";import{f as H}from"./system-DzNitOCO.js";import{requestHook as L}from"./hook-BWfQX1Id.js";const S=E({code:[{required:!0,message:"必填项",trigger:"blur"}],name:[{required:!0,message:"必填项",trigger:"blur"}]}),J=M({name:"SysRoleManagementEdit",__name:"edit",emits:["fetch-data"],setup(w,{expose:v,emit:n}){const b=n,u=s(),c=s({id:null,code:"",name:"",queue:0,description:""}),i=s(!1),e=s(D(c,!0)),t=s(""),m=s(!1),{showEdit:y,closeDialog:V}=G({defaultFormData:c,formData:e,formVisible:i,isAdd:m,ruleFormRef:u,title:t,titleExt:"角色",doneFn:()=>{b("fetch-data")}}),R=p=>g(this,null,function*(){p&&(yield p.validate(l=>g(this,null,function*(){if(l){const{code:f}=yield L(H(e.value));f===0&&(z("提交成功",{type:"success"}),V())}})))});return A(()=>e.value.name,p=>{e.value.name=p.replace(/[^a-zA-Z0-9\\-]/g,"").toLowerCase()}),v({showEdit:y}),(p,l)=>{const f=d("el-input"),_=d("el-form-item"),F=d("el-input-number"),U=d("el-form"),x=d("el-button"),q=d("el-dialog");return B(),N(q,{modelValue:i.value,"onUpdate:modelValue":l[5]||(l[5]=a=>i.value=a),title:t.value,width:"550px","before-close":k(V),draggable:!0},{footer:r(()=>[o(x,{onClick:k(V)},{default:r(()=>[C("取 消")]),_:1},8,["onClick"]),o(x,{type:"primary",onClick:l[4]||(l[4]=a=>R(u.value))},{default:r(()=>[C(" 确定 ")]),_:1})]),default:r(()=>[o(U,{ref_key:"ruleFormRef",ref:u,rules:k(S),"label-width":"100px",model:e.value},{default:r(()=>[o(_,{label:"编码",prop:"code"},{default:r(()=>[o(f,{modelValue:e.value.code,"onUpdate:modelValue":l[0]||(l[0]=a=>e.value.code=a),modelModifiers:{trim:!0},disabled:!m.value,placeholder:"请输入角色编码，限英文和数字"},null,8,["modelValue","disabled"])]),_:1}),o(_,{label:"名称",prop:"name"},{default:r(()=>[o(f,{modelValue:e.value.name,"onUpdate:modelValue":l[1]||(l[1]=a=>e.value.name=a),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),o(_,{label:"排序",prop:"rank"},{default:r(()=>[o(F,{modelValue:e.value.rank,"onUpdate:modelValue":l[2]||(l[2]=a=>e.value.rank=a),min:0,step:5},null,8,["modelValue"])]),_:1}),o(_,{label:"描述",prop:"remark"},{default:r(()=>[o(f,{modelValue:e.value.remark,"onUpdate:modelValue":l[3]||(l[3]=a=>e.value.remark=a),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1})]),_:1},8,["rules","model"])]),_:1},8,["modelValue","title","before-close"])}}});export{J as _};

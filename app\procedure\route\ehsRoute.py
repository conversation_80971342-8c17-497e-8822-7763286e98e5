from flask import Blueprint, request
from app.procedure.functions import download_img
from app.public.functions import responseError, responseGet
import requests
from config import config, myEnv

api = Blueprint('procedure/ehsAPI', __name__)

plantDic = {
    'Suzhou': 'SZ',
    'Wuxi': 'WX',
    'Ningbo': 'NB'
}


@api.route('/getSuggestion', methods=['GET'])  # 获取EHS统计和分类
def getBI():
    rres = request.args
    year = rres.get('year')
    res = requests.get(
        config[myEnv].cloud_url+"welean/bi/safetyAPI/getSummary", params={
            'plant': 'SZ',
            'year': year
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        data = content['data']
        return responseGet('成功', data)
    else:
        return responseError('失败')


@api.route('/getCross', methods=['POST'])  # 获取安全十字数据
def getCross():
    r = request.json
    stime = r.get('stime')
    etime = r.get('etime')
    plant = r.get('plant')
    # print(11, stime, etime, plant)
    wxline = ''
    res = requests.get(
        config[myEnv].cloud_url+"welean/bi/safetyAPI/getCross", params={
            'plant': plantDic[plant],
            'stime': stime,
            'etime': etime,
            'linename': wxline
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        data = content['data']['outArr']
        return responseGet('成功', data)
    else:
        return responseError('失败')


@api.route('/getClose', methods=['GET'])   # 获取按部门的安全问题完成率
def getClose():
    r = request.args
    year = r.get('year')
    res = requests.get(
        config[myEnv].cloud_url+"welean/bi/safetyAPI/getClose", params={
            'plant': 'SZ',
            'year': year
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        data = content['data']['data']
        return responseGet('成功', data)
    else:
        return responseError('失败')


@api.route('/getsafetydays', methods=['POST'])  # 获取安全天数
def getsafetydays():
    r = request.json
    plant = r.get('plant')
    res = requests.get(
        config[myEnv].cloud_url+"welean/bi/safetyAPI/getSafetydays", params={
            'plant': plantDic[plant]
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        data = content['data']
        return responseGet('成功', data)
    else:
        return responseError('失败')


@api.route('/getDetails', methods=['POST'])  # 获取具体的提案信息
def getDetails():
    r = request.json
    idate = r.get('idate')
    plant = r.get('plant')
    res = requests.get(
        config[myEnv].cloud_url+"welean/bi/safetyAPI/getlistbyday", params={
            'plant': plantDic[plant],
            'dt': idate
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        dts = content['data']['suggests']
        dts = downLoadPics(dts)
        return responseGet('成功', dts)
    else:
        return responseError('失败')


def downLoadPics(dts):
    imgPath = config[myEnv].pentair_path+"static/procedure/img/"
    for d in dts:
        p = d['beforepic']
        pa = d['afterpic']
        picurl = p[p.rfind('/')+1:].split('.')
        if len(picurl) == 2:
            picName = picurl[0]
            picAdx = picurl[1]
        else:
            picName = ''
            picAdx = ''
        if p:
            download_img(p, imgPath+picName+'.'+picAdx)
            d['beforepic'] = config[myEnv].pentair_url+'procedure/img/'+picName+'.'+picAdx
            d['picsArr'].append(d['beforepic'])
        if pa:
            download_img(pa, imgPath+picName+'after.'+picAdx)
            d['afterpic'] = config[myEnv].pentair_url+'procedure/img/'+picName+'after.'+picAdx
            d['picsArr'].append(d['afterpic'])
    return dts


localeDic = {
    '虚惊事件': 'Near miss',
    '急救事故/财产损失': 'First aid/property damage',
    '不安全行为或不安全状况': 'Unsafe Act/Condition',
    '可记录事件': 'Recordable accident',
}


@api.route('/getEHS', methods=['GET'])  # 获取EHS统计和分类
def getEHS():
    res = request.args
    plant = res.get('plant')
    year = res.get('year')
    locale = res.get('locale')
    res = requests.get(
        config[myEnv].cloud_url+"welean/bi/safetyAPI/gett3ehs", params={
            'plant': plantDic[plant],
            'year': year
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        pyramid = content['data']['pyramid']
        word = content['data']['word']
        if locale != 'zh':
            for p in pyramid:
                p['name'] = localeDic[p['name']]
        return responseGet('成功', {'pyramid': pyramid, 'word': word})
    else:
        return responseError('获取问题列表失败，请尝试刷新或联系管理员')


@api.route('/getEHSDetail', methods=['GET'])  # 获取EHS统计和分类
def getEHSDetail():
    res = request.args
    plant = res.get('plant')
    year = res.get('year')
    type2 = res.get('type2')
    res = requests.get(
        config[myEnv].cloud_url+"welean/bi/safetyAPI/gett3ehsdetail", params={
            'plant': plant,
            'year': year,
            'type2': type2
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        dts = content['data']['suggests']
        dts = downLoadPics(dts)
        return responseGet('成功', {'detailList': dts})
    else:
        return responseError('失败')

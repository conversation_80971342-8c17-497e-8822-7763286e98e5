import { list<PERSON><PERSON><PERSON>, delMenu, Api<PERSON><PERSON>ult } from "@/api/system";
import { ref, onMounted } from "vue";
import { message } from "@/utils/message";
import { AxiosError } from "axios";

export const requestHook = async (fn: Promise<ApiResult>) => {
  try {
    const { meta, data } = await fn;
    if (meta.status !== 200) {
      message(meta.msg, { type: "error" });
      return;
    }
    return { code: meta.status, msg: meta.msg, data: data };
  } catch (e) {
    if ((e as AxiosError)?.response?.status !== 200) {
      const msg = e.response?.data?.msg ?? e;
      message(msg, { type: "error" });
    } else {
      message(e, { type: "error" });
    }
    return {
      code: -1,
      msg: "error",
      data: null
    };
  }
};

export function useSysMenuManagement(editRef: any) {
  const dataList = ref([]);
  const loading = ref(true);

  async function fetchData() {
    loading.value = true;
    const { data } = await requestHook(listMenu());
    dataList.value = data;
    loading.value = false;
  }

  function handleEdit(row: any) {
    editRef.value.fetchData();
    if (row.id) {
      editRef.value.showEdit(row);
    } else {
      editRef.value.showEdit();
    }
  }

  function handleEditChild(row: any) {
    if (row.id) {
      editRef.value.fetchData();
      editRef.value.showEditWithParent(row.id);
    }
  }

  async function handleDelete(row: any) {
    const { code } = await requestHook(delMenu({ id: row.id }));
    if (code === 0) {
      message("删除成功", { type: "success" });
      fetchData();
    }
  }

  onMounted(() => {
    fetchData();
  });

  return {
    dataList,
    loading,
    handleEdit,
    handleEditChild,
    handleDelete,
    fetchData
  };
}

from extensions import ma


class UserSchema(ma.Schema):
    class Meta:
        fields = ('Id', 'email', 'roleid', 'auth', 'isactive', 'name', 'rolename')


# Init schema
user_schema = UserSchema()
users_schema = UserSchema(many=True)


class RoleSchema(ma.Schema):
    class Meta:
        fields = ('Id', 'name', 'default_auth')


roles_schema = RoleSchema(many=True)


class AuthSchema(ma.Schema):
    class Meta:
        fields = ('Id', 'pid', 'name', 'order', 'path')


auth_schema = AuthSchema(many=True)


class OptionSchema(ma.Schema):
    class Meta:
        fields = ('Id', 'name', 'listitem')


option_schema = OptionSchema(many=True)


class PermissionSchema(ma.Schema):
    class Meta:
        fields = ('Id',  'pid', 'name', 'order', 'path', 'ismenu')


permissions_schema = PermissionSchema(many=True)


class PlannerSchema(ma.Schema):
    class Meta:
        fields = ('Id',  'mprc', 'planner', 'plannerbackup', 'supervisor')


planners_schema = PlannerSchema(many=True)


class EdiSchema(ma.Schema):
    class Meta:
        fields = ('Id',  'planner', 'customer')


edis_schema = EdiSchema(many=True)


class OrderlistSchema(ma.Schema):
    class Meta:
        fields = ('Id',  'salesdoc', 'line', 'customer', 'shipto', 'mgroup',
                  'mgroupdesc',  'sku', 'skudesc', 'orderqty', 'unit', 'requestdate',
                  'orderdate',  'mprc', 'ordertype', 'updatedate', 'updatereason', 'updateremark',
                  'planner',  'ponumber', 'confirmer', 'confirmdate', 'recorddate')


orderlists_schema = OrderlistSchema(many=True)
orderlist_schema = OrderlistSchema()

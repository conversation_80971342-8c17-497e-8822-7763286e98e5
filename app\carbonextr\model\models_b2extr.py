from extensions import db


class feed(db.Model):
    __bind_key__ = "carbon_extr"
    __tablename__ = "feed"
    id = db.Column(db.Integer, primary_key=True)
    machine = db.Column(db.SmallInteger)
    mix_sn = db.Column(db.String(12))
    feeding_date = db.Column(db.DateTime())

    def to_json(self):
        dict = self.__dict__
        if "_sa_instance_state" in dict:
            del dict["_sa_instance_state"]
        return dict

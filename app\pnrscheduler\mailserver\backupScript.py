import os
import time
import pymysql
import pandas as pd
from utils.config import cfg
import datetime
from sqlalchemy import create_engine
import subprocess
import sys
from utils.mylog import logger


def update_libs():
    try:
        # 使用 subprocess.run 来执行 pip 更新命令
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'chinese_calendar'], check=True)
        print("Successfully updated the 'chinese_calendar' library.")
    except subprocess.CalledProcessError as e:
        print(f"Error occurred while updating 'chinese_calendar': {e}")


def backup_database(cursor, db_name, backup_file_path):
    """备份单个数据库"""
    try:
        with open(backup_file_path, 'w', encoding='utf8') as f:
            # 写入数据库创建语句
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"-- 数据库 {db_name} 的备份\n")
            f.write(f"-- 创建时间: {current_time}\n\n")
            f.write(f"CREATE DATABASE IF NOT EXISTS `{db_name}`;\n")
            f.write(f"USE `{db_name}`;\n\n")

            # 获取所有表
            cursor.execute(f"SHOW TABLES FROM {db_name}")
            tables = [table[0] for table in cursor.fetchall()]

            # 备份每个表
            for table in tables:
                # print(f"正在备份表 {db_name}.{table}")

                # 获取建表语句
                cursor.execute(f"SHOW CREATE TABLE {db_name}.{table}")
                create_table_sql = cursor.fetchone()[1]

                # 写入建表语句
                f.write(f"DROP TABLE IF EXISTS `{table}`;\n")
                f.write(f"{create_table_sql};\n\n")

                # 获取数据
                cursor.execute(f"SELECT * FROM {db_name}.{table}")
                rows = cursor.fetchall()
                if rows:
                    # 获取列名
                    columns = [desc[0] for desc in cursor.description]
                    column_names = f"(`{'`, `'.join(columns)}`)"

                    # 写入数据
                    for row in rows:
                        values = []
                        for val in row:
                            if val is None:
                                values.append('NULL')
                            elif isinstance(val, (int, float)):
                                values.append(str(val))
                            elif isinstance(val, bytes):
                                values.append(f"0x{val.hex()}")
                            elif isinstance(val, datetime.datetime):
                                values.append(f"'{val.strftime('%Y-%m-%d %H:%M:%S')}'")
                            else:
                                values.append("'" + str(val).replace("'", "''") + "'")
                        f.write(f"INSERT INTO `{table}` {column_names} VALUES ({', '.join(values)});\n")
                f.write("\n")

        return True
    except Exception as e:
        logger.critical(f"备份数据库 {db_name} 时发生错误: {str(e)}")
        return False


def backupMysql():
    """主备份函数"""
    backup_dir = cfg.backupFolder + 'mysql'

    try:
        # 建立数据库连接
        db_conn = pymysql.connect(
            host=cfg.emdidb['host'],
            user=cfg.emdidb['user'],
            password=cfg.emdidb['password'],
            charset='utf8mb4'
        )

        cursor = db_conn.cursor()

        # 获取所有数据库
        cursor.execute('SHOW DATABASES')
        exclude_dbs = ['information_schema', 'performance_schema', 'phpmyadmin', 'sys', 'pentair', 'mysql']
        databases = [db[0] for db in cursor.fetchall() if db[0] not in exclude_dbs]

        # 创建备份根目录
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # 备份每个数据库
        for db_name in databases:
            try:
                # 创建数据库专属备份目录
                db_backup_dir = os.path.join(backup_dir, db_name)
                if not os.path.exists(db_backup_dir):
                    os.makedirs(db_backup_dir)

                # 生成备份文件路径
                timestamp = time.strftime("%Y%m%d%H%M%S")
                backup_file_path = os.path.join(db_backup_dir, f'{db_name}_{timestamp}.sql')

                # 执行备份
                backup_database(cursor, db_name, backup_file_path)
                removeOldestBackup(backup_dir, db_name)
            except Exception as e:
                logger.critical(f"处理数据库 {db_name} 时发生错误: {str(e)}")
                continue

    except pymysql.Error as e:
        logger.critical(f"数据库连接或操作错误: {str(e)}")
    except Exception as e:
        logger.critical(f"发生未预期的错误: {str(e)}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'db_conn' in locals():
            db_conn.close()


def removeOldestBackup(backup_dir, db_name, max_backups=10):
    db_backup_dir = os.path.join(backup_dir, db_name)
    # 检查数据库备份目录是否存在
    if not os.path.exists(db_backup_dir):
        print(f"备份目录 {db_backup_dir} 不存在，无需删除")
        return
    # 获取备份目录中的所有文件（排除子目录）
    backup_files = [f for f in os.listdir(db_backup_dir) if os.path.isfile(os.path.join(db_backup_dir, f))]
    # 如果文件数量超过最大限制，删除最旧的文件
    print(backup_files)
    if len(backup_files) > max_backups:
        # 按创建时间排序，最旧的文件在前
        backup_files.sort(key=lambda x: os.path.getctime(os.path.join(db_backup_dir, x)))

        # 计算需要删除的文件数量
        files_to_delete = len(backup_files) - max_backups
        # 删除最旧的文件
        print('old', files_to_delete)
        for i in range(files_to_delete):
            oldest_file = backup_files[i]
            oldest_file_path = os.path.join(db_backup_dir, oldest_file)
            if os.path.exists(oldest_file_path):
                os.remove(oldest_file_path)


def backupMssqlVOC():
    # Define the database connection parameters
    server = cfg.vocdb['host']
    database = cfg.vocdb['database']
    username = cfg.vocdb['user']
    password = cfg.vocdb['password']
    tables = ['dvoc', 'dopl']

    # Create the connection string
    connection_string = f'mssql+pymssql://{username}:{password}@{server}/{database}'

    # Create a SQLAlchemy engine
    engine = create_engine(connection_string)

    for table in tables:
        # Create backup folder
        backup_path = os.path.join(cfg.backupFolder, 'mssql', table)
        os.makedirs(backup_path, exist_ok=True)

        # Define the output file path and name
        output_file_path = os.path.join(backup_path, f'{table}_{time.strftime("%Y%m%d%H%M%S")}.csv')

        # Execute the query to select all data from the table
        query = f'SELECT * FROM {table}'
        data = pd.read_sql(query, engine)

        # Save data to CSV
        data.to_csv(output_file_path, index=False)
        removeOldestBackup(cfg.backupFolder+'mssql/', table)
    engine.dispose()

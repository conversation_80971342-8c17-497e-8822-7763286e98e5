import{b as l}from"./front-CiGk0t8u.js";import{u as t}from"./prod-CmDsiAIL.js";import{n,G as c,a2 as r,S as p,q as h,b as m}from"./index-BnxEuBzx.js";import"./moment-C3TZ8gAF.js";function y(){const s=n([]),i=n(!0);c([()=>t().selectedDate,()=>t().shift,()=>t().machineId],(e,b)=>{a.selecteddate=e[0],a.shift=e[1],a.machine=e[2],o()});const a=r({selecteddate:t().selectedDate,machine:t().machineId,shift:t().shift}),o=()=>{l(p(a)).then(e=>{s.value=e.data,i.value=!1})};h(()=>{o(),i.value=!1});const d=r({text:"正在加载不良记录数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `});return{dataList:s,columns:[{label:"小时",prop:"hourid",minWidth:60,cellRenderer:({row:e})=>m("div",null,[`${e.hourid}:00~${e.hourid+1}:00`])},{label:"料号",prop:"pn",minWidth:80},{label:"类型",prop:"defect_type",minWidth:60},{label:"数量",prop:"quantity",minWidth:40},{label:"单位",prop:"unit",minWidth:40}],loadingConfig:d,adaptiveConfig:{fixHeader:!0},loading:i,refreshData:o}}export{y as useColumns};

import traceback
from flask import Blueprint, request
from app.welean.model.models_welean import Appvowners, Userinfo, Auth, Permission
from extensions import db
from app.public.functions import responseError, responseGet, login_required, responsePost
from app.welean.functions import getServer
from datetime import datetime
from openpyxl import load_workbook
api = Blueprint('welean/V304/manageAPI', __name__)

# 目前办公两种服务，services里的picToExcel和excelTrans，对应options里分类services的上锁的pics和trans


@api.route('/submitAuth', methods=['POST'])
@login_required
def submitAuth():
    res = request.json
    list = res.get('list')
    eid = res.get('eid').split('/')[0]
    authArr = []
    for ll in list:
        for c in ll['children']:
            if c['checked']:
                authArr.append(str(c['id']))
                authArr.append(str(ll['id']))
    auth = ','.join(set(authArr))
    user = db.session.query(Auth.eid).filter(Auth.eid == eid).scalar()
    print(user)
    try:
        if user:
            db.session.query(Auth).filter(Auth.eid == eid).update({'auth': auth})
        else:
            myAuth = Auth(eid=eid, auth=auth)
            db.session.add(myAuth)
        db.session.commit()
        return responsePost('权限修改成功')
    except Exception:
        db.session.rollback()
        return responseError('权限修改失败')


@ api.route('/getAuth', methods=['GET'])
@ login_required
def getAuth():
    res = request.args
    eid = res.get('eid').split('/')[0]
    outDic = {}
    myAuth = db.session.query(Auth.auth).filter(Auth.eid == eid).scalar()
    if not myAuth:
        myAuth = []
    else:
        myAuth = myAuth.split(',')
    permissions = db.session.query(Permission).filter(
        Permission.ptype == 'pims').order_by(Permission.order).all()
    for p in permissions:
        if p.pid == 0:
            outDic[p.Id] = {
                'id': p.Id,
                'name': p.name,
                'children': []
            }
    for p in permissions:
        if p.pid > 0 and p.needauth == 1:
            outDic[p.pid]['children'].append(
                {
                    'name': p.name,
                    'id': p.Id,
                    'checked': True if str(p.Id) in myAuth else False,
                    'disabled': True if p.pid == 18 else False
                }
            )
    return responseGet("获取列表成功", {'list': list(outDic.values())})


@ api.route('/uploadUsers', methods=['POST'])
@ login_required
def uploadUsers():
    file_obj = request.files.get('file')
    plant = request.headers["plant"]
    print(file_obj)
    if file_obj:
        try:
            print('aaaaaa')
            rst = getUsersfromExcel(file_obj)
            if rst == 'na':
                return responseError("上传文件格式不正确，请确认是否和下载文件完全一致")
            # print(rst)
            deactivesql = "update wl_userinfo set active=0 where plant='%s'" % plant
            deaccountsql = "update wl_account left join wl_userinfo on wl_account.eid=wl_userinfo.eid set isactive=active"
            db.session.execute(deactivesql)
            db.session.execute(rst)
            db.session.execute(deaccountsql)
            db.session.commit()
            return responsePost("更新成功")
        except Exception:
            db.session.rollback()
            traceback.print_exc()
    return responseError("上传文件失败，请联系管理员")


def getUsersfromExcel(file):
    wb = load_workbook(file)
    ws = wb['userinfo']
    eid = (ws.cell(row=1, column=1).value == 'eid')
    dept1 = (ws.cell(row=1, column=2).value == 'dept1')
    dept2 = (ws.cell(row=1, column=3).value == 'dept2')
    dept3 = (ws.cell(row=1, column=4).value == 'dept3')
    cname = (ws.cell(row=1, column=5).value == 'cname')
    ename = (ws.cell(row=1, column=6).value == 'ename')
    plant = (ws.cell(row=1, column=7).value == 'plant')
    outArr = []
    if not (eid and dept1 and dept2 and dept3 and cname and ename and plant):
        return 'na'
    for r in range(2, ws.max_row + 1):
        if ws.cell(r, 1).value:
            r1 = ws.cell(r, 1).value if ws.cell(r, 1).value else ""
            r2 = ws.cell(r, 2).value if ws.cell(r, 2).value else ""
            r3 = ws.cell(r, 3).value if ws.cell(r, 3).value else ""
            r4 = ws.cell(r, 4).value if ws.cell(r, 4).value else ""
            r5 = ws.cell(r, 5).value if ws.cell(r, 5).value else ""
            r6 = ws.cell(r, 6).value if ws.cell(r, 6).value else ""
            r7 = ws.cell(r, 7).value if ws.cell(r, 7).value else ""
            info = ("'"+r1+"'", "'"+r2+"'", "'"+r3+"'", "'"+r4 +
                    "'", "'"+r5+"'", "'"+r6+"'", "'"+r7+"'", '1')
            outArr.append('('+','.join(info)+')')
    outString = "replace into wl_userinfo(eid,dept1,dept2,dept3,cname,ename,plant,active) values " + \
        ','.join(outArr)
    return outString


@ api.route('/downloadUsers', methods=['GET'])
@ login_required
def downloadUsers():
    res = request.args
    plant = res.get('plant')
    users = db.session.query(Userinfo).filter(
        Userinfo.plant == plant).filter(Userinfo.active == 1).all()
    if users:
        path = getServer()['downloadsPath']
        excelFile = 'users' + datetime.now().strftime("%Y-%m-%d %H%M%S") + '.xlsx'
        createExcel(users, path, excelFile)
        return responseGet("获取列表成功", {'url': getServer()['downloadsUrl']+excelFile})
    else:
        return responseError("没有任何数据可以下载")


def createExcel(query, path, excelFile):
    wb = load_workbook(path+'templates/userTemplate.xlsx')
    sht = wb['userinfo']
    i = 2
    for q in query:
        sht.cell(row=i, column=1).value = q.eid
        sht.cell(row=i, column=2).value = q.dept1
        sht.cell(row=i, column=3).value = q.dept2
        sht.cell(row=i, column=4).value = q.dept3
        sht.cell(row=i, column=5).value = q.cname
        sht.cell(row=i, column=6).value = q.ename
        sht.cell(row=i, column=7).value = q.plant
        i += 1
    wb.save(path + excelFile)


@api.route('/addUser', methods=['POST'])
@login_required
def addUser():
    res = request.json
    eid = res.get('eid')
    cname = res.get('cname')
    dept1 = res.get('dept1')
    plant = res.get('plant')
    try:
        user = Userinfo(eid=eid, cname=cname, dept1=dept1, active=1, dept2='', plant=plant)
        db.session.add(user)
        db.session.commit()
        return responsePost('添加成功')
    except Exception:
        db.session.rollback()
    return responseError('添加失败,可能该员工工号已经再系统中了')


@api.route('/deleteAppv', methods=['POST'])
@login_required
def deleteAppv():
    res = request.json
    id = res.get('id')
    print(id)
    try:
        db.session.query(Appvowners).filter(Appvowners.Id == id).delete()
        db.session.commit()
        return responsePost('删除成功')
    except Exception:
        db.session.rollback()
    return responseError('删除失败')


@api.route('/getAppvList', methods=['GET'])
@login_required
def getAppvList():
    res = request.args
    plant = res.get('plant')
    dept = res.get('dept')
    appvs = db.session.query(Appvowners.Id, Appvowners.eid, Appvowners.dept, Userinfo.cname).join(
        Userinfo, Appvowners.eid == Userinfo.eid).filter(Appvowners.plant == plant)
    if dept != '':
        appvs = appvs.filter(Appvowners.dept == dept)
    appvs = appvs.order_by(Appvowners.dept).all()
    appvList = []
    for a in appvs:
        dic = {
            'id': a.Id,
            'eid': a.eid,
            'cname': a.cname,
            'dept': a.dept
        }
        appvList.append(dic)
    return responseGet("获取列表成功", {'appvList': appvList})


@api.route('/addAppv', methods=['POST'])
@login_required
def addAppv():
    res = request.json
    plant = res.get('plant')
    dept = res.get('dept')
    eid = res.get('eid')
    try:
        appv = Appvowners(eid=eid, plant=plant, dept=dept)
        db.session.add(appv)
        db.session.commit()
        appvs = db.session.query(Appvowners.Id, Appvowners.eid, Appvowners.dept, Userinfo.cname).join(
            Userinfo, Appvowners.eid == Userinfo.eid).filter(Appvowners.plant == plant).filter(Appvowners.dept == dept).order_by(Appvowners.dept).all()
        appvList = []
        for a in appvs:
            dic = {
                'id': a.Id,
                'eid': a.eid,
                'cname': a.cname,
                'dept': a.dept
            }
            appvList.append(dic)
        return responsePost("获取列表成功", {'appvList': appvList})
    except Exception:
        db.session.rollback()
    return responseError('同一个部门不能有重复的批准人')

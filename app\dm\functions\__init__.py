from config import config, env
from app.dm.model.models_dm import K<PERSON>, Probleminfo, Scansub, User, Permission, Lineinfo, Skuinfo, Flexinfo, Routing, Issuelog, Probleminfooee, Epeisku, Epeistock
from extensions import db
from itsdangerous import TimedJSONWebSignatureSerializer as Serializer
from functools import wraps
from flask import request
from sqlalchemy import desc, or_
import traceback
import datetime
import requests
import os
from app.public.functions import responseExpire, responseError
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.sms.v20190711 import sms_client, models
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.common import credential
from app.public.functions.ews import sendMailTread
import hashlib

subCheckList = ['隐码']
mgroups = [
    {
        "label": "CAN",
        'value': 'CAN',
        'children': [
            {
                "label": "CAN-SAM",
                'value': 'CAN-SAM'
            },
            {
                "label": "CAN-Saptum",
                'value': 'CAN-Saptum'
            },
            {
                "label": "CANMaker",
                'value': 'CANMaker'
            }
        ]
    },
    {
        "label": "Meltblown",
        'value': 'Meltblown',
        'children': [
            {
                "label": "MB包装",
                'value': 'MB包装'
            },
            {
                "label": "Meltblown",
                'value': 'Meltblown'
            }
        ]
    },
    {
        "label": "B3",
        'value': 'B3',
        'children': [
            {
                "label": "IM装配",
                'value': 'IM装配'
            }
        ]
    },
    {
        "label": "B2",
        'value': 'B2',
        'children': [
            {
                "label": "RFC",
                'value': 'RFC'
            },
            {
                "label": "GAC",
                'value': 'GAC'
            },
            {
                "label": "Wound",
                'value': 'Wound'
            },
            {
                "label": "Extrusion",
                'value': 'Extrusion'
            }
        ]
    },
    {
        "label": "Tank",
        'value': 'Tank',
        'children': [
            {
                "label": "Winding - 1#/2#",
                'value': 'Winding - 1#/2#'
            },
            {
                "label": "Winding - 3#",
                'value': 'Winding - 3#'
            },
            {
                "label": "Winding - 4#",
                'value': 'Winding - 4#'
            },
            {
                "label": "Roto molding",
                'value': 'Roto molding'
            },
            {
                "label": "Blow molding - 80L",
                'value': 'Blow molding - 80L'
            },
            {
                "label": "Blow molding - 250L",
                'value': 'Blow molding - 250L'
            }
        ]
    }
]
# 计算OEE的linegroup
oeeArr = ['SmallTank', 'LargeTank', 'Tank包装', 'Meltblown', 'CAN', 'Extrusion']
lineShort = {
    'B4WR': '洁净房',
    'B4UF': 'UF',
    'B4Meltblown': '熔喷',
}
scanpackArr = ["IM装配"]
faiArr = ['MB包装', 'RFC', 'GAC', 'Wound']
ctqOptions = {
    'SubSNRecord': {
        'lable': '子序列号记录验证',
        'value': 'SubSNRecord',
        'va1': '序列号长度',
        'va2': '序列号前几位'
    },
    'DataCollection': {
        'lable': '数据采集',
        'value': 'DataCollection',
        'va1': '采集值下限',
        'va2': '采集值上限'
    },
    'Zeroaction': {
        'lable': '仅扫码验证',
        'value': 'Zeroaction',
    }
}


def uploadIMEI(imei, sn, markid, snverify):
    content = [
        [imei, sn, markid]
    ]
    now = datetime.datetime.now()
    td = datetime.datetime.strftime(now, '%Y-%m-%d %H:%M:%S')
    mystr = (sn + str(datetime.datetime.now())).encode('UTF-8')
    id = snverify[:5]+hashlib.md5(mystr).hexdigest()[9:18]
    myJson = {
        "gmt_create": td,
        "id": id,
        "count": 1,
        "gmt_submit": td,
        "sn_code": snverify,
        "content": content
    }
    print(myJson)
    headers = {'x-pentair-application-token': 'c2ad5d337db248c09088ae7c791e77ef'}
    res1 = requests.post(
        "https://api.iot.pentair.com.cn/scan/upload", json=myJson,
        headers=headers
    )
    content1 = res1.json()
    print(content1)
    return content1


def searchIMEI(data):
    headers = {'x-pentair-application-token': 'c2ad5d337db248c09088ae7c791e77ef'}
    res1 = requests.post(
        "https://api.iot.pentair.com.cn/scan/upload", json=data,
        headers=headers
    )
    content1 = res1.json()
    print(content1)
    return content1


def download_img(url, path):
    # print(url, path, r.status_code, os.path.exists(path))  # 返回状态码
    if os.path.exists(path):
        pass
    else:
        r = requests.get(url)
        if r.status_code == 200:
            img = r.content
            with open(path, 'wb') as f:
                f.write(img)


def checkSubunique(sns, names):
    for i in range(len(names)):
        if names[i] in subCheckList:
            sub = db.session.query(Scansub).filter(
                Scansub.subsn == sns[i]).first()
            if sub:
                return [names[i], sns[i]]
    return 'pass'


def andonMsg(phones, mails, linename):  # 根据产线的andon发送短信和发送邮件
    try:
        cred = credential.Credential(
            "AKIDUL5UeX1zZ4HItGqFLIacP0LQFKGx3RLy", "bxNWLvGOIwKn02qNVLuPgs93mMlu9CxV")
        httpProfile = HttpProfile()
        httpProfile.reqMethod = "POST"  # POST 请求（默认为 POST 请求）
        httpProfile.reqTimeout = 30  # 请求超时时间，单位为秒（默认60秒）
        httpProfile.endpoint = "sms.tencentcloudapi.com"  # 指定接入地域域名（默认就近接入）
        # 实例化一个客户端配置对象，可以指定超时时间等配置
        clientProfile = ClientProfile()
        clientProfile.signMethod = "TC3-HMAC-SHA256"  # 指定签名算法
        clientProfile.language = "en-US"
        clientProfile.httpProfile = httpProfile
        client = sms_client.SmsClient(cred, "ap-guangzhou", clientProfile)
        req = models.SendSmsRequest()
        req.SmsSdkAppid = "1400149076"
        req.Sign = "welean"
        req.SessionContext = "xxx"
        req.PhoneNumberSet = phones
        req.TemplateID = "208852"
        req.TemplateParamSet = [linename, "产线安灯警报", "产线员工"]
        resp = client.SendSms(req)
        sendMailTread(mails, '收到产线' + linename + '发出的安灯警报', '请及时去产线处理')
        # 输出 JSON 格式的字符串回包
        print(resp.to_json_string(indent=2))
    except TencentCloudSDKException as err:
        print(err)
        return False
    return True


def getproblems(linegroups, stype='', days=7):  # 获取问题分类和trigger点，trigger点根据选择的日期范围进行换算
    problems = db.session.query(Probleminfo).filter(
        or_(Probleminfo.linename == 'ALL', Probleminfo.linename.in_(linegroups)))
    if stype:
        problems = problems.filter(Probleminfo.sqdctype == stype)
    problems = problems.order_by(Probleminfo.sqdctype).all()
    outArr = []
    for p in problems:
        outArr.append({
            'sqdctype': p.sqdctype,
            'ptype': p.problemtype,
            'trigger': round(p.trigger/7*days, 0)
        })
    return outArr


def getproblemsoee(linegroups, stype='', days=7):  # 获取问题分类和trigger点，trigger点根据选择的日期范围进行换算
    problems = db.session.query(Probleminfooee).filter(
        or_(Probleminfooee.linename == 'ALL', Probleminfooee.linename.in_(linegroups)))
    if stype:
        problems = problems.filter(Probleminfooee.sqdctype == stype)
    problems = problems.order_by(Probleminfooee.sqdctype).all()
    outArr = []
    for p in problems:
        outArr.append({
            'sqdctype': p.sqdctype,
            'ptype': p.problemtype,
            'trigger': round(p.trigger/7*days, 0)
        })
    return outArr


def getkpi(tyear, linegroup):  # 获取QDC的KPI
    target = db.session.query(Kpi).filter(Kpi.targetyear == tyear).filter(
        Kpi.linegroup == linegroup).first()
    if not target:
        target = db.session.query(Kpi).filter(
            Kpi.targetyear == tyear).filter(Kpi.linegroup == 'all').first()
    if not target:
        target = db.session.query(Kpi).filter(Kpi.linegroup == 'all').first()
    outDic = {
        'ctarget': target.ctarget,
        'qtarget': target.qtarget,
        'dtarget': target.dtarget,
        'utiltarget': target.utiltarget
    }
    return outDic


def getReqQtyByShift(headcount, st, ft, rests, rt):  # 根据班次信息获取需求产出
    starttime = st.timestamp()
    finishtime = ft.timestamp()
    replenish = 0
    hour = (finishtime-starttime)/3600
    for r in rests:
        rstart = r['reststart'].timestamp()
        rfinish = r['restfinish'].timestamp()
        if starttime >= rfinish or finishtime <= rstart:
            replenish += 0
        else:
            if starttime < rstart and finishtime > rfinish:
                replenish += (rfinish-rstart)/3600
            else:
                if starttime >= rstart and finishtime > rfinish:
                    replenish += (rfinish-starttime)/3600
                elif starttime < rstart and finishtime <= rfinish:
                    replenish += (finishtime-rstart)/3600
    qty = round((hour-replenish)/float(rt)*float(headcount), 1)
    return qty


# 根据班次日期，产线和休息时间列表获取要求产出
def getFinishtime(shiftdate, st, headcount, linegroup, restList, routing, qty, ft=None):
    # total hours used
    totalMins = float(routing*qty)/float(headcount)*60
    # finishtime equals starttime + totalHJours
    if not ft:
        ft = st + datetime.timedelta(minutes=float(totalMins))
    rests = getRestArr(shiftdate, restList, linegroup)
    starttime = st.timestamp()
    finishtime = ft.timestamp()
    replenish = getReplenishhour(starttime, finishtime, rests)
    newst = ft
    newft = ft + datetime.timedelta(seconds=float(replenish*60*60))
    print('getFinishtime', st, ft, newst, newft)
    if replenish < 5:
        return newft
    return getFinishtime(shiftdate, newst, headcount,
                         linegroup, restList, routing, qty, newft)


def getReqQty(shiftdate, headcount, linegroup, st, ft, restList, rt):  # 根据班次日期，产线和休息时间列表获取要求产出
    rests = getRestArr(shiftdate, restList, linegroup)
    rt = rt
    starttime = st.timestamp()
    finishtime = ft.timestamp()
    hour = (finishtime-starttime)/3600
    replenish = getReplenishhour(starttime, finishtime, rests)
    qty = round((hour-replenish)/float(rt)*float(headcount), 1)
    return qty


def getReplenishhour(starttimestamp, finishtimestamp, rests):
    replenish = 0
    for r in rests:
        rstart = r['reststart'].timestamp()
        rfinish = r['restfinish'].timestamp()
        if starttimestamp >= rfinish or finishtimestamp <= rstart:
            replenish += 0
        else:
            if starttimestamp < rstart and finishtimestamp > rfinish:
                replenish += (rfinish-rstart)/3600
            else:
                if starttimestamp >= rstart and finishtimestamp > rfinish:
                    replenish += (rfinish-starttimestamp)/3600
                elif starttimestamp < rstart and finishtimestamp <= rfinish:
                    replenish += (finishtimestamp-rstart)/3600
                else:
                    replenish += (finishtimestamp-starttimestamp)/3600
    return replenish


def getRestArr(shiftdate, restList, linegroup):  # 根据班次日期和产线组获取休息时间列表
    restDic = {}
    for r in restList:
        restdate = shiftdate
        if str(r.resttime) < '08:00:00':
            restdate = (datetime.datetime.strptime(
                shiftdate, '%Y-%m-%d')+datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        if r.linegroup in restDic.keys():
            restDic[r.linegroup].append({
                'reststart': datetime.datetime.strptime(restdate+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S'),
                'restfinish': datetime.datetime.strptime(restdate+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S')+datetime.timedelta(minutes=r.restmin)
            })
        else:
            restDic[r.linegroup] = [{
                'reststart': datetime.datetime.strptime(restdate+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S'),
                'restfinish': datetime.datetime.strptime(restdate+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S')+datetime.timedelta(minutes=r.restmin)
            }]
    if linegroup in restDic.keys():
        rests = restDic[linegroup]
    else:
        rests = restDic['all']
    return rests
# 通过sku获取routing，获取顺位如下，1. Flex精准定位到linename和headcount的。 2 Flex的首个产线，人数不匹配，3.Routing里的数据


def getRouting(sku, linename, headcount):
    rt = 0  # 真实routing
    # ct = 0  # compromise的flex的工时，如果没有rt则已flex检索的第一个配置为准
    print('routingstart')
    flex = db.session.query(Flexinfo).filter(Flexinfo.sku == sku).filter(
        Flexinfo.linename == linename).order_by(desc(Flexinfo.revision)).all()
    print(flex)
    routing = db.session.query(Routing).filter(
        Routing.sku == sku).order_by(desc(Routing.Id)).first()
    rt = routing.routing
    for f in flex:
        if f.headcount == headcount:
            rt = round(1/f.hourlyrate*f.headcount, 6)
            return rt
        else:
            rt = round(1/f.hourlyrate*f.headcount, 6)
    return rt

# 静态资源路径设置，在后端使用getServer()['xxpath']使用物理地址，getServer()['xxUrl']返回网络地址给前端


def getServer():  # 获取上传文件的访问路径
    urls = {
        'iconPath': config[env].localPath+'BI/lines/',
        'iconUrl': config[env].base_url+'BI/lines/',
        'templatePath': config[env].localPath+'dm/templates/',
        'templateUrl': config[env].base_url+'dm/templates/',
        'uploadPath': config[env].localPath+'dm/uploads/',
        'uploadUrl': config[env].base_url+'dm/uploads/',
        'pcoPath': config[env].localPath+'dm/uploads/pcos/',
        'pcoUrl': config[env].base_url+'dm/uploads/pcos/',
        'labeltemplatesPath': config[env].localPath+'dm/labeltemplates/',
        'labeltemplatesUrl': config[env].base_url+'dm/labeltemplates/'
    }
    return urls


def login_required(view_func):  # 通过一个装饰函数来实现登录验证
    @wraps(view_func)
    def verify_token(*args, **kwargs):
        try:
            email = request.headers["email"]
            auth = request.headers["auth"]
            # print("ddd", auth, email)
            user = db.session.query(User).filter(User.email == email).first()
            myAuth = db.session.query(Permission).filter(
                Permission.Id.in_(user.auth.split(','))).all()
            authArr = []
            for m in myAuth:
                authArr.append(m.path)
            print('autharrarr', authArr)
            if (','.join(authArr) != auth):
                return responseError('登录校验失败，请重新登陆！')
            # 在请求头上拿到token
            token = request.headers["token"]
        except Exception:
            traceback.print_exc()
            return responseError('登录失败！')

        s = Serializer(config[env].SECRET_KEY)
        try:
            s.loads(token)
        except Exception:
            return responseExpire('登录已过期！请重新登录！')

        return view_func(*args, **kwargs)
    return verify_token


def getLinelist(plant):  # 获得产线清单，得到一个树状列表给element的cascade控件使用
    lines = db.session.query(Lineinfo.linename, Lineinfo.area, Lineinfo.linegroup).filter(
        Lineinfo.isactive == 1).filter(Lineinfo.plant == plant).order_by(Lineinfo.area, Lineinfo.linegroup, Lineinfo.linename).all()
    outDic = {}
    for ll in lines:
        if ll.area in outDic.keys():
            if ll.linegroup in outDic[ll.area]['children'].keys():
                outDic[ll.area]['children'][ll.linegroup]['children'].append({
                    'value': ll.linename, 'label': ll.linename
                })
            else:
                outDic[ll.area]['children'][ll.linegroup] = {
                    'value': ll.linegroup, 'label': ll.linegroup, 'children': [{'value': ll.linename, 'label': ll.linename}]}
        else:
            outDic[ll.area] = {
                'value': ll.area,
                'label': ll.area,
                'children': {
                    ll.linegroup: {'value': ll.linegroup, 'label': ll.linegroup,
                                   'children': [{'value': ll.linename, 'label': ll.linename}]}
                }
            }
    outArr = []
    for k, v in outDic.items():
        outArr.append({
            'value': k,
            'label': k,
            'children': list(v['children'].values())
        })
    return outArr


def getLinegroup(plant):  # 获取产线组的梳妆结构，到产线组为止
    print(222, plant)
    lines = db.session.query(Lineinfo.area, Lineinfo.linegroup).filter(
        Lineinfo.isactive == 1).filter(Lineinfo.plant == plant).group_by(Lineinfo.area, Lineinfo.linegroup).order_by(Lineinfo.area, Lineinfo.linegroup).all()
    outDic = {}
    for ll in lines:
        if ll.area in outDic.keys():
            outDic[ll.area]['children'].append({
                'value': ll.linegroup, 'label': ll.linegroup
            })
        else:
            outDic[ll.area] = {
                'value': ll.area,
                'label': ll.area,
                'children': [
                    {'value': ll.linegroup, 'label': ll.linegroup}
                ]
            }
    outArr = list(outDic.values())
    return outArr

# 得到物料清单，给前端select控件使用，注意前端推荐使用虚拟化控件解决东西太多卡的问题


def getShiftissues(shiftdate, shifttype, linename):
    shiftissues = db.session.query(Issuelog).filter(Issuelog.shiftdate == shiftdate).filter(
        Issuelog.shifttype == shifttype, Issuelog.linename == linename).all()
    dic = {}
    for s in shiftissues:
        if s.sqdctype in dic.keys():
            dic[s.sqdctype].append({
                'Id': s.Id,
                'sqdctype': s.sqdctype,
                'problemtype': s.problemtype,
                'sku': s.sku,
                'linename': s.linename,
                'recorder': s.recorder,
                'desc': s.desc,
                'recordtime': datetime.datetime.strftime(s.recordtime, '%Y-%m-%d %H-%M-%S'),
                'qty': s.qty,
                'issuemin': s.issuemin
            })
        else:
            dic[s.sqdctype] = [{
                'Id': s.Id,
                'sqdctype': s.sqdctype,
                'sku': s.sku,
                'problemtype': s.problemtype,
                'linename': s.linename,
                'recorder': s.recorder,
                'desc': s.desc,
                'recordtime': datetime.datetime.strftime(s.recordtime, '%Y-%m-%d %H-%M-%S'),
                'qty': s.qty,
                'issuemin': s.issuemin
            }]
    return dic


def getSKUlist():  # 获取所有的料号清单
    skus = db.session.query(Skuinfo.sku).all()
    skuList = []
    for s in skus:
        skuList.append({
            'value': s.sku,
            'label': s.sku
        })
    return skuList


def dmIssueSubmit(res):
    sqdctype = res.get('sqdctype')  # 问题分类，目前只有质量，效率和交货3个
    problemtype = res.get('problemtype')  # 问题子分类
    shifttype = res.get('shifttype')  # 白中夜班
    shiftdate = res.get('shiftdate')  # 班次日期
    linename = res.get('linename')  # 产线
    linegroup = res.get('linegroup')  # 产线组
    issuemin = res.get('issuemin') if res.get('issuemin') else 0
    desc = res.get('desc') if res.get('desc') else problemtype  # 描述
    sku = res.get('sku') if res.get('sku') else '待完善'  # 料号
    qty = res.get('qty') if res.get('qty') else 1  # 数量
    recordtime = datetime.datetime.strptime(res.get('recordtime'), '%Y-%m-%d %H:%M:%S') if res.get(
        'recordtime') else datetime.datetime.now()  # 记录日期
    if not linegroup:
        linegroup = db.session.query(Lineinfo.linegroup).filter(
            Lineinfo.linename == linename).first()[0]
    recorder = res.get('recorder') if res.get('recorder') else linegroup  # 记录人
    cfdate = res.get('cfdate') if res.get(
        'cfdate') else datetime.datetime.strftime(recordtime, '%Y-%m-%d')
    comments = res.get('comments') if res.get('comments') else ''
    mdi = res.get('mdi') if res.get('mdi') else '是'
    content = res.get('content') if res.get('content') else  \
        sqdctype+'/'+problemtype+'\n'+desc
    exeid = res.get('exeid') if res.get('exeid') else '9999999'
    # print(88888888888888, linename, linegroup, sqdctype, problemtype)
    if not linegroup:
        linegroup = db.session.query(Lineinfo.linegroup).filter(
            Lineinfo.linename == linename).first()[0]
    try:
        aid = 0
        if linename:
            myissue = Issuelog(sqdctype=sqdctype, problemtype=problemtype, shifttype=shifttype, shiftdate=shiftdate, linename=linename,
                               recorder=recorder, desc=desc, sku=sku, qty=qty, recordtime=recordtime, issuemin=issuemin)
            db.session.add(myissue)
            db.session.flush()
            aid = myissue.Id
        requests.post(
            config[env].cloud_url+"welean/bi/issueAPI/newSuggest", json={
                'auditid': aid,
                'cfdate': cfdate,
                'idate': datetime.datetime.strftime(recordtime, '%Y-%m-%d %H:%M:%S'),
                'comments': comments,
                'content': content,
                'exeid': exeid,
                'linename': linegroup,
                'stype': sqdctype,
                'type2': problemtype,
                'plant': 'SZ',
                'mdi': mdi
            })
        db.session.commit()
        return True
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return False


def dmIssueChange(res):
    Id = res.get('Id')
    aid = res.get('auditid')
    sqdctype = res.get('sqdctype')  # 问题分类，目前只有质量，效率和交货3个
    problemtype = res.get('problemtype')  # 问题子分类
    # shiftdate = res.get('shiftdate')  # 班次日期
    linename = res.get('linename')  # 产线
    linegroup = res.get('linegroup')  # 产线组
    issuemin = res.get('issuemin') if res.get('issuemin') else 0
    desc = res.get('desc') if res.get('desc') else problemtype  # 描述
    sku = res.get('sku') if res.get('sku') else '待完善'  # 料号
    qty = res.get('qty') if res.get('qty') else 1  # 数量
    recordtime = datetime.datetime.strptime(res.get('recordtime'), '%Y-%m-%d %H:%M:%S') if res.get(
        'recordtime') else datetime.datetime.now()  # 记录日期
    exeid = res.get('exeid') if res.get('exeid') else '9999999'
    cfdate = res.get('cfdate') if res.get(
        'cfdate') else datetime.datetime.strftime(recordtime, '%Y-%m-%d')
    comments = res.get('comments') if res.get('comments') else ''
    content = res.get('content') if res.get('content') else desc
    if not linegroup:
        linegroup = db.session.query(Lineinfo.linegroup).filter(
            Lineinfo.linename == linename).first()[0]
    recorder = res.get('recorder') if res.get('recorder') else linegroup  # 记录人
    # print(11111111111111111, aid)
    # return True
    try:
        if aid:
            db.session.query(Issuelog).filter(Issuelog.Id == aid).update({
                'sqdctype': sqdctype,
                'problemtype': problemtype,
                'recorder': recorder,
                'desc': content if content else desc,
                'sku': sku,
                'qty': qty,
                'issuemin': issuemin
            })
        requests.post(
            config[env].cloud_url+"welean/bi/issueAPI/addSuggest", json={
                'Id': Id,
                'auditid': aid,
                'cfdate': cfdate,
                'idate': datetime.datetime.strftime(recordtime, '%Y-%m-%d %H:%M:%S'),
                'comments': comments,
                'content': content if content else desc,
                'exeid': exeid,
                'linename': linegroup,
                'stype': sqdctype,
                'type2': problemtype
            })
        db.session.commit()
        return True
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return False


def dmdelSuggest(aid):
    requests.post(
        config[env].cloud_url+"welean/bi/issueAPI/delSuggest", json={
            'auditid': aid,
        })


def epeiadjust(linename, sku, qty):
    epeiareas = ['CANMaker', 'CAN-Septum', 'CAN-SAM']
    if linename not in epeiareas:
        return
    now = datetime.datetime.now()
    getskus = db.session.query(Epeisku).filter(Epeisku.pdsku == sku).all()
    for s in getskus:
        usesku = db.session.query(Epeistock).filter(Epeistock.sku == s.usesku).first()
        if usesku:
            usesku.countstock = usesku.countstock+s.useqty*qty
            usesku.countdate = now
    db.session.commit()

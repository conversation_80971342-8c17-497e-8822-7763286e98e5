<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { getparammonitor, getstdparam } from "@/api/datamonitor";
const cycledata = ref(10);
const intervalid = ref();

const props = defineProps(["queryinfo"]);
const viewsetting = reactive({
  showbasic: false,
  showothers: true,
  showmonitor: true
});

// const props = withDefaults(defineProps<any>(), {
//   stateData: () => ({
//     machine_id: 0,
//     state_id: "0"
//   })
// });

const paramdata = ref<any>({});
const realtimedata = ref<any>({});
onMounted(() => {
  refreshData();
  console.log(props.queryinfo);
});

const realDataInterval = ref(null);
const stdParamInterval = ref(null);

const refreshData = () => {
  getrealdata();
  stdparam();
  realDataInterval.value = setInterval(() => {
    getrealdata();
  }, 3000);
  stdParamInterval.value = setInterval(() => {
    stdparam();
  }, 60000);
};

onUnmounted(() => {
  clearInterval(realDataInterval.value);
  clearInterval(stdParamInterval.value);
});

const stdparam = async () => {
  const res = (await getstdparam({
    machine_id: props.queryinfo.machineid,
    sku: props.queryinfo.sku
  })) as { meta: { status: number }; data: { paraitem: object } };
  if (res.meta.status === 200) {
    paramdata.value = res.data.paraitem[0];
  }
};

const getrealdata = async () => {
  const res = (await getparammonitor({
    machineid: props.queryinfo.machineid
  })) as { meta: { status: number }; data: object };
  if (res.meta.status === 200) {
    realtimedata.value = res.data;
  }
};
</script>
<template>
  <el-form
    ref="vForm"
    label-position="right"
    label-width="100px"
    size="large"
    @submit.prevent
  >
    <el-row>
      <el-col :md="4" :xs="4"
        >显示基本信息<el-switch v-model="viewsetting.showbasic"
      /></el-col>
      <el-col :md="4" :xs="4"
        >显示采集信息<el-switch v-model="viewsetting.showmonitor"
      /></el-col>
      <el-col :md="4" :xs="4"
        >显示所有信息<el-switch v-model="viewsetting.showothers"
      /></el-col>
      <el-col :md="24" :xs="24">
        <table v-show="viewsetting.showbasic" style="width: 100%">
          <thead class="param_head">
            <tr>
              <td colspan="9">
                <i>工艺参数表基本信息</i>
              </td>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>料号</td>
              <td colspan="4">{{ paramdata.sku }}</td>
              <td>机台号</td>
              <td>{{ paramdata.machine_id }}号机</td>
              <td>树脂</td>
              <td>{{ paramdata.resin }}</td>
            </tr>
            <tr>
              <td>描述</td>
              <td colspan="4">{{ paramdata.sku_des }}</td>
              <td>机台吨位</td>
              <td />
              <td>色母</td>
              <td>{{ paramdata.coloran }}</td>
            </tr>
            <tr>
              <td>模具号ID</td>
              <td colspan="4">{{ paramdata.mold_id }}</td>
              <td>型腔数</td>
              <td>{{ paramdata.cavity }}</td>
              <td>颜色</td>
              <td>{{ paramdata.color }}</td>
            </tr>
            <tr>
              <td rowspan="2">镶块/模仁</td>
              <td>1</td>
              <td>2</td>
              <td>3</td>
              <td>4</td>
              <td>浇口类型</td>
              <td>{{ paramdata.sprue }}</td>
              <td>插件</td>
              <td>{{ paramdata.plugin }}</td>
            </tr>
            <tr>
              <td>{{ paramdata.moldcore1 }}</td>
              <td>{{ paramdata.moldcore2 }}</td>
              <td>{{ paramdata.moldcore3 }}</td>
              <td>{{ paramdata.moldcore4 }}</td>
              <td>单件重</td>
              <td>{{ paramdata.unit_weight }}</td>
              <td>一模总重</td>
              <td>{{ paramdata.total_weight }}</td>
            </tr>
          </tbody>
        </table>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="12" :offset="0">
        <el-row>
          <table style="width: 100%">
            <thead class="param_head">
              <tr>
                <td colspan="10">
                  <i>温度 TEMPERATURE</i>

                  <span v-show="viewsetting.showmonitor"
                    >采集时间：{{ realtimedata?.CycleData?.updatetime }}</span
                  >
                </td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>范围</td>
                <td>Zone1</td>
                <td>T1</td>
                <td>T2</td>
                <td>T3</td>
                <td>T4</td>
                <td>T5</td>
                <td>T6</td>
                <td>Feed</td>
                <td>Oil</td>
              </tr>
              <tr>
                <td>±30℃</td>
                <td>{{ paramdata.temp_zone1 }}</td>
                <td>
                  {{ paramdata.temp_t1 }}
                </td>
                <td>{{ paramdata.temp_t2 }}</td>
                <td>{{ paramdata.temp_t3 }}</td>
                <td>{{ paramdata.temp_t4 }}</td>
                <td>{{ paramdata.temp_t5 }}</td>
                <td>{{ paramdata.temp_t6 }}</td>
                <td>{{ paramdata.temp_feed }}</td>
                <td>{{ paramdata.temp_oil }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td>采集值</td>
                <td />
                <td
                  :class="
                    Math.abs(
                      realtimedata?.CycleData?.Z_QDTEMPZ01 - paramdata.temp_t1
                    ) > 30
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.CycleData?.Z_QDTEMPZ01 }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.CycleData?.Z_QDTEMPZ02 - paramdata.temp_t2
                    ) > 30
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.CycleData?.Z_QDTEMPZ02 }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.CycleData?.Z_QDTEMPZ03 - paramdata.temp_t3
                    ) > 30
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.CycleData?.Z_QDTEMPZ03 }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.CycleData?.Z_QDTEMPZ04 - paramdata.temp_t4
                    ) > 30
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.CycleData?.Z_QDTEMPZ04 }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.CycleData?.Z_QDTEMPZ05 - paramdata.temp_t5
                    ) > 30
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.CycleData?.Z_QDTEMPZ05 }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.CycleData?.Z_QDTEMPZ06 - paramdata.temp_t6
                    ) > 30
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.CycleData?.Z_QDTEMPZ06 }}
                </td>
                <td>{{ paramdata.temp_feed }}</td>
                <td>{{ paramdata.temp_oil }}</td>
              </tr>
            </tbody>
          </table>
        </el-row>

        <el-row>
          <table style="width: 100%">
            <thead class="param_head">
              <tr>
                <td colspan="7">
                  <i>熔胶</i>
                  <span v-show="viewsetting.showmonitor"
                    >采集时间：{{ realtimedata?.MoldData?.updatetime }}</span
                  >
                </td>
              </tr>
            </thead>
            <tbody>
              <tr class="title">
                <td>项目</td>
                <td>范围</td>
                <td>1段</td>
                <td>2段</td>
                <td>3段</td>
                <td>4段</td>
                <td>5段</td>
              </tr>
              <tr>
                <td>塑化压力</td>
                <td>±{{ paramdata.range_pre_plastic }}bar</td>
                <td>{{ paramdata.pre_plastic_1 }}</td>
                <td>{{ paramdata.pre_plastic_2 }}</td>
                <td>{{ paramdata.pre_plastic_3 }}</td>
                <td>{{ paramdata.pre_plastic_4 }}</td>
                <td>{{ paramdata.pre_plastic_5 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast1Pres -
                        paramdata.pre_plastic_1
                    ) > paramdata.range_pre_plastic
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast1Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast2Pres -
                        paramdata.pre_plastic_2
                    ) > paramdata.range_pre_plastic
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast2Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast3Pres -
                        paramdata.pre_plastic_3
                    ) > paramdata.range_pre_plastic
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast3Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast4Pres -
                        paramdata.pre_plastic_4
                    ) > paramdata.range_pre_plastic
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast4Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast5Pres -
                        paramdata.pre_plastic_5
                    ) > paramdata.range_pre_plastic
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast5Pres }}
                </td>
              </tr>
              <tr>
                <td>背压</td>
                <td>± {{ paramdata.range_backpre }}bar</td>
                <td>{{ paramdata.backpre_plastic_1 }}</td>
                <td>{{ paramdata.backpre_plastic_2 }}</td>
                <td>{{ paramdata.backpre_plastic_3 }}</td>
                <td>{{ paramdata.backpre_plastic_4 }}</td>
                <td>{{ paramdata.backpre_plastic_5 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast1BackPres -
                        paramdata.backpre_plastic_1
                    ) > paramdata.range_backpre
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast1BackPres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast2BackPres -
                        paramdata.backpre_plastic_2
                    ) > paramdata.range_backpre
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast2BackPres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast3BackPres -
                        paramdata.backpre_plastic_3
                    ) > paramdata.range_backpre
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast3BackPres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast4BackPres -
                        paramdata.backpre_plastic_4
                    ) > paramdata.range_backpre
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast4BackPres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast5BackPres -
                        paramdata.backpre_plastic_5
                    ) > paramdata.range_backpre
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast5BackPres }}
                </td>
              </tr>
              <tr>
                <td>转速</td>
                <td>± {{ paramdata.range_spd_screw }}mm/s</td>
                <td>{{ paramdata.spd_screw_1 }}</td>
                <td>{{ paramdata.spd_screw_2 }}</td>
                <td>{{ paramdata.spd_screw_3 }}</td>
                <td>{{ paramdata.spd_screw_4 }}</td>
                <td>{{ paramdata.spd_screw_5 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast1Speed -
                        paramdata.spd_screw_1
                    ) > paramdata.range_spd_screw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast1Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast2Speed -
                        paramdata.spd_screw_2
                    ) > paramdata.range_spd_screw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast2Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast3Speed -
                        paramdata.spd_screw_3
                    ) > paramdata.range_spd_screw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast3Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast4Speed -
                        paramdata.spd_screw_4
                    ) > paramdata.range_spd_screw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast4Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast5Speed -
                        paramdata.spd_screw_5
                    ) > paramdata.range_spd_screw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast5Speed }}
                </td>
              </tr>
              <tr>
                <td>转换点</td>
                <td>±{{ paramdata.range_swpoint_inj }}mm/s</td>
                <td>{{ paramdata.swpoint_plastic_1 }}</td>
                <td>{{ paramdata.swpoint_plastic_2 }}</td>
                <td>{{ paramdata.swpoint_plastic_3 }}</td>
                <td>{{ paramdata.swpoint_plastic_4 }}</td>
                <td>{{ paramdata.swpoint_plastic_5 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast1Pos -
                        paramdata.swpoint_plastic_1
                    ) > paramdata.range_swpoint_inj
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast1Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast2Pos -
                        paramdata.swpoint_plastic_2
                    ) > paramdata.range_swpoint_inj
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast2Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast3Pos -
                        paramdata.swpoint_plastic_3
                    ) > paramdata.range_swpoint_inj
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast3Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast4Pos -
                        paramdata.swpoint_plastic_4
                    ) > paramdata.range_swpoint_inj
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast4Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Plast5Pos -
                        paramdata.swpoint_plastic_5
                    ) > paramdata.range_swpoint_inj
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Plast5Pos }}
                </td>
              </tr>
            </tbody>
          </table>
        </el-row>
        <el-row>
          <table style="width: 100%">
            <tr class="title">
              <td>螺杆松退</td>
              <td>压力</td>
              <td>速度</td>
              <td>位置</td>
            </tr>
            <tr>
              <td>标准值</td>
              <td>{{ paramdata.pre_screwback }}</td>
              <td>{{ paramdata.spd_screwback }}</td>
              <td>{{ paramdata.pos_screwback }}</td>
            </tr>
            <tr v-show="viewsetting.showmonitor" class="monitor_row">
              <td>采集值</td>
              <td>{{ realtimedata?.MoldData?.DecompPres }}</td>
              <td>{{ realtimedata?.MoldData?.DecompSpeed }}</td>
              <td>{{ realtimedata?.MoldData?.DecompPos }}</td>
            </tr>
          </table>
        </el-row>

        <el-row>
          <table style="width: 100%">
            <thead class="param_head">
              <tr>
                <td colspan="12">
                  <i>注塑保压过程</i>
                </td>
              </tr>
            </thead>
            <tr class="title">
              <td>项目</td>
              <td>范围</td>
              <td>1段</td>
              <td>2段</td>
              <td>3段</td>
              <td>4段</td>
              <td>5段</td>
              <td>6段</td>
              <td>7段</td>
              <td>8段</td>
              <td>9段</td>
              <td>10段</td>
            </tr>
            <tr>
              <td>注射压力</td>
              <td>±{{ paramdata.range_pre_inj }}bar</td>
              <td>{{ paramdata.pre_inj_1 }}</td>
              <td>{{ paramdata.pre_inj_2 }}</td>
              <td>{{ paramdata.pre_inj_3 }}</td>
              <td>{{ paramdata.pre_inj_4 }}</td>
              <td>{{ paramdata.pre_inj_5 }}</td>
              <td>{{ paramdata.pre_inj_6 }}</td>
              <td>{{ paramdata.pre_inj_7 }}</td>
              <td>{{ paramdata.pre_inj_8 }}</td>
              <td>{{ paramdata.pre_inj_9 }}</td>
              <td>{{ paramdata.pre_inj_10 }}</td>
            </tr>
            <tr v-show="viewsetting.showmonitor" class="monitor_row">
              <td colspan="2">采集值</td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject1Pres - paramdata.pre_inj_1
                  ) > paramdata.range_pre_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject1Pres }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject2Pres - paramdata.pre_inj_2
                  ) > paramdata.range_pre_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject2Pres }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject3Pres - paramdata.pre_inj_3
                  ) > paramdata.range_pre_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject3Pres }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject4Pres - paramdata.pre_inj_4
                  ) > paramdata.range_pre_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject4Pres }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject5Pres - paramdata.pre_inj_5
                  ) > paramdata.range_pre_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject5Pres }}
              </td>
              <td />
              <td />
              <td />
              <td />
              <td />
            </tr>
            <tr>
              <td>注射速度</td>
              <td>±{{ paramdata.range_spd_inj }}mm/s</td>
              <td>{{ paramdata.spd_inj_1 }}</td>
              <td>{{ paramdata.spd_inj_2 }}</td>
              <td>{{ paramdata.spd_inj_3 }}</td>
              <td>{{ paramdata.spd_inj_4 }}</td>
              <td>{{ paramdata.spd_inj_5 }}</td>
              <td>{{ paramdata.spd_inj_6 }}</td>
              <td>{{ paramdata.spd_inj_7 }}</td>
              <td>{{ paramdata.spd_inj_8 }}</td>
              <td>{{ paramdata.spd_inj_9 }}</td>
              <td>{{ paramdata.spd_inj_10 }}</td>
            </tr>
            <tr v-show="viewsetting.showmonitor" class="monitor_row">
              <td colspan="2">采集值</td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject1Speed - paramdata.spd_inj_1
                  ) > paramdata.range_spd_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject1Speed }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject2Speed - paramdata.spd_inj_2
                  ) > paramdata.range_spd_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject2Speed }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject3Speed - paramdata.spd_inj_3
                  ) > paramdata.range_spd_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject3Speed }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject4Speed - paramdata.spd_inj_4
                  ) > paramdata.range_spd_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject4Speed }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject5Speed - paramdata.spd_inj_5
                  ) > paramdata.range_spd_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject5Speed }}
              </td>
              <td />
              <td />
              <td />
              <td />
              <td />
            </tr>
            <tr>
              <td>转换点</td>
              <td>±{{ paramdata.range_swpoint_inj }}mm</td>
              <td>{{ paramdata.swpoint_inj_1 }}</td>
              <td>{{ paramdata.swpoint_inj_2 }}</td>
              <td>{{ paramdata.swpoint_inj_3 }}</td>
              <td>{{ paramdata.swpoint_inj_4 }}</td>
              <td>{{ paramdata.swpoint_inj_5 }}</td>
              <td>{{ paramdata.swpoint_inj_6 }}</td>
              <td>{{ paramdata.swpoint_inj_7 }}</td>
              <td>{{ paramdata.swpoint_inj_8 }}</td>
              <td>{{ paramdata.swpoint_inj_9 }}</td>
              <td>{{ paramdata.swpoint_inj_10 }}</td>
            </tr>
            <tr v-show="viewsetting.showmonitor" class="monitor_row">
              <td colspan="2">采集值</td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject1Pos - paramdata.swpoint_inj_1
                  ) > paramdata.range_swpoint_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject1Pos }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject2Pos - paramdata.swpoint_inj_2
                  ) > paramdata.range_swpoint_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject2Pos }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject3Pos - paramdata.swpoint_inj_3
                  ) > paramdata.range_swpoint_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject3Pos }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject4Pos - paramdata.swpoint_inj_4
                  ) > paramdata.range_swpoint_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject4Pos }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Inject5Pos - paramdata.swpoint_inj_5
                  ) > paramdata.range_swpoint_inj
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Inject5Pos }}
              </td>
              <td />
              <td />
              <td />
              <td />
              <td />
            </tr>
          </table>
          <table style="width: 100%">
            <tr class="title">
              <td rowspan="2">保压压力</td>
              <td rowspan="2">±{{ paramdata.range_pre_hold }}bar</td>
              <td>HP1</td>
              <td>HP2</td>
              <td>HP3</td>
              <td>HP4</td>
              <td>HP5</td>
              <td>HV1</td>
              <td>HV2</td>
              <td>HV3</td>
              <td>HV4</td>
              <td>HV5</td>
            </tr>
            <tr>
              <td>{{ paramdata.pre_hold_1 }}</td>
              <td>{{ paramdata.pre_hold_2 }}</td>
              <td>{{ paramdata.pre_hold_3 }}</td>
              <td>{{ paramdata.pre_hold_4 }}</td>
              <td>{{ paramdata.pre_hold_5 }}</td>
              <td>{{ paramdata.spd_hold_1 }}</td>
              <td>{{ paramdata.spd_hold_2 }}</td>
              <td>{{ paramdata.spd_hold_3 }}</td>
              <td>{{ paramdata.spd_hold_4 }}</td>
              <td>{{ paramdata.spd_hold_5 }}</td>
            </tr>
            <tr v-show="viewsetting.showmonitor" class="monitor_row">
              <td colspan="2">采集值</td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold1Pres - paramdata.pre_hold_1
                  ) > paramdata.range_pre_hold
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold1Pres }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold2Pres - paramdata.pre_hold_2
                  ) > paramdata.range_pre_hold
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold2Pres }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold3Pres - paramdata.pre_hold_3
                  ) > paramdata.range_pre_hold
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold3Pres }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold4Pres - paramdata.pre_hold_4
                  ) > paramdata.range_pre_hold
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold4Pres }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold5Pres - paramdata.pre_hold_5
                  ) > paramdata.range_pre_hold
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold5Pres }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold1Speed - paramdata.spd_hold_1
                  ) > paramdata.range_pre_hold
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold1Speed }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold2Speed - paramdata.spd_hold_2
                  ) > paramdata.range_pre_hold
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold2Speed }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold3Speed - paramdata.spd_hold_3
                  ) > paramdata.range_pre_hold
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold3Speed }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold4Speed - paramdata.spd_hold_4
                  ) > paramdata.range_pre_hold
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold4Speed }}
              </td>
              <td
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold5Speed - paramdata.spd_hold_5
                  ) > paramdata.range_pre_hold
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold5Speed }}
              </td>
            </tr>
          </table>
          <table style="width: 100%">
            <tr class="title">
              <td colspan="2" rowspan="2">保压时间</td>
              <td>范围</td>
              <td colspan="2">1段</td>
              <td colspan="2">2段</td>
              <td colspan="2">3段</td>
              <td colspan="2">4段</td>
              <td colspan="2">5段</td>
            </tr>
            <tr>
              <td>±{{ paramdata.range_time_holdpre }}S</td>
              <td colspan="2">{{ paramdata.time_holdpre_1 }}</td>
              <td colspan="2">{{ paramdata.time_holdpre_2 }}</td>
              <td colspan="2">{{ paramdata.time_holdpre_3 }}</td>
              <td colspan="2">{{ paramdata.time_holdpre_4 }}</td>
              <td colspan="2">{{ paramdata.time_holdpre_5 }}</td>
            </tr>
            <tr v-show="viewsetting.showmonitor" class="monitor_row">
              <td colspan="3">采集值</td>
              <td
                colspan="2"
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold1Time - paramdata.time_holdpre_1
                  ) > paramdata.range_time_holdpre
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold1Time }}
              </td>
              <td
                colspan="2"
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold2Time - paramdata.time_holdpre_2
                  ) > paramdata.range_time_holdpre
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold2Time }}
              </td>
              <td
                colspan="2"
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold3Time - paramdata.time_holdpre_3
                  ) > paramdata.range_time_holdpre
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold3Time }}
              </td>
              <td
                colspan="2"
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold4Time - paramdata.time_holdpre_4
                  ) > paramdata.range_time_holdpre
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold4Time }}
              </td>
              <td
                colspan="2"
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.Hold5Time - paramdata.time_holdpre_5
                  ) > paramdata.range_time_holdpre
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.Hold5Time }}
              </td>
            </tr>
          </table>
          <table style="width: 100%">
            <tr class="title">
              <td colspan="2" rowspan="2">其它</td>
              <td>项目</td>
              <td colspan="2">注射延迟</td>
              <td colspan="2">注射时间</td>
              <td colspan="2">冷却时间</td>
              <td colspan="2">周期</td>
              <td colspan="2">保压模式</td>
            </tr>
            <tr>
              <td>标准值</td>
              <td>{{ paramdata.time_injdelay }}</td>
              <td>±{{ paramdata.range_time_injdelay }}s</td>
              <td>{{ paramdata.time_inj }}</td>
              <td>±{{ paramdata.range_time_inj }}s</td>
              <td>{{ paramdata.time_cooling }}</td>
              <td>±{{ paramdata.range_time_cooling }}s</td>
              <td>{{ paramdata.time_cycle }}</td>
              <td>±{{ paramdata.range_time_cycle }}s</td>
              <td colspan="2">{{ paramdata.mode_hold }}</td>
            </tr>
            <tr v-show="viewsetting.showmonitor" class="monitor_row">
              <td colspan="3">采集值</td>
              <td colspan="2">-</td>
              <td
                colspan="2"
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.MaxInjTime - paramdata.time_inj
                  ) > paramdata.range_time_injdelay
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.MaxInjTime }}
              </td>
              <td
                colspan="2"
                :class="
                  Math.abs(
                    realtimedata?.MoldData?.CoolingTime - paramdata.time_cooling
                  ) > paramdata.range_time_cooling
                    ? 'alert_state'
                    : ''
                "
              >
                {{ realtimedata?.MoldData?.CoolingTime }}
              </td>
              <td colspan="2">-</td>
              <td colspan="2">{{ realtimedata?.MoldData?.HoldChange }}</td>
            </tr>
          </table>

          <table width="100%">
            <thead class="param_head">
              <tr>
                <td colspan="11">热烧道控制</td>
              </tr>
            </thead>
            <tbody>
              <tr class="title">
                <td>Zone</td>
                <td>1</td>
                <td>2</td>
                <td>3</td>
                <td>4</td>
                <td>5</td>
                <td>6</td>
                <td>7</td>
                <td>8</td>
                <td>9</td>
                <td>10</td>
              </tr>
              <tr>
                <td>标准值</td>
                <td>{{ paramdata.hotrunner_zone1 }}</td>
                <td>{{ paramdata.hotrunner_zone2 }}</td>
                <td>{{ paramdata.hotrunner_zone3 }}</td>
                <td>{{ paramdata.hotrunner_zone4 }}</td>
                <td>{{ paramdata.hotrunner_zone5 }}</td>
                <td>{{ paramdata.hotrunner_zone6 }}</td>
                <td>{{ paramdata.hotrunner_zone7 }}</td>
                <td>{{ paramdata.hotrunner_zone8 }}</td>
                <td>{{ paramdata.hotrunner_zone9 }}</td>
                <td>{{ paramdata.hotrunner_zone10 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td>采集值</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
              </tr>
              <tr class="param_head">
                <td>Zone</td>
                <td>11</td>
                <td>12</td>
                <td>13</td>
                <td>14</td>
                <td>15</td>
                <td>16</td>
                <td>17</td>
                <td>18</td>
                <td>19</td>
                <td>20</td>
              </tr>
              <tr>
                <td>标准值</td>
                <td>{{ paramdata.hotrunner_zone11 }}</td>
                <td>{{ paramdata.hotrunner_zone12 }}</td>
                <td>{{ paramdata.hotrunner_zone13 }}</td>
                <td>{{ paramdata.hotrunner_zone14 }}</td>
                <td>{{ paramdata.hotrunner_zone15 }}</td>
                <td>{{ paramdata.hotrunner_zone16 }}</td>
                <td>{{ paramdata.hotrunner_zone17 }}</td>
                <td>{{ paramdata.hotrunner_zone18 }}</td>
                <td>{{ paramdata.hotrunner_zone19 }}</td>
                <td>{{ paramdata.hotrunner_zone20 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td>采集值</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
              </tr>
            </tbody>
          </table>
        </el-row>
      </el-col>

      <el-col :span="12" :offset="0">
        <el-row>
          <table style="width: 100%">
            <thead class="param_head">
              <tr>
                <td colspan="7">
                  <i>合模</i>
                </td>
              </tr>
            </thead>
            <tbody>
              <tr class="title">
                <td>项目</td>
                <td>范围</td>
                <td>1段-></td>
                <td>2段-></td>
                <td>3段-></td>
                <td>4段-></td>
                <td>5段</td>
              </tr>
              <tr>
                <td>压力</td>
                <td>±{{ paramdata.range_pre_moldclose }}bar</td>
                <td>{{ paramdata.pre_moldclose_1 }}</td>
                <td>{{ paramdata.pre_moldclose_2 }}</td>
                <td>{{ paramdata.pre_moldclose_3 }}</td>
                <td>{{ paramdata.pre_moldclose_4 }}</td>
                <td>{{ paramdata.pre_moldclose_5 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Clamp1Pres -
                        paramdata.pre_moldclose_1
                    ) > paramdata.range_pre_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Clamp1Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Clamp2Pres -
                        paramdata.pre_moldclose_2
                    ) > paramdata.range_pre_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Clamp2Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Clamp3Pres -
                        paramdata.pre_moldclose_3
                    ) > paramdata.range_pre_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Clamp3Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.ClampLpPres -
                        paramdata.pre_moldclose_4
                    ) > paramdata.range_pre_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.ClampLpPres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.ClampHpPres -
                        paramdata.pre_moldclose_5
                    ) > paramdata.range_pre_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.ClampHpPres }}
                </td>
              </tr>
              <tr>
                <td>速度</td>
                <td>±{{ paramdata.range_spd_moldclose }}mm/s</td>
                <td>{{ paramdata.spd_moldclose_1 }}</td>
                <td>{{ paramdata.spd_moldclose_2 }}</td>
                <td>{{ paramdata.spd_moldclose_3 }}</td>
                <td>{{ paramdata.spd_moldclose_4 }}</td>
                <td>{{ paramdata.spd_moldclose_5 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Clamp1Speed -
                        paramdata.spd_moldclose_1
                    ) > paramdata.range_spd_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Clamp1Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Clamp2Speed -
                        paramdata.spd_moldclose_2
                    ) > paramdata.range_spd_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Clamp2Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Clamp3Speed -
                        paramdata.spd_moldclose_3
                    ) > paramdata.range_spd_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Clamp3Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.ClampLpSpeed -
                        paramdata.spd_moldclose_4
                    ) > paramdata.range_spd_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.ClampLpSpeed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.ClampHpSpeed -
                        paramdata.spd_moldclose_5
                    ) > paramdata.range_spd_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.ClampHpSpeed }}
                </td>
              </tr>
              <tr>
                <td>位置</td>
                <td>±{{ paramdata.range_pos_moldclose }}mm</td>
                <td>{{ paramdata.pos_moldclose_1 }}</td>
                <td>{{ paramdata.pos_moldclose_2 }}</td>
                <td>{{ paramdata.pos_moldclose_3 }}</td>
                <td>{{ paramdata.pos_moldclose_4 }}</td>
                <td>{{ paramdata.pos_moldclose_5 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Clamp1Pos -
                        paramdata.spd_moldclose_1
                    ) > paramdata.range_spd_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Clamp1Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Clamp2Pos -
                        paramdata.spd_moldclose_2
                    ) > paramdata.range_spd_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Clamp2Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Clamp3Pos -
                        paramdata.spd_moldclose_3
                    ) > paramdata.range_spd_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Clamp3Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.ClampLpPos -
                        paramdata.spd_moldclose_4
                    ) > paramdata.range_spd_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.ClampLpPos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.ClampHpPos -
                        paramdata.spd_moldclose_5
                    ) > paramdata.range_spd_moldclose
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.ClampHpPos }}
                </td>
              </tr>
            </tbody>
          </table>
        </el-row>
        <el-row>
          <table style="width: 100%">
            <thead class="param_head">
              <tr>
                <td colspan="7">
                  <i>开模</i>
                </td>
              </tr>
            </thead>
            <tbody>
              <tr class="title">
                <td>项目</td>
                <td>范围</td>
                <td>5段</td>
                <td>&lt;-4段</td>
                <td>&lt;-3段</td>
                <td>&lt;-2段</td>
                <td>&lt;-1段</td>
              </tr>
              <tr>
                <td>压力</td>
                <td>±{{ paramdata.range_pre_moldopen }}bar</td>
                <td>{{ paramdata.pre_moldopen_5 }}</td>
                <td>{{ paramdata.pre_moldopen_4 }}</td>
                <td>{{ paramdata.pre_moldopen_3 }}</td>
                <td>{{ paramdata.pre_moldopen_2 }}</td>
                <td>{{ paramdata.pre_moldopen_1 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Open1Pres -
                        paramdata.pre_moldopen_1
                    ) > paramdata.range_pre_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Open1Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Open2Pres -
                        paramdata.pre_moldopen_2
                    ) > paramdata.range_pre_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Open2Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Open3Pres -
                        paramdata.pre_moldopen_3
                    ) > paramdata.range_pre_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Open3Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.OpenFastPres -
                        paramdata.pre_moldopen_4
                    ) > paramdata.range_pre_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.OpenFastPres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.OpenSlowPres -
                        paramdata.pre_moldopen_5
                    ) > paramdata.range_pre_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.OpenSlowPres }}
                </td>
              </tr>
              <tr>
                <td>速度</td>
                <td>±{{ paramdata.range_spd_moldopen }}mm/s</td>
                <td>{{ paramdata.spd_moldopen_5 }}</td>
                <td>{{ paramdata.spd_moldopen_4 }}</td>
                <td>{{ paramdata.spd_moldopen_3 }}</td>
                <td>{{ paramdata.spd_moldopen_2 }}</td>
                <td>{{ paramdata.spd_moldopen_1 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Open1Speed -
                        paramdata.spd_moldopen_1
                    ) > paramdata.range_spd_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Open1Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Open2Speed -
                        paramdata.spd_moldopen_2
                    ) > paramdata.range_spd_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Open2Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Open3Speed -
                        paramdata.spd_moldopen_3
                    ) > paramdata.range_spd_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Open3Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.OpenFastSpeed -
                        paramdata.spd_moldopen_4
                    ) > paramdata.range_spd_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.OpenFastSpeed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.OpenSlowSpeed -
                        paramdata.spd_moldopen_5
                    ) > paramdata.range_spd_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.OpenSlowSpeed }}
                </td>
              </tr>
              <tr>
                <td>位置</td>
                <td>±{{ paramdata.range_pos_moldopen }}mm</td>
                <td>{{ paramdata.pos_moldopen_5 }}</td>
                <td>{{ paramdata.pos_moldopen_4 }}</td>
                <td>{{ paramdata.pos_moldopen_3 }}</td>
                <td>{{ paramdata.pos_moldopen_2 }}</td>
                <td>{{ paramdata.pos_moldopen_1 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Open1Pos -
                        paramdata.spd_moldopen_1
                    ) > paramdata.range_spd_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Open1Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Open2Pos -
                        paramdata.spd_moldopen_2
                    ) > paramdata.range_spd_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Open2Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.Open3Pos -
                        paramdata.spd_moldopen_3
                    ) > paramdata.range_spd_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.Open3Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.OpenFastPos -
                        paramdata.spd_moldopen_4
                    ) > paramdata.range_spd_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.OpenFastPos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.OpenSlowPos -
                        paramdata.spd_moldopen_5
                    ) > paramdata.range_spd_moldopen
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.OpenSlowPos }}
                </td>
              </tr>
            </tbody>
          </table>
        </el-row>

        <el-row>
          <table style="width: 100%">
            <thead class="param_head">
              <tr>
                <td colspan="5">
                  <i>顶出</i>
                </td>
              </tr>
            </thead>
            <tbody>
              <tr class="title">
                <td colspan="5" style="text-align: left">&gt;&gt;顶针前进</td>
              </tr>
              <tr class="title">
                <td>项目</td>
                <td>范围</td>
                <td>1段</td>
                <td>2段</td>
                <td>3段</td>
              </tr>
              <tr>
                <td>前进压力</td>
                <td>±{{ paramdata.range_pre_ejectorforw }}bar</td>
                <td>{{ paramdata.pre_ejectorforw_1 }}</td>
                <td>{{ paramdata.pre_ejectorforw_2 }}</td>
                <td>{{ paramdata.pre_ejectorforw_3 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectOut1Pres -
                        paramdata.pre_ejectorforw_1
                    ) > paramdata.range_pre_ejectorforw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectOut1Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectOut2Pres -
                        paramdata.pre_ejectorforw_2
                    ) > paramdata.range_pre_ejectorforw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectOut2Pres }}
                </td>
                <td>-</td>
              </tr>
              <tr>
                <td>前进速度</td>
                <td>±{{ paramdata.range_spd_ejectorforw }}mm/s</td>
                <td>{{ paramdata.spd_ejectorforw_1 }}</td>
                <td>{{ paramdata.spd_ejectorforw_2 }}</td>
                <td>{{ paramdata.spd_ejectorforw_3 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectOut1Speed -
                        paramdata.spd_ejectorforw_1
                    ) > paramdata.range_spd_ejectorforw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectOut1Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectOut2Speed -
                        paramdata.spd_ejectorforw_2
                    ) > paramdata.range_spd_ejectorforw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectOut2Speed }}
                </td>
                <td>-</td>
              </tr>
              <tr>
                <td>前进位置</td>
                <td>±{{ paramdata.range_pos_ejectorforw }}mm</td>
                <td>{{ paramdata.pos_ejectorforw_1 }}</td>
                <td>{{ paramdata.pos_ejectorforw_2 }}</td>
                <td>{{ paramdata.pos_ejectorforw_3 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectOut1Pos -
                        paramdata.pos_ejectorforw_1
                    ) > paramdata.range_pos_ejectorforw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectOut1Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectOut2Pos -
                        paramdata.pos_ejectorforw_2
                    ) > paramdata.range_pos_ejectorforw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectOut2Pos }}
                </td>
                <td>-</td>
              </tr>
              <tr>
                <td colspan="5" style="text-align: left">&gt;&gt;顶针后退</td>
              </tr>
              <tr>
                <td>项目</td>
                <td>范围</td>
                <td>1段</td>
                <td>2段</td>
                <td>3段</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td>1段</td>
                <td>2段</td>
                <td>3段</td>
              </tr>
              <tr>
                <td>后退压力</td>
                <td>±{{ paramdata.range_pre_ejectorbackw }}bar</td>
                <td>{{ paramdata.pre_ejectorbackw_1 }}</td>
                <td>{{ paramdata.pre_ejectorbackw_2 }}</td>
                <td />
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectIn1Pres -
                        paramdata.pre_ejectorbackw_1
                    ) > paramdata.range_pre_ejectorbackw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectIn1Pres }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectIn2Pres -
                        paramdata.pre_ejectorbackw_2
                    ) > paramdata.range_pre_ejectorbackw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectIn2Pres }}
                </td>
                <td>-</td>
              </tr>
              <tr>
                <td>后退速度</td>
                <td>±{{ paramdata.range_spd_ejectorbackw }}mm/s</td>
                <td>{{ paramdata.spd_ejectorbackw_1 }}</td>
                <td>{{ paramdata.spd_ejectorbackw_2 }}</td>
                <td />
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectIn1Speed -
                        paramdata.spd_ejectorbackw_1
                    ) > paramdata.range_spd_ejectorbackw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectIn1Speed }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectIn2Speed -
                        paramdata.spd_ejectorbackw_2
                    ) > paramdata.range_spd_ejectorbackw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectIn2Speed }}
                </td>
                <td>-</td>
              </tr>
              <tr>
                <td>后退位置</td>
                <td>±{{ paramdata.range_pos_ejectorbackw }}mm</td>
                <td>{{ paramdata.pos_ejectorbackw_1 }}</td>
                <td>{{ paramdata.pos_ejectorbackw_2 }}</td>
                <td />
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td colspan="2">采集值</td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectIn1Pos -
                        paramdata.pos_ejectorbackw_1
                    ) > paramdata.range_pos_ejectorbackw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectIn1Pos }}
                </td>
                <td
                  :class="
                    Math.abs(
                      realtimedata?.MoldData?.EjectIn2Pos -
                        paramdata.pos_ejectorbackw_2
                    ) > paramdata.range_pos_ejectorbackw
                      ? 'alert_state'
                      : ''
                  "
                >
                  {{ realtimedata?.MoldData?.EjectIn2Pos }}
                </td>
                <td>-</td>
              </tr>
            </tbody>
          </table>
          <table style="width: 100%">
            <thead class="param_head">
              <tr>
                <td colspan="7">抽芯</td>
              </tr>
            </thead>
            <tbody>
              <tr class="title">
                <td>抽芯号</td>
                <td>1 IN</td>
                <td>1 OUT</td>
                <td>2 IN</td>
                <td>2 OUT</td>
                <td>3 IN</td>
                <td>3 OUT</td>
              </tr>
              <tr>
                <td>抽芯压力</td>
                <td>{{ paramdata.pre_core1_in }}</td>
                <td>{{ paramdata.pre_core1_out }}</td>
                <td>{{ paramdata.pre_core2_in }}</td>
                <td>{{ paramdata.pre_core2_out }}</td>
                <td>{{ paramdata.pre_core3_in }}</td>
                <td>{{ paramdata.pre_core3_out }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td>采集值</td>
                <td>{{ realtimedata?.MoldData?.CoreAInPres }}</td>
                <td>{{ realtimedata?.MoldData?.CoreAOutPres }}</td>
                <td>{{ realtimedata?.MoldData?.CoreBInPres }}</td>
                <td>{{ realtimedata?.MoldData?.CoreBOutPres }}</td>
                <td>{{ realtimedata?.MoldData?.CoreCInPres }}</td>
                <td>{{ realtimedata?.MoldData?.CoreCOutPres }}</td>
              </tr>
              <tr>
                <td>抽芯速度</td>
                <td>{{ paramdata.spd_core1_in }}</td>
                <td>{{ paramdata.spd_core1_out }}</td>
                <td>{{ paramdata.spd_core2_in }}</td>
                <td>{{ paramdata.spd_core2_out }}</td>
                <td>{{ paramdata.spd_core3_in }}</td>
                <td>{{ paramdata.spd_core3_out }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td>采集值</td>
                <td>{{ realtimedata?.MoldData?.CoreAInSpeed }}</td>
                <td>{{ realtimedata?.MoldData?.CoreAOutSpeed }}</td>
                <td>{{ realtimedata?.MoldData?.CoreBInSpeed }}</td>
                <td>{{ realtimedata?.MoldData?.CoreBOutSpeed }}</td>
                <td>{{ realtimedata?.MoldData?.CoreCInSpeed }}</td>
                <td>{{ realtimedata?.MoldData?.CoreCOutSpeed }}</td>
              </tr>
              <tr>
                <td>抽芯位置</td>
                <td>{{ paramdata.pos_core1_in }}</td>
                <td>{{ paramdata.pos_core1_out }}</td>
                <td>{{ paramdata.pos_core2_in }}</td>
                <td>{{ paramdata.pos_core2_out }}</td>
                <td>{{ paramdata.pos_core3_in }}</td>
                <td>{{ paramdata.pos_core3_out }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td>采集值</td>
                <td>{{ realtimedata?.MoldData?.CoreAInPos }}</td>
                <td>{{ realtimedata?.MoldData?.CoreAOutPos }}</td>
                <td>{{ realtimedata?.MoldData?.CoreBInPos }}</td>
                <td>{{ realtimedata?.MoldData?.CoreBOutPos }}</td>
                <td>{{ realtimedata?.MoldData?.CoreCInPos }}</td>
                <td>{{ realtimedata?.MoldData?.CoreCOutPos }}</td>
              </tr>
              <tr>
                <td>抽芯监控</td>
                <td>{{ paramdata.monitor_core1_in }}</td>
                <td>{{ paramdata.monitor_core1_out }}</td>
                <td>{{ paramdata.monitor_core2_in }}</td>
                <td>{{ paramdata.monitor_core2_out }}</td>
                <td>{{ paramdata.monitor_core3_in }}</td>
                <td>{{ paramdata.monitor_core3_out }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td>采集值</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
              </tr>
            </tbody>
          </table>
          <table style="width: 100%">
            <thead class="param_head">
              <tr>
                <td colspan="4">吹气</td>
              </tr>
            </thead>
            <tbody>
              <tr class="title">
                <td>吹气号</td>
                <td>吹气1</td>
                <td>吹气2</td>
                <td>吹气3</td>
              </tr>
              <tr>
                <td>吹气开始位置</td>
                <td>{{ paramdata.pos_start_air1 }}</td>
                <td>{{ paramdata.pos_start_air2 }}</td>
                <td>{{ paramdata.pos_start_air3 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td>采集值</td>
                <td>{{ realtimedata?.MoldData?.Air1Pos }}</td>
                <td>{{ realtimedata?.MoldData?.Air2Pos }}</td>
                <td>{{ realtimedata?.MoldData?.Air3Pos }}</td>
              </tr>
              <tr>
                <td>吹气停止位置</td>
                <td>{{ paramdata.pos_end_air1 }}</td>
                <td>{{ paramdata.pos_end_air2 }}</td>
                <td>{{ paramdata.pos_end_air3 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td>采集值</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
              </tr>
              <tr>
                <td>吹气时间</td>
                <td>{{ paramdata.time_air1 }}</td>
                <td>{{ paramdata.time_air2 }}</td>
                <td>{{ paramdata.time_air3 }}</td>
              </tr>
              <tr v-show="viewsetting.showmonitor" class="monitor_row">
                <td>采集值</td>
                <td>{{ realtimedata?.MoldData?.Air1Time }}</td>
                <td>{{ realtimedata?.MoldData?.Air2Time }}</td>
                <td>{{ realtimedata?.MoldData?.Air3Time }}</td>
              </tr>
            </tbody>
          </table>
        </el-row>
      </el-col>
    </el-row>
  </el-form>
</template>
<style lang="scss" scoped>
table {
  border-collapse: collapse;
  table-layout: fixed;
  border: 3px solid rgb(125, 125, 125);
  margin-top: 2px;
  tr {
    height: 24px;
  }
  tr td {
    border: 1px solid rgb(125, 125, 125);
    text-align: center;
  }
}
.param_head {
  background: linear-gradient(135deg, #0c3471, #0c3471 40%, #1f4e99);
  color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  tr td {
    text-align: left;
    color: #fff;
    span {
      float: right;
      margin-right: 5px;
    }
  }
  tr td i {
    font-size: medium;
  }
}
.alert_state {
  background-image: linear-gradient(
    to bottom,
    #f44336,
    #f44336 33%,
    #ffb3b3 66%,
    #ffb3b3
  );
  color: #fff;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}
.monitor_row {
  background-color: #f0f0f0;
  box-shadow: inset 0 0 10px 0 rgba(0, 0, 0, 0.3);
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 40px 40px;
}

.title {
  background: linear-gradient(135deg, #0c3471, #1f4e99);
  color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
}
</style>

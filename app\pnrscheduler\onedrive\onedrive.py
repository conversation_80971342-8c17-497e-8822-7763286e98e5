from O365 import Account, FileSystemTokenBackend
import os
import readline
from config import cfg


class MY365:
    def __init__(self):
        self.warningtimes = 0
        self.smscount = 0
        user = cfg.o365[0]
        client_id = user['client_id']
        client_secret = user['client_secret']
        tenant_id = user['tenant_id']
        email = user['email']
        fname = '/Users/<USER>/Documents/Develop/Backend/flask307/tokens/o365_token_onedrive_{}.txt'.format(email)
        token_backend = FileSystemTokenBackend(
            token_path='.', token_filename=fname)
        credentials = (client_id, client_secret)
        self.account = Account(credentials, tenant_id=tenant_id, token_backend=token_backend)
        if not self.account.is_authenticated:
            # sendSMS('连接', f'token失效 {user["email"]}')
            self.account.authenticate(scopes=[
                'basic',
                'message_all',
                'User.Read',
                'Files.ReadWrite.AppFolder'
            ])
        if not os.path.exists('files'):
            os.makedirs('files')

    def getFolder(self):
        storage = self.account.storage()
        onedrive = storage.get_default_drive()
        self.app_folder = onedrive.get_special_folder('approot')
        # folder_name = 'lean'
        # self.folder = onedrive.get_item_by_path(folder_name)
        # if self.folder is None:
        #     self.folder = onedrive.create_folder(folder_name)
        #     print(f"已创建文件夹: {folder_name}")
        # else:
        #     print(f"文件夹已存在: {folder_name}")
        # print("\n文件夹内容:")
        items = self.app_folder.get_items()
        for item in items:
            if item.is_file:
                print(item.name)
                if item.name == 'Dive Into Growth - Enabling Team Success.mp4':
                    item.download('/Users/<USER>/Documents/Develop/Backend/flask310/app/pnrscheduler/onedrive/files/')
            print(f"- {item.name} ({'文件夹' if item.is_folder else '文件'})")

    def getFile(self):
        file_to_download = 'dd.txt'
        download_path = 'downloaded_example.txt'
        file_to_download = file_to_download.download(file_to_download)
        if file_to_download:
            with open(download_path, 'wb') as f:
                file_to_download.download(to=f)
            print(f"已下载文件到: {download_path}")

    def postFile(self):
        file_to_upload = 'example.txt'
        # 先检查文件是否存在
        uploaded_file = self.folder.get_item_by_name(file_to_upload)
        if uploaded_file is None:
            # 文件不存在，上传新文件
            with open(file_to_upload, 'rb') as f:
                self.folder.upload_file(item=f, item_name=file_to_upload)
            print(f"已上传文件: {file_to_upload}")
        else:
            print(f"文件已存在: {file_to_upload}")


if __name__ == '__main__':
    my365 = MY365()
    my365.getFolder()
    # my365.getFile()

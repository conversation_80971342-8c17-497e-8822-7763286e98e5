import{useColumns as p}from"./columns-BirzvcTI.js";import{d as m,q as _,G as f,r as h,o as g,c as v,e as C,b,u as o}from"./index-BnxEuBzx.js";const k=m({__name:"index",props:{search_condition:{},hour_id:{}},setup(a){const{loading:n,columns:i,loadingConfig:t,dataList:d,refreshData:r,adaptiveConfig:s}=p(),e=a;return _(()=>{r({machine_id:e.search_condition.machine,hourid:e.hour_id,shift:e.search_condition.shift,selecteddate:e.search_condition.selecteddate})}),f(()=>e.hour_id,(c,l)=>{},{immediate:!0}),(c,l)=>{const u=h("pure-table");return g(),v("div",null,[C("div",null,[b(u,{ref:"tableRef",border:"",adaptive:"",adaptiveConfig:o(s),"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:o(n),"loading-config":o(t),data:o(d),columns:o(i)},null,8,["adaptiveConfig","loading","loading-config","data","columns"])])])}}});export{k as _};

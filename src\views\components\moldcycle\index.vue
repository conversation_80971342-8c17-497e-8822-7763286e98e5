<script setup lang="ts">
import { useColumns } from "./utils/columns";
import { computed, toRaw, watch, onMounted } from "vue";
const {
  loading,
  columns,
  loadingConfig,
  dataList,
  refreshData,
  adaptiveConfig
} = useColumns();

const props = defineProps<{
  search_condition: { selecteddate: string; machine: number; shift: string };
  hour_id?: number;
}>();

onMounted(() => {
  refreshData({
    machine_id: props.search_condition.machine,
    hourid: props.hour_id,
    shift: props.search_condition.shift,
    selecteddate: props.search_condition.selecteddate
  });
});
watch(
  () => props.hour_id,
  (newValue, oldValue) => {
    if (newValue !== undefined) {
    }
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div>
    <div>
      <pure-table
        ref="tableRef"
        border
        adaptive
        :adaptiveConfig="adaptiveConfig"
        row-key="id"
        alignWhole="center"
        showOverflowTooltip
        :loading="loading"
        :loading-config="loadingConfig"
        :data="dataList"
        :columns="columns"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

import type {
  AdaptiveConfig,
  PaginationProps,
  LoadingConfig
} from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import {
  getroutinglist,
  syncrouting,
  updaterouting,
  addrouting,
  deleteRouting
} from "@/api/setting";
import { ref, onMounted, reactive, h } from "vue";
import { addDialog } from "@/components/ReDialog";
import moment from "moment";
import { toRaw } from "vue";
import { message } from "@/utils/message";
import itemForm from "../item.vue";
import { FormItemProps } from "./types";
import { ElMessageBox } from "element-plus";

export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);
  const is_current = ref(true);
  const sql_count = ref(0);
  const redis_count = ref(0);
  const search_condition = reactive({
    pn: ""
  });
  const formRef = ref();
  const columns: TableColumnList = [
    {
      label: "料号",
      prop: "pn",
      width: "200px"
    },
    {
      label: "料号描述",
      width: "360px",
      prop: "pn_des"
    },
    {
      label: "模穴数",
      width: "70px",
      prop: "cavity"
    },
    {
      label: "机台号",
      width: "80px",
      prop: "machine"
    },
    {
      label: "自动下料",
      prop: "unload",
      width: "90px",
      cellRenderer: ({ row }) => {
        return (
          <el-switch
            v-model={row.unload}
            style={{
              "--el-switch-on-color": "#13ce66",
              "--el-switch-off-color": "#ff4949",
              height: "16px"
            }}
            inline-prompt
            disabled
            active-text="是"
            inactive-text="否"
            active-value={1}
            inactive-value={0}
          />
        );
      }
    },
    {
      label: "成型周期",
      prop: "moldingcycle",
      formatter(row) {
        const value = row.moldingcycle;
        return value.toFixed(value % 1 === 0 ? 0 : 1);
      }
    },
    {
      label: "上下料时间",
      prop: "manualcycle"
    },
    {
      label: "SAP工时",
      prop: "sap_value"
    },
    {
      label: "创建时间",
      prop: "create_time",
      formatter(row) {
        if (row.create_time) {
          return moment(row.create_time).format("YYYY/MM/DD HH:mm");
        }
      }
    },
    {
      label: "更新时间",
      prop: "update_time",
      formatter(row) {
        if (row.update_time) {
          return moment(row.update_time).format("YYYY/MM/DD HH:mm");
        }
      }
    },
    {
      label: "操作",
      cellRenderer: ({ row }) => {
        return (
          <div style="display:flex;justify-content:space-around">
            <el-link
              type="primary"
              underline
              onClick={() => openItem("change", row)}
            >
              编辑
            </el-link>
            <el-link
              type="danger"
              underline
              onClick={() => onDeleteConfirm(row)}
            >
              删除
            </el-link>
          </div>
        );
      }
    }
  ];

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载生产采集数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  /** 撑满内容区自适应高度相关配置 */
  const adaptiveConfig: AdaptiveConfig = {
    /** 表格距离页面底部的偏移量，默认值为 `96` */
    offsetBottom: 70
    /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
    // fixHeader: true
    /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
    // timeout: 60
    /** 表头的 `z-index`，默认值为 `100` */
    // zIndex: 100
  };

  function onSizeChange(val) {
    console.log("onSizeChange", val);
  }

  const getRoutingList = () => {
    loading.value = true;
    getroutinglist(toRaw(search_condition)).then((res: { data: any }) => {
      dataList.value = res.data.res;
      pagination.total = res.data.sql_count;
      sql_count.value = res.data.sql_count;
      redis_count.value = res.data.redis_count;
      loading.value = false;
    });
  };

  const asyncRouting = async () => {
    const res = (await syncrouting()) as { meta: any; data: any };
    if (res.meta.status != 201) {
      message(res.meta.msg, { customClass: "el", type: "error" });
    } else {
      message(res.meta.msg, { customClass: "el", type: "success" });
    }
  };

  onMounted(() => {
    getRoutingList();
  });

  function onDeleteConfirm(row) {
    ElMessageBox.confirm(
      `确认删除该routing吗，删除时将调整相邻状态时间，请确认?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        deleteRouting({
          id: row.id
        }).then((res: any) => {
          if (res.meta.status == 201) {
            getRoutingList();
          }
          message(res.meta.msg, {
            customClass: "el",
            type: res.meta.status == 201 ? "success" : "error",
            duration: 2000
          });
        });
      })
      .catch(() => {
        console.log("取消操作");
      });
  }

  const openItem = (action: string, row?: FormItemProps) => {
    addDialog({
      title: action == "change" ? "修改Routing" : "新增routing",
      props: {
        formInline: {
          pn: row?.pn ?? "",
          pn_des: row?.pn_des ?? "",
          cavity: row?.cavity ?? 1,
          machine: row?.machine ?? "",
          moldingcycle: row?.moldingcycle ?? 0,
          manualcycle: row?.manualcycle ?? 0,
          unload: row?.unload ?? 0,
          sap_value: row?.sap_value ?? 0
        },
        ops: {
          action_type: action == "change" ? 1 : 0
        }
      },
      width: "30%",
      draggable: true,
      fullscreenIcon: false,
      closeOnClickModal: false,
      contentRenderer: () => h(itemForm, { ref: formRef }),
      beforeSure: async (done, { options }) => {
        const FormRef = formRef.value.getRef();
        function chores() {
          message(`${action == "change" ? "修改" : "新增"}数据成功！`, {
            type: "success"
          });
          done(); // 关闭弹框
          getRoutingList(); // 刷新表格数据
        }
        const formdata = options.props.formInline as FormItemProps;
        FormRef.validate(valid => {
          if (valid) {
            if (action == "change") {
              updaterouting(toRaw(formdata)).then((res: { meta: any }) => {
                console.log(res);
                if (res.meta.status == 201) {
                  chores();
                } else {
                  message("Routing变更失败", { type: "error" });
                }
              });
            } else {
              addrouting(toRaw(formdata)).then((res: { meta: any }) => {
                if (res.meta.status == 201) {
                  chores();
                } else {
                  message(res.meta.msg, { type: "error" });
                }
              });
            }
          }
        });
      }
    });
  };

  /** 分页配置 */
  const pagination = reactive<PaginationProps>({
    pageSize: 15,
    currentPage: 1,
    pageSizes: [10, 15, 20, 50],
    total: 0,
    align: "right",
    background: true,
    small: false
  });

  function onCurrentChange(val) {
    loadingConfig.text = `正在加载第${val}页...`;
    loading.value = true;
    delay(200).then(() => {
      loading.value = false;
    });
  }

  return {
    search_condition,
    sql_count,
    redis_count,
    asyncRouting,
    openItem,
    is_current,
    loading,
    pagination,
    columns,
    dataList,
    loadingConfig,
    getRoutingList,
    adaptiveConfig,
    onSizeChange,
    onCurrentChange
  };
}

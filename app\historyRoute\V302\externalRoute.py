from flask import Blueprint, request
from app.welean.model.models_welean import Userinfo
from extensions import db
import pymysql
from app.public.functions import responsePut, responseGet


api = Blueprint('welean/V302/externalAPI', __name__)


@api.route('/updateQR', methods=['PUT'])
def updateQR():
    d = {
        'host': "**********",
        'port': 3306,
        'user': "pentairlean",
        'password': "Zhao.jiayun_0217",
        'database': "welean",
        'charset': "utf8"
    }
    db = pymysql.connect(host=d['host'], port=d['port'], user=d['user'], password=d['password'],
                         database=d['database'],
                         charset=d['charset'])
    cursor = db.cursor()
    res = request.json
    print(request)
    users = res.get('users')
    for user in users:
        u = user['eid']
        if user['status'] == 'N':
            pos = 'restrict'
        else:
            pos = 'pass'
        try:
            # 使用 cursor() 方法创建一个游标对象 cursor
            sql = "update wl_userinfo set position='%s' where eid='%s'" % (pos, u)
            cursor.execute(sql)
        except Exception:
            # 发生错误时回滚
            db.rollback()
    db.commit()  # 事务提交
    cursor.close()
    db.close()
    return responsePut("获取用户信息")


@api.route('/getUser', methods=['GET'])
def getUser():
    user = db.session.query(Userinfo).filter(Userinfo.eid == '1197455').scalar()
    if user:
        return responseGet("获取用户信息", {'name': user.position})
    else:
        return responseGet('出错了！')

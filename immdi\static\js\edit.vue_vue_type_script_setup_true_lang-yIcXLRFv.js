var U=(q,C,v)=>new Promise((D,b)=>{var w=p=>{try{t(v.next(p))}catch(V){b(V)}},k=p=>{try{t(v.throw(p))}catch(V){b(V)}},t=p=>p.done?D(p.value):Promise.resolve(p.value).then(w,k);t((v=v.apply(q,C)).next())});import{e as H,l as T}from"./system-DzNitOCO.js";import{a2 as W,d as G,n as c,b2 as J,r as s,o as g,g as K,h as l,b as e,u as h,f as n,y as Q,z as X,a3 as Y,e as d,c as M,aE as Z}from"./index-BnxEuBzx.js";import{R as ee}from"./index-8CyAUJH-.js";import{requestHook as N}from"./hook-pzVMp2q7.js";const le=W({"meta.title":[{required:!0,message:"必填项",trigger:"blur"}],name:[{required:!0,message:"必填项",trigger:"blur"}],path:[{required:!0,message:"必填项",trigger:"blur"}]}),te={class:"flex",style:{"align-items":"center"}},ae=d("label",null,"可见性",-1),oe={key:0},ne={class:"flex",style:{"align-items":"center"}},de=d("span",null,"外链地址",-1),se={key:1},ue={class:"flex",style:{"align-items":"center"}},ie=d("span",null,"唯一标识",-1),me={key:0},pe={class:"flex",style:{"align-items":"center"}},re=d("span",null,"操作地址",-1),_e={key:1},fe={class:"flex",style:{"align-items":"center"}},ce=d("span",null,"路由地址",-1),ve={class:"flex",style:{"align-items":"center"}},be=d("label",null,"图标",-1),Ve={class:"flex",style:{"align-items":"center"}},ye=d("label",null,"显示父级",-1),Ue=G({name:"SysMenuManagementEdit",__name:"edit",emits:["fetch-data"],setup(q,{expose:C,emit:v}){const D=v,b=c(),w=c({parent_id:0,genre:1,path:"",queue:0,meta:{title:"",showLink:!0,keepAlive:!0,showParent:!0}}),k=c(!1),t=c(J(w,!0)),p=c(""),V=c(!1),{showEdit:F,closeDialog:E}=ee({defaultFormData:w,formData:t,formVisible:k,isAdd:V,ruleFormRef:b,title:p,titleExt:"菜单",doneFn:()=>{D("fetch-data")}}),R=c(),O=r=>U(this,null,function*(){r&&(yield r.validate(a=>U(this,null,function*(){if(a){const{code:I}=yield N(H(t.value));I===0&&(Z("提交成功",{type:"success"}),E())}})))}),A=r=>{r>=0&&F({parent_id:r})},L=()=>U(this,null,function*(){const{data:r}=yield N(T());R.value=[{id:0,parent_id:0,meta:{title:"顶级菜单"},children:r}]}),P=r=>r.title;return C({showEdit:F,showEditWithParent:A,fetchData:L}),(r,a)=>{const I=s("el-tree-select"),u=s("el-form-item"),i=s("el-col"),m=s("el-radio"),y=s("el-row"),x=s("el-input"),_=s("IconifyIconOnline"),f=s("el-tooltip"),S=s("el-divider"),$=s("el-input-number"),j=s("el-form"),B=s("el-button"),z=s("el-dialog");return g(),K(z,{modelValue:k.value,"onUpdate:modelValue":a[19]||(a[19]=o=>k.value=o),title:p.value,width:"750px","before-close":h(E),draggable:!0},{footer:l(()=>[e(B,{onClick:h(E)},{default:l(()=>[n("取消")]),_:1},8,["onClick"]),e(B,{type:"primary",onClick:a[18]||(a[18]=o=>O(b.value))},{default:l(()=>[n(" 确定 ")]),_:1})]),default:l(()=>[e(j,{ref_key:"ruleFormRef",ref:b,rules:h(le),"label-width":"100px",model:t.value},{default:l(()=>[e(y,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(u,{label:"上级菜单"},{default:l(()=>[e(I,{modelValue:t.value.parent_id,"onUpdate:modelValue":a[0]||(a[0]=o=>t.value.parent_id=o),"check-strictly":"",clearable:"",data:R.value,"default-expand-all":"",props:{children:"children",label:P,value:"id"},"render-after-expand":!1,style:{width:"255px"}},{default:l(({node:o})=>[n(Q(h(X)(h(Y)(o.label))),1)]),_:1},8,["modelValue","data","props"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(u,{label:"菜单类型",prop:"genre"},{default:l(()=>[e(m,{modelValue:t.value.node_type,"onUpdate:modelValue":a[1]||(a[1]=o=>t.value.node_type=o),label:1},{default:l(()=>[n("菜单")]),_:1},8,["modelValue"]),e(m,{modelValue:t.value.node_type,"onUpdate:modelValue":a[2]||(a[2]=o=>t.value.node_type=o),label:2},{default:l(()=>[n("操作")]),_:1},8,["modelValue"]),e(m,{modelValue:t.value.node_type,"onUpdate:modelValue":a[3]||(a[3]=o=>t.value.node_type=o),label:3},{default:l(()=>[n("按钮")]),_:1},8,["modelValue"]),e(m,{modelValue:t.value.node_type,"onUpdate:modelValue":a[4]||(a[4]=o=>t.value.node_type=o),label:4},{default:l(()=>[n("外链")]),_:1},8,["modelValue"]),e(m,{modelValue:t.value.node_type,"onUpdate:modelValue":a[5]||(a[5]=o=>t.value.node_type=o),label:5},{default:l(()=>[n("Iframe")]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(u,{label:"标题",prop:"title"},{default:l(()=>[e(x,{modelValue:t.value.title,"onUpdate:modelValue":a[6]||(a[6]=o=>t.value.title=o),modelModifiers:{trim:!0},style:{width:"255px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(u,{prop:"show_link"},{label:l(()=>[d("div",te,[e(f,{class:"box-item",content:"在左侧菜单中隐藏",effect:"dark",placement:"top"},{default:l(()=>[e(_,{icon:"material-symbols:help-outline"})]),_:1}),ae])]),default:l(()=>[e(m,{modelValue:t.value.show_link,"onUpdate:modelValue":a[7]||(a[7]=o=>t.value.show_link=o),label:!0},{default:l(()=>[n(" 显示 ")]),_:1},8,["modelValue"]),e(m,{modelValue:t.value.show_link,"onUpdate:modelValue":a[8]||(a[8]=o=>t.value.show_link=o),label:!1},{default:l(()=>[n(" 隐藏 ")]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(u,{prop:"name"},{label:l(()=>[t.value.node_type==3?(g(),M("span",oe,[d("div",ne,[e(f,{class:"box-item",content:"外链地址，需以 http:// https:// // 开头",effect:"dark",placement:"top"},{default:l(()=>[e(_,{icon:"material-symbols:help-outline"})]),_:1}),de])])):(g(),M("span",se,[d("div",ue,[e(f,{class:"box-item",content:"首字母大写，一定要与 vue 文件 defineOptions 的 name 对应起来",effect:"dark",placement:"top"},{default:l(()=>[e(_,{icon:"material-symbols:help-outline"})]),_:1}),ie])]))]),default:l(()=>[e(x,{modelValue:t.value.name,"onUpdate:modelValue":a[9]||(a[9]=o=>t.value.name=o),modelModifiers:{trim:!0},disabled:!V.value&&t.value.node_type!=3,style:{width:"255px"}},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(u,{prop:"path"},{label:l(()=>[t.value.node_type==2?(g(),M("span",me,[d("div",pe,[e(f,{class:"box-item",content:"请求服务器的路径",effect:"dark",placement:"top"},{default:l(()=>[e(_,{icon:"material-symbols:help-outline"})]),_:1}),re])])):(g(),M("span",_e,[d("div",fe,[e(f,{class:"box-item",content:"path必须以/开头",effect:"dark",placement:"top"},{default:l(()=>[e(_,{icon:"material-symbols:help-outline"})]),_:1}),ce])]))]),default:l(()=>[e(x,{modelValue:t.value.path,"onUpdate:modelValue":a[10]||(a[10]=o=>t.value.path=o),modelModifiers:{trim:!0},style:{width:"255px"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(S),e(y,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(u,{label:"组件路径"},{default:l(()=>[e(x,{modelValue:t.value.component,"onUpdate:modelValue":a[11]||(a[11]=o=>t.value.component=o),modelModifiers:{trim:!0},clearable:"",disabled:t.value.genre!=1,style:{width:"255px"},placeholder:"可选"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(u,{label:"是否缓存",prop:"keep_alive"},{default:l(()=>[e(m,{modelValue:t.value.keep_alive,"onUpdate:modelValue":a[12]||(a[12]=o=>t.value.keep_alive=o),label:!0},{default:l(()=>[n(" 是 ")]),_:1},8,["modelValue"]),e(m,{modelValue:t.value.keep_alive,"onUpdate:modelValue":a[13]||(a[13]=o=>t.value.keep_alive=o),label:!1},{default:l(()=>[n(" 否 ")]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(u,{prop:"icon"},{label:l(()=>[d("div",ve,[e(f,{class:"box-item",content:"请访问 https://icones.js.org/ 查询",effect:"dark",placement:"top"},{default:l(()=>[e(_,{icon:"material-symbols:help-outline"})]),_:1}),be])]),default:l(()=>[e(x,{modelValue:t.value.icon,"onUpdate:modelValue":a[14]||(a[14]=o=>t.value.icon=o),modelModifiers:{trim:!0},clearable:"",style:{width:"255px"}},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(u,{prop:"keep_alive"},{label:l(()=>[d("div",Ve,[e(f,{class:"box-item",content:"当下级菜单只有一个的时候是否显示父级菜单",effect:"dark",placement:"top"},{default:l(()=>[e(_,{icon:"material-symbols:help-outline"})]),_:1}),ye])]),default:l(()=>[e(m,{modelValue:t.value.show_parent,"onUpdate:modelValue":a[15]||(a[15]=o=>t.value.show_parent=o),label:!0},{default:l(()=>[n(" 是 ")]),_:1},8,["modelValue"]),e(m,{modelValue:t.value.show_parent,"onUpdate:modelValue":a[16]||(a[16]=o=>t.value.show_parent=o),label:!1},{default:l(()=>[n(" 否 ")]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,null,{default:l(()=>[e(i,{span:12},{default:l(()=>[e(u,{label:"排序",prop:"rank"},{default:l(()=>[e($,{modelValue:t.value.rank,"onUpdate:modelValue":a[17]||(a[17]=o=>t.value.rank=o),min:0,step:5},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["rules","model"])]),_:1},8,["modelValue","title","before-close"])}}});export{Ue as _};

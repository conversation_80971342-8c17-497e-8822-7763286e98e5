import datetime
import requests
from chinese_calendar import get_workdays
from model import MsModel, MyModel
from utils.config import cfg
from o365pentairnew import O365
from utils.workday import workDays


class Sendmail(object):
    def __init__(self):
        self.mailserver = O365()

    def getSupvisor(self, owner):
        publiccnt = MyModel(cfg.emdidb, 'pentair_public')
        sql = "select supervisor from userinfo where email='%s'" % owner
        row = publiccnt.upSql(sql)
        if row:
            return row[0][0]
        else:
            return cfg.gm1

    def sendTest(self):
        self.mailserver.sendMail([cfg.gm1],  [],
                                 'Pentair-Mail-Server Test '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                 '邮件发送成功,测试完成', 'Mail-Server')

    def finmail(self, a, content):
        if a == 0:
            self.mailserver.sendMail([cfg.gm1],  [cfg.gm2],
                                     'Pentair-Mail-Server Send error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), content, 'Mail-Server')
            sql = "insert into mail_record(senddate,stype,content,status,record,recorddate) values ('%s','%s','%s',%d,%d,'%s')" % (
                datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'send', content, 0, 1, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            pubModel = MyModel(cfg.emdidb, 'pentair_public')
            pubModel.insSql(sql)
        else:
            sql = "insert into mail_record(senddate,stype,content,status,record,recorddate) values ('%s','%s','%s',%d,%d,'%s')" % (
                datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'send', content, 1, 1, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # print(sql)
            pubModel = MyModel(cfg.emdidb, 'pentair_public')
            b = pubModel.insSql(sql)
            if b == 0:
                self.mailserver.sendMail([cfg.gm1],  [cfg.gm2],
                                         'Pentair-Mail-Server Record error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '邮件发送成功,但是无法记录 '+content, 'Mail-Server')

    def sendtp(self):
        mycnt = MyModel(cfg.emdidb, 'transformationplan')
        sql = '''select owner,funnel,planstart from tp_funnel where status='open' and datediff(planstart,now())=10'''
        row = mycnt.upSql(sql)
        for i in range(len(row)):
            owner = row[i][0]
            funnel = row[i][1]
            planstart = datetime.datetime.strftime(row[i][2], "%Y-%m-%d")
            to = [owner]
            msg = "你的变革计划任务即将开始，项目为：" + \
                funnel + '<br>计划开始时间：' + planstart + \
                "<br>此提醒邮件在不变更开始时间的情况下只会发送一次，项目在10天后会自动开始，如需要改变计划开始时间，请联系部门经理更改"
            msg = msg + "<br>请登录<a href='https://szlean.pentair.pvt/pnr-mainpage'>EMDI主页</a>，添加TP系统到我的系统后点击进入"
            title = '变革计划即将在10天后自动开始，项目为：'+funnel
            cc = []
            # print(to, cc, title, msg)
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'Pentair-SuZhou Transformation Plan')
            self.finmail(a, 'TP系统发送planstart系统-'+','.join(to))

        mycnt = MyModel(cfg.emdidb, 'transformationplan')
        sql = '''select funnel,owner,requiredate, datediff(now(),requiredate),logtype from tp_funnellog left join tp_funnel on
        tp_funnellog.funnelid=tp_funnel.Id where recorddate is Null and tp_funnel.Id is not Null'''
        row = mycnt.upSql(sql)
        dic = {}
        for i in range(len(row)):
            funnel = row[i][0]
            owner = row[i][1]
            requiredate = datetime.datetime.strftime(row[i][2], "%Y-%m-%d")
            diffday = row[i][3]
            logtype = row[i][4]
            if owner in dic.keys():
                dic[owner]['item'].append({
                    'funnel': funnel,
                    'requiredate': requiredate,
                    'diffday': diffday,
                    'logtype': logtype
                })
                dic[owner]['maxdiffday'] = diffday if diffday > dic[owner]['maxdiffday'] else dic[owner]['maxdiffday']
                dic[owner]['nb'] = dic[owner]['nb']+1
            else:
                dic[owner] = {
                    'item': [{
                        'funnel': funnel,
                        'requiredate': requiredate,
                        'diffday': diffday,
                        'logtype': logtype
                    }],
                    'maxdiffday': diffday,
                    'nb': 1
                }
        for k, v in dic.items():
            to = [k]
            msg = "你有TP项目还未更新，最长超时已达"+str(v['maxdiffday'])+'天<br>'
            for vv in v['item']:
                if vv['funnel']:
                    msg = msg+'项目：'+vv['funnel']+'    要求更新：' + \
                        vv['requiredate']+'    超时时间：' + \
                        str(vv['diffday'])+'天<br>'+'  更新类型:'+vv['logtype']
            msg = msg + "<br>请登录<a href='https://szlean.pentair.pvt/pnr-mainpage'>EMDI主页</a>，添加TP系统到我的系统后点击进入"
            title = k+'有'+str(v['nb'])+'项TP项目超时未更新,最长已超时' + \
                str(v['maxdiffday'])+'天'
            cc = []
            if v['maxdiffday'] > 2:
                spv = self.getSupvisor(k)
                # print(k,spv)
                if spv:
                    cc.append(spv)
            # print(to, cc, title, msg)
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'Pentair-SuZhou Transformation Plan')
            self.finmail(a, 'TP系统Log超时提醒-'+','.join(to))
        # send ot chart

    def sendTPchart(self):
        res = requests.get(
            cfg.flaskurl+"tp/chartsAPI/getTaskslocal", params={
                'plant': 'Suzhou'
            })
        content = res.json()
        # print(content)
        # return
        if content["meta"]["status"] == 200:
            liststr = content['data']['url']
            to = content['data']['to']
            # to = ['<EMAIL>']
            cc = content['data']['cc']
            title = content['data']['title']
            msg = """
              TP未更新项目周报：<br>
              下列项目请及时更新，谢谢<br>
              %s
              <br>请登录<a href='https://szlean.pentair.pvt/pnr-mainpage'>EMDI主页</a>进入TP模块后查看详情
              """ % liststr
            # print(to, title, msg)
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'Pentair-SuZhou Transformation Plan')
            self.finmail(a, 'TP系统Log超时提醒-'+','.join(to))

    def sendcs(self):
        cscnt = MyModel(cfg.emdidb, 'cs')
        sql = '''select cs_orderlist.Id,cs_orderlist.recorddate,cs_planner.planner as planner,plannerbackup,supervisor
                from cs_orderlist left join cs_planner on cs_orderlist.mprc=cs_planner.mprc
                where cs_orderlist.updatedate is null'''
        row = cscnt.upSql(sql)
        plannerList = {}
        for i in range(len(row)):
            recorddate = row[i][1]
            planner = row[i][2]
            plannerbackup = row[i][3]
            supervisor = row[i][4]
            try:
                d = len(get_workdays(recorddate, datetime.date.today()))
            except Exception:
                d = workDays(recorddate, datetime.date.today()).daysCount()
            if planner not in plannerList.keys():
                pass48 = 0
                pass24 = 0
                if d > 2:
                    pass48 += 1
                elif d == 2:
                    pass24 += 1
                plannerList[planner] = {'pass48': pass48, 'pass24': pass24,
                                        'planner': planner, 'plannerbackup': plannerbackup, 'supervisor': supervisor}
            else:
                if d > 2:
                    plannerList[planner]['pass48'] += 1
                elif d == 2:
                    plannerList[planner]['pass24'] += 1
        # print(plannerList)
        # return
        for item in plannerList.values():
            cc = []
            to = []
            to.append(item['planner'])
            if item['pass48'] > 0:
                msg = item['planner'].split('@')[0]+"的订单交期超过48小时未回复，数量为：" + str(
                    item['pass48']) + "<br>请登录<a href='http://10.76.1.234/cs'>客户交期确认系统</a>，选择TA的未回复订单进行操作"
                title = item['planner'].split(
                    '@')[0]+"的订单交期超过48小时未回复，数量为：" + str(item['pass48'])
                to.append(item['plannerbackup'])
                cc.append(item['supervisor'])
                a = self.mailserver.sendMail(to, cc, title, msg, '客户交期确认系统')
                self.finmail(a, 'CS发送48-'+','.join(to))
            if item['pass24'] > 0:
                msg = "你的订单交期超过24小时未回复，数量为：" + str(
                    item['pass24']) + "<br>请登录<a href='http://10.76.1.234/cs'>客户交期确认系统</a>，选择自己的未回复订单进行操作"
                title = "你的订单交期超过24小时未回复，数量为：" + str(item['pass24'])
                a = self.mailserver.sendMail(to, cc, title, msg, '客户交期确认系统')
                self.finmail(a, 'CS发送24-'+','.join(to))

    def sendreceiving(self):
        mycnt = MyModel(cfg.emdidb, 'receiving')
        sql = '''
        SELECT
        Id, sku, owner,
        (
            SELECT COUNT(*)
            FROM (
                SELECT ADDDATE('2023-01-01', t4.i*10000 + t3.i*1000 + t2.i*100 + t1.i*10 + t0.i) AS date
                FROM
                    (SELECT 0 AS i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS t0,
                    (SELECT 0 AS i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS t1,
                    (SELECT 0 AS i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS t2,
                    (SELECT 0 AS i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS t3,
                    (SELECT 0 AS i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS t4
            ) AS dates
            WHERE
                date BETWEEN re_ncmr.initiatedate AND CURDATE()
                AND DAYOFWEEK(date) NOT IN (1, 7)
        ) AS diffday
            FROM re_ncmr
            WHERE re_ncmr.status = 'open'
            HAVING diffday > 1
        '''
        row = mycnt.upSql(sql)
        for i in range(len(row)):
            id = row[i][0]
            sku = row[i][1]
            owner = row[i][2]
            diffday = row[i][3]
            to = [owner]
            msg = "你的NCMR迟迟未处理，序列号为：" + \
                str(id) + '<br>料号：' + sku + \
                "<br>请登录<a href='http://10.76.1.234/receiving'>IQC管理系统</a>，并点击关于我的-我的NCMR进行操作"
            title = 'NCMR过期处理提醒，料号为：'+sku+'已过期：'+str(diffday)+'工作日（不算周末）'
            cc = ['<EMAIL>']
            if diffday > 2:
                spv = self.getSupvisor(owner)
                if spv:
                    cc.append(spv)
            # print(to, cc, title, msg)
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'Pentair-SuZhou IQC')
            self.finmail(a, 'QC系统发送NCMR处理-'+','.join(to))

        mycnt = MyModel(cfg.emdidb, 'receiving')
        sql = '''
        SELECT
        re_ncmr.Id,
        sku,
        name,
        (
            SELECT COUNT(*)
            FROM (
                SELECT ADDDATE('2023-01-01', t4.i*10000 + t3.i*1000 + t2.i*100 + t1.i*10 + t0.i) AS date
                FROM
                    (SELECT 0 AS i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS t0,
                    (SELECT 0 AS i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS t1,
                    (SELECT 0 AS i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS t2,
                    (SELECT 0 AS i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS t3,
                    (SELECT 0 AS i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS t4
            ) AS dates
            WHERE
                date BETWEEN re_ncmr.actiondate AND CURDATE()
                AND DAYOFWEEK(date) NOT IN (1, 7)
        ) AS diffday
        FROM
            re_ncmrauth
            LEFT JOIN re_ncmr ON re_ncmrauth.ncmrid = re_ncmr.Id
        WHERE
            re_ncmr.status = 'ongoing'
            AND appStatus = 0
        HAVING
            diffday > 1
        '''
        row = mycnt.upSql(sql)
        for i in range(len(row)):
            id = row[i][0]
            sku = row[i][1]
            owner = row[i][2]
            diffday = row[i][3]
            to = [owner]
            msg = "你有NCMR迟迟未批准，序列号为：" + str(
                id) + '<br>料号：' + sku + "<br>请登录<a href='http://10.76.1.234/receiving'>IQC管理系统</a>，并点击关于我的-我的签核进行操作"
            title = 'NCMR批准签核不及时提醒，料号为：' + sku + '已过期：' + str(diffday) + '工作日（不算周末）'
            cc = ['<EMAIL>']
            if diffday > 2:
                spv = self.getSupvisor(owner)
                if spv:
                    cc.append(spv)
            # print(to, cc, title, msg)
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'Pentair-SuZhou IQC')
            self.finmail(a, 'QC系统发送NCMR批准-'+','.join(to))

        mycnt = MyModel(cfg.emdidb, 'receiving')
        sql = '''select re_car.Id,initiator,email,sku,opendate,DATEDIFF(now(),opendate) as diffday from re_car
        left join re_supplier on re_car.supplier_name=re_supplier.name where status='open' and DATEDIFF(now(),opendate)>2'''
        row = mycnt.upSql(sql)
        for i in range(len(row)):
            id = row[i][0]
            initiator = row[i][1]
            supplier = row[i][2].split(';')
            sku = row[i][3]
            opendate = row[i][4]
            diffday = row[i][5]
            to = supplier
            msg = """
                贵司已关于%s的CAR-%s已经超过%s天没有回复<br>
                请查找我司于%s发出的邮件，下载并编辑附件，并在7天内进行回复，谢谢！<br>
                The CAR-%s for %s has not been replied for %s days.<br>
                Please seach for the CAR mail sent on %s, download and edit the attachment, then reply in 7 days, thanks!
            """ % (sku, str(id), str(diffday), opendate, str(id), sku, str(diffday), opendate)
            title = '贵司的关于' + sku + '的Car已超过' + \
                str(diffday) + '天未回复/'+'The CAR for ' + sku + \
                ' has not been replied for ' + str(diffday) + ' days'
            cc = [initiator]
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'Pentair-SuZhou IQC')
            self.finmail(a, 'QC发送CAR-'+','.join(to))

        mycnt = MyModel(cfg.emdidb, 'receiving')
        sql = '''select re_sku.sku,qe,planner,receive_date from re_inspect  left join re_sku on  re_sku.sku=re_inspect.sku where issap>=2'''
        row = mycnt.upSql(sql)
        to = []
        rdata = ''
        for i in range(len(row)):
            sku = row[i][0]
            qe = row[i][1]
            ml = row[i][2]
            rdate = row[i][3].strftime("%Y-%m-%d, %H:%M:%S")
            if qe:
                to.append(qe)
            if ml:
                to.append(ml)
            rdata = rdata+"""
            <tr>
                <td>%s</td>
                <td>%s</td>
                <td>%s</td>
                <td>%s</td>
              </tr>
            """ % (sku, qe, ml, rdate)
        to = list(set(to))
        msg = """"<table border='1'>
              <tr>
                <th>SKU</th>
                <th>质量</th>
                <th>ML</th>
                <th>接收日期</th>
              </tr>
              %s
            </table>""" % rdata+'<br>'+'请及时处理，处理完成请通知IQC进系统IQC检验-PROP65确认完成'
        title = '你有PROP65的物料还未处理完毕，请及时处理'
        cc = ['<EMAIL>']
        a = self.mailserver.sendMail(
            to, cc, title, msg, 'Pentair-SuZhou IQC')
        self.finmail(a, 'QC发送PROP提醒-'+','.join(to))

    def sendWeleanpassdue(self):
        users = {}
        suggests = {}
        res = requests.get(
            cfg.flaskurl+"public/info/getUsers", params={
                'plant': 'Suzhou'
            })
        # print(res)
        content = res.json()
        if content["meta"]["status"] == 200:
            users = content['data']['users']
        res = requests.get(
            "https://welean.pentair.cn/flaskserver/welean/bi/issueAPI/getPastdueIssues", params={
                'plant': 'Suzhou'
            })
        content = res.json()
        if content["meta"]["status"] == 200:
            suggests = content['data']['suggests']
        for v in suggests.values():
            if v['eid'] in users.keys():
                # print(users[v['eid']]['email'],v['content'],users[v['eid']]['supervisor'])
                # print(suggests)
                to = [users[v['eid']]['email']]
                cc = []
                # to = ['<EMAIL>']
                title = '微信小程序提案任务超时提醒'
                msg = """
              微信小程序提案任务超时提醒(每周一早上10点左右提醒)：<br>
              下列项目没有给出确认完成日期或确认完成日期已经超时，请确认。<br>
              对于已经拖了很长时间或无法完成的项目，也请点击取消完成关闭这个项目的跟踪。<br>
              请通过手机微信小程序进入后，在提案页面上方的提案任务里查看详情，有红色数字图标的说明有多少个未完成的项目<br>
              也可用电脑浏览器登录<a href='http://10.76.1.234/weleanH5'>提案系统</a>查看详情，操作一样<br>
              过期项目内容如下：<br>
              '%s'
              """ % v['content']
                # print(to, title, msg)
                a = self.mailserver.sendMail(
                    to, cc, title, msg, 'Pentair-SuZhou Wlean提案系统')
                self.finmail(a, '提案小程序任务完成超时提醒-'+','.join(to))

    def sendWeleanlayered(self):
        users = {}
        res = requests.get(
            cfg.flaskurl+"public/info/getUsers", params={
                'plant': 'Suzhou'
            })
        content = res.json()
        if content["meta"]["status"] == 200:
            users = content['data']['users']
        res = requests.get(
            "https://welean.pentair.cn/flaskserver/welean/bi/taskAPI/getLayeredPastdue", params={
                'plant': 'Suzhou'
            })
        content = res.json()
        if content["meta"]["status"] == 200:
            results = content['data']['results']
        for v in results:
            if v['eid'] in users.keys():
                # print(users[v['eid']]['email'],v['content'],users[v['eid']]['supervisor'])
                # print(suggests)
                to = [users[v['eid']]['email']]
                cc = []
                # to = ['<EMAIL>']
                title = f"{v['subitem']}每周提醒,剩余期限{28+v['gap']}天"
                msg = """
                微信小程序稽核任务每周提醒（任务完成期限4周，3次提醒后自动结束）：<br>
                收到此邮件说明你有未完成的稽核项目。<br>
                <div style='color:red;'>请特别注意任务中的5S的稽核任务，你的5S稽核对该区域很重要。</div><br>
                请通过手机微信小程序进入后，在提案页面上方的工作任务里查看详情，有红色数字图标的说明有多少个未完成的项目。<br>
                也可用电脑浏览器登录<a href='http://10.76.1.234/weleanH5'>提案系统</a>查看详情，操作一样。<br>
              """
                # print(to,title,msg)
                a = self.mailserver.sendMail(
                    to, cc, title, msg, 'Pentair-SuZhou Wlean提案系统')
                self.finmail(a, '提案小程序任务完成超时提醒-'+','.join(to))

    def sendrccm(self):
        cnt = MsModel(cfg.rccmdb)
        sql = """
                SELECT rccm_action.actionowner, rccm_action.action, rccm_action.duedate
                FROM rccm_action WHERE
                status = 'ongoing'
                AND duedate < CAST(GETDATE() AS DATE)"""
        actionDic = {}
        row = cnt.upSql(sql)
        for i in range(len(row)):
            actionowner = row[i][0]
            action = row[i][1]
            duedate = datetime.datetime.strftime(row[i][2], '%Y-%m-%d') if row[i][2] else ''
            if actionowner not in actionDic.keys():
                actionDic[actionowner] = []
            actionDic[actionowner].append({
                'type': "RCCM任务超时",
                'action': action,
                'duedate': duedate,
            })
        cnt = MsModel(cfg.rccmdb)
        sql2 = """
                SELECT DISTINCT rccm_rccm.status, rccm_rccm.reqreason, rccm_rccm.rccmname, rccm_rccm.initdate, rccm_rccm.owner,rccm_rccm.closedate,rccm_rccm.Id
                FROM rccm_rccm
                left JOIN rccm_action ON rccm_rccm.Id = rccm_action.rccmid
                WHERE rccm_rccm.status IN ('open', 'ongoing', 'closed')
            """
        row2 = cnt.upSql(sql2)
        # print(22222, len(row2))
        for i in range(len(row2)):
            mystatus = row2[i][0]
            reqreason = row2[i][1]
            name = row2[i][2]
            initdate = datetime.datetime.strftime(row2[i][3], '%Y-%m-%d') if row2[i][3] else ''
            actionowner = row2[i][4]
            closedate = row2[i][5]
            Id = row2[i][6]
            # print(1111111, name, mystatus)
            if mystatus == 'open':
                if actionowner not in actionDic.keys():
                    actionDic[actionowner] = []
                actionDic[actionowner].append({
                    'type': "你有新的RCCM",
                    'action': reqreason,
                    'duedate': initdate,
                })
            elif mystatus == 'closed':
                # print('111111', name)
                if (datetime.date.today()-closedate).days > 90:
                    if actionowner not in actionDic.keys():
                        actionDic[actionowner] = []
                    actionDic[actionowner].append({
                        'type': "RCCM 369更新提醒",
                        'action': name,
                        'duedate': "你的RCCM结束已经过去了90天,请更新369数据"
                    })
            else:
                cnt = MsModel(cfg.rccmdb)
                sql3 = """
                        SELECT status,action,initdate,actionowner
                        FROM rccm_action
                        WHERE rccmid = %d;
                        """ % Id
                isongoing = 0
                rrows = cnt.upSql(sql3)
                if len(rrows) > 0:
                    for a in range(len(rrows)):
                        status = rrows[a][0]
                        action = rrows[a][1]
                        duedate = rrows[a][2]
                        actionowner = rrows[a][3]
                        if status == 'ongoing':
                            isongoing += 1
                            if duedate < datetime.date.today():
                                if actionowner not in actionDic.keys():
                                    actionDic[actionowner] = []
                                actionDic[actionowner].append({
                                    'type': "RCCM有下属任务超时",
                                    'action': name,
                                    'duedate': datetime.datetime.strftime(duedate, "%Y-%m-%d") if duedate else ''
                                })
                                break
                if isongoing == 0:
                    if actionowner not in actionDic.keys():
                        actionDic[actionowner] = []
                    actionDic[actionowner].append({
                        'type': "RCCM下属任务超时",
                        'action': name,
                        'duedate': initdate
                    })
        for k, v in actionDic.items():
            mail = k
            to = [mail]
            liststr = """
                        <table style='width: 100%; border-collapse: collapse;'>
                            <thead>
                                <tr>
                                    <th style='border: 1px solid #ddd; padding: 8px;'>类型</th>
                                    <th style='border: 1px solid #ddd; padding: 8px;'>内容</th>
                                    <th style='border: 1px solid #ddd; padding: 8px;'>相关日期</th>
                                </tr>
                            </thead>
                            <tbody>
                    """
            for kk in v:
                liststr += """
                    <tr>
                        <td style='border: 1px solid #ddd; padding: 8px;'>'%s'</td>
                        <td style='border: 1px solid #ddd; padding: 8px;'>'%s'</td>
                        <td style='border: 1px solid #ddd; padding: 8px;'>'%s'</td>
                    </tr>
            """ % (kk['type'], kk['action'], kk['duedate'])
            liststr += "</tbody></table>"

            msg = "<p>你有过期RCCM项目待处理</p>"+"<br>"+liststr + \
                "<br>请登录<a href='http://10.76.1.234/pnr-capa'>EMDI-CAPA模块</a>登陆后点击右上方小铃铛快速处理"
            title = 'RCCM任系统提醒'
            cc = []
            # print(mail, msg)
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'RCCM待处理任务提醒')
            self.finmail(a, 'RCCM-'+','.join(to))

    def sendvoc(self):
        cnt = MsModel(cfg.vocdb)
        sql = "select id, qa, vocdesc, caction, cactiondue from dvoc where DateDiff(dd, cactiondue, getdate()) > 0 and cactiondate is null"
        row = cnt.upSql(sql)
        for i in range(len(row)):
            id = row[i][0]
            mail = row[i][1]+'@pentair.com'
            if not row[i][3]:
                ct = ''
            else:
                ct = row[i][3]
            desc = row[i][2]+'<br>短期措施：'+ct + \
                '<br>短期措施交期：'+row[i][4].strftime("%Y-%m-%d")
            to = [mail]
            msg = "你有过期VOC项目待处理，序列号为：" + str(id) + "<br>" + '内容：' + \
                desc + '<br>' + "请及时完成，或通知管理员修改目标日期已防止再次收到此邮件" + \
                "<br>请登录<a href='http://10.76.1.234/pnr-voc'>EMDI-VOC模块</a>进行处理"
            title = 'VOC措施过期提醒'
            cc = ['<EMAIL>', '<EMAIL>']
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'VOC过期处理提醒')
            self.finmail(a, 'VOC发送短期-'+','.join(to))

        cnt = MsModel(cfg.vocdb)
        sql = "select id,qa,vocdesc,laction,lactiondue from dvoc where DateDiff(dd,lactiondue,getdate())>0 and lactiondate is null"
        row = cnt.upSql(sql)
        for i in range(len(row)):
            id = row[i][0]
            mail = row[i][1] + '@pentair.com'
            if not row[i][3]:
                ct = ''
            else:
                ct = row[i][3]
            desc = row[i][2] + '<br>长期措施：' + ct + \
                '<br>长期措施交期：' + row[i][4].strftime("%Y-%m-%d")
            to = [mail]
            msg = "你有过期VOC项目待处理，序列号为："+str(id)+"<br>"+'内容：'+desc + \
                '<br>'+"请及时完成，或通知管理员修改目标日期已防止再次收到此邮件" + \
                "<br>请登录<a href='http://10.76.1.234/pnr-voc'>EMDI-VOC模块</a>进行处理"
            title = 'VOC措施过期提醒'
            cc = ['<EMAIL>', '<EMAIL>']
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'VOC过期处理提醒')
            self.finmail(a, 'VOC发送长期-'+','.join(to))

    def sendcal1(self):
        cnt = MsModel(cfg.vocdb)
        sql = "select COUNT(1) from dcal_cal2 join dcal_cal_version on dcal_cal2.id = " + \
            "dcal_cal_version.calid where DATEDIFF(DAY, GETDATE(), dcal_cal_version.duedate)<=37" + \
            " and dcal_cal_version.checkstatus!=3"
        row = cnt.upSql(sql)
        if row and row[0][0] > 0:
            title = 'CAL定期校验提醒'
            msg = '请登录<a href=\'http://10.76.1.234/pnr-cal/#/cal\'>量具系统</a> 量具管理模块 进行处理'
            to = ['<EMAIL>', '<EMAIL>']
            cc = []
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'CAL定期校验提醒')
            self.finmail(a, 'cal定期发送-'+','.join(to))

    def sendCalMonth(self):
        res = requests.get(
            cfg.flaskurl+"cal/settingAPI/getdownloadneedcal", params={
                'plant': 'Suzhou'
            })
        content = res.json()
        # print(content)
        # return
        if content["meta"]["status"] == 200:
            url = content['data']['url']
            to = ['<EMAIL>', '<EMAIL>']
            cc = []
            title = '到下个月底校验过期的量具清单'
            msg = """
              ：<br>
              下列项目请及时更新，谢谢<br>
              <br>请登录 <a href='%s'>量具阅读检验过期清单</a> 进行下载
              """ % url
            # print(to, title, msg)
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'Pentair-SuZhou Transformation Plan')
            self.finmail(a, 'TP系统Log超时提醒-'+','.join(to))

    def sendcal2(self):
        cnt = MsModel(cfg.vocdb)
        sql = """
        SELECT     CASE WHEN dcal_cal_version.austatus IS NULL AND dcal_cal_version.abstatus = 2 THEN dcal_cal_version.deptB + '@pentair.com' WHEN dcal_cal_version.austatus = 1 AND
                            dcal_cal_version.abstatus = 2 THEN dcal_cal_version.deptC + '@pentair.com' WHEN dcal_cal_version.austatus = 2 AND
                            dcal_cal_version.abstatus = 2 THEN dcal_cal_version.deptA + '@pentair.com' WHEN dcal_cal_version.abstatus = 3 THEN
                            dcal_cal_version.opensingnl + '@pentair.com' END AS needsendmail
        FROM         dcal_cal_version
        WHERE     (CASE WHEN dcal_cal_version.austatus IS NULL AND dcal_cal_version.abstatus = 2 THEN dcal_cal_version.deptB + '@pentair.com' WHEN dcal_cal_version.austatus = 1 AND
                            dcal_cal_version.abstatus = 2 THEN dcal_cal_version.deptC + '@pentair.com' WHEN dcal_cal_version.austatus = 2 AND
                            dcal_cal_version.abstatus = 2 THEN dcal_cal_version.deptA + '@pentair.com' WHEN dcal_cal_version.abstatus = 3 THEN
                            dcal_cal_version.opensingnl + '@pentair.com' END IS NOT NULL) AND
                            (CASE WHEN dcal_cal_version.austatus IS NULL AND dcal_cal_version.abstatus = 2 THEN
                              dcal_cal_version.deptB + '@pentair.com' WHEN dcal_cal_version.austatus = 1 AND
                            dcal_cal_version.abstatus = 2 THEN dcal_cal_version.deptC + '@pentair.com' WHEN dcal_cal_version.austatus = 2 AND
                            dcal_cal_version.abstatus = 2 THEN dcal_cal_version.deptA + '@pentair.com' WHEN dcal_cal_version.abstatus = 3 THEN
                            dcal_cal_version.opensingnl + '@pentair.com' END <> '@pentair.com')

                """
        row = cnt.upSql(sql)
        # print(row)
        needmaillist = []
        for i in range(len(row)):
            title = 'CAL异常核签流程过期提醒'
            msg = '请登录<a href=\'http://10.76.1.234/pnr-cal/#/cal\'>量具系统</a> 异常量具列表模块 进行处理'
            needmaillist.append(row[i][0])
        needmaillist = list(set(needmaillist))
        # print(needmaillist)
        if len(needmaillist) > 0:
            for i in needmaillist:
                to = [i]
                cc = ['<EMAIL>']
                # print(to)
                a = self.mailserver.sendMail(
                    to, cc, title, msg, 'CAL系统异常过期提醒')
                self.finmail(a, 'CAL定期发送-'+','.join(to))

        cnt = MsModel(cfg.vocdb)
        sql = """select case
            when dcal_cal_version.carstatus =1 then dcal_cal_version.deptA+'@pentair.com'
            when dcal_cal_version.carstatus =2 then dcal_cal_version.opensingnl+'@pentair.com'
            END as needsendmail
            from dcal_cal_version
            where case
            when dcal_cal_version.carstatus =1 then dcal_cal_version.deptA+'@pentair.com'
            when dcal_cal_version.carstatus =2 then dcal_cal_version.opensingnl+'@pentair.com'
            END is not null """
        row = cnt.upSql(sql)
        needmaillist = []
        for i in range(len(row)):
            title = 'CAL系统CAR流程过期提醒'
            msg = '请登录<a href=\'http://10.76.1.234/pnr-cal/#/cal\'>量具系统</a> CAR流程 模块 进行处理'
            needmaillist.append(row[i][0])
        needmaillist = list(set(needmaillist))
        if len(needmaillist) > 0:
            for i in needmaillist:
                to = [i]
                cc = ['<EMAIL>']
                a = self.mailserver.sendMail(
                    to, cc, title, msg, 'CAR流程过期提醒')
                self.finmail(a, 'CAR流程定期发送-'+','.join(to))

    def sendMROsafety(self):
        res = requests.get(
            "https://welean.pentair.cn/flaskserver/welean/V310/mroAPI/getMROsafety", params={
                'plant': 'Suzhou'
            })
        content = res.json()
        mystr = ''
        mylist = []
        if content["meta"]["status"] == 200:
            mylist = content['data']['mro']
            owner = content['data']['owner']
            cc = content['data']['cc']
            for m in mylist:
                mystr += f"<tr><td>{m['csku']}</td><td>{m['sku']}</td><td>{m['mrotype']}</td><td>{m['address']}</td><td>{m['stock']}/{m['safetystock']}</td></tr>"

        msg = f"""
                <!DOCTYPE html>
                <html lang="en">
                <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>邮件表格示例</title>
                </head>
                <body>

                <table border="1">
                <thead>
                    <tr>
                    <th>物品</th>
                    <th>料号</th>
                    <th>分类</th>
                    <th>地址</th>
                    <th>当前库存/安全库存</th>
                    </tr>
                </thead>
                <tbody>
                   {mystr}
                </tbody>
                </table>

                </body>
                </html>
             """
        if len(mylist) > 0:
            title = 'MRO仓库库存不足安全库存提醒'
            to = owner
            a = self.mailserver.sendMail(
                to, cc, title, msg, 'MRO安全库存提醒')
            self.finmail(a, 'MRO安全库存'+','.join(to))


if __name__ == '__main__':
    cn = Sendmail()
    cn.sendcal2()

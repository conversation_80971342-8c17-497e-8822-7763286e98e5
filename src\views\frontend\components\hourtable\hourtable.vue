<script setup lang="ts">
import { useColumns } from "./utils/column";
import { onMounted } from "vue";

const { dataList, columns, loadingConfig, adaptiveConfig, loading } =
  useColumns();

onMounted(() => {});
</script>

<template>
  <pure-table
    ref="tableRef"
    border
    stripe
    adaptive
    :adaptiveConfig="adaptiveConfig"
    alignWhole="center"
    showOverflowTooltip
    :loading="loading"
    :loading-config="loadingConfig"
    :data="dataList"
    :columns="columns"
    max-height="100%"
    height="100%"
  />
</template>

<style lang="scss" scoped>
:deep(.el-table__row) {
  background-color: transparent;
}

:deep(.el-tag) {
  font-size: 14px;
}

:deep(.ops) {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
</style>

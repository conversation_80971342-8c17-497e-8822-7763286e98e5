from flask import Blueprint, request, json
from app.public.functions import responseError, responseGet, responsePost
from extensions import db
from app.client.model.models_client import ClientIn<PERSON>, User,Role
from app.public.model.models_public import Publicuser
from extensions import ma
import datetime
import re
from marshmallow import fields, validate, ValidationError
import requests
from config import config, env
from app.client.functions.token import create_token

api = Blueprint('client/auth', __name__)


class LoginSchema(ma.Schema):
    username = fields.String(required=True, validate=[
                          validate.Length(min=5), validate.Regexp(r'^[\x00-\x7F]*$')])
    password = fields.String(required=True, validate=[
                          validate.Length(min=3), validate.Regexp(r'^[\x00-\x7F]*$')])
    
# 获取Publicuser表中email字段列表，并将@前面的部分提取出来，组成新的列表,新的列表中需要@前面的部分


@api.route('/get_name_list', methods=['GET'])
def get_email_list():
    email_list = Publicuser.query.with_entities(Publicuser.email).all()
    new_email_list = []
    for email in email_list:
        new_email_list.append(re.findall(r'[^@]+@', email[0])[0][:-1])
    return responseGet("获取用户名列表成功!", new_email_list)

@api.route('/login',methods=['POST'])
def login():
    login_info = request.get_json(silent=True) or request.form
    print(login_info)
    if not login_info:
        return responseError('请输入用户名密码')   
    eid = login_info.get('username')
    password = login_info.get('password')   
    if not eid or not password:
        return responseError('用户名和密码不能为空')    
    # 校验参数
    try:
        schema = LoginSchema()
        schema.load(login_info)
    except ValidationError as err:
        return responseError(err.messages)
    # try:
    #     r = requests.post(
    #         f"{config[env].api_url}/public/login",
    #         data={'username': eid, 'password': password},
    #         timeout=6
    #     )   
    #     if r.json().get('meta', {}).get('status') != 201:
    #         return responseError('登录验证失败，请检查用户名密码！')           
    # except requests.exceptions.RequestException as e:
        # return responseError("统一认证接口访问错误，登录失败！")
    res = db.session.query(User).filter(
        User.eid == login_info['username']).first()
    if not res:
        return responseError("已通过验证，但该用户在本系统中不存在，请联系Boots Tian添加！")
    # 取登录用户的权限组
    roles = db.session.query(Role).filter(
        Role.id.in_(res.role_id.split(","))).all()
    role_names = [role.name for role in roles]
    data = {
        "username": res.eid,
        "roles": role_names,
        "accessToken": create_token(res.eid),
        "refreshToken": create_token(res.eid, 2592000),
        "expires": (datetime.datetime.now()+config[env].JWT_ACCESS_TOKEN_EXPIRES).strftime('%Y/%m/%d %H:%M:%S')
    }
    return responsePost("返回成功", data)
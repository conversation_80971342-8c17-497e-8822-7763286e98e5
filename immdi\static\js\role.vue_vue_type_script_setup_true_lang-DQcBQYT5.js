import{R as u}from"./index-CykdORMA.js";import{d as V,n as b,r as a,o as r,g as m,h as e,b as o,u as p,c as w,F as g,t as x,f as C,y}from"./index-BnxEuBzx.js";const O=V({__name:"role",props:{formInline:{default:()=>({username:"",nickname:"",roleOptions:[],ids:[]})}},setup(d){const n=b(d.formInline);return(F,t)=>{const _=a("el-input"),s=a("el-form-item"),c=a("el-option"),i=a("el-select"),f=a("el-row"),v=a("el-form");return r(),m(v,{model:n.value},{default:e(()=>[o(f,{gutter:30},{default:e(()=>[o(p(u),null,{default:e(()=>[o(s,{label:"用户昵称",prop:"nickname"},{default:e(()=>[o(_,{modelValue:n.value.nickname,"onUpdate:modelValue":t[0]||(t[0]=l=>n.value.nickname=l),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),o(p(u),null,{default:e(()=>[o(s,{label:"角色列表",prop:"ids"},{default:e(()=>[o(i,{modelValue:n.value.ids,"onUpdate:modelValue":t[1]||(t[1]=l=>n.value.ids=l),placeholder:"请选择",class:"w-full",clearable:"",multiple:""},{default:e(()=>[(r(!0),w(g,null,x(n.value.roleOptions,(l,k)=>(r(),m(c,{key:k,value:l.id,label:l.name},{default:e(()=>[C(y(l.name),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])}}});export{O as _};

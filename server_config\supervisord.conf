[supervisord]
nodaemon=true

# nginx代理服务器启动
[program:nginx]
command=/usr/sbin/nginx
autostart=true
autorestart=false
stderr_logfile=/apiserver/log/nginx.err.log
stdout_logfile=/apiserver/log/nginx.out.log

# flask server启动
[program:gunicorn]
command=gunicorn -c gunicorn.conf.py manage:apiserver --preload
directory=/apiserver/flask
autostart=true
autorestart=true
stderr_logfile=/apiserver/log/flask.err.log
stdout_logfile=/apiserver/log/flask.out.log

# websockets server启动
# [program:websockets]
# command=python3 server.py
# directory=/apiserver/sockets
# stderr_logfile=/apiserver/log/websockets.err.log
# stdout_logfile=/apiserver/log/websockets.out.log

# 生产节拍采集程序
[program:producer]
directory=/apiserver/flask/script
command=python producer.py
autorestart=true
autostart=true
stderr_logfile=/apiserver/log/producer.err.log
stdout_logfile=/apiserver/log/producer.out.log

# 生产节拍处理程序
[program:consumer]
directory=/apiserver/flask/script
command=python consumer.py
autorestart=true
autostart=true
stderr_logfile=/apiserver/log/consumer.err.log
stdout_logfile=/apiserver/log/consumer.out.log

# 班次刷新程序
[program:refresh]
directory=/apiserver/flask/script
command=python refresh.py
autorestart=true
autostart=true
stderr_logfile=/apiserver/log/refresh.err.log
stdout_logfile=/apiserver/log/refresh.out.log

# 参数采集程序
[program:param]
directory=/apiserver/flask/script
command=python param.py
autorestart=true
autostart=true
stderr_logfile=/apiserver/log/param.err.log
stdout_logfile=/apiserver/log/param.out.log

import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";

import { ref, onMounted, reactive } from "vue";
import moment from "moment";
import { watch, toRaw } from "vue";
import { getCycle } from "@/api/prod";
import { message } from "@/utils/message";
export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);
  const columns: TableColumnList = [
    {
      label: "合模时间",
      prop: "start",
      formatter: (row: any) => {
        return moment(row.start).format("MM-DD HH:mm:ss");
      }
    },
    {
      label: "开模时间",
      prop: "end",
      formatter: (row: any) => {
        return moment(row.end).format("MM-DD HH:mm:ss");
      },
      minWidth: "150px"
    },
    {
      label: "注塑时间",
      prop: "duration",
      cellRenderer: ({ row }) => {
        if (row.flag == 1) {
          return <div style="color: #f50">{row.duration.toFixed(1)}</div>;
        } else {
          return <div>{row.duration.toFixed(1)}</div>;
        }
      }
    },
    {
      label: "取料时间",
      prop: "interval",
      formatter: (row: any) => {
        return row.interval.toFixed(1);
      }
    }
  ];

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载小时记录表数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  /** 撑满内容区自适应高度相关配置 */
  const adaptiveConfig: AdaptiveConfig = {
    /** 表格距离页面底部的偏移量，默认值为 `96` */
    // offsetBottom: 12,
    /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
    fixHeader: true
    /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
    // timeout: 60
    /** 表头的 `z-index`，默认值为 `100` */
    // zIndex: 100
  };

  const refreshData = query_obj => {
    getCycle(query_obj).then((res: any) => {
      dataList.value = res;
      console.log(dataList.value);
      loading.value = false;
    });
  };

  return {
    refreshData,
    loading,
    columns,
    dataList,
    loadingConfig,
    adaptiveConfig
  };
}

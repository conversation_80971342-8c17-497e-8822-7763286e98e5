<script setup lang="ts">
import hourtable from "../components/hourtable/hourtable.vue";
import mstatus from "../components/mstatus.vue";
import quality from "../components/quality/index.vue";
import runlog from "../components/runlog/index.vue";
import { useColumns } from "../components/hourtable/utils/column";
import { useProdStoreHook } from "@/store/modules/prod";
import { isCurrentShift } from "@/views/functions/shift";
import { changehourdata } from "@/views/components/edithourdata/index";
import { show_param } from "@/views/components/param_monitor/index";
import { watch, ref, onMounted } from "vue";
const { refreshData } = useColumns();

const current_shift = ref(true);

watch(
  [
    () => useProdStoreHook().selectedDate,
    () => useProdStoreHook().shift,
    () => useProdStoreHook().machineId
  ],
  newvalue => {
    current_shift.value = isCurrentShift(newvalue[0], newvalue[1]);
  }
);
onMounted(() => {
  current_shift.value = isCurrentShift(
    useProdStoreHook().selectedDate,
    useProdStoreHook().shift
  );
});
</script>

<template>
  <div class="main">
    <div class="panel">
      <div class="left_side">
        <div v-show="current_shift" class="machine_state">
          <dv-border-box10>
            <div style="height: 100%; padding: 10px 20px 5px 20px">
              <mstatus data="test" />
            </div>
          </dv-border-box10>
        </div>
        <div class="defect">
          <dv-border-box10>
            <div style="height: 100%; padding: 10px 10px 10px 10px">
              <quality /></div
          ></dv-border-box10>
        </div>
      </div>
      <div class="right_side">
        <dv-border-box12>
          <div style="height: 100%; padding: 10px 10px 10px 10px">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin-bottom: 5px;
              "
            >
              <span style="margin-left: 10px" class="lightfont"
                >小时记录表</span
              >
              <span style="padding-left: 5px">（每小时需要确认生产数据）</span>
              <el-button type="primary" style="padding: 0 5px" v-if="false"
                ><el-icon :size="18"><CircleCheck /></el-icon
                >&nbsp;批量确认</el-button
              >
              <el-button
                type="primary"
                style="padding: 0 5px"
                @click="refreshData"
                ><el-icon :size="18"><Refresh /></el-icon>&nbsp; 刷新
              </el-button>
              <el-button
                type="warning"
                style="padding: 0 5px"
                @click="changehourdata('add', useProdStoreHook().machineId)"
                ><el-icon :size="18"><CirclePlus /></el-icon>&nbsp; 新增小时数据
              </el-button>
              <el-button
                type="primary"
                style="padding: 0 5px"
                @click="show_param(useProdStoreHook().machineId)"
                ><el-icon :size="18"><Odometer /></el-icon>&nbsp;
                机台参数查看</el-button
              >
            </div>
            <div class="hourtable">
              <hourtable />
            </div>
          </div>
        </dv-border-box12>
      </div>
    </div>
    <div class="bottomcotent">
      <div class="runlog">
        <dv-border-box10
          ><div style="height: 100%; padding: 10px 20px 5px 20px">
            <runlog /></div
        ></dv-border-box10>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.lightfont {
  font-size: 18px;
  text-shadow:
    0 0 10px red,
    0 0 20px red,
    0 0 30px red,
    0 0 40px red;
}
.main {
  display: flex;
  flex-direction: column;
  flex: 1;
  .panel {
    display: flex;
    flex-direction: row;
    width: 100vw;
    flex: 2;
    overflow: hidden;
    .left_side {
      display: flex;
      flex-direction: column;
      flex: 1;
      width: 33.33%;
      max-height: 100%;
      .machine_state {
        margin: 2px;
        height: 200px;
      }
      .defect {
        margin: 2px;
        display: flex;
        flex: 1;
        height: 90%;
        overflow: hidden;
      }
    }
    .right_side {
      flex: 2;
      width: 66.66%;
      max-height: 100%;
      .hourtable {
        display: flex;
        flex: 1;
        width: 100%;
        height: calc(100% - 40px);
      }
    }
  }
  .bottomcotent {
    display: flex;
    flex-direction: row;
    flex: 1;
    width: 100vw;
    .runlog {
      margin: 2px;
      flex-grow: 1;
      height: 100%;
      overflow: hidden;
    }
  }
}

:deep(.el-dialog__body) {
  background-color: red;
}
</style>

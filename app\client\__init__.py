def importClientRoute(app):
    from app.client.route.common import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/client/commonAPI")

    from app.client.route.auth import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/client/auth")

    from app.client.route.tunnel import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/client/tunnel")

    from app.client.route.menu import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/client/menu")

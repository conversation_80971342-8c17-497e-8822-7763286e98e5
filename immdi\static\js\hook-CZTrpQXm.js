var Bi=Object.defineProperty,$i=Object.defineProperties;var zi=Object.getOwnPropertyDescriptors;var Ee=Object.getOwnPropertySymbols;var ji=Object.prototype.hasOwnProperty,Hi=Object.prototype.propertyIsEnumerable;var gt=Math.pow,_e=(a,t,e)=>t in a?Bi(a,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[t]=e,F=(a,t)=>{for(var e in t||(t={}))ji.call(t,e)&&_e(a,e,t[e]);if(Ee)for(var e of Ee(t))Hi.call(t,e)&&_e(a,e,t[e]);return a},rt=(a,t)=>$i(a,zi(t));var Et=(a,t,e)=>new Promise((i,r)=>{var n=c=>{try{o(e.next(c))}catch(h){r(h)}},s=c=>{try{o(e.throw(c))}catch(h){r(h)}},o=c=>c.done?i(c.value):Promise.resolve(c.value).then(n,s);o((e=e.apply(a,t)).next())});import{o as V,c as G,e as H,a as Wi,d as te,n as N,p as Ot,q as ti,ar as Yi,X as Ui,u as ee,W as Xi,b as k,w as X,D as ei,as as Vi,az as Gi,bb as Fi,bc as qi,bd as Zi,be as Ki,j as z,bf as Qi,I as Ji,ap as ii,r as ie,h as Te,g as ta,C as Se,y as Ht,bg as ea,a2 as Ft,G as ia,P as aa,bh as Ae,R as _t,bi as na,bj as ra,aW as sa,aE as Tt,bk as oa,S as ca,aX as Wt,aj as qt,bl as ha,bm as la,bn as ua,bo as da,F as fa}from"./index-BnxEuBzx.js";import{_ as pa}from"./role.vue_vue_type_script_setup_true_lang-DQcBQYT5.js";import{u as ga,_ as ma}from"./index.vue_vue_type_script_setup_true_lang-jY2_8jVF.js";import{u as va}from"./user-DNtD0Fqa.js";import{g as ba,h as wa,i as ya,j as xa}from"./system-DzNitOCO.js";import"./index-CykdORMA.js";const ae=(a,t)=>a.push.apply(a,t),xt=a=>a.sort((t,e)=>t.i-e.i||t.j-e.j),Re=a=>{const t={};let e=1;return a.forEach(i=>{t[i]=e,e+=1}),t};var Ma={4:[[1,2],[2,3]],5:[[1,3],[2,3],[2,4]],6:[[1,2],[2,4],[4,5]],7:[[1,3],[2,3],[4,5],[4,6]],8:[[2,4],[4,6]]};const ke=2050,Oe=1e3,Da=Ma,Ca=10,Ea=1e4,ai=10,ni=50,ri=20,si=/^[A-Z\xbf-\xdf][^A-Z\xbf-\xdf]+$/,_a=/^[^A-Z\xbf-\xdf]+[A-Z\xbf-\xdf]$/,Ta=/^[A-Z\xbf-\xdf]+$/,oi=/^[^a-z\xdf-\xff]+$/,Sa=/^[a-z\xdf-\xff]+$/,Aa=/^[^A-Z\xbf-\xdf]+$/,Ra=/[a-z\xdf-\xff]/,ka=/[A-Z\xbf-\xdf]/,Oa=/[^A-Za-z\xbf-\xdf]/gi,Ia=/^\d+$/,ge=new Date().getFullYear(),La={recentYear:/19\d\d|200\d|201\d|202\d/g},ci=[" ",",",";",":","|","/","\\","_",".","-"],Na=ci.length;class Pa{match({password:t}){const e=[...this.getMatchesWithoutSeparator(t),...this.getMatchesWithSeparator(t)],i=this.filterNoise(e);return xt(i)}getMatchesWithSeparator(t){const e=[],i=/^(\d{1,4})([\s/\\_.-])(\d{1,2})\2(\d{1,4})$/;for(let r=0;r<=Math.abs(t.length-6);r+=1)for(let n=r+5;n<=r+9&&!(n>=t.length);n+=1){const s=t.slice(r,+n+1||9e9),o=i.exec(s);if(o!=null){const c=this.mapIntegersToDayMonthYear([parseInt(o[1],10),parseInt(o[3],10),parseInt(o[4],10)]);c!=null&&e.push({pattern:"date",token:s,i:r,j:n,separator:o[2],year:c.year,month:c.month,day:c.day})}}return e}getMatchesWithoutSeparator(t){const e=[],i=/^\d{4,8}$/,r=n=>Math.abs(n.year-ge);for(let n=0;n<=Math.abs(t.length-4);n+=1)for(let s=n+3;s<=n+7&&!(s>=t.length);s+=1){const o=t.slice(n,+s+1||9e9);if(i.exec(o)){const c=[],h=o.length;if(Da[h].forEach(([u,d])=>{const m=this.mapIntegersToDayMonthYear([parseInt(o.slice(0,u),10),parseInt(o.slice(u,d),10),parseInt(o.slice(d),10)]);m!=null&&c.push(m)}),c.length>0){let u=c[0],d=r(c[0]);c.slice(1).forEach(m=>{const p=r(m);p<d&&(u=m,d=p)}),e.push({pattern:"date",token:o,i:n,j:s,separator:"",year:u.year,month:u.month,day:u.day})}}}return e}filterNoise(t){return t.filter(e=>{let i=!1;const r=t.length;for(let n=0;n<r;n+=1){const s=t[n];if(e!==s&&s.i<=e.i&&s.j>=e.j){i=!0;break}}return!i})}mapIntegersToDayMonthYear(t){if(t[1]>31||t[1]<=0)return null;let e=0,i=0,r=0;for(let n=0,s=t.length;n<s;n+=1){const o=t[n];if(o>99&&o<Oe||o>ke)return null;o>31&&(i+=1),o>12&&(e+=1),o<=0&&(r+=1)}return i>=2||e===3||r>=2?null:this.getDayMonth(t)}getDayMonth(t){const e=[[t[2],t.slice(0,2)],[t[0],t.slice(1,3)]],i=e.length;for(let r=0;r<i;r+=1){const[n,s]=e[r];if(Oe<=n&&n<=ke){const o=this.mapIntegersToDayMonth(s);return o!=null?{year:n,month:o.month,day:o.day}:null}}for(let r=0;r<i;r+=1){const[n,s]=e[r],o=this.mapIntegersToDayMonth(s);if(o!=null)return{year:this.twoToFourDigitYear(n),month:o.month,day:o.day}}return null}mapIntegersToDayMonth(t){const e=[t,t.slice().reverse()];for(let i=0;i<e.length;i+=1){const r=e[i],n=r[0],s=r[1];if(n>=1&&n<=31&&s>=1&&s<=12)return{day:n,month:s}}return null}twoToFourDigitYear(t){return t>99?t:t>50?t+1900:t+2e3}}const nt=new Uint32Array(65536),Ba=(a,t)=>{const e=a.length,i=t.length,r=1<<e-1;let n=-1,s=0,o=e,c=e;for(;c--;)nt[a.charCodeAt(c)]|=1<<c;for(c=0;c<i;c++){let h=nt[t.charCodeAt(c)];const l=h|s;h|=(h&n)+n^n,s|=~(h|n),n&=h,s&r&&o++,n&r&&o--,s=s<<1|1,n=n<<1|~(l|s),s&=l}for(c=e;c--;)nt[a.charCodeAt(c)]=0;return o},$a=(a,t)=>{const e=t.length,i=a.length,r=[],n=[],s=Math.ceil(e/32),o=Math.ceil(i/32);for(let p=0;p<s;p++)n[p]=-1,r[p]=0;let c=0;for(;c<o-1;c++){let p=0,y=-1;const v=c*32,D=Math.min(32,i)+v;for(let w=v;w<D;w++)nt[a.charCodeAt(w)]|=1<<w;for(let w=0;w<e;w++){const M=nt[t.charCodeAt(w)],O=n[w/32|0]>>>w&1,I=r[w/32|0]>>>w&1,f=M|p,C=((M|I)&y)+y^y|M|I;let A=p|~(C|y),$=y&C;A>>>31^O&&(n[w/32|0]^=1<<w),$>>>31^I&&(r[w/32|0]^=1<<w),A=A<<1|O,$=$<<1|I,y=$|~(f|A),p=A&f}for(let w=v;w<D;w++)nt[a.charCodeAt(w)]=0}let h=0,l=-1;const u=c*32,d=Math.min(32,i-u)+u;for(let p=u;p<d;p++)nt[a.charCodeAt(p)]|=1<<p;let m=i;for(let p=0;p<e;p++){const y=nt[t.charCodeAt(p)],v=n[p/32|0]>>>p&1,D=r[p/32|0]>>>p&1,w=y|h,M=((y|D)&l)+l^l|y|D;let O=h|~(M|l),I=l&M;m+=O>>>i-1&1,m-=I>>>i-1&1,O>>>31^v&&(n[p/32|0]^=1<<p),I>>>31^D&&(r[p/32|0]^=1<<p),O=O<<1|v,I=I<<1|D,l=I|~(w|O),h=O&w}for(let p=u;p<d;p++)nt[a.charCodeAt(p)]=0;return m},za=(a,t)=>{if(a.length<t.length){const e=t;t=a,a=e}return t.length===0?a.length:a.length<=32?Ba(a,t):$a(a,t)},ja=(a,t,e)=>{const i=a.length<=t.length,r=a.length<=e;return i||r?Math.ceil(a.length/4):e},Ha=(a,t,e)=>{let i=0;const r=Object.keys(t).find(n=>{const s=ja(a,n,e);if(Math.abs(a.length-n.length)>s)return!1;const o=za(a,n),c=o<=s;return c&&(i=o),c});return r?{levenshteinDistance:i,levenshteinDistanceEntry:r}:{}};var Ie={a:["4","@"],b:["8"],c:["(","{","[","<"],d:["6","|)"],e:["3"],f:["#"],g:["6","9","&"],h:["#","|-|"],i:["1","!","|"],k:["<","|<"],l:["!","1","|","7"],m:["^^","nn","2n","/\\\\/\\\\"],n:["//"],o:["0","()"],q:["9"],u:["|_|"],s:["$","5"],t:["+","7"],v:["<",">","/"],w:["^/","uu","vv","2u","2v","\\\\/\\\\/"],x:["%","><"],z:["2"]},Zt={warnings:{straightRow:"straightRow",keyPattern:"keyPattern",simpleRepeat:"simpleRepeat",extendedRepeat:"extendedRepeat",sequences:"sequences",recentYears:"recentYears",dates:"dates",topTen:"topTen",topHundred:"topHundred",common:"common",similarToCommon:"similarToCommon",wordByItself:"wordByItself",namesByThemselves:"namesByThemselves",commonNames:"commonNames",userInputs:"userInputs",pwned:"pwned"},suggestions:{l33t:"l33t",reverseWords:"reverseWords",allUppercase:"allUppercase",capitalization:"capitalization",dates:"dates",recentYears:"recentYears",associatedYears:"associatedYears",sequences:"sequences",repeated:"repeated",longerKeyboardPattern:"longerKeyboardPattern",anotherWord:"anotherWord",useWords:"useWords",noNeed:"noNeed",pwned:"pwned"},timeEstimation:{ltSecond:"ltSecond",second:"second",seconds:"seconds",minute:"minute",minutes:"minutes",hour:"hour",hours:"hours",day:"day",days:"days",month:"month",months:"months",year:"year",years:"years",centuries:"centuries"}};class Lt{constructor(t=[]){this.parents=t,this.children=new Map}addSub(t,...e){const i=t.charAt(0);this.children.has(i)||this.children.set(i,new Lt([...this.parents,i]));let r=this.children.get(i);for(let n=1;n<t.length;n+=1){const s=t.charAt(n);r.hasChild(s)||r.addChild(s),r=r.getChild(s)}return r.subs=(r.subs||[]).concat(e),this}getChild(t){return this.children.get(t)}isTerminal(){return!!this.subs}addChild(t){this.hasChild(t)||this.children.set(t,new Lt([...this.parents,t]))}hasChild(t){return this.children.has(t)}}var Le=(a,t)=>(Object.entries(a).forEach(([e,i])=>{i.forEach(r=>{t.addSub(r,e)})}),t);class Wa{constructor(){this.matchers={},this.l33tTable=Ie,this.trieNodeRoot=Le(Ie,new Lt),this.dictionary={userInputs:[]},this.rankedDictionaries={},this.rankedDictionariesMaxWordSize={},this.translations=Zt,this.graphs={},this.useLevenshteinDistance=!1,this.levenshteinThreshold=2,this.l33tMaxSubstitutions=100,this.maxLength=256,this.setRankedDictionaries()}setOptions(t={}){t.l33tTable&&(this.l33tTable=t.l33tTable,this.trieNodeRoot=Le(t.l33tTable,new Lt)),t.dictionary&&(this.dictionary=t.dictionary,this.setRankedDictionaries()),t.translations&&this.setTranslations(t.translations),t.graphs&&(this.graphs=t.graphs),t.useLevenshteinDistance!==void 0&&(this.useLevenshteinDistance=t.useLevenshteinDistance),t.levenshteinThreshold!==void 0&&(this.levenshteinThreshold=t.levenshteinThreshold),t.l33tMaxSubstitutions!==void 0&&(this.l33tMaxSubstitutions=t.l33tMaxSubstitutions),t.maxLength!==void 0&&(this.maxLength=t.maxLength)}setTranslations(t){if(this.checkCustomTranslations(t))this.translations=t;else throw new Error("Invalid translations object fallback to keys")}checkCustomTranslations(t){let e=!0;return Object.keys(Zt).forEach(i=>{if(i in t){const r=i;Object.keys(Zt[r]).forEach(n=>{n in t[r]||(e=!1)})}else e=!1}),e}setRankedDictionaries(){const t={},e={};Object.keys(this.dictionary).forEach(i=>{t[i]=Re(this.dictionary[i]),e[i]=this.getRankedDictionariesMaxWordSize(this.dictionary[i])}),this.rankedDictionaries=t,this.rankedDictionariesMaxWordSize=e}getRankedDictionariesMaxWordSize(t){const e=t.map(i=>typeof i!="string"?i.toString().length:i.length);return e.length===0?0:e.reduce((i,r)=>Math.max(i,r),-1/0)}buildSanitizedRankedDictionary(t){const e=[];return t.forEach(i=>{const r=typeof i;(r==="string"||r==="number"||r==="boolean")&&e.push(i.toString().toLowerCase())}),Re(e)}extendUserInputsDictionary(t){this.dictionary.userInputs||(this.dictionary.userInputs=[]);const e=[...this.dictionary.userInputs,...t];this.rankedDictionaries.userInputs=this.buildSanitizedRankedDictionary(e),this.rankedDictionariesMaxWordSize.userInputs=this.getRankedDictionariesMaxWordSize(e)}addMatcher(t,e){this.matchers[t]?console.info(`Matcher ${t} already exists`):this.matchers[t]=e}}const x=new Wa;class Ya{constructor(t){this.defaultMatch=t}match({password:t}){const e=t.split("").reverse().join("");return this.defaultMatch({password:e}).map(i=>rt(F({},i),{token:i.token.split("").reverse().join(""),reversed:!0,i:t.length-1-i.j,j:t.length-1-i.i}))}}class Ua{constructor({substr:t,limit:e,trieRoot:i}){this.buffer=[],this.finalPasswords=[],this.substr=t,this.limit=e,this.trieRoot=i}getAllPossibleSubsAtIndex(t){const e=[];let i=this.trieRoot;for(let r=t;r<this.substr.length;r+=1){const n=this.substr.charAt(r);if(i=i.getChild(n),!i)break;e.push(i)}return e}helper({onlyFullSub:t,isFullSub:e,index:i,subIndex:r,changes:n,lastSubLetter:s,consecutiveSubCount:o}){if(this.finalPasswords.length>=this.limit)return;if(i===this.substr.length){t===e&&this.finalPasswords.push({password:this.buffer.join(""),changes:n});return}const c=[...this.getAllPossibleSubsAtIndex(i)];let h=!1;for(let l=i+c.length-1;l>=i;l-=1){const u=c[l-i];if(u.isTerminal()){if(s===u.parents.join("")&&o>=3)continue;h=!0;const d=u.subs;for(const m of d){this.buffer.push(m);const p=n.concat({i:r,letter:m,substitution:u.parents.join("")});if(this.helper({onlyFullSub:t,isFullSub:e,index:l+1,subIndex:r+m.length,changes:p,lastSubLetter:u.parents.join(""),consecutiveSubCount:s===u.parents.join("")?o+1:1}),this.buffer.pop(),this.finalPasswords.length>=this.limit)return}}}if(!t||!h){const l=this.substr.charAt(i);this.buffer.push(l),this.helper({onlyFullSub:t,isFullSub:e&&!h,index:i+1,subIndex:r+1,changes:n,lastSubLetter:s,consecutiveSubCount:o}),this.buffer.pop()}}getAll(){return this.helper({onlyFullSub:!0,isFullSub:!0,index:0,subIndex:0,changes:[],lastSubLetter:void 0,consecutiveSubCount:0}),this.helper({onlyFullSub:!1,isFullSub:!0,index:0,subIndex:0,changes:[],lastSubLetter:void 0,consecutiveSubCount:0}),this.finalPasswords}}const Xa=(a,t,e)=>new Ua({substr:a,limit:t,trieRoot:e}).getAll(),Va=(a,t,e)=>{const r=a.changes.filter(h=>h.i<t).reduce((h,l)=>h-l.letter.length+l.substitution.length,t),n=a.changes.filter(h=>h.i>=t&&h.i<=e),s=n.reduce((h,l)=>h-l.letter.length+l.substitution.length,e-t+r),o=[],c=[];return n.forEach(h=>{o.findIndex(u=>u.letter===h.letter&&u.substitution===h.substitution)<0&&(o.push({letter:h.letter,substitution:h.substitution}),c.push(`${h.substitution} -> ${h.letter}`))}),{i:r,j:s,subs:o,subDisplay:c.join(", ")}};class Ga{constructor(t){this.defaultMatch=t}isAlreadyIncluded(t,e){return t.some(i=>Object.entries(i).every(([r,n])=>r==="subs"||n===e[r]))}match({password:t}){const e=[],i=Xa(t,x.l33tMaxSubstitutions,x.trieNodeRoot);let r=!1,n=!0;return i.forEach(s=>{if(r)return;const o=this.defaultMatch({password:s.password,useLevenshtein:n});n=!1,o.forEach(c=>{r||(r=c.i===0&&c.j===t.length-1);const h=Va(s,c.i,c.j),l=t.slice(h.i,+h.j+1||9e9),u=F(rt(F({},c),{l33t:!0,token:l}),h),d=this.isAlreadyIncluded(e,u);l.toLowerCase()!==c.matchedWord&&!d&&e.push(u)})}),e.filter(s=>s.token.length>1)}}class Fa{constructor(){this.l33t=new Ga(this.defaultMatch),this.reverse=new Ya(this.defaultMatch)}match({password:t}){const e=[...this.defaultMatch({password:t}),...this.reverse.match({password:t}),...this.l33t.match({password:t})];return xt(e)}defaultMatch({password:t,useLevenshtein:e=!0}){const i=[],r=t.length,n=t.toLowerCase();return Object.keys(x.rankedDictionaries).forEach(s=>{const o=x.rankedDictionaries[s],c=x.rankedDictionariesMaxWordSize[s],h=Math.min(c,r);for(let l=0;l<r;l+=1){const u=Math.min(l+h,r);for(let d=l;d<u;d+=1){const m=n.slice(l,+d+1||9e9),p=m in o;let y={};const v=l===0&&d===r-1;x.useLevenshteinDistance&&v&&!p&&e&&(y=Ha(m,o,x.levenshteinThreshold));const D=Object.keys(y).length!==0;if(p||D){const w=D?y.levenshteinDistanceEntry:m,M=o[w];i.push(F({pattern:"dictionary",i:l,j:d,token:t.slice(l,+d+1||9e9),matchedWord:m,rank:M,dictionaryName:s,reversed:!1,l33t:!1},y))}}}}),i}}class qa{match({password:t,regexes:e=La}){const i=[];return Object.keys(e).forEach(r=>{const n=e[r];n.lastIndex=0;let s;for(;s=n.exec(t);)if(s){const o=s[0];i.push({pattern:"regex",token:o,i:s.index,j:s.index+s[0].length-1,regexName:r,regexMatch:s})}}),xt(i)}}var dt={nCk(a,t){let e=a;if(t>e)return 0;if(t===0)return 1;let i=1;for(let r=1;r<=t;r+=1)i*=e,i/=r,e-=1;return i},log10(a){return a===0?0:Math.log(a)/Math.log(10)},log2(a){return Math.log(a)/Math.log(2)},factorial(a){let t=1;for(let e=2;e<=a;e+=1)t*=e;return t}},Za=({token:a})=>{let t=gt(Ca,a.length);t===Number.POSITIVE_INFINITY&&(t=Number.MAX_VALUE);let e;return a.length===1?e=ai+1:e=ni+1,Math.max(t,e)},Ka=({year:a,separator:t})=>{let i=Math.max(Math.abs(a-ge),ri)*365;return t&&(i*=4),i};const Qa=a=>{const t=a.split(""),e=t.filter(s=>s.match(ka)).length,i=t.filter(s=>s.match(Ra)).length;let r=0;const n=Math.min(e,i);for(let s=1;s<=n;s+=1)r+=dt.nCk(e+i,s);return r};var Ja=a=>{const t=a.replace(Oa,"");if(t.match(Aa)||t.toLowerCase()===t)return 1;const e=[si,_a,oi],i=e.length;for(let r=0;r<i;r+=1){const n=e[r];if(t.match(n))return 2}return Qa(t)};const Ne=(a,t)=>{let e=0,i=a.indexOf(t);for(;i>=0;)e+=1,i=a.indexOf(t,i+t.length);return e},tn=({sub:a,token:t})=>{const e=t.toLowerCase(),i=Ne(e,a.substitution),r=Ne(e,a.letter);return{subbedCount:i,unsubbedCount:r}};var en=({l33t:a,subs:t,token:e})=>{if(!a)return 1;let i=1;return t.forEach(r=>{const{subbedCount:n,unsubbedCount:s}=tn({sub:r,token:e});if(n===0||s===0)i*=2;else{const o=Math.min(s,n);let c=0;for(let h=1;h<=o;h+=1)c+=dt.nCk(s+n,h);i*=c}}),i},an=({rank:a,reversed:t,l33t:e,subs:i,token:r,dictionaryName:n})=>{const s=a,o=Ja(r),c=en({l33t:e,subs:i,token:r}),h=t&&2||1;let l;return n==="diceware"?l=gt(6,5)/2:l=s*o*c*h,{baseGuesses:s,uppercaseVariations:o,l33tVariations:c,calculation:l}},nn=({regexName:a,regexMatch:t,token:e})=>{const i={alphaLower:26,alphaUpper:26,alpha:52,alphanumeric:62,digits:10,symbols:33};if(a in i)return gt(i[a],e.length);switch(a){case"recentYear":return Math.max(Math.abs(parseInt(t[0],10)-ge),ri)}return 0},rn=({baseGuesses:a,repeatCount:t})=>a*t,sn=({token:a,ascending:t})=>{const e=a.charAt(0);let i=0;return["a","A","z","Z","0","1","9"].includes(e)?i=4:e.match(/\d/)?i=10:i=26,t||(i*=2),i*a.length};const on=a=>{let t=0;return Object.keys(a).forEach(e=>{const i=a[e];t+=i.filter(r=>!!r).length}),t/=Object.entries(a).length,t},cn=({token:a,graph:t,turns:e})=>{const i=Object.keys(x.graphs[t]).length,r=on(x.graphs[t]);let n=0;const s=a.length;for(let o=2;o<=s;o+=1){const c=Math.min(e,o-1);for(let h=1;h<=c;h+=1)n+=dt.nCk(o-1,h-1)*i*gt(r,h)}return n};var hn=({graph:a,token:t,shiftedCount:e,turns:i})=>{let r=cn({token:t,graph:a,turns:i});if(e){const n=t.length-e;if(e===0||n===0)r*=2;else{let s=0;for(let o=1;o<=Math.min(e,n);o+=1)s+=dt.nCk(e+n,o);r*=s}}return Math.round(r)},ln=()=>Na;const un=(a,t)=>{let e=1;return a.token.length<t.length&&(a.token.length===1?e=ai:e=ni),e},Pe={bruteforce:Za,date:Ka,dictionary:an,regex:nn,repeat:rn,sequence:sn,spatial:hn,separator:ln},dn=(a,t)=>Pe[a]?Pe[a](t):x.matchers[a]&&"scoring"in x.matchers[a]?x.matchers[a].scoring(t):0;var fn=(a,t)=>{const e={};if("guesses"in a&&a.guesses!=null)return a;const i=un(a,t),r=dn(a.pattern,a);let n=0;typeof r=="number"?n=r:a.pattern==="dictionary"&&(n=r.calculation,e.baseGuesses=r.baseGuesses,e.uppercaseVariations=r.uppercaseVariations,e.l33tVariations=r.l33tVariations);const s=Math.max(n,i);return rt(F(F({},a),e),{guesses:s,guessesLog10:dt.log10(s)})};const K={password:"",optimal:{},excludeAdditive:!1,separatorRegex:void 0,fillArray(a,t){const e=[];for(let i=0;i<a;i+=1){let r=[];t==="object"&&(r={}),e.push(r)}return e},makeBruteforceMatch(a,t){return{pattern:"bruteforce",token:this.password.slice(a,+t+1||9e9),i:a,j:t}},update(a,t){const e=a.j,i=fn(a,this.password);let r=i.guesses;t>1&&(r*=this.optimal.pi[i.i-1][t-1]);let n=dt.factorial(t)*r;this.excludeAdditive||(n+=gt(Ea,t-1));let s=!1;Object.keys(this.optimal.g[e]).forEach(o=>{const c=this.optimal.g[e][o];parseInt(o,10)<=t&&c<=n&&(s=!0)}),s||(this.optimal.g[e][t]=n,this.optimal.m[e][t]=i,this.optimal.pi[e][t]=r)},bruteforceUpdate(a){let t=this.makeBruteforceMatch(0,a);this.update(t,1);for(let e=1;e<=a;e+=1){t=this.makeBruteforceMatch(e,a);const i=this.optimal.m[e-1];Object.keys(i).forEach(r=>{i[r].pattern!=="bruteforce"&&this.update(t,parseInt(r,10)+1)})}},unwind(a){const t=[];let e=a-1,i=0,r=1/0;const n=this.optimal.g[e];for(n&&Object.keys(n).forEach(s=>{const o=n[s];o<r&&(i=parseInt(s,10),r=o)});e>=0;){const s=this.optimal.m[e][i];t.unshift(s),e=s.i-1,i-=1}return t}};var ne={mostGuessableMatchSequence(a,t,e=!1){K.password=a,K.excludeAdditive=e;const i=a.length;let r=K.fillArray(i,"array");t.forEach(c=>{r[c.j].push(c)}),r=r.map(c=>c.sort((h,l)=>h.i-l.i)),K.optimal={m:K.fillArray(i,"object"),pi:K.fillArray(i,"object"),g:K.fillArray(i,"object")};for(let c=0;c<i;c+=1)r[c].forEach(h=>{h.i>0?Object.keys(K.optimal.m[h.i-1]).forEach(l=>{K.update(h,parseInt(l,10)+1)}):K.update(h,1)}),K.bruteforceUpdate(c);const n=K.unwind(i),s=n.length,o=this.getGuesses(a,s);return{password:a,guesses:o,guessesLog10:dt.log10(o),sequence:n}},getGuesses(a,t){const e=a.length;let i=0;return a.length===0?i=1:i=K.optimal.g[e-1][t],i}};class pn{match({password:t,omniMatch:e}){const i=[];let r=0;for(;r<t.length;){const s=this.getGreedyMatch(t,r),o=this.getLazyMatch(t,r);if(s==null)break;const{match:c,baseToken:h}=this.setMatchToken(s,o);if(c){const l=c.index+c[0].length-1,u=this.getBaseGuesses(h,e);i.push(this.normalizeMatch(h,l,c,u)),r=l+1}}return i.some(s=>s instanceof Promise)?Promise.all(i):i}normalizeMatch(t,e,i,r){const n={pattern:"repeat",i:i.index,j:e,token:i[0],baseToken:t,baseGuesses:0,repeatCount:i[0].length/t.length};return r instanceof Promise?r.then(s=>rt(F({},n),{baseGuesses:s})):rt(F({},n),{baseGuesses:r})}getGreedyMatch(t,e){const i=/(.+)\1+/g;return i.lastIndex=e,i.exec(t)}getLazyMatch(t,e){const i=/(.+?)\1+/g;return i.lastIndex=e,i.exec(t)}setMatchToken(t,e){const i=/^(.+?)\1+$/;let r,n="";if(e&&t[0].length>e[0].length){r=t;const s=i.exec(r[0]);s&&(n=s[1])}else r=e,r&&(n=r[1]);return{match:r,baseToken:n}}getBaseGuesses(t,e){const i=e.match(t);return i instanceof Promise?i.then(n=>ne.mostGuessableMatchSequence(t,n).guesses):ne.mostGuessableMatchSequence(t,i).guesses}}class gn{constructor(){this.MAX_DELTA=5}match({password:t}){const e=[];if(t.length===1)return[];let i=0,r=null;const n=t.length;for(let s=1;s<n;s+=1){const o=t.charCodeAt(s)-t.charCodeAt(s-1);if(r==null&&(r=o),o!==r){const c=s-1;this.update({i,j:c,delta:r,password:t,result:e}),i=c,r=o}}return this.update({i,j:n-1,delta:r,password:t,result:e}),e}update({i:t,j:e,delta:i,password:r,result:n}){if(e-t>1||Math.abs(i)===1){const s=Math.abs(i);if(s>0&&s<=this.MAX_DELTA){const o=r.slice(t,+e+1||9e9),{sequenceName:c,sequenceSpace:h}=this.getSequence(o);return n.push({pattern:"sequence",i:t,j:e,token:r.slice(t,+e+1||9e9),sequenceName:c,sequenceSpace:h,ascending:i>0})}}return null}getSequence(t){let e="unicode",i=26;return Sa.test(t)?(e="lower",i=26):Ta.test(t)?(e="upper",i=26):Ia.test(t)&&(e="digits",i=10),{sequenceName:e,sequenceSpace:i}}}class mn{constructor(){this.SHIFTED_RX=/[~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:"ZXCVBNM<>?]/}match({password:t}){const e=[];return Object.keys(x.graphs).forEach(i=>{const r=x.graphs[i];ae(e,this.helper(t,r,i))}),xt(e)}checkIfShifted(t,e,i){return!t.includes("keypad")&&this.SHIFTED_RX.test(e.charAt(i))?1:0}helper(t,e,i){let r;const n=[];let s=0;const o=t.length;for(;s<o-1;){let c=s+1,h=null,l=0;for(r=this.checkIfShifted(i,t,s);;){const u=t.charAt(c-1),d=e[u]||[];let m=!1,p=-1,y=-1;if(c<o){const v=t.charAt(c),D=d.length;for(let w=0;w<D;w+=1){const M=d[w];if(y+=1,M){const O=M.indexOf(v);if(O!==-1){m=!0,p=y,O===1&&(r+=1),h!==p&&(l+=1,h=p);break}}}}if(m)c+=1;else{c-s>2&&n.push({pattern:"spatial",i:s,j:c-1,token:t.slice(s,c),graph:i,turns:l,shiftedCount:r}),s=c;break}}}return n}}const vn=new RegExp(`[${ci.join("")}]`);class Xt{static getMostUsedSeparatorChar(t){const e=[...t.split("").filter(r=>vn.test(r)).reduce((r,n)=>{const s=r.get(n);return s?r.set(n,s+1):r.set(n,1),r},new Map).entries()].sort(([r,n],[s,o])=>o-n);if(!e.length)return;const i=e[0];if(!(i[1]<2))return i[0]}static getSeparatorRegex(t){return new RegExp(`([^${t}
])(${t})(?!${t})`,"g")}match({password:t}){const e=[];if(t.length===0)return e;const i=Xt.getMostUsedSeparatorChar(t);if(i===void 0)return e;const r=Xt.getSeparatorRegex(i);for(const n of t.matchAll(r)){if(n.index===void 0)continue;const s=n.index+1;e.push({pattern:"separator",token:i,i:s,j:s})}return e}}class bn{constructor(){this.matchers={date:Pa,dictionary:Fa,regex:qa,repeat:pn,sequence:gn,spatial:mn,separator:Xt}}match(t){const e=[],i=[];return[...Object.keys(this.matchers),...Object.keys(x.matchers)].forEach(n=>{if(!this.matchers[n]&&!x.matchers[n])return;const s=this.matchers[n]?this.matchers[n]:x.matchers[n].Matching,c=new s().match({password:t,omniMatch:this});c instanceof Promise?(c.then(h=>{ae(e,h)}),i.push(c)):ae(e,c)}),i.length>0?new Promise((n,s)=>{Promise.all(i).then(()=>{n(xt(e))}).catch(o=>{s(o)})}):xt(e)}}const hi=1,li=hi*60,ui=li*60,di=ui*24,fi=di*31,pi=fi*12,wn=pi*100,Kt={second:hi,minute:li,hour:ui,day:di,month:fi,year:pi,century:wn};class yn{translate(t,e){let i=t;e!==void 0&&e!==1&&(i+="s");const{timeEstimation:r}=x.translations;return r[i].replace("{base}",`${e}`)}estimateAttackTimes(t){const e={onlineThrottling100PerHour:t/.027777777777777776,onlineNoThrottling10PerSecond:t/10,offlineSlowHashing1e4PerSecond:t/1e4,offlineFastHashing1e10PerSecond:t/1e10},i={onlineThrottling100PerHour:"",onlineNoThrottling10PerSecond:"",offlineSlowHashing1e4PerSecond:"",offlineFastHashing1e10PerSecond:""};return Object.keys(e).forEach(r=>{const n=e[r];i[r]=this.displayTime(n)}),{crackTimesSeconds:e,crackTimesDisplay:i,score:this.guessesToScore(t)}}guessesToScore(t){return t<1005?0:t<1000005?1:t<100000005?2:t<1e10+5?3:4}displayTime(t){let e="centuries",i;const r=Object.keys(Kt),n=r.findIndex(s=>t<Kt[s]);return n>-1&&(e=r[n-1],n!==0?i=Math.round(t/Kt[e]):e="ltSecond"),this.translate(e,i)}}var xn=()=>null,Mn=()=>({warning:x.translations.warnings.dates,suggestions:[x.translations.suggestions.dates]});const Dn=(a,t)=>{let e=null;return t&&!a.l33t&&!a.reversed?a.rank<=10?e=x.translations.warnings.topTen:a.rank<=100?e=x.translations.warnings.topHundred:e=x.translations.warnings.common:a.guessesLog10<=4&&(e=x.translations.warnings.similarToCommon),e},Cn=(a,t)=>{let e=null;return t&&(e=x.translations.warnings.wordByItself),e},En=(a,t)=>t?x.translations.warnings.namesByThemselves:x.translations.warnings.commonNames,_n=(a,t)=>{let e=null;const i=a.dictionaryName,r=i==="lastnames"||i.toLowerCase().includes("firstnames");return i==="passwords"?e=Dn(a,t):i.includes("wikipedia")?e=Cn(a,t):r?e=En(a,t):i==="userInputs"&&(e=x.translations.warnings.userInputs),e};var Tn=(a,t)=>{const e=_n(a,t),i=[],r=a.token;return r.match(si)?i.push(x.translations.suggestions.capitalization):r.match(oi)&&r.toLowerCase()!==r&&i.push(x.translations.suggestions.allUppercase),a.reversed&&a.token.length>=4&&i.push(x.translations.suggestions.reverseWords),a.l33t&&i.push(x.translations.suggestions.l33t),{warning:e,suggestions:i}},Sn=a=>a.regexName==="recentYear"?{warning:x.translations.warnings.recentYears,suggestions:[x.translations.suggestions.recentYears,x.translations.suggestions.associatedYears]}:{warning:null,suggestions:[]},An=a=>{let t=x.translations.warnings.extendedRepeat;return a.baseToken.length===1&&(t=x.translations.warnings.simpleRepeat),{warning:t,suggestions:[x.translations.suggestions.repeated]}},Rn=()=>({warning:x.translations.warnings.sequences,suggestions:[x.translations.suggestions.sequences]}),kn=a=>{let t=x.translations.warnings.keyPattern;return a.turns===1&&(t=x.translations.warnings.straightRow),{warning:t,suggestions:[x.translations.suggestions.longerKeyboardPattern]}},On=()=>null;const Be={warning:null,suggestions:[]};class In{constructor(){this.matchers={bruteforce:xn,date:Mn,dictionary:Tn,regex:Sn,repeat:An,sequence:Rn,spatial:kn,separator:On},this.defaultFeedback={warning:null,suggestions:[]},this.setDefaultSuggestions()}setDefaultSuggestions(){this.defaultFeedback.suggestions.push(x.translations.suggestions.useWords,x.translations.suggestions.noNeed)}getFeedback(t,e){if(e.length===0)return this.defaultFeedback;if(t>2)return Be;const i=x.translations.suggestions.anotherWord,r=this.getLongestMatch(e);let n=this.getMatchFeedback(r,e.length===1);return n!=null?n.suggestions.unshift(i):n={warning:null,suggestions:[i]},n}getLongestMatch(t){let e=t[0];return t.slice(1).forEach(r=>{r.token.length>e.token.length&&(e=r)}),e}getMatchFeedback(t,e){return this.matchers[t.pattern]?this.matchers[t.pattern](t,e):x.matchers[t.pattern]&&"feedback"in x.matchers[t.pattern]?x.matchers[t.pattern].feedback(t,e):Be}}const gi=()=>new Date().getTime(),Ln=(a,t,e)=>{const i=new In,r=new yn,n=ne.mostGuessableMatchSequence(t,a),s=gi()-e,o=r.estimateAttackTimes(n.guesses);return rt(F(F({calcTime:s},n),o),{feedback:i.getFeedback(o.score,n.sequence)})},Nn=(a,t)=>new bn().match(a),Pn=(a,t)=>{const e=gi(),i=Nn(a);if(i instanceof Promise)throw new Error("You are using a Promised matcher, please use `zxcvbnAsync` for it.");return Ln(i,a,e)};/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:05.335Z
 */function $e(a,t){var e=Object.keys(a);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(a);t&&(i=i.filter(function(r){return Object.getOwnPropertyDescriptor(a,r).enumerable})),e.push.apply(e,i)}return e}function mi(a){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?$e(Object(e),!0).forEach(function(i){jn(a,i,e[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):$e(Object(e)).forEach(function(i){Object.defineProperty(a,i,Object.getOwnPropertyDescriptor(e,i))})}return a}function Bn(a,t){if(typeof a!="object"||!a)return a;var e=a[Symbol.toPrimitive];if(e!==void 0){var i=e.call(a,t||"default");if(typeof i!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(a)}function vi(a){var t=Bn(a,"string");return typeof t=="symbol"?t:t+""}function re(a){"@babel/helpers - typeof";return re=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},re(a)}function $n(a,t){if(!(a instanceof t))throw new TypeError("Cannot call a class as a function")}function ze(a,t){for(var e=0;e<t.length;e++){var i=t[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(a,vi(i.key),i)}}function zn(a,t,e){return t&&ze(a.prototype,t),e&&ze(a,e),Object.defineProperty(a,"prototype",{writable:!1}),a}function jn(a,t,e){return t=vi(t),t in a?Object.defineProperty(a,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):a[t]=e,a}function bi(a){return Hn(a)||Wn(a)||Yn(a)||Un()}function Hn(a){if(Array.isArray(a))return se(a)}function Wn(a){if(typeof Symbol!="undefined"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function Yn(a,t){if(a){if(typeof a=="string")return se(a,t);var e=Object.prototype.toString.call(a).slice(8,-1);if(e==="Object"&&a.constructor&&(e=a.constructor.name),e==="Map"||e==="Set")return Array.from(a);if(e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return se(a,t)}}function se(a,t){(t==null||t>a.length)&&(t=a.length);for(var e=0,i=new Array(t);e<t;e++)i[e]=a[e];return i}function Un(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var Gt=typeof window!="undefined"&&typeof window.document!="undefined",at=Gt?window:{},me=Gt&&at.document.documentElement?"ontouchstart"in at.document.documentElement:!1,ve=Gt?"PointerEvent"in at:!1,P="cropper",be="all",wi="crop",yi="move",xi="zoom",ht="e",lt="w",mt="s",st="n",St="ne",At="nw",Rt="se",kt="sw",oe="".concat(P,"-crop"),je="".concat(P,"-disabled"),Z="".concat(P,"-hidden"),He="".concat(P,"-hide"),Xn="".concat(P,"-invisible"),Vt="".concat(P,"-modal"),ce="".concat(P,"-move"),Nt="".concat(P,"Action"),Yt="".concat(P,"Preview"),we="crop",Mi="move",Di="none",he="crop",le="cropend",ue="cropmove",de="cropstart",We="dblclick",Vn=me?"touchstart":"mousedown",Gn=me?"touchmove":"mousemove",Fn=me?"touchend touchcancel":"mouseup",Ye=ve?"pointerdown":Vn,Ue=ve?"pointermove":Gn,Xe=ve?"pointerup pointercancel":Fn,Ve="ready",Ge="resize",Fe="wheel",fe="zoom",qe="image/jpeg",qn=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,Zn=/^data:/,Kn=/^data:image\/jpeg;base64,/,Qn=/^img|canvas$/i,Ci=200,Ei=100,Ze={viewMode:0,dragMode:we,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:Ci,minContainerHeight:Ei,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},Jn='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',tr=Number.isNaN||at.isNaN;function E(a){return typeof a=="number"&&!tr(a)}var Ke=function(t){return t>0&&t<1/0};function Qt(a){return typeof a=="undefined"}function ut(a){return re(a)==="object"&&a!==null}var er=Object.prototype.hasOwnProperty;function vt(a){if(!ut(a))return!1;try{var t=a.constructor,e=t.prototype;return t&&e&&er.call(e,"isPrototypeOf")}catch(i){return!1}}function q(a){return typeof a=="function"}var ir=Array.prototype.slice;function _i(a){return Array.from?Array.from(a):ir.call(a)}function W(a,t){return a&&q(t)&&(Array.isArray(a)||E(a.length)?_i(a).forEach(function(e,i){t.call(a,e,i,a)}):ut(a)&&Object.keys(a).forEach(function(e){t.call(a,a[e],e,a)})),a}var B=Object.assign||function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return ut(t)&&i.length>0&&i.forEach(function(n){ut(n)&&Object.keys(n).forEach(function(s){t[s]=n[s]})}),t},ar=/\.\d*(?:0|9){12}\d*$/;function wt(a){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return ar.test(a)?Math.round(a*t)/t:a}var nr=/^width|height|left|top|marginLeft|marginTop$/;function ot(a,t){var e=a.style;W(t,function(i,r){nr.test(r)&&E(i)&&(i="".concat(i,"px")),e[r]=i})}function rr(a,t){return a.classList?a.classList.contains(t):a.className.indexOf(t)>-1}function U(a,t){if(t){if(E(a.length)){W(a,function(i){U(i,t)});return}if(a.classList){a.classList.add(t);return}var e=a.className.trim();e?e.indexOf(t)<0&&(a.className="".concat(e," ").concat(t)):a.className=t}}function it(a,t){if(t){if(E(a.length)){W(a,function(e){it(e,t)});return}if(a.classList){a.classList.remove(t);return}a.className.indexOf(t)>=0&&(a.className=a.className.replace(t,""))}}function bt(a,t,e){if(t){if(E(a.length)){W(a,function(i){bt(i,t,e)});return}e?U(a,t):it(a,t)}}var sr=/([a-z\d])([A-Z])/g;function ye(a){return a.replace(sr,"$1-$2").toLowerCase()}function pe(a,t){return ut(a[t])?a[t]:a.dataset?a.dataset[t]:a.getAttribute("data-".concat(ye(t)))}function Pt(a,t,e){ut(e)?a[t]=e:a.dataset?a.dataset[t]=e:a.setAttribute("data-".concat(ye(t)),e)}function or(a,t){if(ut(a[t]))try{delete a[t]}catch(e){a[t]=void 0}else if(a.dataset)try{delete a.dataset[t]}catch(e){a.dataset[t]=void 0}else a.removeAttribute("data-".concat(ye(t)))}var Ti=/\s\s*/,Si=function(){var a=!1;if(Gt){var t=!1,e=function(){},i=Object.defineProperty({},"once",{get:function(){return a=!0,t},set:function(n){t=n}});at.addEventListener("test",e,i),at.removeEventListener("test",e,i)}return a}();function tt(a,t,e){var i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},r=e;t.trim().split(Ti).forEach(function(n){if(!Si){var s=a.listeners;s&&s[n]&&s[n][e]&&(r=s[n][e],delete s[n][e],Object.keys(s[n]).length===0&&delete s[n],Object.keys(s).length===0&&delete a.listeners)}a.removeEventListener(n,r,i)})}function J(a,t,e){var i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},r=e;t.trim().split(Ti).forEach(function(n){if(i.once&&!Si){var s=a.listeners,o=s===void 0?{}:s;r=function(){delete o[n][e],a.removeEventListener(n,r,i);for(var h=arguments.length,l=new Array(h),u=0;u<h;u++)l[u]=arguments[u];e.apply(a,l)},o[n]||(o[n]={}),o[n][e]&&a.removeEventListener(n,o[n][e],i),o[n][e]=r,a.listeners=o}a.addEventListener(n,r,i)})}function yt(a,t,e){var i;return q(Event)&&q(CustomEvent)?i=new CustomEvent(t,{detail:e,bubbles:!0,cancelable:!0}):(i=document.createEvent("CustomEvent"),i.initCustomEvent(t,!0,!0,e)),a.dispatchEvent(i)}function Ai(a){var t=a.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}var Jt=at.location,cr=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Qe(a){var t=a.match(cr);return t!==null&&(t[1]!==Jt.protocol||t[2]!==Jt.hostname||t[3]!==Jt.port)}function Je(a){var t="timestamp=".concat(new Date().getTime());return a+(a.indexOf("?")===-1?"?":"&")+t}function It(a){var t=a.rotate,e=a.scaleX,i=a.scaleY,r=a.translateX,n=a.translateY,s=[];E(r)&&r!==0&&s.push("translateX(".concat(r,"px)")),E(n)&&n!==0&&s.push("translateY(".concat(n,"px)")),E(t)&&t!==0&&s.push("rotate(".concat(t,"deg)")),E(e)&&e!==1&&s.push("scaleX(".concat(e,")")),E(i)&&i!==1&&s.push("scaleY(".concat(i,")"));var o=s.length?s.join(" "):"none";return{WebkitTransform:o,msTransform:o,transform:o}}function hr(a){var t=mi({},a),e=0;return W(a,function(i,r){delete t[r],W(t,function(n){var s=Math.abs(i.startX-n.startX),o=Math.abs(i.startY-n.startY),c=Math.abs(i.endX-n.endX),h=Math.abs(i.endY-n.endY),l=Math.sqrt(s*s+o*o),u=Math.sqrt(c*c+h*h),d=(u-l)/l;Math.abs(d)>Math.abs(e)&&(e=d)})}),e}function Ut(a,t){var e=a.pageX,i=a.pageY,r={endX:e,endY:i};return t?r:mi({startX:e,startY:i},r)}function lr(a){var t=0,e=0,i=0;return W(a,function(r){var n=r.startX,s=r.startY;t+=n,e+=s,i+=1}),t/=i,e/=i,{pageX:t,pageY:e}}function ct(a){var t=a.aspectRatio,e=a.height,i=a.width,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",n=Ke(i),s=Ke(e);if(n&&s){var o=e*t;r==="contain"&&o>i||r==="cover"&&o<i?e=i/t:i=e*t}else n?e=i/t:s&&(i=e*t);return{width:i,height:e}}function ur(a){var t=a.width,e=a.height,i=a.degree;if(i=Math.abs(i)%180,i===90)return{width:e,height:t};var r=i%90*Math.PI/180,n=Math.sin(r),s=Math.cos(r),o=t*s+e*n,c=t*n+e*s;return i>90?{width:c,height:o}:{width:o,height:c}}function dr(a,t,e,i){var r=t.aspectRatio,n=t.naturalWidth,s=t.naturalHeight,o=t.rotate,c=o===void 0?0:o,h=t.scaleX,l=h===void 0?1:h,u=t.scaleY,d=u===void 0?1:u,m=e.aspectRatio,p=e.naturalWidth,y=e.naturalHeight,v=i.fillColor,D=v===void 0?"transparent":v,w=i.imageSmoothingEnabled,M=w===void 0?!0:w,O=i.imageSmoothingQuality,I=O===void 0?"low":O,f=i.maxWidth,C=f===void 0?1/0:f,A=i.maxHeight,$=A===void 0?1/0:A,_=i.minWidth,S=_===void 0?0:_,R=i.minHeight,T=R===void 0?0:R,L=document.createElement("canvas"),Y=L.getContext("2d"),et=ct({aspectRatio:m,width:C,height:$}),ft=ct({aspectRatio:m,width:S,height:T},"cover"),pt=Math.min(et.width,Math.max(ft.width,p)),Mt=Math.min(et.height,Math.max(ft.height,y)),Bt=ct({aspectRatio:r,width:C,height:$}),$t=ct({aspectRatio:r,width:S,height:T},"cover"),g=Math.min(Bt.width,Math.max($t.width,n)),b=Math.min(Bt.height,Math.max($t.height,s)),j=[-g/2,-b/2,g,b];return L.width=wt(pt),L.height=wt(Mt),Y.fillStyle=D,Y.fillRect(0,0,pt,Mt),Y.save(),Y.translate(pt/2,Mt/2),Y.rotate(c*Math.PI/180),Y.scale(l,d),Y.imageSmoothingEnabled=M,Y.imageSmoothingQuality=I,Y.drawImage.apply(Y,[a].concat(bi(j.map(function(Q){return Math.floor(wt(Q))})))),Y.restore(),L}var Ri=String.fromCharCode;function fr(a,t,e){var i="";e+=t;for(var r=t;r<e;r+=1)i+=Ri(a.getUint8(r));return i}var pr=/^data:.*,/;function gr(a){var t=a.replace(pr,""),e=atob(t),i=new ArrayBuffer(e.length),r=new Uint8Array(i);return W(r,function(n,s){r[s]=e.charCodeAt(s)}),i}function mr(a,t){for(var e=[],i=8192,r=new Uint8Array(a);r.length>0;)e.push(Ri.apply(null,_i(r.subarray(0,i)))),r=r.subarray(i);return"data:".concat(t,";base64,").concat(btoa(e.join("")))}function vr(a){var t=new DataView(a),e;try{var i,r,n;if(t.getUint8(0)===255&&t.getUint8(1)===216)for(var s=t.byteLength,o=2;o+1<s;){if(t.getUint8(o)===255&&t.getUint8(o+1)===225){r=o;break}o+=1}if(r){var c=r+4,h=r+10;if(fr(t,c,4)==="Exif"){var l=t.getUint16(h);if(i=l===18761,(i||l===19789)&&t.getUint16(h+2,i)===42){var u=t.getUint32(h+4,i);u>=8&&(n=h+u)}}}if(n){var d=t.getUint16(n,i),m,p;for(p=0;p<d;p+=1)if(m=n+p*12+2,t.getUint16(m,i)===274){m+=8,e=t.getUint16(m,i),t.setUint16(m,1,i);break}}}catch(y){e=1}return e}function br(a){var t=0,e=1,i=1;switch(a){case 2:e=-1;break;case 3:t=-180;break;case 4:i=-1;break;case 5:t=90,i=-1;break;case 6:t=90;break;case 7:t=90,e=-1;break;case 8:t=-90;break}return{rotate:t,scaleX:e,scaleY:i}}var wr={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,i=this.container,r=this.cropper,n=Number(e.minContainerWidth),s=Number(e.minContainerHeight);U(r,Z),it(t,Z);var o={width:Math.max(i.offsetWidth,n>=0?n:Ci),height:Math.max(i.offsetHeight,s>=0?s:Ei)};this.containerData=o,ot(r,{width:o.width,height:o.height}),U(t,Z),it(r,Z)},initCanvas:function(){var t=this.containerData,e=this.imageData,i=this.options.viewMode,r=Math.abs(e.rotate)%180===90,n=r?e.naturalHeight:e.naturalWidth,s=r?e.naturalWidth:e.naturalHeight,o=n/s,c=t.width,h=t.height;t.height*o>t.width?i===3?c=t.height*o:h=t.width/o:i===3?h=t.width/o:c=t.height*o;var l={aspectRatio:o,naturalWidth:n,naturalHeight:s,width:c,height:h};this.canvasData=l,this.limited=i===1||i===2,this.limitCanvas(!0,!0),l.width=Math.min(Math.max(l.width,l.minWidth),l.maxWidth),l.height=Math.min(Math.max(l.height,l.minHeight),l.maxHeight),l.left=(t.width-l.width)/2,l.top=(t.height-l.height)/2,l.oldLeft=l.left,l.oldTop=l.top,this.initialCanvasData=B({},l)},limitCanvas:function(t,e){var i=this.options,r=this.containerData,n=this.canvasData,s=this.cropBoxData,o=i.viewMode,c=n.aspectRatio,h=this.cropped&&s;if(t){var l=Number(i.minCanvasWidth)||0,u=Number(i.minCanvasHeight)||0;o>1?(l=Math.max(l,r.width),u=Math.max(u,r.height),o===3&&(u*c>l?l=u*c:u=l/c)):o>0&&(l?l=Math.max(l,h?s.width:0):u?u=Math.max(u,h?s.height:0):h&&(l=s.width,u=s.height,u*c>l?l=u*c:u=l/c));var d=ct({aspectRatio:c,width:l,height:u});l=d.width,u=d.height,n.minWidth=l,n.minHeight=u,n.maxWidth=1/0,n.maxHeight=1/0}if(e)if(o>(h?0:1)){var m=r.width-n.width,p=r.height-n.height;n.minLeft=Math.min(0,m),n.minTop=Math.min(0,p),n.maxLeft=Math.max(0,m),n.maxTop=Math.max(0,p),h&&this.limited&&(n.minLeft=Math.min(s.left,s.left+(s.width-n.width)),n.minTop=Math.min(s.top,s.top+(s.height-n.height)),n.maxLeft=s.left,n.maxTop=s.top,o===2&&(n.width>=r.width&&(n.minLeft=Math.min(0,m),n.maxLeft=Math.max(0,m)),n.height>=r.height&&(n.minTop=Math.min(0,p),n.maxTop=Math.max(0,p))))}else n.minLeft=-n.width,n.minTop=-n.height,n.maxLeft=r.width,n.maxTop=r.height},renderCanvas:function(t,e){var i=this.canvasData,r=this.imageData;if(e){var n=ur({width:r.naturalWidth*Math.abs(r.scaleX||1),height:r.naturalHeight*Math.abs(r.scaleY||1),degree:r.rotate||0}),s=n.width,o=n.height,c=i.width*(s/i.naturalWidth),h=i.height*(o/i.naturalHeight);i.left-=(c-i.width)/2,i.top-=(h-i.height)/2,i.width=c,i.height=h,i.aspectRatio=s/o,i.naturalWidth=s,i.naturalHeight=o,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,ot(this.canvas,B({width:i.width,height:i.height},It({translateX:i.left,translateY:i.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,i=this.imageData,r=i.naturalWidth*(e.width/e.naturalWidth),n=i.naturalHeight*(e.height/e.naturalHeight);B(i,{width:r,height:n,left:(e.width-r)/2,top:(e.height-n)/2}),ot(this.image,B({width:i.width,height:i.height},It(B({translateX:i.left,translateY:i.top},i)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,i=t.aspectRatio||t.initialAspectRatio,r=Number(t.autoCropArea)||.8,n={width:e.width,height:e.height};i&&(e.height*i>e.width?n.height=n.width/i:n.width=n.height*i),this.cropBoxData=n,this.limitCropBox(!0,!0),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),n.width=Math.max(n.minWidth,n.width*r),n.height=Math.max(n.minHeight,n.height*r),n.left=e.left+(e.width-n.width)/2,n.top=e.top+(e.height-n.height)/2,n.oldLeft=n.left,n.oldTop=n.top,this.initialCropBoxData=B({},n)},limitCropBox:function(t,e){var i=this.options,r=this.containerData,n=this.canvasData,s=this.cropBoxData,o=this.limited,c=i.aspectRatio;if(t){var h=Number(i.minCropBoxWidth)||0,l=Number(i.minCropBoxHeight)||0,u=o?Math.min(r.width,n.width,n.width+n.left,r.width-n.left):r.width,d=o?Math.min(r.height,n.height,n.height+n.top,r.height-n.top):r.height;h=Math.min(h,r.width),l=Math.min(l,r.height),c&&(h&&l?l*c>h?l=h/c:h=l*c:h?l=h/c:l&&(h=l*c),d*c>u?d=u/c:u=d*c),s.minWidth=Math.min(h,u),s.minHeight=Math.min(l,d),s.maxWidth=u,s.maxHeight=d}e&&(o?(s.minLeft=Math.max(0,n.left),s.minTop=Math.max(0,n.top),s.maxLeft=Math.min(r.width,n.left+n.width)-s.width,s.maxTop=Math.min(r.height,n.top+n.height)-s.height):(s.minLeft=0,s.minTop=0,s.maxLeft=r.width-s.width,s.maxTop=r.height-s.height))},renderCropBox:function(){var t=this.options,e=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,t.movable&&t.cropBoxMovable&&Pt(this.face,Nt,i.width>=e.width&&i.height>=e.height?yi:be),ot(this.cropBox,B({width:i.width,height:i.height},It({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),yt(this.element,he,this.getData())}},yr={initPreview:function(){var t=this.element,e=this.crossOrigin,i=this.options.preview,r=e?this.crossOriginUrl:this.url,n=t.alt||"The image to preview",s=document.createElement("img");if(e&&(s.crossOrigin=e),s.src=r,s.alt=n,this.viewBox.appendChild(s),this.viewBoxImage=s,!!i){var o=i;typeof i=="string"?o=t.ownerDocument.querySelectorAll(i):i.querySelector&&(o=[i]),this.previews=o,W(o,function(c){var h=document.createElement("img");Pt(c,Yt,{width:c.offsetWidth,height:c.offsetHeight,html:c.innerHTML}),e&&(h.crossOrigin=e),h.src=r,h.alt=n,h.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',c.innerHTML="",c.appendChild(h)})}},resetPreview:function(){W(this.previews,function(t){var e=pe(t,Yt);ot(t,{width:e.width,height:e.height}),t.innerHTML=e.html,or(t,Yt)})},preview:function(){var t=this.imageData,e=this.canvasData,i=this.cropBoxData,r=i.width,n=i.height,s=t.width,o=t.height,c=i.left-e.left-t.left,h=i.top-e.top-t.top;!this.cropped||this.disabled||(ot(this.viewBoxImage,B({width:s,height:o},It(B({translateX:-c,translateY:-h},t)))),W(this.previews,function(l){var u=pe(l,Yt),d=u.width,m=u.height,p=d,y=m,v=1;r&&(v=d/r,y=n*v),n&&y>m&&(v=m/n,p=r*v,y=m),ot(l,{width:p,height:y}),ot(l.getElementsByTagName("img")[0],B({width:s*v,height:o*v},It(B({translateX:-c*v,translateY:-h*v},t))))}))}},xr={bind:function(){var t=this.element,e=this.options,i=this.cropper;q(e.cropstart)&&J(t,de,e.cropstart),q(e.cropmove)&&J(t,ue,e.cropmove),q(e.cropend)&&J(t,le,e.cropend),q(e.crop)&&J(t,he,e.crop),q(e.zoom)&&J(t,fe,e.zoom),J(i,Ye,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&J(i,Fe,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&J(i,We,this.onDblclick=this.dblclick.bind(this)),J(t.ownerDocument,Ue,this.onCropMove=this.cropMove.bind(this)),J(t.ownerDocument,Xe,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&J(window,Ge,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,i=this.cropper;q(e.cropstart)&&tt(t,de,e.cropstart),q(e.cropmove)&&tt(t,ue,e.cropmove),q(e.cropend)&&tt(t,le,e.cropend),q(e.crop)&&tt(t,he,e.crop),q(e.zoom)&&tt(t,fe,e.zoom),tt(i,Ye,this.onCropStart),e.zoomable&&e.zoomOnWheel&&tt(i,Fe,this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&tt(i,We,this.onDblclick),tt(t.ownerDocument,Ue,this.onCropMove),tt(t.ownerDocument,Xe,this.onCropEnd),e.responsive&&tt(window,Ge,this.onResize)}},Mr={resize:function(){if(!this.disabled){var t=this.options,e=this.container,i=this.containerData,r=e.offsetWidth/i.width,n=e.offsetHeight/i.height,s=Math.abs(r-1)>Math.abs(n-1)?r:n;if(s!==1){var o,c;t.restore&&(o=this.getCanvasData(),c=this.getCropBoxData()),this.render(),t.restore&&(this.setCanvasData(W(o,function(h,l){o[l]=h*s})),this.setCropBoxData(W(c,function(h,l){c[l]=h*s})))}}},dblclick:function(){this.disabled||this.options.dragMode===Di||this.setDragMode(rr(this.dragBox,oe)?Mi:we)},wheel:function(t){var e=this,i=Number(this.options.wheelZoomRatio)||.1,r=1;this.disabled||(t.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout(function(){e.wheeling=!1},50),t.deltaY?r=t.deltaY>0?1:-1:t.wheelDelta?r=-t.wheelDelta/120:t.detail&&(r=t.detail>0?1:-1),this.zoom(-r*i,t)))},cropStart:function(t){var e=t.buttons,i=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(E(e)&&e!==1||E(i)&&i!==0||t.ctrlKey))){var r=this.options,n=this.pointers,s;t.changedTouches?W(t.changedTouches,function(o){n[o.identifier]=Ut(o)}):n[t.pointerId||0]=Ut(t),Object.keys(n).length>1&&r.zoomable&&r.zoomOnTouch?s=xi:s=pe(t.target,Nt),qn.test(s)&&yt(this.element,de,{originalEvent:t,action:s})!==!1&&(t.preventDefault(),this.action=s,this.cropping=!1,s===wi&&(this.cropping=!0,U(this.dragBox,Vt)))}},cropMove:function(t){var e=this.action;if(!(this.disabled||!e)){var i=this.pointers;t.preventDefault(),yt(this.element,ue,{originalEvent:t,action:e})!==!1&&(t.changedTouches?W(t.changedTouches,function(r){B(i[r.identifier]||{},Ut(r,!0))}):B(i[t.pointerId||0]||{},Ut(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var e=this.action,i=this.pointers;t.changedTouches?W(t.changedTouches,function(r){delete i[r.identifier]}):delete i[t.pointerId||0],e&&(t.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,bt(this.dragBox,Vt,this.cropped&&this.options.modal)),yt(this.element,le,{originalEvent:t,action:e}))}}},Dr={change:function(t){var e=this.options,i=this.canvasData,r=this.containerData,n=this.cropBoxData,s=this.pointers,o=this.action,c=e.aspectRatio,h=n.left,l=n.top,u=n.width,d=n.height,m=h+u,p=l+d,y=0,v=0,D=r.width,w=r.height,M=!0,O;!c&&t.shiftKey&&(c=u&&d?u/d:1),this.limited&&(y=n.minLeft,v=n.minTop,D=y+Math.min(r.width,i.width,i.left+i.width),w=v+Math.min(r.height,i.height,i.top+i.height));var I=s[Object.keys(s)[0]],f={x:I.endX-I.startX,y:I.endY-I.startY},C=function($){switch($){case ht:m+f.x>D&&(f.x=D-m);break;case lt:h+f.x<y&&(f.x=y-h);break;case st:l+f.y<v&&(f.y=v-l);break;case mt:p+f.y>w&&(f.y=w-p);break}};switch(o){case be:h+=f.x,l+=f.y;break;case ht:if(f.x>=0&&(m>=D||c&&(l<=v||p>=w))){M=!1;break}C(ht),u+=f.x,u<0&&(o=lt,u=-u,h-=u),c&&(d=u/c,l+=(n.height-d)/2);break;case st:if(f.y<=0&&(l<=v||c&&(h<=y||m>=D))){M=!1;break}C(st),d-=f.y,l+=f.y,d<0&&(o=mt,d=-d,l-=d),c&&(u=d*c,h+=(n.width-u)/2);break;case lt:if(f.x<=0&&(h<=y||c&&(l<=v||p>=w))){M=!1;break}C(lt),u-=f.x,h+=f.x,u<0&&(o=ht,u=-u,h-=u),c&&(d=u/c,l+=(n.height-d)/2);break;case mt:if(f.y>=0&&(p>=w||c&&(h<=y||m>=D))){M=!1;break}C(mt),d+=f.y,d<0&&(o=st,d=-d,l-=d),c&&(u=d*c,h+=(n.width-u)/2);break;case St:if(c){if(f.y<=0&&(l<=v||m>=D)){M=!1;break}C(st),d-=f.y,l+=f.y,u=d*c}else C(st),C(ht),f.x>=0?m<D?u+=f.x:f.y<=0&&l<=v&&(M=!1):u+=f.x,f.y<=0?l>v&&(d-=f.y,l+=f.y):(d-=f.y,l+=f.y);u<0&&d<0?(o=kt,d=-d,u=-u,l-=d,h-=u):u<0?(o=At,u=-u,h-=u):d<0&&(o=Rt,d=-d,l-=d);break;case At:if(c){if(f.y<=0&&(l<=v||h<=y)){M=!1;break}C(st),d-=f.y,l+=f.y,u=d*c,h+=n.width-u}else C(st),C(lt),f.x<=0?h>y?(u-=f.x,h+=f.x):f.y<=0&&l<=v&&(M=!1):(u-=f.x,h+=f.x),f.y<=0?l>v&&(d-=f.y,l+=f.y):(d-=f.y,l+=f.y);u<0&&d<0?(o=Rt,d=-d,u=-u,l-=d,h-=u):u<0?(o=St,u=-u,h-=u):d<0&&(o=kt,d=-d,l-=d);break;case kt:if(c){if(f.x<=0&&(h<=y||p>=w)){M=!1;break}C(lt),u-=f.x,h+=f.x,d=u/c}else C(mt),C(lt),f.x<=0?h>y?(u-=f.x,h+=f.x):f.y>=0&&p>=w&&(M=!1):(u-=f.x,h+=f.x),f.y>=0?p<w&&(d+=f.y):d+=f.y;u<0&&d<0?(o=St,d=-d,u=-u,l-=d,h-=u):u<0?(o=Rt,u=-u,h-=u):d<0&&(o=At,d=-d,l-=d);break;case Rt:if(c){if(f.x>=0&&(m>=D||p>=w)){M=!1;break}C(ht),u+=f.x,d=u/c}else C(mt),C(ht),f.x>=0?m<D?u+=f.x:f.y>=0&&p>=w&&(M=!1):u+=f.x,f.y>=0?p<w&&(d+=f.y):d+=f.y;u<0&&d<0?(o=At,d=-d,u=-u,l-=d,h-=u):u<0?(o=kt,u=-u,h-=u):d<0&&(o=St,d=-d,l-=d);break;case yi:this.move(f.x,f.y),M=!1;break;case xi:this.zoom(hr(s),t),M=!1;break;case wi:if(!f.x||!f.y){M=!1;break}O=Ai(this.cropper),h=I.startX-O.left,l=I.startY-O.top,u=n.minWidth,d=n.minHeight,f.x>0?o=f.y>0?Rt:St:f.x<0&&(h-=u,o=f.y>0?kt:At),f.y<0&&(l-=d),this.cropped||(it(this.cropBox,Z),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0));break}M&&(n.width=u,n.height=d,n.left=h,n.top=l,this.action=o,this.renderCropBox()),W(s,function(A){A.startX=A.endX,A.startY=A.endY})}},Cr={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&U(this.dragBox,Vt),it(this.cropBox,Z),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=B({},this.initialImageData),this.canvasData=B({},this.initialCanvasData),this.cropBoxData=B({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(B(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),it(this.dragBox,Vt),U(this.cropBox,Z)),this},replace:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,W(this.previews,function(i){i.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,it(this.cropper,je)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,U(this.cropper,je)),this},destroy:function(){var t=this.element;return t[P]?(t[P]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,i=this.canvasData,r=i.left,n=i.top;return this.moveTo(Qt(t)?t:r+Number(t),Qt(e)?e:n+Number(e))},moveTo:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,i=this.canvasData,r=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(E(t)&&(i.left=t,r=!0),E(e)&&(i.top=e,r=!0),r&&this.renderCanvas(!0)),this},zoom:function(t,e){var i=this.canvasData;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(i.width*t/i.naturalWidth,null,e)},zoomTo:function(t,e,i){var r=this.options,n=this.canvasData,s=n.width,o=n.height,c=n.naturalWidth,h=n.naturalHeight;if(t=Number(t),t>=0&&this.ready&&!this.disabled&&r.zoomable){var l=c*t,u=h*t;if(yt(this.element,fe,{ratio:t,oldRatio:s/c,originalEvent:i})===!1)return this;if(i){var d=this.pointers,m=Ai(this.cropper),p=d&&Object.keys(d).length?lr(d):{pageX:i.pageX,pageY:i.pageY};n.left-=(l-s)*((p.pageX-m.left-n.left)/s),n.top-=(u-o)*((p.pageY-m.top-n.top)/o)}else vt(e)&&E(e.x)&&E(e.y)?(n.left-=(l-s)*((e.x-n.left)/s),n.top-=(u-o)*((e.y-n.top)/o)):(n.left-=(l-s)/2,n.top-=(u-o)/2);n.width=l,n.height=u,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return t=Number(t),E(t)&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,E(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(E(e)?e:1,t)},scale:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,i=this.imageData,r=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(E(t)&&(i.scaleX=t,r=!0),E(e)&&(i.scaleY=e,r=!0),r&&this.renderCanvas(!0,!0)),this},getData:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.options,i=this.imageData,r=this.canvasData,n=this.cropBoxData,s;if(this.ready&&this.cropped){s={x:n.left-r.left,y:n.top-r.top,width:n.width,height:n.height};var o=i.width/i.naturalWidth;if(W(s,function(l,u){s[u]=l/o}),t){var c=Math.round(s.y+s.height),h=Math.round(s.x+s.width);s.x=Math.round(s.x),s.y=Math.round(s.y),s.width=h-s.x,s.height=c-s.y}}else s={x:0,y:0,width:0,height:0};return e.rotatable&&(s.rotate=i.rotate||0),e.scalable&&(s.scaleX=i.scaleX||1,s.scaleY=i.scaleY||1),s},setData:function(t){var e=this.options,i=this.imageData,r=this.canvasData,n={};if(this.ready&&!this.disabled&&vt(t)){var s=!1;e.rotatable&&E(t.rotate)&&t.rotate!==i.rotate&&(i.rotate=t.rotate,s=!0),e.scalable&&(E(t.scaleX)&&t.scaleX!==i.scaleX&&(i.scaleX=t.scaleX,s=!0),E(t.scaleY)&&t.scaleY!==i.scaleY&&(i.scaleY=t.scaleY,s=!0)),s&&this.renderCanvas(!0,!0);var o=i.width/i.naturalWidth;E(t.x)&&(n.left=t.x*o+r.left),E(t.y)&&(n.top=t.y*o+r.top),E(t.width)&&(n.width=t.width*o),E(t.height)&&(n.height=t.height*o),this.setCropBoxData(n)}return this},getContainerData:function(){return this.ready?B({},this.containerData):{}},getImageData:function(){return this.sized?B({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&W(["left","top","width","height","naturalWidth","naturalHeight"],function(i){e[i]=t[i]}),e},setCanvasData:function(t){var e=this.canvasData,i=e.aspectRatio;return this.ready&&!this.disabled&&vt(t)&&(E(t.left)&&(e.left=t.left),E(t.top)&&(e.top=t.top),E(t.width)?(e.width=t.width,e.height=t.width/i):E(t.height)&&(e.height=t.height,e.width=t.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var t=this.cropBoxData,e;return this.ready&&this.cropped&&(e={left:t.left,top:t.top,width:t.width,height:t.height}),e||{}},setCropBoxData:function(t){var e=this.cropBoxData,i=this.options.aspectRatio,r,n;return this.ready&&this.cropped&&!this.disabled&&vt(t)&&(E(t.left)&&(e.left=t.left),E(t.top)&&(e.top=t.top),E(t.width)&&t.width!==e.width&&(r=!0,e.width=t.width),E(t.height)&&t.height!==e.height&&(n=!0,e.height=t.height),i&&(r?e.height=e.width/i:n&&(e.width=e.height*i)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var e=this.canvasData,i=dr(this.image,this.imageData,e,t);if(!this.cropped)return i;var r=this.getData(t.rounded),n=r.x,s=r.y,o=r.width,c=r.height,h=i.width/Math.floor(e.naturalWidth);h!==1&&(n*=h,s*=h,o*=h,c*=h);var l=o/c,u=ct({aspectRatio:l,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),d=ct({aspectRatio:l,width:t.minWidth||0,height:t.minHeight||0},"cover"),m=ct({aspectRatio:l,width:t.width||(h!==1?i.width:o),height:t.height||(h!==1?i.height:c)}),p=m.width,y=m.height;p=Math.min(u.width,Math.max(d.width,p)),y=Math.min(u.height,Math.max(d.height,y));var v=document.createElement("canvas"),D=v.getContext("2d");v.width=wt(p),v.height=wt(y),D.fillStyle=t.fillColor||"transparent",D.fillRect(0,0,p,y);var w=t.imageSmoothingEnabled,M=w===void 0?!0:w,O=t.imageSmoothingQuality;D.imageSmoothingEnabled=M,O&&(D.imageSmoothingQuality=O);var I=i.width,f=i.height,C=n,A=s,$,_,S,R,T,L;C<=-o||C>I?(C=0,$=0,S=0,T=0):C<=0?(S=-C,C=0,$=Math.min(I,o+C),T=$):C<=I&&(S=0,$=Math.min(o,I-C),T=$),$<=0||A<=-c||A>f?(A=0,_=0,R=0,L=0):A<=0?(R=-A,A=0,_=Math.min(f,c+A),L=_):A<=f&&(R=0,_=Math.min(c,f-A),L=_);var Y=[C,A,$,_];if(T>0&&L>0){var et=p/o;Y.push(S*et,R*et,T*et,L*et)}return D.drawImage.apply(D,[i].concat(bi(Y.map(function(ft){return Math.floor(wt(ft))})))),v},setAspectRatio:function(t){var e=this.options;return!this.disabled&&!Qt(t)&&(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e=this.options,i=this.dragBox,r=this.face;if(this.ready&&!this.disabled){var n=t===we,s=e.movable&&t===Mi;t=n||s?t:Di,e.dragMode=t,Pt(i,Nt,t),bt(i,oe,n),bt(i,ce,s),e.cropBoxMovable||(Pt(r,Nt,t),bt(r,oe,n),bt(r,ce,s))}return this}},Er=at.Cropper,ki=function(){function a(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if($n(this,a),!t||!Qn.test(t.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,this.options=B({},Ze,vt(e)&&e),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return zn(a,[{key:"init",value:function(){var e=this.element,i=e.tagName.toLowerCase(),r;if(!e[P]){if(e[P]=this,i==="img"){if(this.isImg=!0,r=e.getAttribute("src")||"",this.originalUrl=r,!r)return;r=e.src}else i==="canvas"&&window.HTMLCanvasElement&&(r=e.toDataURL());this.load(r)}}},{key:"load",value:function(e){var i=this;if(e){this.url=e,this.imageData={};var r=this.element,n=this.options;if(!n.rotatable&&!n.scalable&&(n.checkOrientation=!1),!n.checkOrientation||!window.ArrayBuffer){this.clone();return}if(Zn.test(e)){Kn.test(e)?this.read(gr(e)):this.clone();return}var s=new XMLHttpRequest,o=this.clone.bind(this);this.reloading=!0,this.xhr=s,s.onabort=o,s.onerror=o,s.ontimeout=o,s.onprogress=function(){s.getResponseHeader("content-type")!==qe&&s.abort()},s.onload=function(){i.read(s.response)},s.onloadend=function(){i.reloading=!1,i.xhr=null},n.checkCrossOrigin&&Qe(e)&&r.crossOrigin&&(e=Je(e)),s.open("GET",e,!0),s.responseType="arraybuffer",s.withCredentials=r.crossOrigin==="use-credentials",s.send()}}},{key:"read",value:function(e){var i=this.options,r=this.imageData,n=vr(e),s=0,o=1,c=1;if(n>1){this.url=mr(e,qe);var h=br(n);s=h.rotate,o=h.scaleX,c=h.scaleY}i.rotatable&&(r.rotate=s),i.scalable&&(r.scaleX=o,r.scaleY=c),this.clone()}},{key:"clone",value:function(){var e=this.element,i=this.url,r=e.crossOrigin,n=i;this.options.checkCrossOrigin&&Qe(i)&&(r||(r="anonymous"),n=Je(i)),this.crossOrigin=r,this.crossOriginUrl=n;var s=document.createElement("img");r&&(s.crossOrigin=r),s.src=n||i,s.alt=e.alt||"The image to crop",this.image=s,s.onload=this.start.bind(this),s.onerror=this.stop.bind(this),U(s,He),e.parentNode.insertBefore(s,e.nextSibling)}},{key:"start",value:function(){var e=this,i=this.image;i.onload=null,i.onerror=null,this.sizing=!0;var r=at.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(at.navigator.userAgent),n=function(h,l){B(e.imageData,{naturalWidth:h,naturalHeight:l,aspectRatio:h/l}),e.initialImageData=B({},e.imageData),e.sizing=!1,e.sized=!0,e.build()};if(i.naturalWidth&&!r){n(i.naturalWidth,i.naturalHeight);return}var s=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=s,s.onload=function(){n(s.width,s.height),r||o.removeChild(s)},s.src=i.src,r||(s.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(s))}},{key:"stop",value:function(){var e=this.image;e.onload=null,e.onerror=null,e.parentNode.removeChild(e),this.image=null}},{key:"build",value:function(){if(!(!this.sized||this.ready)){var e=this.element,i=this.options,r=this.image,n=e.parentNode,s=document.createElement("div");s.innerHTML=Jn;var o=s.querySelector(".".concat(P,"-container")),c=o.querySelector(".".concat(P,"-canvas")),h=o.querySelector(".".concat(P,"-drag-box")),l=o.querySelector(".".concat(P,"-crop-box")),u=l.querySelector(".".concat(P,"-face"));this.container=n,this.cropper=o,this.canvas=c,this.dragBox=h,this.cropBox=l,this.viewBox=o.querySelector(".".concat(P,"-view-box")),this.face=u,c.appendChild(r),U(e,Z),n.insertBefore(o,e.nextSibling),it(r,He),this.initPreview(),this.bind(),i.initialAspectRatio=Math.max(0,i.initialAspectRatio)||NaN,i.aspectRatio=Math.max(0,i.aspectRatio)||NaN,i.viewMode=Math.max(0,Math.min(3,Math.round(i.viewMode)))||0,U(l,Z),i.guides||U(l.getElementsByClassName("".concat(P,"-dashed")),Z),i.center||U(l.getElementsByClassName("".concat(P,"-center")),Z),i.background&&U(o,"".concat(P,"-bg")),i.highlight||U(u,Xn),i.cropBoxMovable&&(U(u,ce),Pt(u,Nt,be)),i.cropBoxResizable||(U(l.getElementsByClassName("".concat(P,"-line")),Z),U(l.getElementsByClassName("".concat(P,"-point")),Z)),this.render(),this.ready=!0,this.setDragMode(i.dragMode),i.autoCrop&&this.crop(),this.setData(i.data),q(i.ready)&&J(e,Ve,i.ready,{once:!0}),yt(e,Ve)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var e=this.cropper.parentNode;e&&e.removeChild(this.cropper),it(this.element,Z)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=Er,a}},{key:"setDefaults",value:function(e){B(Ze,vt(e)&&e)}}])}();B(ki.prototype,wr,yr,xr,Mr,Dr,Cr);const _r={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},Tr=H("path",{fill:"currentColor",d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 0 1 755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 0 0 3 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 0 0 8 8.2h60c4.4 0 7.9-3.5 8-7.8m756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 0 1 512.1 856a342.24 342.24 0 0 1-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 0 0-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 0 0-8-8.2"},null,-1),Sr=[Tr];function Ar(a,t){return V(),G("svg",_r,[...Sr])}const Rr={render:Ar},kr={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},Or=H("path",{fill:"currentColor",d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 0 0-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13M878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8"},null,-1),Ir=[Or];function Lr(a,t){return V(),G("svg",kr,[...Ir])}const Nr={render:Lr},Pr={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",class:"icon",viewBox:"0 0 1024 1024"},Br=H("path",{d:"m296.992 216.992-272 272L3.008 512l21.984 23.008 272 272 46.016-46.016L126.016 544h772L680.992 760.992l46.016 46.016 272-272L1020.992 512l-21.984-23.008-272-272-46.048 46.048L898.016 480h-772l216.96-216.992z"},null,-1),$r=[Br];function zr(a,t){return V(),G("svg",Pr,[...$r])}const jr={render:zr},Hr={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",class:"icon",viewBox:"0 0 1024 1024"},Wr=H("path",{d:"m512 67.008-23.008 21.984-256 256 46.048 46.048L480 190.016v644L279.008 632.96l-46.048 46.08 256 256 23.008 21.984 23.008-21.984 256-256-46.016-46.016L544 834.016v-644l200.992 200.96 46.016-45.984-256-256z"},null,-1),Yr=[Wr];function Ur(a,t){return V(),G("svg",Hr,[...Yr])}const Xr={render:Ur},Vr={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},Gr=H("path",{fill:"currentColor",d:"M868 545.5 536.1 163a31.96 31.96 0 0 0-48.3 0L156 545.5a7.97 7.97 0 0 0 6 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2"},null,-1),Fr=[Gr];function qr(a,t){return V(),G("svg",Vr,[...Fr])}const Zr={render:qr},Kr={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",class:"icon",viewBox:"0 0 1024 1024"},Qr=Wi('<path d="M956.8 988.8H585.6c-16 0-25.6-9.6-25.6-28.8V576c0-16 9.6-28.8 25.6-28.8h371.2c16 0 25.6 9.6 25.6 28.8v384c0 16-9.6 28.8-25.6 28.8M608 937.6h326.4V598.4H608zm-121.6 44.8C262.4 982.4 144 848 144 595.2c0-19.2 9.6-28.8 25.6-28.8s25.6 12.8 25.6 28.8c0 220.8 96 326.4 288 326.4 16 0 25.6 12.8 25.6 28.8s-6.4 32-22.4 32"></path><path d="M262.4 694.4c-6.4 0-9.6-3.2-16-6.4L160 601.6c-9.6-9.6-9.6-22.4 0-28.8s22.4-9.6 28.8 0l86.4 86.4c9.6 9.6 9.6 22.4 0 28.8-3.2 3.2-6.4 6.4-12.8 6.4"></path><path d="M86.4 694.4c-6.4 0-9.6-3.2-16-6.4-9.6-9.6-9.6-22.4 0-28.8l86.4-86.4c9.6-9.6 22.4-9.6 28.8 0 9.6 9.6 9.6 22.4 0 28.8L99.2 688c-3.2 3.2-6.4 6.4-12.8 6.4m790.4-249.6c-16 0-28.8-12.8-28.8-32 0-224-99.2-336-300.8-336-16 0-28.8-12.8-28.8-32s9.6-32 28.8-32c233.6 0 355.2 137.6 355.2 396.8 0 22.4-9.6 35.2-25.6 35.2"></path><path d="M876.8 448c-6.4 0-9.6-3.2-16-6.4l-86.4-86.4c-9.6-9.6-9.6-22.4 0-28.8s22.4-9.6 28.8 0l86.4 86.4c9.6 9.6 9.6 22.4 0 28.8 0 3.2-6.4 6.4-12.8 6.4"></path><path d="M876.8 448c-6.4 0-9.6-3.2-16-6.4-9.6-9.6-9.6-22.4 0-28.8l86.4-86.4c9.6-9.6 22.4-9.6 28.8 0s9.6 22.4 0 28.8l-86.4 86.4c-3.2 3.2-6.4 6.4-12.8 6.4M288 524.8C156.8 524.8 48 416 48 278.4S156.8 35.2 288 35.2 528 144 528 281.6 419.2 524.8 288 524.8m-3.2-432c-99.2 0-179.2 83.2-179.2 185.6S185.6 464 284.8 464 464 380.8 464 278.4 384 92.8 284.8 92.8"></path>',5),Jr=[Qr];function ts(a,t){return V(),G("svg",Kr,[...Jr])}const es={render:ts},is={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},as=H("path",{fill:"currentColor",d:"M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0 0 48.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2"},null,-1),ns=[as];function rs(a,t){return V(),G("svg",is,[...ns])}const ss={render:rs},os={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},cs=H("path",{fill:"currentColor",d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 0 0 0 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8"},null,-1),hs=[cs];function ls(a,t){return V(),G("svg",os,[...hs])}const us={render:ls},ds={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},fs=H("path",{fill:"currentColor",d:"M505.7 661a8 8 0 0 0 12.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8"},null,-1),ps=[fs];function gs(a,t){return V(),G("svg",ds,[...ps])}const ms={render:gs},vs={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},bs=H("path",{fill:"currentColor",d:"M869 487.8 491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 0 0 0-48.4"},null,-1),ws=[bs];function ys(a,t){return V(),G("svg",vs,[...ws])}const xs={render:ys},Ms={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},Ds=H("path",{fill:"currentColor",d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32m-44 402H188V494h440z"},null,-1),Cs=H("path",{fill:"currentColor",d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8 11 40.7 14 82.7 8.9 124.8-.7 5.4-1.4 10.8-2.4 16.1h74.9c14.8-103.6-11.3-213-81-302.3"},null,-1),Es=[Ds,Cs];function _s(a,t){return V(),G("svg",Ms,[...Es])}const Ts={render:_s},Ss={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},As=H("path",{fill:"currentColor",d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8m284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11M696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430"},null,-1),Rs=[As];function ks(a,t){return V(),G("svg",Ss,[...Rs])}const Os={render:ks},Is={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},Ls=H("path",{fill:"currentColor",d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2-69.6 89.2-95.7 198.6-81.1 302.4h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8"},null,-1),Ns=H("path",{fill:"currentColor",d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32m-44 402H396V494h440z"},null,-1),Ps=[Ls,Ns];function Bs(a,t){return V(),G("svg",Is,[...Ps])}const $s={render:Bs},zs={xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 1024 1024"},js=H("path",{fill:"currentColor",d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8m284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11M696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430"},null,-1),Hs=[js];function Ws(a,t){return V(),G("svg",zs,[...Hs])}const Ys={render:Ws},Us={aspectRatio:1,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,autoCrop:!0,background:!0,highlight:!0,center:!0,responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,scalable:!0,modal:!0,guides:!0,movable:!0,rotatable:!0},Xs={src:{type:String,required:!0},alt:{type:String},circled:{type:Boolean,default:!1},isClose:{type:Boolean,default:!0},realTimePreview:{type:Boolean,default:!0},height:{type:[String,Number],default:"360px"},crossorigin:{type:String,default:void 0},imageStyle:{type:Object,default:()=>({})},options:{type:Object,default:()=>({})}},Vs=te({name:"ReCropper",props:Xs,setup(a,{attrs:t,emit:e}){const i=N(),r=N(),n=N(),s=N(a.circled),o=N(a.isClose),c=N(a.src),h=N(!1),l=N();let u=1,d=1;const m=Vi(M,80),p=Ot(()=>F({height:a.height,maxWidth:"100%"},a.imageStyle)),y=Ot(()=>[t.class,{"re-circled":s.value}]),v=Ot(()=>["p-[6px]","h-[30px]","w-[30px]","outline-none","rounded-[4px]","cursor-pointer","hover:bg-[rgba(0,0,0,0.06)]"]),D=Ot(()=>({height:`${a.height}`.replace(/px/,"")+"px"}));ti(w),Yi(()=>{var _;(_=n.value)==null||_.destroy(),h.value=!1,n.value=null,l.value="",u=1,d=1}),Ui(i,()=>f("reset"));function w(){return Et(this,null,function*(){const _=ee(r);_&&(n.value=new ki(_,F(rt(F({},Us),{ready:()=>{h.value=!0,M(),Xi(400).then(()=>e("readied",n.value))},crop(){m()},zoom(){m()},cropmove(){m()}}),a.options)))})}function M(){a.realTimePreview&&O()}function O(){if(!n.value)return;(s.value?I():n.value.getCroppedCanvas()).toBlob(S=>{if(!S)return;const R=new FileReader;R.readAsDataURL(S),R.onloadend=T=>{var L;!((L=T.target)!=null&&L.result)||!S||(l.value=T.target.result,e("cropper",{base64:T.target.result,blob:S,info:F({size:S.size},n.value.getData())}))},R.onerror=()=>{e("error")}})}function I(){const _=n.value.getCroppedCanvas(),S=document.createElement("canvas"),R=S.getContext("2d"),T=_.width,L=_.height;return S.width=T,S.height=L,R.imageSmoothingEnabled=!0,R.drawImage(_,0,0,T,L),R.globalCompositeOperation="destination-in",R.beginPath(),R.arc(T/2,L/2,Math.min(T,L)/2,0,2*Math.PI,!0),R.fill(),S}function f(_,S){var R,T,L,Y;_==="scaleX"&&(u=S=u===-1?1:-1),_==="scaleY"&&(d=S=d===-1?1:-1),S&&Ji(S)?(T=(R=n.value)==null?void 0:R[_])==null||T.call(R,...S):(Y=(L=n.value)==null?void 0:L[_])==null||Y.call(L,S)}function C(_){const S=new FileReader;return S.readAsDataURL(_),c.value="",S.onload=R=>{var T;c.value=(T=R.target)==null?void 0:T.result},S.onloadend=()=>{w()},!1}const A=te({directives:{tippy:qi,longpress:Zi},setup(){return()=>k("div",{class:"flex flex-wrap w-[60px] justify-between"},[k(Ki,{accept:"image/*","show-file-list":!1,"before-upload":C},{default:()=>[X(k(Nr,{class:v.value},null),[[z("tippy"),{content:"上传",placement:"left-start"}]])]}),X(k(ms,{class:v.value,onClick:()=>Qi(l.value,"cropping.png")},null),[[z("tippy"),{content:"下载",placement:"right-start"}]]),X(k(es,{class:v.value,onClick:()=>{s.value=!s.value,M()}},null),[[z("tippy"),{content:"圆形、矩形裁剪",placement:"left-start"}]]),X(k(Rr,{class:v.value,onClick:()=>f("reset")},null),[[z("tippy"),{content:"重置",placement:"right-start"}]]),X(k(Zr,{class:v.value},null),[[z("tippy"),{content:"上移（可长按）",placement:"left-start"}],[z("longpress"),()=>f("move",[0,-10]),"0:100"]]),X(k(ss,{class:v.value},null),[[z("tippy"),{content:"下移（可长按）",placement:"right-start"}],[z("longpress"),()=>f("move",[0,10]),"0:100"]]),X(k(us,{class:v.value},null),[[z("tippy"),{content:"左移（可长按）",placement:"left-start"}],[z("longpress"),()=>f("move",[-10,0]),"0:100"]]),X(k(xs,{class:v.value},null),[[z("tippy"),{content:"右移（可长按）",placement:"right-start"}],[z("longpress"),()=>f("move",[10,0]),"0:100"]]),X(k(jr,{class:v.value,onClick:()=>f("scaleX",-1)},null),[[z("tippy"),{content:"水平翻转",placement:"left-start"}]]),X(k(Xr,{class:v.value,onClick:()=>f("scaleY",-1)},null),[[z("tippy"),{content:"垂直翻转",placement:"right-start"}]]),X(k(Ts,{class:v.value,onClick:()=>f("rotate",-45)},null),[[z("tippy"),{content:"逆时针旋转",placement:"left-start"}]]),X(k($s,{class:v.value,onClick:()=>f("rotate",45)},null),[[z("tippy"),{content:"顺时针旋转",placement:"right-start"}]]),X(k(Os,{class:v.value},null),[[z("tippy"),{content:"放大（可长按）",placement:"left-start"}],[z("longpress"),()=>f("zoom",.1),"0:100"]]),X(k(Ys,{class:v.value},null),[[z("tippy"),{content:"缩小（可长按）",placement:"right-start"}],[z("longpress"),()=>f("zoom",-.1),"0:100"]])])}});function $(_){_.preventDefault();const{show:S,setProps:R,destroy:T,state:L}=Gi(i,{content:A,arrow:!1,theme:"light",trigger:"manual",interactive:!0,appendTo:"parent",placement:"bottom-end"});if(R({getReferenceClientRect:()=>({width:0,height:0,top:_.clientY,bottom:_.clientY,left:_.clientX,right:_.clientX})}),S(),o.value){if(!L.value.isShown&&!L.value.isVisible)return;Fi(i,"click",T)}}return{inSrc:c,props:a,imgElRef:r,tippyElRef:i,getClass:y,getWrapperStyle:D,getImageStyle:p,isReady:h,croppered:O,onContextmenu:$}},render(){const{inSrc:a,isReady:t,getClass:e,getImageStyle:i,onContextmenu:r,getWrapperStyle:n}=this,{alt:s,crossorigin:o}=this.props;return a?k("div",{ref:"tippyElRef",class:e,style:n,onContextmenu:c=>r(c)},[X(k("img",{ref:"imgElRef",style:i,src:a,alt:s,crossorigin:o},null),[[ei,t]])]):null}}),Gs=ii(Vs),Fs={"element-loading-background":"transparent"},qs={class:"w-[18vw]"},Zs={class:"mt-1 text-center"},Ks={class:"flex flex-wrap justify-center items-center text-center"},Qs={key:1,class:"mt-1"},Js=te({name:"ReCropperPreview",__name:"index",props:{imgSrc:String},emits:["cropper"],setup(a,{expose:t,emit:e}){const i=e,r=N(),n=N(),s=N(),o=N(!1),c=N("");function h({base64:u,blob:d,info:m}){r.value=m,c.value=u,i("cropper",{base64:u,blob:d,info:m})}function l(){n.value.hide()}return t({hidePopover:l}),(u,d)=>{const m=ie("el-image"),p=ie("el-popover"),y=z("loading");return X((V(),G("div",Fs,[k(p,{ref_key:"popoverRef",ref:n,visible:o.value,placement:"right",width:"18vw"},{reference:Te(()=>[H("div",qs,[k(ee(Gs),{ref_key:"refCropper",ref:s,src:a.imgSrc,circled:"",onCropper:h,onReadied:d[0]||(d[0]=v=>o.value=!0)},null,8,["src"]),X(H("p",Zs," 温馨提示：右键上方裁剪区可开启功能菜单 ",512),[[ei,o.value]])])]),default:Te(()=>[H("div",Ks,[c.value?(V(),ta(m,{key:0,src:c.value,"preview-src-list":Array.of(c.value),fit:"cover"},null,8,["src","preview-src-list"])):Se("",!0),r.value?(V(),G("div",Qs,[H("p",null," 图像大小："+Ht(parseInt(r.value.width))+" × "+Ht(parseInt(r.value.height))+"像素 ",1),H("p",null," 文件大小："+Ht(ee(ea)(r.value.size))+"（"+Ht(r.value.size)+" 字节） ",1)])):Se("",!0)])]),_:1},8,["visible"])])),[[y,!o.value]])}}}),to=ii(Js);function co(a,t){const e=Ft({deptId:"",username:"",phone:"",status:""}),i=N(),r=N(),n=N([]),s=N(!0),o=N(),c=N({}),{switchStyle:h}=ga(),l=N(),u=N([]),d=N(!0),m=N(0),p=Ft({total:0,pageSize:10,currentPage:1,background:!0}),y=[{label:"勾选列",type:"selection",fixed:"left",reserveSelection:!0},{label:"用户编号",prop:"id",width:90},{label:"用户名称",prop:"username",minWidth:130},{label:"用户昵称",prop:"nickname",minWidth:130},{label:"部门",prop:"dept.name",minWidth:90},{label:"手机号码",prop:"phone",minWidth:90,formatter:({phone:g})=>na(g,{start:3,end:6})},{label:"状态",prop:"status",minWidth:90,cellRenderer:g=>{var b;return k(ie("el-switch"),{size:g.props.size==="small"?"small":"default",loading:(b=c.value[g.index])==null?void 0:b.loading,modelValue:g.row.status,"onUpdate:modelValue":j=>g.row.status=j,"active-value":1,"inactive-value":0,"active-text":"已启用","inactive-text":"已停用","inline-prompt":!0,style:h.value,onChange:()=>I(g)},null)}},{label:"创建时间",minWidth:90,prop:"createTime",formatter:({createTime:g})=>ra(g).format("YYYY-MM-DD HH:mm:ss")},{label:"操作",fixed:"right",width:180,slot:"operation"}],v=Ot(()=>["!h-[20px]","reset-margin","!text-gray-500","dark:!text-white","dark:hover:!text-primary"]),D=Ft({newPwd:""}),w=[{color:"#e74242",text:"非常弱"},{color:"#EFBD47",text:"弱"},{color:"#ffa500",text:"一般"},{color:"#1bbf1b",text:"强"},{color:"#008000",text:"非常强"}],M=N(),O=N([]);function I({row:g,index:b}){sa.confirm(`确认要<strong>${g.status===0?"停用":"启用"}</strong><strong style='color:var(--el-color-primary)'>${g.username}</strong>用户吗?`,"系统提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0,draggable:!0}).then(()=>{c.value[b]=Object.assign({},c.value[b],{loading:!0}),setTimeout(()=>{c.value[b]=Object.assign({},c.value[b],{loading:!1}),Tt("已成功修改用户状态",{type:"success"})},300)}).catch(()=>{g.status===0?g.status=1:g.status=0})}function f(g){}function C(g){Tt(`您删除了用户编号为${g.id}的这条数据`,{type:"success"}),T()}function A(g){}function $(g){}function _(g){m.value=g.length,a.value.setAdaptive()}function S(){m.value=0,a.value.getTableRef().clearSelection()}function R(){const g=a.value.getTableRef().getSelectionRows();Tt(`已删除用户编号为 ${oa(g,"id")} 的数据`,{type:"success"}),a.value.getTableRef().clearSelection(),T()}function T(){return Et(this,null,function*(){s.value=!0;const{data:g}=yield ya(ca(e));n.value=g.list,p.total=g.total,p.pageSize=g.pageSize,p.currentPage=g.currentPage,setTimeout(()=>{s.value=!1},500)})}const L=g=>{g&&(g.resetFields(),e.deptId="",t.value.onTreeReset(),T())};function Y({id:g,selected:b}){e.deptId=b?g:"",T()}function et(g){if(!g||!g.length)return;const b=[];for(let j=0;j<g.length;j++)g[j].disabled=g[j].status===0,et(g[j].children),b.push(g[j]);return b}function ft(g="新增",b){var j,Q,Dt,Ct,zt,jt,xe,Me,De;Wt({title:`${g}用户`,props:{formInline:{title:g,higherDeptOptions:et(l.value),parentId:(j=b==null?void 0:b.dept.id)!=null?j:0,nickname:(Q=b==null?void 0:b.nickname)!=null?Q:"",username:(Dt=b==null?void 0:b.username)!=null?Dt:"",password:(Ct=b==null?void 0:b.password)!=null?Ct:"",phone:(zt=b==null?void 0:b.phone)!=null?zt:"",email:(jt=b==null?void 0:b.email)!=null?jt:"",sex:(xe=b==null?void 0:b.sex)!=null?xe:"",status:(Me=b==null?void 0:b.status)!=null?Me:1,remark:(De=b==null?void 0:b.remark)!=null?De:""}},width:"46%",draggable:!0,fullscreen:_t(),fullscreenIcon:!0,closeOnClickModal:!1,contentRenderer:()=>qt(ma,{ref:i}),beforeSure:(Oi,{options:Ii})=>{const Li=i.value.getRef(),Ni=Ii.props.formInline;function Ce(){Tt(`您${g}了用户名称为${Ni.username}的这条数据`,{type:"success"}),Oi(),T()}Li.validate(Pi=>{Pi&&Ce()})}})}const pt=N();function Mt(g){Wt({title:"裁剪、上传头像",width:"40%",closeOnClickModal:!1,fullscreen:_t(),contentRenderer:()=>qt(to,{ref:pt,imgSrc:g.avatar||va,onCropper:b=>o.value=b}),beforeSure:b=>{b(),T()},closeCallBack:()=>pt.value.hidePopover()})}ia(D,({newPwd:g})=>M.value=aa(g)?-1:Pn(g).score);function Bt(g){Wt({title:`重置 ${g.username} 用户的密码`,width:"30%",draggable:!0,closeOnClickModal:!1,fullscreen:_t(),contentRenderer:()=>k(fa,null,[k(ha,{ref:r,model:D},{default:()=>[k(la,{prop:"newPwd",rules:[{required:!0,message:"请输入新密码",trigger:"blur"}]},{default:()=>[k(ua,{clearable:!0,"show-password":!0,type:"password",modelValue:D.newPwd,"onUpdate:modelValue":b=>D.newPwd=b,placeholder:"请输入新密码"},null)]})]}),k("div",{class:"mt-4 flex"},[w.map(({color:b,text:j},Q)=>k("div",{class:"w-[19vw]",style:{marginLeft:Q!==0?"4px":0}},[k(da,{striped:!0,"striped-flow":!0,duration:M.value===Q?6:0,percentage:M.value>=Q?100:0,color:b,"stroke-width":10,"show-text":!1},null),k("p",{class:"text-center",style:{color:M.value===Q?b:""}},[j])]))])]),closeCallBack:()=>D.newPwd="",beforeSure:b=>{r.value.validate(j=>{j&&(Tt(`已成功重置 ${g.username} 用户的密码`,{type:"success"}),b(),T())})}})}function $t(g){return Et(this,null,function*(){var j,Q,Dt,Ct;const b=(j=(yield xa({userId:g.id})).data)!=null?j:[];Wt({title:`分配 ${g.username} 用户的角色`,props:{formInline:{username:(Q=g==null?void 0:g.username)!=null?Q:"",nickname:(Dt=g==null?void 0:g.nickname)!=null?Dt:"",roleOptions:(Ct=O.value)!=null?Ct:[],ids:b}},width:"400px",draggable:!0,fullscreen:_t(),fullscreenIcon:!0,closeOnClickModal:!1,contentRenderer:()=>qt(pa),beforeSure:(zt,{options:jt})=>{jt.props.formInline,zt()}})})}return ti(()=>Et(this,null,function*(){d.value=!0,T();const{data:g}=yield ba();l.value=Ae(g),u.value=Ae(g),d.value=!1,O.value=(yield wa()).data})),{form:e,loading:s,columns:y,dataList:n,treeData:u,treeLoading:d,selectedNum:m,pagination:p,buttonClass:v,deviceDetection:_t,onSearch:T,resetForm:L,onbatchDel:R,openDialog:ft,onTreeSelect:Y,handleUpdate:f,handleDelete:C,handleUpload:Mt,handleReset:Bt,handleRole:$t,handleSizeChange:A,onSelectionCancel:S,handleCurrentChange:$,handleSelectionChange:_}}export{co as useUser};

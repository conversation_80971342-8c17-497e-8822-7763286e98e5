<script setup lang="ts">
import moment from "moment";
import { ref, onMounted } from "vue";
import { genFileId } from "element-plus";
import { message } from "@/utils/message";
import { useColumns } from "./utils/column";
import { baseUrlApi } from "@/api/utils";

const { openUpload } = useColumns();
const filelist = ref([]);

const uploadError = () => {
  console.log("上传失败");
};

const beforeUpload = (file: any) => {
  const suffix = file.name
    .substring(file.name.lastIndexOf(".") + 1)
    .toLowerCase();
  if (suffix != "xlsx") {
    message("上传文件格式不正确", { type: "error" });
    return false;
  }
  if (file.size / 1024 / 1024 > 2) {
    message("文件大小不允许超出2MB，请处理后再上传", { type: "error" });
    return false;
  }
};

const showurl = () => {
  console.log(
    import.meta.env.VITE_APP_BASE_URL + baseUrlApi("common/getmachineids")
  );
};
</script>

<template>
  <div>
    <el-form :inline="true" class="search-form bg-bg_color">
      <el-form-item>
        <el-upload
          action="http://***********"
          :file-list="filelist"
          :show-file-list="false"
          :auto-upload="true"
          :on-error="uploadError"
        >
          <el-button type="primary">上传EXCEL计划</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="showurl">显示上传url</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.search-form {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .extra_info {
    font-size: 14px;
  }
  .el-form-item {
    display: flex;
    margin: 8px 10px;
  }
  :deep(.el-form-item__content) {
    line-height: 16px;
    .upload-demo {
      display: flex;

      :deep(.el-upload-list__item) {
        margin: 0;
        display: flex;
      }
    }
  }
}
:deep(.el-upload-list) {
  margin: 0;
}
</style>

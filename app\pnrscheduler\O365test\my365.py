from O365 import Account, FileSystemTokenBackend
import os
from setting import cfg
import datetime


class MY365:
    def __init__(self, email):
        self.accounts = []
        self.warningtimes = 0
        self.smscount = 0
        user = cfg.o365[email]
        client_id = user['client_id']
        client_secret = user['client_secret']
        tenant_id = user['tenant_id']
        fname = '/Users/<USER>/Documents/Develop/Backend/flask307/tokens/o365_token_{}.txt'.format(email)
        token_backend = FileSystemTokenBackend(
            token_path='.', token_filename=fname)
        credentials = (client_id, client_secret)
        account = Account(credentials, tenant_id=tenant_id, token_backend=token_backend)
        if not account.is_authenticated:
            # sendSMS('连接', f'token失效 {user["email"]}')
            account.authenticate(scopes=[
                'basic',
                'message_all',
                'User.Read',
                'Sites.Manage.All',
                'Files.ReadWrite.All'
            ])
        self.accounts.append(account)
        if not os.path.exists('mails'):
            os.makedirs('mails')

    def sendMail(self):
        try:
            m = self.accounts[0].new_message()
            m.to.add('<EMAIL>')
            m.subject = 'Testing!'
            m.body = "George Best quote: I've stopped drinking, but only while I'm asleep."
            m.send()
        except Exception as e:
            print(e)

    def receiveMail(self):
        account = self.accounts[0]
        try:
            end_date = datetime.datetime.now()
            start_date = end_date - datetime.timedelta(days=1)
            mailbox = account.mailbox()
            inbox = mailbox.inbox_folder()
            query = inbox.new_query('receivedDateTime').greater(
                start_date).less_equal(end_date)
            messages = inbox.get_messages(query=query, download_attachments=True)
            for message in messages:
                print(message.subject)
        except Exception as e:
            print('POGR收件箱失败'+str(e))
            return


if __name__ == '__main__':
    my365 = MY365('<EMAIL>')
    my365.receiveMail()

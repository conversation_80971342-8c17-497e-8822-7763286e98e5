from flask import Blueprint, request
import traceback
from app.public.models_public import Publicuser
from extensions import db
from app.public.functions import responseError, responsePost, responseDelete, responsePut
api = Blueprint('public', __name__)


@api.route('/userid/<int:uid>', methods=['DELETE'])
def deleteUser(uid):
    user = db.session.query(Publicuser).filter(Publicuser.Id == uid).scalar()
    db.session.delete(user)
    db.session.commit()
    return responseDelete("删除用户成功", {'data': 'success'})


@api.route('/resetPassword', methods=['POST'])
def resetPassword():
    res = request.json
    if res is None:
        res = request.form
    if not len(res):
        return responseError('请输入用户名')
    username = res.get('username')
    if not username or '@' not in username:
        return responseError('请输入完整邮箱用户名')
    if username.isdigit():
        user = db.session.query(Publicuser).filter(
            Publicuser.eid == username).scalar()
    else:
        user = db.session.query(Publicuser).filter(
            Publicuser.email == username).scalar()
    user.password = '123'
    db.session.commit()
    return responsePut("重置密码成功", {'newpass': '123'})


@api.route('/changePassword', methods=['POST'])
def changePassword():
    res = request.json
    if res is None:
        res = request.form
    if not len(res):
        return responseError('请输入用户名密码')
    username = res.get('username')
    oldpass = res.get('oldpass')
    newpass = res.get('newpass')
    if not (username and oldpass and newpass):
        return responseError('请输入用户名密码')
    if username.isdigit():
        user = db.session.query(Publicuser).filter(
            Publicuser.eid == username).scalar()
    else:
        if '@' not in username:
            return responseError('请输入完整邮箱用户名')
        user = db.session.query(Publicuser).filter(
            Publicuser.email == username).scalar()
    if user.check_password_hash(oldpass):
        user.password = newpass
        db.session.commit()
        return responsePut("修改密码成功，下次登陆请使用新密码！", {'newpass': newpass})
    return responseError('原始密码错误，请重新输入！')


@api.route('/addUser', methods=['POST'])
def addUser():
    res = request.json
    if res is None:
        res = request.form
    if not len(res):
        return responseError('请输入用户名')
    email = res.get('email').lower()
    eid = res.get('eid')
    if not (email and eid):
        return responseError('请输入邮箱和EID')
    if '@' not in email:
        return responseError('请输入完整邮箱用户名')
    user = db.session.query(Publicuser).filter(Publicuser.eid == eid).scalar()
    if not user:
        try:
            user2 = Publicuser(email=email, eid=eid, password='123')
            db.session.add(user2)
            db.session.commit()
        except Exception:
            return responseError('建立新用户失败,可能已经存在邮箱并和输入的EID不符合！')
        return responsePost('成功建立新用户', {'eid': eid})
    else:
        checkemail = user.email
        if email == checkemail:
            return responsePost('在公用账户中已经存在该用户', {'eid': eid})
        else:
            return responseError('建立新用户失败,用户EID已存在，但是邮箱和已存在的邮箱不符合，请进一步确认')


@api.route("/login", methods=["POST"])
def login():
    res_dir = request.json
    if res_dir is None:
        res_dir = request.form
    if not len(res_dir):
        return responseError('请输入用户名密码')
    # 获取前端传过来的参数
    email = res_dir.get("username")
    password = res_dir.get("password")
    print('ddddd', email, password)
    if not (email and password):
        return responseError('请输入用户名密码')
    else:
        if "@" not in email and "." in email:
            email = email+"@pentair.com"
    try:
        if email.isdigit():
            publicuser = db.session.query(Publicuser).filter(
                Publicuser.eid == email).first()
        else:
            publicuser = db.session.query(Publicuser).filter(
                Publicuser.email == email).first()
        if publicuser:
            eid = publicuser.eid
            email = publicuser.email
    except Exception:
        traceback.print_exc()
        return responseError('查询错误，请联系管理员！')
    if publicuser is None:
        return responseError('用户名不存在或已被禁用！')
    elif not publicuser.check_password_hash(password):
        return responseError('密码错误！')

    data = {'eid': eid, 'email': email}
    print(data)
    return responsePost('登录成功', data)

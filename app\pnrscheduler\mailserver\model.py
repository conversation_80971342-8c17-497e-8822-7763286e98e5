import pymysql
import pymssql
from utils.config import cfg
import traceback


class MyModel:
    def __init__(self, d, name):
        # self.mailserver=O365()
        self.db = pymysql.connect(host=d['host'], port=d['port'], user=d['user'], password=d['password'],
                                  database=name,
                                  charset=d['charset'])

    def upSql(self, sql):
        try:
            # 使用 cursor() 方法创建一个游标对象 cursor
            cursor = self.db.cursor()
            cursor.execute(sql)
            result = cursor.fetchall()
        except Exception:
            # 发生错误时回滚
            self.db.rollback()
            return ''
        else:
            self.db.commit()  # 事务提交
            cursor.close()
            self.db.close()
            return result

    def insSql(self, sql):
        try:
            # 使用 cursor() 方法创建一个游标对象 cursor
            cursor = self.db.cursor()
            cursor.execute(sql)
        except Exception:
            # 发生错误时回滚
            self.db.rollback()
            return 0
        else:
            self.db.commit()  # 事务提交
            # cursor.close()
            # self.db.close()
            return 1


class MsModel:
    def __init__(self, d):
        # self.mailserver=O365()
        self.db = pymssql.connect(d['host'], d['user'], d['password'], d['database'])

    def upSql(self, sql):
        try:
            # 使用 cursor() 方法创建一个游标对象 cursor
            cursor = self.db.cursor()
            cursor.execute(sql)
            result = cursor.fetchall()
        except Exception:
            traceback.print_exc()
            self.db.rollback()
            return ''
        else:
            self.db.commit()  # 事务提交
            cursor.close()
            self.db.close()
            return result


if __name__ == '__main__':
    cdmodel = MsModel(cfg.vocdb)
    sql = 'select * from dvoc where id=233'
    result = cdmodel.upSql(sql)
    print(result)

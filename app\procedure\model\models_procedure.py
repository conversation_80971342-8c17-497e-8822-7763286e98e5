from sqlalchemy import UniqueConstraint
from werkzeug.security import generate_password_hash, check_password_hash
from extensions import db

# 修改时间 2023.8.17


# from extensions import db
# 修改时间 2023.8.17


class User(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_user"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    email = db.Column(db.String(64))
    plant = db.Column(db.String(10))
    role = db.Column(db.String(50))
    name = db.Column(db.String(50))
    picurl = db.Column(db.String(100))
    eid = db.Column(db.String(8))
    status = db.Column(db.SmallInteger)
    createtime = db.Column(db.DateTime())
    permissions = db.Column(db.String(200))
    password_hash = db.Column(db.String(255))

    @property
    def password(self):
        raise AttributeError("密码不允许读取")

    # 转换密码为hash存入数据库
    @password.setter
    def password(self, password):
        self.password_hash = generate_password_hash(password)

    # 检查密码
    def check_password_hash(self, password):
        return check_password_hash(self.password_hash, password)


class Payhour(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_payhour"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    eid = db.Column(db.String(8))
    workdate = db.Column(db.Date())
    station = db.Column(db.String(20))
    starttime = db.Column(db.DateTime())
    endtime = db.Column(db.DateTime())


class Mo(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_mo"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    mono = db.Column(db.String(20))
    moqty = db.Column(db.Integer)
    sku = db.Column(db.String(50))
    desc = db.Column(db.String(100))
    impellerd = db.Column(db.String(10))
    duetime = db.Column(db.DateTime())
    requiretime = db.Column(db.DateTime())
    createtime = db.Column(db.DateTime())
    finishtime = db.Column(db.DateTime())
    plant = db.Column(db.String(10))
    status = db.Column(db.String(10))
    priority = db.Column(db.SmallInteger)
    pono = db.Column(db.String(20))
    project = db.Column(db.String(100))
    mcomments = db.Column(db.String(500))
    wduetime = db.Column(db.DateTime())
    wfinishtime = db.Column(db.DateTime())
    wcomments = db.Column(db.String(500))
    wlackmaterials = db.Column(db.String(100))
    packfinishtime = db.Column(db.DateTime())
    pcomments = db.Column(db.String(500))
    tcomments = db.Column(db.String(500))
    printed = db.Column(db.SmallInteger)
    __table_args__ = (
        UniqueConstraint('mono', name='uq_mono'),
    )


class Moitem(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_moitem"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    mono = db.Column(db.String(20))
    sku = db.Column(db.String(50))
    sn = db.Column(db.String(50))
    itemfinishtime = db.Column(db.DateTime())
    status = db.Column(db.String(10))
    __table_args__ = (
        UniqueConstraint('sn', name='uq_sn'),
    )


class Moitempath(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_moitempath"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    sn = db.Column(db.String(50))
    station = db.Column(db.String(20))
    starttime = db.Column(db.DateTime())
    finishtime = db.Column(db.DateTime())
    status = db.Column(db.String(10))
    owner = db.Column(db.String(8))


class Probleminfo(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_probleminfo"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    sn = db.Column(db.String(50))
    station = db.Column(db.String(20))
    sqdctype = db.Column(db.String(20))
    problemtype = db.Column(db.String(20))
    trigger = db.Column(db.Integer)


class Issuelog(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_issuelog"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    sn = db.Column(db.String(50))
    station = db.Column(db.String(20))
    sqdctype = db.Column(db.String(20))
    problemtype = db.Column(db.String(20))
    problemdesc = db.Column(db.String(255))
    action = db.Column(db.String(255))
    qty = db.Column(db.Integer)
    recorder = db.Column(db.String(8))
    recordtime = db.Column(db.DateTime())
    owner = db.Column(db.String(8))
    status = db.Column(db.String(10))
    closetime = db.Column(db.DateTime())


class Ctqresult(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_ctqresult"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    sn = db.Column(db.String(50))
    pid = db.Column(db.BigInteger)
    result = db.Column(db.String(100))
    status = db.Column(db.String(10))


class Ctqinfo(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_ctqinfo"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    sku = db.Column(db.String(50))
    pid = db.Column(db.BigInteger)
    lsl = db.Column(db.Float(9, 3))
    usl = db.Column(db.Float(9, 3))
    stdurl = db.Column(db.String(100))


class Station(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_station"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    station = db.Column(db.String(20))
    estation = db.Column(db.String(50))
    __table_args__ = (
        UniqueConstraint('station', name='uq_station'),
    )
    area = db.Column(db.String(10))
    plant = db.Column(db.String(10))


class Skucascade(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_skucascade"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    sku = db.Column(db.String(50))
    presku = db.Column(db.String(50))
    preqty = db.Column(db.SmallInteger)
    group = db.Column(db.String(10))
    __table_args__ = (
        UniqueConstraint('sku', name='uq_skucascade'),
    )


class Skuinfo(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_skuinfo"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    plant = db.Column(db.String(10))
    sku = db.Column(db.String(50))
    name = db.Column(db.String(50))
    picurl = db.Column(db.String(100))
    procedure = db.Column(db.String(20))
    routing = db.Column(db.Float(9, 6))
    __table_args__ = (
        UniqueConstraint('sku', name='uq_sku'),
    )


class Ctqprocedure(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_ctqprocedure"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    stdprocedurename = db.Column(db.String(20))
    station = db.Column(db.String(20))
    seq = db.Column(db.SmallInteger)
    label = db.Column(db.String(20))
    elabel = db.Column(db.String(50))
    validtype = db.Column(db.String(10))
    validdata = db.Column(db.String(100))
    __table_args__ = (
        UniqueConstraint('station', 'stdprocedurename', 'label', name='ctqname'),
    )


class Standardprocedure(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_standardprocedure"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    stdprocedurename = db.Column(db.String(20))
    station = db.Column(db.String(20))
    seq = db.Column(db.SmallInteger)
    __table_args__ = (
        UniqueConstraint('station', name='uq_procedurestation'),
    )


# class Po(db.Model):
#     __bind_key__ = "procedure"
#     __tablename__ = "procedure_po"
#     Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
#     pono = db.Column(db.String(20))
#     customerno = db.Column(db.String(20))
#     customername = db.Column(db.String(50))
#     postatus = db.Column(db.String(10))
#     createtime = db.Column(db.DateTime())
#     duetime = db.Column(db.DateTime())
#     closetime = db.Column(db.DateTime())
#     comments = db.Column(db.String(500))
#     plant = db.Column(db.String(10))
#     __table_args__ = (
#         UniqueConstraint('pono', name='uq_pono'),
#     )


# class Poitem(db.Model):
#     __bind_key__ = "procedure"
#     __tablename__ = "procedure_poitem"
#     Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
#     pono = db.Column(db.String(20))
#     sku = db.Column(db.String(50))
#     qty = db.Column(db.Integer)


class Document(db.Model):
    __bind_key__ = "procedure"
    __tablename__ = "procedure_document"
    Id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    docno = db.Column(db.String(20))
    docname = db.Column(db.String(100))
    revision = db.Column(db.String(2))
    releasedate = db.Column(db.Date())
    docurl = db.Column(db.String(100))
    owner = db.Column(db.String(8))
    status = db.Column(db.String(10))

from flask import Blueprint, request, send_from_directory
from app.dm.model.models_dm import Printbom,Printlabel,Printtemplate,Scaninfo,Skuinfo,Printparam,Printbomtemplate,Printgraph
from extensions import db,redis_client
from sqlalchemy import func, or_, desc, and_
from openpyxl import load_workbook
from PIL import Image
import re
import io
import json
import os
import datetime
import traceback
import hashlib
import requests
from config import config, env
from app.public.functions import responseError, responsePost, responseGet, responsePut
from app.dm.functions import login_required, getLinelist, getLinegroup, getServer
from app.dm.functions.zebra import create_text_image,toZpl
from app.hro.model.models_hro import Material, WorkflowSetting, Workflow
api = Blueprint('dm/printAPI', __name__)

def checkDup():
    req=['12','22','123']
    dupArr=[]
    mgs=redis_client.mget(req)
    for i in range(len(mgs)):
        if mgs[i]:
            dupArr.append(req[i])
    print(dupArr)

@api.route('/adjustTemplate', methods=['POST'])
@login_required
def adjustTemplate():
    res=request.json
    tp=res.get('tp')
    try:
        db.session.query(Printbomtemplate).filter(Printbomtemplate.skuid==tp[0]['skuid']).delete()
        for t in tp:
            skuid=t['skuid']
            templateid=t['templateid']
            duplicate=t['qty']
            newParam=Printbomtemplate(skuid=skuid,templateid=templateid,duplicate=duplicate)
            db.session.add(newParam)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('更新失败')
    text = '更新成功'
    return responsePost(text)

@ api.route('/changeSingleparam', methods=['POST'])
@ login_required
def changeSingleparam():
    res = request.json
    sku= res.get('sku')
    revision=res.get('revision')
    name = res.get('name')
    value = res.get('value')
    try:
        param=db.session.query(Printparam.Id).join(Printbom,Printparam.skuid==Printbom.Id).filter(
            Printbom.sku==sku).filter(Printbom.revision==revision).filter(Printparam.name==name).first()
        if param:
            db.session.query(Printparam).filter(Printparam.Id==param.Id).update({Printparam.value:value})
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('更新失败')
    text = '更新成功'
    return responsePost(text)


@ api.route('/activateBom', methods=['POST'])
@ login_required
def activateBom():
    res = request.json
    skuid = res.get('skuid')
    try:
        db.session.query(Printbom).filter(Printbom.Id == skuid).update({Printbom.isactive: 1})
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('激活失败')
    text = '激活成功'
    return responsePost(text, {'skuid': skuid})

def getZplCode(skuid,templateid,variables,dpi=300):
    pattern = r"\{(\w+)\}"
    template = db.session.query(Printtemplate.rotated,Printtemplate.Id,Printtemplate.code).outerjoin(Printbomtemplate,Printbomtemplate.templateid==Printtemplate.Id).filter(
        Printbomtemplate.skuid==skuid).filter(Printtemplate.Id==templateid).first()
    templateid=template.Id
    params=db.session.query(Printparam.name,Printparam.value).filter(Printparam.skuid==skuid).all()
    paramsDic={}
    for p in params:
        paramsDic[p.name]=p.value
    #合并variables和paramsDic
    for key, value in variables.items():
        paramsDic[key]=value
    code=template.code
    codeArr=code.split('{graph}')
    graphs=db.session.query(Printgraph).filter(Printgraph.templateid==templateid).order_by(Printgraph.Id).all()
    pos=1
    k=0
    for g in graphs:
        fixedH=int(g.fixedH*dpi/25.4)
        if g.graph:
            if paramsDic.get('T'+str(templateid)+'-图像-'+str(k+1))=='N':
                codeArr.insert(pos,'')
                pos+=2
            if g.isgraph:
                image = Image.open(getServer()['labeltemplatesPath']+g.graph)
                graph_zpl,width=toZpl(image)
                graph_zpl=graph_zpl.replace('^FO0,0','')
                codeArr.insert(pos,graph_zpl)
                pos+=2
            else:
                tfont=int(round(g.font*dpi/72,0))
                graph=g.graph
                for key, value in paramsDic.items():
                    graph = graph.replace("{" + key + "}", value)
                graph = re.sub(pattern, lambda match: paramsDic.get(match.group(1), "<NA>"),graph)
                tgraph=graph.replace('\r\n','\n')
                if tgraph!='<NA>':
                    graph_zpl,width=create_text_image(tgraph,font_size=tfont,output_path=getServer()['labeltemplatesPath']+'print/graph'+str(g.Id)+'.png',rotated=template.rotated,fixedH=fixedH)
                    graph_zpl=graph_zpl.replace('^FO0,0','')
                    codeArr.insert(pos,graph_zpl)
                    pos+=2
        k+=1

    #把variables的字段添加到paramsDic
    for key, value in variables.items():
        paramsDic[key]=value
    zpl='\n'.join(codeArr)
    for key, value in paramsDic.items():
        zpl = zpl.replace("{" + key + "}", value)
    zpl = re.sub(pattern, lambda match: paramsDic.get(match.group(1), ""),zpl)
    # 把zpl写入a.txt
    # with open('a.txt', 'w') as f:
    #     f.write(zpl)
    return zpl,template.rotated


@ api.route("/getCode", methods=["get"])
@ login_required
def getCode():
    res=request.args
    sku=res.get('sku')
    skuid=res.get('skuid')
    sn= res.get('sn')
    revision=res.get('revision')
    printx=res.get('printx')
    printy=res.get('printy')
    printspeed=res.get('printspeed')
    printdensity=res.get('printdensity')
    start=res.get('start')
    templateid=res.get('templateid')
    n=res.get('n')
    snarr=[]
    variables={
        'printx': printx,
        'sku':sku,
        'printdate':'20'+sn[5:7]+'年'+sn[7:9]+'月'+sn[9:11]+'日',
        'printy': printy,
        'printspeed':printspeed,
        'printdensity':printdensity,
        'sn': sn,
        'start': start,
        'n': n
    }
    dpi=300
    zpl,rotated=getZplCode(skuid,templateid,variables,dpi)
    try:
        pre=sn[:len(sn)-4]
        nb=sn[len(sn)-4:]
        for i in range(int(n)):
            sns=pre+str(int(nb)-i).zfill(4)
            snarr.append(sns)
        if len(snarr)>0:
            print(snarr)
            for ssn in snarr:
                dic={
                    'sku':sku,
                    'revision':revision,
                    'printtime':datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'sn':ssn
                }
                redis_client.hmset(ssn,dic)
        return responseGet('成功', {'zpl':zpl})
    except Exception:
        traceback.print_exc()
        return responseError('获取失败,请联系管理员')


@api.route('/getTemplatepreview', methods=['GET'])
@login_required
def getTemplatepreview():
    res = request.args
    timestamp=datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    templateids = res.get('templateids')
    skuid = res.get('skuid')
    tdes=templateids.split('|')
    sku=res.get('sku')
    template=db.session.query(Printtemplate.Id,Printlabel.width,Printlabel.height).join(
        Printlabel,Printtemplate.pn==Printlabel.pn).filter(Printtemplate.des==tdes[0]).filter(Printtemplate.revision==tdes[1]).first()
    skuinfo=db.session.query(Skuinfo).filter(Skuinfo.sku==sku).first()
    snverify=skuinfo.snverify if skuinfo else '00000'
    sn=snverify+datetime.datetime.now().strftime('%y%m%d')+'0001'
    variables={
        'printx': '0',
        'sku':sku,
        'printdate':'20'+sn[5:7]+'年'+sn[7:9]+'月'+sn[9:11]+'日',
        'printy': '0',
        'printspeed':'6',
        'printdensity':'6',
        'sn': sn,
        'start': '0001',
        'n': '1'
    }
    dpi=300
    zpl,rotated=getZplCode(skuid,template.Id,variables,dpi)
    dot=int(round(dpi/25.4,0))
    width=round(float(template.width)/25.4,2)
    height=round(float(template.height)/25.4,2)
    url = f'http://api.labelary.com/v1/printers/{dot}dpmm/labels/{width}x{height}/0/'
    print(url)
    files = {'file' :zpl}
    # headers = {'Accept' : 'application/pdf'} # omit this line to get PNG images back
    try:
        response = requests.post(url, headers = {}, files = files, stream = True)
        if response.status_code == 200:
            image_file = io.BytesIO(response.content)
            image = Image.open(image_file)
            filename="print/preview"+timestamp+".png"
            image.save(getServer()['labeltemplatesPath']+filename)  # 这里的"output.png"是你要保存的文件名，可以根据需要修改后缀名
            image_file.close()
            return responseGet('获取成功', {'img':getServer()['labeltemplatesUrl']+filename,'rotated':rotated})
        else:
            return responseError('图像获取失败')
    except Exception:
        traceback.print_exc()
        return responseError('获取失败')


@api.route('/submitTemplate', methods=['POST'])
@login_required
def submitTemplate():
    res = request.json
    template = res.get('template')
    graphArr=res.get('graphArr')
    templateid = template['Id']
    pn=template['pn']
    des=template['des']
    revision=template['revision']
    code=template['code']
    isactive=template['isactive']
    linegroup=template['linegroup']
    try:
        if templateid:
            db.session.query(Printtemplate).filter(
                Printtemplate.Id == templateid).update({Printtemplate.pn: pn, Printtemplate.des: des, Printtemplate.revision: revision, Printtemplate.code: code, Printtemplate.isactive: isactive, Printtemplate.linegroup: linegroup})
        else:
            newTemplate = Printtemplate(
                pn=pn, des=des, revision=revision, code=code, isactive=isactive, linegroup=linegroup)
            db.session.add(newTemplate)
            db.session.flush()
            templateid = newTemplate.Id
        db.session.query(Printgraph).filter(Printgraph.templateid==templateid).delete()
        for g in graphArr:
                newGraph = Printgraph(
                    templateid=templateid, graph=g['graph'], font=g['font'],fixedH=g['fixedH'],isgraph=g['isgraph'])
                db.session.add(newGraph)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('提交失败')
    text = '提交成功'
    return responsePost(text, {'templateid': templateid})

@api.route('/getTemplatebyid', methods=['GET'])
@login_required
def getTemplatebyid():
    res = request.args
    templateid = res.get('templateid')
    template = db.session.query(Printtemplate).filter(
        Printtemplate.Id == templateid).first()
    graphs=db.session.query(Printgraph).filter(Printgraph.templateid==templateid).order_by(Printgraph.Id).all()
    rawLabels=db.session.query(Printlabel).all()
    labelArr=[]
    dic={}
    for r in rawLabels:
        labelArr.append({
            'value':r.pn,
            'label':r.pn
        })
    graphArr=[]
    for g in graphs:
        graphArr.append({
            'Id':g.Id,
            'graph':g.graph,
            'font':g.font,
            'fixedH':g.fixedH,
            'isgraph':g.isgraph,
            'fileList': [{'url':getServer()['labeltemplatesUrl']+g.graph}] if g.isgraph else []
        })
    if template:
        dic={
            'Id': template.Id,
            'pn': template.pn,
            'des': template.des,
            'revision': template.revision,
            'code': template.code,
            'url': getServer()['labeltemplatesUrl']+template.img if template.img else '',
            'name': template.img,
            'isactive': template.isactive,
            'linegroup': template.linegroup
        }
    return responseGet('获取成功', {'labelArr': labelArr,'graphArr':graphArr,'template': dic})

@api.route('/getParamsbyid', methods=['GET'])
@login_required
def getParamsbyid():
    res = request.args
    skuid = res.get('skuid')
    sku=res.get('sku')
    params = db.session.query(Printparam.name, Printparam.value).filter(
        Printparam.skuid == skuid).all()
    resvisons=db.session.query(Printbom.Id,Printbom.revision,Printbom.revisiondate,Printbom.isactive).filter(Printbom.sku==sku).all()
    arr=[]
    paramsArr=[]
    for r in resvisons:
        isa='-当前版本' if r.isactive else ''
        arr.append({
            'value':str(r.Id)+'|'+r.revision,
            'label':r.revision+isa
        })
    for p in params:
        paramsArr.append({
            'name': p.name,
            'value': p.value
        })
    print(arr)
    return responseGet('获取成功', {'params': paramsArr,'revisions':arr})

@api.route('/downloadLabel', methods=['GET'])
@login_required
def downloadLabel():
    res = request.args
    query = json.loads(res.get('query'))
    linegroup = query['linegroup']
    path = getServer()['templatePath']
    filename = res.get('url')
    templates=db.session.query(Printbom.sku,Printbom.revision,Printbomtemplate.templateid,Printbomtemplate.duplicate).outerjoin(Printbomtemplate,Printbom.Id==Printbomtemplate.skuid).filter(Printbom.linegroup==linegroup).filter(Printbom.isactive==1).all()
    skuDic={}
    for t in templates:
        if t.sku not in skuDic:
            skuDic[t.sku]={'templateid':[]}
        if t.templateid and t.duplicate:
            skuDic[t.sku]['templateid'].append(str(t.templateid)+':'+str(t.duplicate))
    params=db.session.query(Printbom.sku,Printparam.name,Printparam.value).outerjoin(Printparam,Printbom.Id==Printparam.skuid).filter(Printbom.linegroup==linegroup).filter(Printbom.isactive==1).all()
    headers=db.session.query(Printparam.name).filter(Printparam.skuid==Printbom.Id).filter(Printbom.linegroup==linegroup).filter(Printbom.isactive==1).distinct().all()
    headerArr=[]
    for h in headers:
        if h.name:
            headerArr.append(h.name)
    for p in params:
        if p.sku not in skuDic:
            skuDic[p.sku]={'templateid':[]}
        skuDic[p.sku][p.name]=p.value
    newFilename = getFile(path+'/prints/', path+filename, skuDic,headerArr,linegroup)
    if os.path.isfile(os.path.join(path+'/prints/', newFilename)):
        response = send_from_directory(
            path+'/prints/', newFilename, as_attachment=True)
        response.headers["Access-Control-Expose-Headers"] = "fname"
        response.headers['fname'] = newFilename
        return response
    return responseError('没有找到下载文件')


def getFile(path, file, skuDic,headerArr,linegroup):
    wb = load_workbook(file)
    ws = wb['params']
    i=2
    hh=3
    ws.cell(1, 1, linegroup)
    ws.cell(1, 2, 'templates')
    for h in headerArr:
        if h!='revision' and h!='sku':
            ws.cell(1, hh, h)
            hh+=1
    for k,v in skuDic.items():
        ws.cell(i, 1, k)
        print(skuDic[k].get('templateid'))
        ws.cell(i, 2, ','.join(skuDic[k].get('templateid')))
        for j in range(3,ws.max_column+1):
            vname=skuDic[k].get(ws.cell(1, j).value)
            if vname:
                ws.cell(i, j, str(vname))
        i+=1
    newFilename = datetime.datetime.strftime(
        datetime.date.today(), '%Y-%m-%d')+' - '+'labelInfo.xlsx'
    wb.save(path+newFilename)
    wb.close()
    return newFilename

@ api.route('/uploadLabelimg', methods=['POST'])
@ login_required
def uploadLabelimg():
    file_obj = request.files.get('file')
    templateid = request.headers["templateid"]
    mystr = ('labelUPLOADimg' + str(datetime.datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    appendix = file_obj.filename[file_obj.filename.rfind('.'):]
    try:
        db.session.query(Printtemplate).filter(Printtemplate.Id==templateid).update({Printtemplate.img:name+appendix})
        file_obj.save(getServer()['labeltemplatesPath']+name+appendix)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('上传失败')
    text = '上传项目全部更新成功'
    return responsePost(text, {
        'upload_url': {
            'url': getServer()['labeltemplatesUrl']+name+appendix,
            'name': name+appendix
        }})


@ api.route('/uploadGraph', methods=['POST'])
@ login_required
def uploadGraph():
    file_obj = request.files.get('file')
    index= request.form["index"]
    mystr = ('labelUPLOADimggraph' + str(datetime.datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    appendix = file_obj.filename[file_obj.filename.rfind('.'):]
    try:
        file_obj.save(getServer()['labeltemplatesPath']+name+appendix)
    except Exception:
        traceback.print_exc()
        return responseError('上传失败')
    text = '上传项目全部更新成功'
    return responsePost(text, {
        'upload_url': {
            'url': getServer()['labeltemplatesUrl']+name+appendix,
            'name': name+appendix,
            'index':index
        }})


@ api.route('/uploadLabel', methods=['POST'])
@ login_required
def uploadLabel():
    file_obj = request.files.get('file')
    linegroup = request.headers["linegroup"]
    mystr = ('labelUPLOAD' + str(datetime.datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    appendix = file_obj.filename[file_obj.filename.rfind('.'):]
    file_obj.save(getServer()['uploadPath']+name+appendix)
    moarr = filetoordertankplan(getServer()['uploadPath']+name+appendix, linegroup)
    if moarr == 'fail':
        return responseError('上传失败，请检查模板格式或请联系管理员')
    elif moarr == 'templatefail':
        return responseError('上传失败，模板不匹配，请检查1.模板是否存在且属于该产线,2.模板是否是文本格式（有可能自动转成时间格式了）或请联系管理员')
    if len(moarr) > 0:
        text = "部分更新成功，失败工单号如下："+','.join(moarr)+'请检查产线或料号是否存在且内容完整'
    else:
        text = '上传项目全部更新成功'
    return responsePost(text, {
        'upload_url': getServer()['uploadUrl']+name+appendix})


def filetoordertankplan(file, linegroup):
    wb = load_workbook(file)
    ws = wb['params']
    td= datetime.date.today()
    if ws.cell(1, 1).value != linegroup or ws.cell(1, 2).value != 'templates':
        print('fail',ws.cell(1, 1).value,linegroup,ws.cell(1, 2).value)
        return 'fail'
    cskus=db.session.query(Printbom.sku,Printbom.revision).filter(Printbom.isactive>=1).filter(Printbom.linegroup==linegroup).all()
    ctemplates=db.session.query(Printtemplate.Id).filter(Printtemplate.linegroup==linegroup).filter(Printtemplate.isactive==1).all()
    ctemplatesArr=[]
    for ct in ctemplates:
        ctemplatesArr.append(ct.Id)
    cskuDic={}
    for c in cskus:
        if c.sku not in cskuDic:
            cskuDic[c.sku]=c.revision
        else:
            if c.revision>cskuDic[c.sku]:
                cskuDic[c.sku]=c.revision
    moArr = []
    nameArr=[]
    try:
        for c in range(3,ws.max_column+1):
            if ws.cell(1, c).value:
                nameArr.append(str(ws.cell(1, c).value).upper())
        exceltemplatesArr=[]
        for r in range(2, ws.max_row + 1):
            if ws.cell(r, 1).value:
                templates = ws.cell(r, 2).value.split(',')
                for t in templates:
                    if int(t.split(':')[0]) not in exceltemplatesArr:
                        exceltemplatesArr.append(int(t.split(':')[0]))
        # ctemplatesArr 是否完全包含 exceltemplatesArr
        if not set(exceltemplatesArr).issubset(set(ctemplatesArr)):
            return 'templatefail'
    except Exception:
        return 'templatefail'
    try:
        for r in range(2, ws.max_row + 1):
            if ws.cell(r, 1).value:
                sku=ws.cell(r, 1).value
                revision=getNextversion(cskuDic.get(sku))
                templates = ws.cell(r, 2).value.split(',')
                newBom=Printbom(sku=sku,revision=revision,linegroup=linegroup,revisiondate=td,isactive=2)
                db.session.query(Printbom).filter(Printbom.sku==sku).filter(Printbom.linegroup==linegroup).update({Printbom.isactive:0})
                db.session.add(newBom)
                db.session.flush()
                skuid=newBom.Id
                newParam1=Printparam(skuid=skuid,name='sku',value=sku)
                db.session.add(newParam1)
                newParam2=Printparam(skuid=skuid,name='revision',value=revision)
                db.session.add(newParam2)
                for t in templates:
                    templateid=int(t.split(':')[0])
                    duplicate=int(t.split(':')[1])
                    newBomtemplate=Printbomtemplate(skuid=skuid,templateid=templateid,duplicate=duplicate)
                    db.session.add(newBomtemplate)
                for j in range(len(nameArr)):
                    if ws.cell(r, j+3).value and nameArr[j]!='revision' and nameArr[j]!='sku':
                        newParam=Printparam(skuid=skuid,name=nameArr[j],value=ws.cell(r, j+3).value)
                        db.session.add(newParam)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return 'fail'
    wb.close()
    return moArr

def getNextversion(v):
    if not v:
        return 'A'
    if v == 'Z':
        return 'AA'
    if v[-1] == 'Z':
        return v[:-1] + chr(ord(v[-2]) + 1) + 'A'
    return v[:-1] + chr(ord(v[-1]) + 1)

@api.route("/getLabellists", methods=["get"])  # 获取所有用户信息
@login_required
def getLabellists():
    res = request.args
    linegroup = res.get('linegroup')
    keywords = res.get('keywords')
    name=res.get('name')
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    if keywords and name:
        boms =db.session.query(Printbom.Id,Printbom.sku,Printbom.revision,Printbom.linegroup,Printbom.revisiondate,Printparam.value,Printbom.isactive).filter(Printbom.linegroup==linegroup).filter(
            Printbom.isactive>=1).outerjoin(Printparam,Printparam.skuid==Printbom.Id).filter(Printparam.value.like('%'+keywords+'%')).filter(Printparam.name==name)
        total=db.session.query(func.count(Printbom.Id)).filter(Printbom.linegroup==linegroup).filter(Printbom.isactive>=1).outerjoin(
            Printparam,Printparam.skuid==Printbom.Id).filter(Printparam.value.like('%'+keywords+'%')).filter(Printparam.name==name)
    else:
        boms=db.session.query(Printbom.Id,Printbom.sku,Printbom.revision,Printbom.linegroup,Printbom.revisiondate,Printbom.isactive).filter(Printbom.linegroup==linegroup).filter(Printbom.isactive>=1)
        total=db.session.query(func.count(Printbom.Id)).filter(Printbom.linegroup==linegroup).filter(Printbom.isactive>=1)
    boms=boms.order_by(Printbom.sku).paginate(pagenum, pagesize, error_out=False).items
    total=total.scalar()
    outArr=[]
    templateIds=db.session.query(Printbomtemplate.skuid,Printbomtemplate.templateid,Printbomtemplate.duplicate,Printtemplate.des,Printtemplate.revision).join(
        Printtemplate,Printbomtemplate.templateid==Printtemplate.Id).filter(Printbomtemplate.skuid.in_([b.Id for b in boms])).all()
    skuDic={}
    for t in templateIds:
        if t.skuid not in skuDic:
            skuDic[t.skuid]={'templateid':[]}
        if t.templateid and t.duplicate:
            skuDic[t.skuid]['templateid'].append(t.des+'|'+t.revision+'|'+str(t.duplicate))
    for b in boms:
        dic={
            'Id': b.Id,
            'sku': b.sku,
            'isactive':b.isactive,
            'revision': b.revision,
            'linegroup': b.linegroup,
            'revisiondate': datetime.datetime.strftime(b.revisiondate, '%Y-%m-%d'),
            'templateid':','.join(skuDic.get(b.Id)['templateid'])
        }
        if keywords and name:
            dic['keywords']=b.value
        outArr.append(dic)
    headers=db.session.query(Printparam.name).filter(Printparam.skuid==Printbom.Id).filter(Printbom.linegroup==linegroup).filter(Printbom.isactive==1).distinct().all()
    headerArr=[]
    for h in headers:
        if h.name:
            headerArr.append(h.name)
    templates=db.session.query(Printtemplate.Id,Printtemplate.img,Printtemplate.pn,Printlabel.waterproof,Printtemplate.revision,
                               Printtemplate.des).join(
                                       Printlabel,Printtemplate.pn==Printlabel.pn).filter(Printtemplate.linegroup==linegroup).filter(Printtemplate.isactive==1).order_by(Printtemplate.des).all()
    options=[]
    for t in templates:
        options.append({
            'templateid':t.Id,
            'img':getServer()['labeltemplatesUrl']+t.img if t.img else '',
            'pn':t.pn,
            'des':t.des+'|'+t.revision,
            'waterproof':"是" if t.waterproof else "否"
        })
    return responseGet('获取成功', {'outArr':outArr,'total':total,'headerArr':headerArr,'templates':options})


@api.route("/getLinegroup", methods=["get"])
@login_required
def getLinegroups():
    res=request.args
    plant=res.get('plant')
    linegroup=getLinegroup(plant)
    return responseGet('成功', {'linegroup': linegroup})

@ api.route("/checkPrintdupilate", methods=["post"])  # 获取routing列表
@ login_required
def checkPrintdupilate():
    res = request.json
    snarr = res.get('snarr')
    dupArr=[]
    for key in snarr:
        if redis_client.exists(key):
            dupArr.append(key)
    print('dup',dupArr)
    return responsePost('获取成功', {'sns': dupArr})


@ api.route("/getPrinttemplates", methods=["get"])
@ login_required
def getPrinttemplates():
    res=request.args
    sku=res.get('sku')
    templates=db.session.query(Printtemplate.Id,Printtemplate.img,Printtemplate.pn,Printlabel.waterproof,Printtemplate.revision,
                               Printtemplate.des,Printtemplate.linegroup).join(
                                       Printlabel,Printtemplate.pn==Printlabel.pn).filter(Printtemplate.isactive==1).filter(Printtemplate.linegroup=='All').order_by(Printtemplate.des).all()
    options=[]
    for t in templates:
        options.append({
            'sku':sku,
            'qty':1,
            'templateid':t.Id,
            'img':getServer()['labeltemplatesUrl']+t.img,
            'pn':t.pn,
            'des':t.des+'|'+t.revision,
            'waterproof':"是" if t.waterproof else "否"
        })
    return responseGet('成功', {'options':options})


@ api.route("/getSkuinfobypn", methods=["get"])
@ login_required
def getSkuinfobypn():
    res=request.args
    sku=res.get('sku')
    revision=res.get('revision')
    skuinfo=db.session.query(Skuinfo).filter(Skuinfo.sku==sku).first()
    templates=db.session.query(Printbom.sku,Printbomtemplate.skuid,Printbom.revision.label('bomrev'),Printbomtemplate.templateid,Printtemplate.img,Printtemplate.pn,Printlabel.waterproof,Printtemplate.revision,
                               Printtemplate.des,Printbomtemplate.duplicate).outerjoin(Printbomtemplate,Printbom.Id==Printbomtemplate.skuid).join(
                                   Printtemplate,Printtemplate.Id==Printbomtemplate.templateid).join(
                                       Printlabel,Printtemplate.pn==Printlabel.pn).filter(Printbom.sku==sku).filter(Printtemplate.isactive==1)
    if revision:
        templates=templates.filter(Printbom.revision==revision).all()
    else:
        templates= templates.filter(Printbom.isactive==1).all()
    dic={}
    options=[]
    for t in templates:
        options.append({
            'sku':sku,
            'skuid':t.skuid,
            'revision':t.bomrev,
            'qty':t.duplicate,
            'pqty':0,
            'templateid':t.templateid,
            'img':getServer()['labeltemplatesUrl']+t.img,
            'pn':t.pn,
            'des':t.des+'|'+t.revision,
            'waterproof':"是" if t.waterproof else "否"
        })
    if skuinfo:
        dic={
            'sku':skuinfo.sku,
            'des':skuinfo.des,
            'lenverify':skuinfo.lenverify,
            'snverify':skuinfo.snverify,
        }
    return responseGet('成功', {'skuinfo': dic,'options':options})

def has_variable(string):
    pattern = r"\{\}"
    match = re.search(pattern, string)
    return match is not None
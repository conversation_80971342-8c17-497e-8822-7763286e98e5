import{n as r,G as g,a2 as s,q as y,S as _,b as t,f as c,r as a,w as b,D as v,aW as x,aE as C}from"./index-BnxEuBzx.js";import{b as W}from"./dashboard-dtTxmf4X.js";import{c as j}from"./front-CiGk0t8u.js";import{changehourdata as p}from"./index-BNcz4cED.js";import{changedefectdata as m}from"./index-QtNKVBLo.js";import{u as l}from"./prod-CmDsiAIL.js";import"./editDataForm.vue_vue_type_script_setup_true_lang-Dl-sZNXt.js";import"./editDefectForm-CA2niQRC.js";import"./moment-C3TZ8gAF.js";function A(){const d=r([]),n=r(!0);g([()=>l().selectedDate,()=>l().shift,()=>l().machineId],(e,o)=>{u.selecteddate=e[0],u.shift=e[1],u.machine=e[2],i()});const u=s({selecteddate:l().selectedDate,machine:l().machineId,shift:l().shift}),i=()=>{n.value=!0,W(_(u)).then(e=>{d.value=e.data,n.value=!1})};function f(e){x.confirm("确认该小时产量记录吗?","系统提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0,draggable:!0}).then(()=>{j({selecteddate:u.selecteddate,machine:u.machine,hourid:e.hourid}).then(o=>{o.meta.status==201&&(i(),C("确认小时数据成功！",{customClass:"el",type:"success"}))})}).catch(()=>{})}y(()=>{i()});const h=s({text:"正在加载小时记录表数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `});return{dataList:d,columns:[{label:"小时号",prop:"hourid",minWidth:52,fixed:!0,formatter(e){return e.hourid+"~"+(e.hourid+1)}},{label:"料号",prop:"pn",minWidth:100,fixed:!0},{label:"产出",prop:"output",minWidth:60},{label:"调整",prop:"adjustion",minWidth:60,cellRenderer({row:e}){return e.adjustion==0?t(a("el-tag"),{type:"info",onClick:()=>{p("edit",u.machine,e,i)}},{default:()=>[c("+")]}):t(a("el-tag"),{size:"large",type:"warning",style:"width:100%;",onClick:()=>{p("edit",u.machine,e,i)}},{default:()=>[e.adjustion]})}},{label:"实际",prop:"",minWidth:60,cellRenderer({row:e}){if(e.output==0&&e.adjustion==0)return t("div",null,null);if(e.output+e.adjustion>=e.computed_output)return t(a("el-tag"),{size:"large",type:"success",style:"width:100%"},{default:()=>[e.output+e.adjustion]});if(e.output+e.adjustion<e.computed_output)return t(a("el-tag"),{size:"large",type:"danger",style:"width:100%"},{default:()=>[e.output+e.adjustion]})}},{label:"标准",prop:"computed_output",minWidth:60,formatter(e){return e.computed_output.toFixed(0)}},{label:"实际累积",prop:"acumulated_output",minWidth:100,cellRenderer({row:e}){if(e.acumulated_output==0)return t("div",null,null);if(e.acumulated_output>=e.acumulated_computedvalue)return t(a("el-tag"),{size:"large",type:"success",style:"width:60%"},{default:()=>[e.acumulated_output]});if(e.acumulated_output<e.acumulated_computedvalue)return t(a("el-tag"),{size:"large",type:"danger",style:"width:60%"},{default:()=>[e.acumulated_output]})}},{label:"标准累积",prop:"acumulated_computedvalue",minWidth:100,formatter(e){return e.acumulated_computedvalue.toFixed(0)}},{label:"不良",prop:"defect_count",minWidth:60,cellRenderer:({row:e})=>e.defect_count>0?t(a("el-tag"),{size:"large",type:"danger",style:"width:100%;",onClick:()=>{m("edit",u.machine,e,i)}},{default:()=>[e.defect_count]}):t(a("el-tag"),{type:"info",onClick:()=>{m("add",u.machine,e,i)}},{default:()=>[c("+")]})},{label:"操作",prop:"",fixed:"right",minWidth:60,cellRenderer:({row:e})=>t("div",{class:"ops"},[b(t(a("el-button"),{type:e.confirm_state==1?"default":"warning",onClick:()=>f(e)},{default:()=>[t(a("el-icon"),{size:"20"},{default:()=>[t(a("CircleCheck"),null,null)]})]}),[[v,e.confirm_state!=1]])])}],loadingConfig:h,adaptiveConfig:{fixHeader:!0},loading:n,refreshData:i}}export{A as useColumns};

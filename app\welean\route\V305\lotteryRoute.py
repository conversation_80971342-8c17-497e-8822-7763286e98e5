
from flask import Blueprint, request
from app.welean.model.models_welean import Lotteryprize, Lotteryget, Userinfo, Getscore
from extensions import db
import math
import hashlib
import random
import traceback
from datetime import datetime, date, timedelta
from sqlalchemy import func, desc
from app.public.functions import responseError, responseGet,  responsePut, login_required, responsePost
from app.welean.functions import getServer, OTthread, getAdmins
api = Blueprint('welean/V305/lotteryAPI', __name__)

luckyRange = 1000


@ api.route('/handlePrize', methods=['PUT'])
@ login_required
def handlePrize():
    res = request.json
    btn = res.get('btn')
    id = res.get('id')
    try:
        if btn == '发放':
            db.session.query(Lotteryget).filter(Lotteryget.Id == id).update(
                {'paid': 1, 'paiddate': datetime.strftime(date.today(), "%Y-%m-%d")})
        else:
            db.session.query(Lotteryget).filter(Lotteryget.Id == id).update(
                {'paid': 0, 'paiddate': ''})
        db.session.commit()
        return responsePut('修改成功')
    except Exception as e:
        db.session.rollback()
        print(e)
        return responseError('修改失败')


@api.route('/getAllPrizes', methods=['GET'])
@login_required
def getAllPrizes():
    res = request.args
    eid = res.get('eid').split('/')[0]
    plant = res.get('plant')
    year = res.get('year')
    myPrizes = []
    prizes = db.session.query(Lotteryget.getdate, Lotteryget.Id, Lotteryget.getprize, Userinfo.cname).outerjoin(Userinfo, Lotteryget.eid == Userinfo.eid).filter(
        func.year(Lotteryget.getdate) == year).filter(Lotteryget.paid == 0).filter(Userinfo.plant == plant).order_by(desc(Lotteryget.getdate))
    if eid:
        prizes = prizes.filter(Lotteryget.eid == eid)
    prizes = prizes.all()
    for p in prizes:
        if (date.today()-p.getdate).days <= 60:
            myPrizes.append({
                'cname': p.cname,
                'getprize': p.getprize,
                'id': p.Id,
                'btn': '发放'
            })
    return responseGet("获取列表成功 ", {'myPrizes': myPrizes})


@api.route('/getMyPrizes', methods=['GET'])
@login_required
def getMyPrizes():
    res = request.args
    eid = res.get('eid')
    # eid = '1220326'
    year = res.get('year')
    myPrizes = []
    prizes = db.session.query(Lotteryget).filter(
        Lotteryget.eid == eid).filter(func.year(Lotteryget.getdate) == year).filter(Lotteryget.getprize != '谢谢参与').order_by(desc(Lotteryget.getdate)).all()
    for p in prizes:
        if (date.today()-p.getdate).days > 60:
            delay = 1
        else:
            delay = 0
        myPrizes.append({
            'getdate': datetime.strftime(p.getdate, "%Y-%m-%d"),
            'paid': p.paid,
            'delay': delay,
            'getprize': p.getprize,
            'paiddate': datetime.strftime(p.paiddate, "%Y-%m-%d") if p.paiddate else '未记录'
        })
    return responseGet("获取列表成功 ", {'myPrizes': myPrizes})


@api.route('/getPrizes', methods=['GET'])
@login_required
def getPrizes():
    res = request.args
    plant = res.get('plant')
    lotterys = db.session.query(Lotteryprize).filter(
        Lotteryprize.plant == plant).filter(Lotteryprize.status == 'inuse').all()
    larr = []
    tweight = 0
    for lt in lotterys[:5]:
        dic = {
            'id': lt.Id,
            'weight': lt.weight,
            'item': lt.item,
            'totalQty': lt.totalqty,
            'getQty': lt.getqty,
            'price': lt.price,
            'picurl': getServer()['prizesUrl']+lt.url,
            'inuse': True
        }
        tweight += lt.weight
        larr.append(dic)
    total = len(larr)
    if len(larr) < 6:
        for i in range(len(larr), 6):
            dic = {
                'id': 0,
                'weight': int(tweight/(6-len(larr))) if tweight < luckyRange else 0,
                'item': '谢谢参与',
                'totalQty': "-",
                'getQty': "-",
                'price': 0,
                'picurl': getServer()['prizesUrl']+'xiexiecanyu.jpg',
            }
            tweight += dic['weight']
            larr.append(dic)
    backups = db.session.query(Lotteryprize).filter(
        Lotteryprize.plant == plant).filter(Lotteryprize.status == 'backup').all()
    backupArr = []
    for bb in backups:
        backupArr.append({
            'id': bb.Id,
            'weight': bb.weight,
            'item': bb.item,
            'totalQty': bb.totalqty,
            'getQty': bb.getqty,
            'restQty': bb.totalqty-bb.getqty,
            'price': bb.price,
            'picurl': getServer()['prizesUrl']+bb.url,
            'inuse': False
        })
    return responseGet("获取列表成功 ", {'inuses': larr, 'backups': backupArr, 'tweight': luckyRange if tweight < luckyRange else tweight, 'total': total})


@ api.route('/getPrize', methods=['GET'])
@ login_required
def getPrize():
    res = request.args
    plant = res.get('plant')
    eid = res.get('eid')
    lotterys = db.session.query(Lotteryprize).filter(
        Lotteryprize.plant == plant).filter(Lotteryprize.status == 'inuse').all()
    min = 0
    larr = []
    for lt in lotterys[:5]:
        print(lt)
        if lt.totalqty-lt.getqty > 0:
            dic = {
                'id': lt.Id,
                'min': min,
                'max': min+lt.weight,
                'item': lt.item,
                'picurl': getServer()['prizesUrl']+lt.url
            }
            min = min+lt.weight
        else:
            dic = {
                'id': lt.Id,
                'min': min,
                'max': min,
                'item': lt.item,
                'picurl': getServer()['prizesUrl']+lt.url
            }
        larr.append(dic)
    if len(larr) < 6:
        tscore = math.ceil((luckyRange-min)/(6-len(larr)))
        for i in range(len(larr), 6):
            dic = {
                'id': 0,
                'min': min,
                'max': min+tscore,
                'item': '谢谢参与',
                'picurl': getServer()['prizesUrl']+'xiexiecanyu.jpg',
            }
            min = min+tscore
            larr.append(dic)
    luckNumber = random.randint(0, luckyRange)
    chosedItem = {}
    index = 0
    for item in larr:
        if(item['min'] <= luckNumber and item['max'] > luckNumber):
            chosedItem = item
            chosedItem['index'] = index
            break
        index += 1
    try:
        if chosedItem['id']:
            gprize = db.session.query(Lotteryprize).filter(Lotteryprize.Id ==
                                                           chosedItem['id']).scalar()
            gprize.getqty = gprize.getqty+1
            title = 'prize'
            if gprize.getqty >= gprize.totalqty:
                admins = getAdmins(plant, title)
                for ad in admins:
                    taskBody = {
                        'content': gprize.item+'已经发完',
                        'owner': '奖品管理',
                        'comments': '请进入管理-奖品管理确认并调整奖品'
                    }
                    OTthread(taskBody, ad['wxid'], 'pages/login/login')
        getItem = Lotteryget(
            eid=eid, getprize=chosedItem['item'], getdate=date.today(), prizeid=chosedItem['id'], paid=(1 if chosedItem['item'] == '谢谢参与' else 0))
        db.session.add(getItem)
        db.session.commit()
        return responseGet("获取列表成功 ", {'prize': chosedItem})
    except Exception:
        db.session.rollback()
        traceback.print_exc()
    return responseError('抽奖失败，请联系精益部门反馈该问题')


@ api.route('/getLuckyData', methods=['GET'])
@ login_required
def getLuckyData():
    res = request.args
    plant = res.get('plant')
    eid = res.get('eid')
    lotterys = db.session.query(Lotteryprize).filter(
        Lotteryprize.plant == plant).filter(Lotteryprize.status == 'inuse').all()
    prizes = []
    cindex = 0
    for lt in lotterys[:5]:
        if cindex % 2 == 0:
            color = '#f9e3bb'
        else:
            color = '#f8d384'
        dic = {
            'id': lt.Id,
            'background': color,
            'fonts': [{'text': lt.item, 'top': '18%'}]
            # 'imgs': [{'src': getServer()['prizesUrl']+lt.url, 'width': '120rpx', 'height':'120rpx', 'top': '10%'}]
        }
        cindex += 1
        prizes.append(dic)
    if len(prizes) < 6:
        for i in range(len(prizes), 6):
            if cindex % 2 == 0:
                color = '#f9e3bb'
            else:
                color = '#f8d384'
            dic = {
                'id': 0,
                'background': color,
                'fonts': [{'text': '谢谢参与', 'top': '18%'}]
                # 'imgs': [{'src': getServer()['prizesUrl']+'xiexiecanyu.jpg', 'width': '120rpx', 'height':'120rpx', 'top': '10%'}]
            }
            cindex += 1
            prizes.append(dic)
    drawTimes = getDrawTimes(eid)
    luckyMen = getLuckymen(plant)
    return responseGet("获取列表成功", {'prizes': prizes, 'drawTimes': drawTimes, 'luckyMen': luckyMen})


@ api.route('/getTimes', methods=['GET'])
def getTimes():
    a = random.randint(0, 5)
    return responseGet("获取列表成功", {'getTimes': a})


def getDrawed(eid, now, mstart):
    getTimes = db.session.query(func.count(Lotteryget.Id)).filter(Lotteryget.eid == eid).filter(
        Lotteryget.getdate.between(mstart, now)).group_by(Lotteryget.eid).scalar()
    if not getTimes:
        getTimes = 0
    return getTimes


def getLuckymen(plant):
    now = datetime.now()
    m2 = now+timedelta(days=-60)
    men = db.session.query(Lotteryget.getprize, Userinfo.dept1, Userinfo.cname).outerjoin(
        Lotteryprize, Lotteryget.prizeid == Lotteryprize.Id).outerjoin(Userinfo, Lotteryget.eid == Userinfo.eid).filter(
            Lotteryget.getdate.between(m2, now)).filter(Userinfo.plant == plant).order_by(desc(Lotteryprize.price)).limit(20).all()
    luckyMen = []
    for m in men:
        if m.dept1 and m.cname and m.getprize:
            luckyMen.append(''+m.dept1+'/'+m.cname+' 获得 '+m.getprize)
    return luckyMen


def getDrawTimes(eid):
    now = datetime.now()
    mstart = datetime(now.year, now.month, 1)
    drawed = getDrawed(eid, now, mstart)
    totalScore = db.session.query(func.sum(Getscore.getscore)).filter(Getscore.eid == eid).filter(
        Getscore.getdate.between(mstart, now)).group_by(Getscore.eid).scalar()
    if not totalScore:
        totalScore = 0
    myTimes = math.ceil(math.sqrt(totalScore)*0.18)-drawed
    print(totalScore, drawed, myTimes)
    return myTimes


@ api.route('/uploadLotterypic', methods=['POST'])
@ login_required
def uploadLotterypic():
    file_obj = request.files.get('file')
    sid = request.form.get('sid')
    mystr = ('lottery' + str(datetime.now())).encode('UTF-8')
    fname = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        print(getServer()['prizesPath'], fname, appendix)
        try:
            file_obj.save(getServer()['prizesPath']+fname+appendix)
            db.session.query(Lotteryprize).filter(Lotteryprize.Id == sid).update({
                'url': fname+appendix})
            db.session.commit()
            return responsePost("上传成功", {'url': getServer()['prizesUrl']+fname+appendix})
        except Exception as e:
            db.session.rollback()
            print(e)
    return responseError("上传文件失败，请联系管理员")


@ api.route('/submitPrize', methods=['POST'])
@ login_required
def submitPrize():
    res = request.json
    id = int(res.get('id'))
    item = res.get('item')
    totalqty = res.get('totalQty')
    weight = res.get('weight')
    purchasedate = date.today()
    getqty = res.get('getQty')
    inuse = res.get('inuse')
    plant = res.get('plant')
    price = res.get('price')
    print(id, item, totalqty, getqty, weight, purchasedate, inuse, plant)
    if not inuse:
        if totalqty == getqty:
            tag = 'empty'
        else:
            tag = 'backup'
    else:
        tag = 'inuse'
    if id > 0:
        try:
            db.session.query(Lotteryprize).filter(Lotteryprize.Id == id).update({
                'item': item,
                'totalqty': totalqty,
                'weight': weight,
                'status': tag,
                'plant': plant,
                'price': price
            })
            db.session.commit()
            return responsePost("更新成功")
        except Exception:
            db.session.rollback()
    else:
        try:
            lottery = Lotteryprize(item=item, totalqty=totalqty, weight=weight,
                                   getqty=0, purchasedate=purchasedate, status=tag, plant=plant, price=price)
            db.session.add(lottery)
            db.session.flush()
            sid = lottery.Id
            db.session.commit()
            return responsePost("新建成功", {'sid': sid})
        except Exception:
            db.session.rollback()
    return responseError("提交失败")

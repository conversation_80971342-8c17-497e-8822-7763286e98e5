import{d as S,a2 as D,G as T,q as w,r as c,o as l,c as b,e as a,b as n,h as d,f as C,u as o,g as _,C as h,y as k,F as B,t as N,aE as V,ak as W,al as E,_ as P}from"./index-BnxEuBzx.js";import{g as $}from"./front-CiGk0t8u.js";import{u as t}from"./prod-CmDsiAIL.js";import{i as q}from"./shift-DH35BNzV.js";import{c as G}from"./index-CA30dg9C.js";import{c as H}from"./index-Ctm3qPP9.js";import"./moment-C3TZ8gAF.js";import"./editStateForm-BQH9QYuy.js";import"./dashboard-dtTxmf4X.js";import"./index.vue_vue_type_script_setup_true_lang-DEM1uiK0.js";import"./editPnForm-mTeSfSrW.js";import"./prod-CfeywgVC.js";const u=p=>(W("data-v-ad99f063"),p=p(),E(),p),L={class:"basic"},M={class:"basic_header"},j=u(()=>a("span",{class:"lightfont"},"生产基本信息",-1)),A={class:"basic_body"},J={class:"border-box-content"},K=u(()=>a("div",{class:"title"},"设备状态",-1)),O=u(()=>a("div",{class:"title"},[a("span",null,"实时生产料号")],-1)),Q={class:"body_content"},R=S({__name:"mstatus",setup(p){const e=D({state:"",state_id:"",state_des:"",pn:""});T([()=>t().selectedDate,()=>t().shift,()=>t().machineId],(i,s)=>{q(i[0],i[1])&&m({selecteddate:i[0],shift:i[1],machine:i[2]})});const m=i=>{$(i).then(s=>{s.meta.status==200?(e.state=s.data.state,e.state_des=s.data.state_des,e.state_id=s.data.state_id,e.pn=s.data.pn):V("查询状态失败",{customClass:"el",type:"error"})})};return w(()=>{m({selecteddate:t().selectedDate,shift:t().shift,machine:t().machineId})}),(i,s)=>{const v=c("WarningFilled"),r=c("el-icon"),I=c("el-button"),g=c("Tools"),x=c("WarnTriangleFilled"),F=c("CircleCloseFilled"),y=c("el-card"),z=c("el-tag");return l(),b("div",L,[a("div",M,[j,n(I,{round:"",type:"danger"},{default:d(()=>[n(r,{size:24,style:{"margin-right":"5px"}},{default:d(()=>[n(v)]),_:1}),C("安灯报警")]),_:1})]),a("div",A,[a("div",J,[n(y,{shadow:"always","body-style":{padding:"0px"},class:"status"},{default:d(()=>[K,a("div",{class:"body_content",onClick:s[0]||(s[0]=f=>{o(G)(o(t)().machineId,e.state_id,()=>{m({selecteddate:o(t)().selectedDate,shift:o(t)().shift,machine:o(t)().machineId})})})},[e.state=="1"?(l(),_(r,{key:0,color:"#00ff00",size:48,class:"is-loading"},{default:d(()=>[n(g)]),_:1})):h("",!0),e.state=="2"?(l(),_(r,{key:1,color:"yellow",size:48},{default:d(()=>[n(x)]),_:1})):h("",!0),e.state=="3"?(l(),_(r,{key:2,color:"red",size:48},{default:d(()=>[n(F)]),_:1})):h("",!0),e.state=="4"?(l(),_(r,{key:3,color:"grey",size:48},{default:d(()=>[n(g)]),_:1})):h("",!0),a("div",null,k(e.state_des),1)])]),_:1}),n(y,{shadow:"always","body-style":{padding:"0px"},class:"pn"},{default:d(()=>[O,a("div",Q,[a("div",{class:"pn_content",onClick:s[1]||(s[1]=f=>{o(H)(o(t)().machineId,e.pn,()=>{m({selecteddate:o(t)().selectedDate,shift:o(t)().shift,machine:o(t)().machineId})})})},[(l(!0),b(B,null,N(e.pn.split("@"),f=>(l(),_(z,{key:f,type:"success",size:"large",style:{"font-size":"16px"}},{default:d(()=>[C(k(f),1)]),_:2},1024))),128))])])]),_:1})])])])}}}),ct=P(R,[["__scopeId","data-v-ad99f063"]]);export{ct as default};

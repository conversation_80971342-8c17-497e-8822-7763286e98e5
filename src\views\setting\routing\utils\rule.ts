import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 自定义表单规则校验 */
export const formRules = reactive(<FormRules>{
  pn: [{ required: true, message: "料号为必填项", trigger: "blur" }],
  pn_des: [{ required: true, message: "料号描述为必填项", trigger: "blur" }],
  cavity: [{ required: true, message: "模穴数为必填项", trigger: "blur" }],
  machine: [{ required: true, message: "注塑机编号为必填项", trigger: "blur" }],
  moldingcycle: [
    { required: true, message: "成型周期为必填项", trigger: "blur" }
  ]
});

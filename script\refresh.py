from apscheduler.schedulers.blocking import BlockingScheduler
import json
from db_model import Connection, shift_info, shift_state_log, shift_sku_log, hourly_output, routing, defect
from app.im.functions.shift import get_shift_time, find_shift_bytime, get_shift_hour
from app.im.functions.cycle import get_sample_cycle_data
from app.im.functions.material import get_routing_info
import pandas as pd
from datetime import datetime, timedelta, time
from datetime import datetime, timedelta
from sqlalchemy import desc, and_, or_, func
import threading
import redis
import dateparser
import pytz
from script_config import my_config
from app.im.functions.cycle import fix_broken_cycle

try:
    pool = redis.ConnectionPool.from_url(
        my_config['REDIS_URL'], db=0, decode_responses=True)
    r = redis.Redis(connection_pool=pool)
except Exception as message:
    print('连接服务器报错%s' % message)
else:
    print('redis服务连接成功')


def refresh(shift):
    res = r.hgetall('SHIFTINFO')
    r.delete('SHIFTINFO')
    if res:
        for key, value in res.items():
            # 同步转移各机台班次数据
            T_update_shift_data = threading.Thread(
                name='update_shift_data', target=update_shift_data, args=(key, value))
            T_update_shift_data.start()
    res = r.hgetall('LASTRUN')
    if res:
        for key, value in res.items():
            # 同步补填各机台交接班状态
            T_update_shift_status = threading.Thread(
                name='update_shift_status', target=update_shift_status, args=(key, shift))
            T_update_shift_status.start()

# 确认小时产量


def func_check_hourly_output(machine_id, check_time):
    with Connection() as connection:
        session = connection.get_session()
        check_time = dateparser.parse(str(check_time))
        # 将check_time前面一个整小时的前后整点毫秒时间戳取出来
        previous_hour_start = check_time.replace(
            minute=0, second=0, microsecond=0) - timedelta(hours=1)
        previous_hour_end = previous_hour_start + timedelta(hours=1)
        start_timestamp = int(previous_hour_start.timestamp() * 1000)
        end_timestamp = int(previous_hour_end.timestamp() * 1000)

        shift_check_time = check_time - \
            timedelta(minutes=5) if check_time.hour in [
                0, 8, 16] else check_time
        check_shift_info = find_shift_bytime(shift_check_time)  # 定义所校验班次的信息
        this_shift_sku = session.query(shift_sku_log).filter(
            shift_sku_log.machine_id == machine_id,
            shift_sku_log.start_time >= check_shift_info[
                'start_time'],
            shift_sku_log.start_time < check_shift_info['end_time']).all()
        # 如果没有设置料号信息，将redis中的料号信息设置为开班料号
        if len(this_shift_sku) == 0:
            this_shift_sku = shift_sku_log(machine_id=machine_id,
                                           sku=r.hget('PN', f'IM{machine_id}'),
                                           shift_code=check_shift_info[
                                               'shift'],
                                           shift_date=check_shift_info[
                                               'shift_date'],
                                           start_time=check_shift_info[
                                               'start_time'],
                                           end_time=None,
                                           update_time=datetime.now())
            session.add(this_shift_sku)
            this_shift_sku = [this_shift_sku]

        hour_index = check_time.hour-1 if check_time.hour != 0 else 23
        time_header = time(hour_index, 0, 0)
        time_footer = time(
            hour_index+1, 0, 0) if hour_index != 23 else time(23, 59, 59)

        # 取小时记录表
        hourly_data = session.query(hourly_output).filter(
            hourly_output.machine_id == machine_id,
            hourly_output.recorddate == shift_check_time.date(),
            hourly_output.hourid == hour_index).all()

        # 将料号信息转为dataframe
        sku_info = [row.__dict__ for row in this_shift_sku]
        df_sku_info = pd.DataFrame(sku_info)
        df_sku_info.drop('_sa_instance_state', axis=1, inplace=True)
        df_sku_info['start_time'] = pd.to_datetime(df_sku_info['start_time'])
        df_sku_info['end_time'] = pd.to_datetime(df_sku_info['end_time'])
        item_header = df_sku_info[df_sku_info['start_time'].dt.time <= time_header].sort_values(by='start_time', ascending=False).drop_duplicates(
            subset=['machine_id', 'shift_date', 'shift_code'])
        item_footer = df_sku_info[df_sku_info['start_time'].dt.time <= time_footer].sort_values(by='start_time', ascending=False).drop_duplicates(
            subset=['machine_id', 'shift_date', 'shift_code'])
        if not item_header.empty and not item_footer.empty:
            if item_header['sku'].values[0] == item_footer['sku'].values[0]:  # 如果小时首尾料号相同
                std_hour_data = get_sample_cycle_data(
                    r, machine_id, start_timestamp, end_timestamp)
                routing_info = get_routing_info(
                    r, item_header['sku'].values[0])
                routing_value = routing_info['moldingcycle']/routing_info['cavity'] if routing_info[
                    'manualcycle'] is None else (routing_info['moldingcycle']+routing_info['manualcycle'])/routing_info['cavity']
                output_value = std_hour_data['std_count'] * \
                    routing_info['cavity'] if std_hour_data else 0
                if "/" in item_header['sku'].values[0]:  # 多模料号
                    sku_count = item_header['sku'].values[0].split("/")
                    if len(hourly_data) == 0:
                        for i in range(len(sku_count)):
                            if output_value > 0:
                                session.add(hourly_output(
                                    recorddate=shift_check_time.date(),
                                    hourid=hour_index,
                                    machine_id=machine_id,
                                    pn=sku_count[i],
                                    output=output_value,
                                    adjustion=0,
                                    routing=routing_value,
                                    confirm_state=0
                                ))
                    elif len(hourly_data) >= 1:
                        if len(hourly_data) >= len(sku_count):
                            for i in range(len(hourly_data)-len(sku_count)):
                                session.delete(hourly_data[-1])  # 删除脏写的多模料号记录
                            for i in range(len(sku_count)):
                                print('开始更新料号')
                                if i < len(hourly_data):
                                    hourly_data[i].recorddate = shift_check_time.date(
                                    )
                                    hourly_data[i].hourid = hour_index
                                    hourly_data[i].machine_id = machine_id
                                    hourly_data[i].pn = sku_count[i]
                                    hourly_data[i].output = output_value
                                    hourly_data[i].adjustion = 0
                                    hourly_data[i].routing = routing_value
                                    hourly_data[i].confirm_state = 0
                        elif len(hourly_data) < len(sku_count):
                            for i in range(len(sku_count)):
                                if i < len(hourly_data):
                                    hourly_data[i].recorddate = shift_check_time.date(
                                    )
                                    hourly_data[i].hourid = hour_index
                                    hourly_data[i].machine_id = machine_id
                                    hourly_data[i].pn = sku_count[i]
                                    hourly_data[i].output = output_value
                                    hourly_data[i].adjustion = 0
                                    hourly_data[i].routing = routing_value
                                    hourly_data[i].confirm_state = 0
                                else:
                                    if output_value > 0:
                                        session.add(hourly_output(
                                            recorddate=shift_check_time.date(),
                                            hourid=hour_index,
                                            machine_id=machine_id,
                                            pn=sku_count[i],
                                            output=output_value,
                                            adjustion=0,
                                            routing=routing_value,
                                            confirm_state=0
                                        ))
                else:  # 单模料号
                    if len(hourly_data) == 0:
                        if output_value > 0:
                            session.add(hourly_output(
                                recorddate=shift_check_time.date(),
                                hourid=hour_index,
                                machine_id=machine_id,
                                pn=item_header['sku'].values[0],
                                output=output_value,
                                adjustion=0,
                                routing=routing_value,
                                confirm_state=0
                            ))
                    elif len(hourly_data) >= 1:
                        if len(hourly_data) > 1:
                            for i in range(1, len(hourly_data)):
                                session.delete(hourly_data[i])  # 删除脏写的多模料号记录
                        hourly_data[0].recorddate = shift_check_time.date()
                        hourly_data[0].hourid = hour_index
                        hourly_data[0].machine_id = machine_id
                        hourly_data[0].pn = item_header['sku'].values[0]
                        hourly_data[0].output = output_value
                        hourly_data[0].adjustion = 0
                        hourly_data[0].routing = routing_value
                        hourly_data[0].confirm_state = 0
            else:  # 如果小时段首尾料号不同
                print("料号切换")
        session.commit()

        # 将本小时的uptime写上
        state_list = session.query(shift_state_log).filter(
            shift_state_log.machine_id == machine_id,
            shift_state_log.shift_date == check_shift_info[
                'shift_date'],
            shift_state_log.shift_code == check_shift_info[
                'shift'],
            or_(and_(shift_state_log.start_time >= previous_hour_start, shift_state_log.start_time <= previous_hour_end),
                and_(shift_state_log.end_time >= previous_hour_start, shift_state_log.end_time <= previous_hour_end))).order_by(shift_state_log.start_time.asc()).all()
        scheduled_uptime = 0
        if len(state_list) > 0:
            state_list[0].start_time = previous_hour_start
            state_list[-1].end_time = previous_hour_end
            for item in state_list:
                # 遍历状态列表，如果状态标示为记产量，则将时间加到scheduled_uptime上
                sum_code = json.loads(
                    r.hget('STATEDES', f'ID{item.machine_state_des}'))['is_output']
                if sum_code == 1:
                    scheduled_uptime += (item.end_time -
                                         item.start_time).seconds*1000
        else:
            # 如果没有小时中间的状态，打本班次该小时前的最近状态
            state_list = session.query(shift_state_log).filter(
                shift_state_log.machine_id == machine_id,
                shift_state_log.shift_date == check_shift_info[
                    'shift_date'],
                shift_state_log.shift_code == check_shift_info[
                    'shift'],
                shift_state_log.start_time <= previous_hour_start).order_by(shift_state_log.start_time.desc()).first()
            if state_list:
                sum_code = json.loads(
                    r.hget('STATEDES', f'ID{state_list.machine_state_des}'))['is_output']
                if sum_code == 1:
                    scheduled_uptime = 3600000
        session.rollback()
        computed_uptime = std_hour_data['uptime'] if std_hour_data else 0
        uptime = scheduled_uptime
        if scheduled_uptime == 0 and computed_uptime > 300000:
            uptime = computed_uptime
        elif scheduled_uptime > 0 and computed_uptime > 0 and computed_uptime > scheduled_uptime:
            uptime = computed_uptime
        if abs(uptime-3600000) < 600000 or uptime > 3600000:
            uptime = 3600000
        session.query(hourly_output).filter(
            hourly_output.machine_id == machine_id,
            hourly_output.recorddate == shift_check_time.date(),
            hourly_output.hourid == hour_index).update({
                'uptime': uptime
            })
        session.commit()
        # 将本班次的产量汇总到班次记录表
        # 取计划停机时间汇总
        state_list = session.query(shift_state_log).filter(
            shift_state_log.machine_id == machine_id,
            shift_state_log.shift_date == check_shift_info[
                'shift_date'],
            shift_state_log.shift_code == check_shift_info[
                'shift'],
            shift_state_log.start_time >= check_shift_info[
                'start_time'],
            shift_state_log.start_time < check_shift_info['end_time']).order_by(shift_state_log.start_time.asc()).all()
        planned_stop = 0
        if len(state_list) == 0:
            planned_stop = 0
        elif len(state_list) > 0:
            state_list[-1].end_time = previous_hour_end
            for item in state_list:
                sum_code = json.loads(
                    r.hget('STATEDES', f'ID{item.machine_state_des}'))['is_scheduled_stop']
                if sum_code == 1:
                    planned_stop += (item.end_time -
                                     item.start_time).seconds*1000
        session.rollback()
        # 取uptime时间汇总,如果是共模料号，uptime需要除以模穴数
        total_uptime = 0  # 总稼动时间
        std_time = 0  # 标准生产时间
        total_output = 0  # 总产量
        std_output = 0  # 总标准产量
        hour_border = get_shift_hour(
            check_shift_info['shift'])
        hour_list = session.query(hourly_output).filter(
            hourly_output.machine_id == machine_id,
            hourly_output.recorddate == shift_check_time.date(),
            hourly_output.hourid >= hour_border['start_hour'],
            hourly_output.hourid < hour_index+1).order_by(hourly_output.hourid.asc()).all()
        for hour_id in range(hour_border['start_hour'], hour_index+1):
            cavity = 0
            found = False  # 增加标识位，提升遍历效率
            for hour_item in hour_list:
                if hour_item.hourid == hour_id:
                    cavity += 1
                    found = True
                else:
                    if found:
                        break
            found = False
            for hour_item in hour_list:
                if hour_item.hourid == hour_id:
                    total_uptime += hour_item.uptime/cavity if cavity > 0 else 0
                    std_time += hour_item.routing / \
                        (cavity if cavity > 0 else 1)*hour_item.output*1000
                    total_output += hour_item.output
                    std_output += (hour_item.uptime/cavity if cavity >
                                   0 else 0)/hour_item.routing/1000
                    print("模穴数", cavity, "uptime", hour_item.uptime,
                          "std_output", std_output)

                    found = True
                else:
                    if found:
                        break
        # 取班次不良数据
        total_defect = session.query(func.sum(defect.quantity)).filter(defect.machine_id == machine_id,
                                                                       defect.recorddate == shift_check_time.date(),
                                                                       defect.hourid >= hour_border['start_hour'],
                                                                       defect.hourid < hour_index+1).scalar()
        # 开始写班次库，班次整点写在mysql中，班次中间的产量写在redis中
        if check_time.hour in [0, 8, 16]:
            session.query(shift_info).filter(
                shift_info.machine_id == machine_id,
                shift_info.proddate == check_shift_info[
                    'shift_date'],
                shift_info.shift == check_shift_info['shift']).update({
                    'planned_stop': planned_stop,
                    'uptime': total_uptime,
                    'stdtime': std_time,
                    'total_output': total_output,
                    'defect': total_defect})
            session.commit()
        else:
            refresh_data = {
                "prodate": str(check_shift_info['shift_date']),
                "shift": check_shift_info['shift'],
                "planned_stop": planned_stop,
                "uptime": total_uptime,
                "stdtime": std_time,
                "total_output": total_output,
                "std_output": std_output,
                "defect": 0 if total_defect is None else total_defect
            }
            r.hset('SHIFTINFO', f'IM{machine_id}', json.dumps(
                refresh_data))


def check_hourly_output():
    res = r.hgetall('PN')
    if res:
        for key, value in res.items():
            # 确认小时产量
            machine_id = key.replace("IM", '')
            check_time = datetime.now()
            T_check_hourly_output = threading.Thread(
                name='check_hourly_output', target=func_check_hourly_output, args=(machine_id, check_time))
            T_check_hourly_output.start()

# 将resdis中缓存的当班的数据移动到mysql中


def update_shift_data(key, value):
    with Connection() as connection:
        machine_id = key.replace("IM", '')
        obj_shift = json.loads(value)
        session = connection.get_session()
        new_shift = shift_info(machine_id=machine_id, proddate=obj_shift['prodate'], shift=obj_shift['shift'],
                               planned_stop=obj_shift['planned_stop'], uptime=obj_shift['uptime'], stdtime=obj_shift['stdtime'], total_output=obj_shift['total_output'], defect=obj_shift['defect'], update_time=datetime.now())
        session.add(new_shift)
        session.commit()


# 班次交接节点，延续班次状态
def update_shift_status(key, shift):
    with Connection() as connection:
        machine_id = key.replace("IM", '')
        session = connection.get_session()
        datestr = str(datetime.now())
        last_shift_index = get_shift_time(
            find_shift_bytime(datestr)['shift_date'], shift, -1)
        this_shift_index = get_shift_time(
            find_shift_bytime(datestr)['shift_date'], shift)
        last_shift_item = session.query(
            shift_state_log).filter(
                shift_state_log.start_time < last_shift_index['end_time'],
                shift_state_log.machine_id == machine_id).order_by(desc(shift_state_log.start_time)).first()
        if last_shift_item:
            last_shift_item.end_time = find_shift_bytime(
                str(last_shift_item.start_time))['end_time']
            last_shift_item.update_mode = 1
        session.add(shift_state_log(
            machine_id=last_shift_item.machine_id,
            shift_date=this_shift_index['shift_date'],
            shift_code=shift,
            machine_state=last_shift_item.machine_state if last_shift_item else 4,
            machine_state_des=last_shift_item.machine_state_des if last_shift_item else 8,
            start_time=this_shift_index['start_time'],
            update_mode=1))
        session.commit()

# 每个小时检查上个小时节拍是否有遗漏,检查完成后将相应的小时数据刷新到mysql中


def check_last_hour_cycle():
    res = r.hgetall('LASTRUN')
    if res:
        for key, value in res.items():
            # 检查各个机台上个小时的节拍是否有遗漏
            machine_id = key.replace("IM", '')
            # 获取当前时间
            now = datetime.now()
            # 获取前一个整点小时的时间
            previous_hour = now - timedelta(hours=1)
            previous_hour_start = previous_hour.replace(
                minute=0, second=0, microsecond=0)
            previous_hour_end = previous_hour_start + timedelta(hours=1)

            # 将时间转换为毫秒时间戳
            previous_start_timestamp = int(
                previous_hour_start.timestamp() * 1000)-600000  # 开始检查的时间往前推10分钟，避免出现小时整点检查不到
            previous_end_timestamp = int(previous_hour_end.timestamp() * 1000)

            T_update_shift_data = threading.Thread(
                name='update_shift_data', target=fix_broken_cycle, args=(r, machine_id, previous_start_timestamp, previous_end_timestamp))
            T_update_shift_data.start()


# 获取"Asia/Shanghai"时区
tz = pytz.timezone('Asia/Hong_Kong')
# 设置系统时区
datetime.now(tz).astimezone().tzinfo


# 创建调度器对象
scheduler = BlockingScheduler()

# 添加定时任务，每天0点，8点及16点执行班次刷新程序
scheduler.add_job(refresh, 'cron', hour=8, minute=0, second=0, args=['A'])
scheduler.add_job(refresh, 'cron', hour=16, minute=0, second=0, args=['B'])
scheduler.add_job(refresh, 'cron', hour=0, minute=0, second=0, args=['C'])

# 每个整点30s后，检查上个小时的节拍是否有遗漏
scheduler.add_job(check_last_hour_cycle, 'cron', minute=0, second=30)

# 每个整点60秒后，将确认上个小时的产量
scheduler.add_job(check_hourly_output, 'cron', minute=0, second=59)


# 启动调度器
scheduler.start()

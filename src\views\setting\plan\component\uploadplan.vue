<script setup lang="ts">
import { ref } from "vue";
const fileList = ref([]);
</script>

<template>
  <div>
    <el-form ref="sopFormref" class="sop_form" :label-width="100">
      <el-form-item prop="pdfnew" label="上传文件:">
        <el-upload
          ref="upload"
          class="myupload"
          action="testtest"
          accept=".xlsx"
          :limit="1"
          :file-list="fileList"
          :auto-upload="false"
          list-type="text"
        >
          <el-button type="primary" round>上传EXCEL计划</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-upload-list__item-file-name) {
  overflow: visible;
}
</style>

import openpyxl
import json
import base64
# 导入腾讯AI api
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.ocr.v20181119 import ocr_client, models

# 定义函数


def excelFromPictures(picture):
    try:
        with open(picture, "rb") as f:
            img_data = f.read()
        img_base64 = base64.b64encode(img_data)
        cred = credential.Credential("AKIDUL5UeX1zZ4HItGqFLIacP0LQFKGx3RLy",
                                     "bxNWLvGOIwKn02qNVLuPgs93mMlu9CxV")  # ID和Secret从腾讯云申请
        httpProfile = HttpProfile()
        httpProfile.endpoint = "ocr.tencentcloudapi.com"

        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        client = ocr_client.OcrClient(cred, "ap-shanghai", clientProfile)

        req = models.RecognizeTableOCRRequest()
        params = '{"ImageBase64":"' + str(img_base64, 'utf-8') + '"}'
        req.from_json_string(params)
        resp = client.RecognizeTableOCR(req)

    except TencentCloudSDKException as err:
        print(err)
        return
    result1 = json.loads(resp.to_json_string())
    wb = openpyxl.Workbook()
    ws = wb.create_sheet(title='识别内容页在这里', index=0)
    print(result1['TableDetections'][0]['Cells'])
    for item in result1['TableDetections'][0]['Cells']:
        text = item['Text']
        print(text)
        c = item['ColTl']+2
        r = item['RowTl']+2
        ws.cell(row=r, column=c).value = text
    wb.save(picture[:picture.rfind('.')]+'.xlsx')


# excelFromPictures("../../app/static/welean/services/3dc5557dad541d95.jpg")

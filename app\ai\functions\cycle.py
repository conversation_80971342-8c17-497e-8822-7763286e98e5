import math
from app.im.functions.response import responsePost, responseError
import pandas as pd


def euclidean_distance(x, y):
    """计算两点之间的欧几里得距离"""
    # 确保x和y是可迭代的
    if not isinstance(x, (list, tuple)):
        x = [x]
    if not isinstance(y, (list, tuple)):
        y = [y]

    return math.sqrt(sum([(a - b) ** 2 for a, b in zip(x, y)]))


def group_by_similarity(data, threshold):
    """根据相似度阈值对数据进行分组"""
    groups = []  # 存储分组结果
    for item in data:
        added_to_group = False
        for group in groups:
            # 检查当前项是否可以加入现有的任何一个组
            if any(euclidean_distance(item, existing_item) <= threshold for existing_item in group):
                group.append(item)
                added_to_group = True
                break
        if not added_to_group:
            # 如果当前项没有加入任何一个现有组，则创建一个新的组
            groups.append([item])
    return groups


def tolerance(data):
    data = [x for x in data if not (x > 360000 or x < 2000)]
    if len(data) == 0:
        return 1000
    avg = sum(data)/len(data)
    if avg < 60000:
        return 1000
    elif avg < 120000 and avg >= 60000:
        return 3000
    elif avg < 180000 and avg >= 120000:
        return 5000
    elif avg >= 180000:
        return 8000


def get_sample_cycle_data(rs, machine_id, start_timestamp, end_timestamp):
    """获取指定机器在指定时间段内的注塑和下料时间样本"""
    data = rs.ts().range(f'CYCLE:IM{machine_id}',
                         start_timestamp, end_timestamp)
    if data:
        if data[0][1] == 1:
            # 如果第一个是开模，则将将前一个在10分钟内的节拍拼接到数据中
            patch_cycle = rs.ts().range(
                f'CYCLE:IM{machine_id}', start_timestamp-600000, start_timestamp)
            if patch_cycle:
                if int(patch_cycle[-1][1]) == 2:
                    data = patch_cycle[-1:] + data
    if len(data) < 6:
        return False  # "样本不足，无法分析"
    # 样本分组数据列表，列表中第一个子列表为注塑时间样本，第二个子列表为手工时间样本,第三个为异常时间样本
    seed_group = [[], [], []]
    for index in range(len(data)-1):
        if data[index][1] != data[index+1][1]:
            seed_group[int(data[index][1] -
                       1)].append(data[index+1][0]-data[index][0])
        else:
            seed_group[2].append(data[index+1][0]-data[index][0])
    uptime_hand = sum(x for x in seed_group[0] if 3000 < x < 30000)
    seed_group[0] = group_by_similarity(seed_group[0], 1000)
    seed_group[1] = group_by_similarity(
        seed_group[1], tolerance(seed_group[1]))
    arr_hand_time = max(seed_group[0], key=len)
    arr_mold_time = max(seed_group[1], key=len)
    uptime = uptime_hand+sum(seed_group[1][0])

    std_time = [sum(arr_mold_time)/len(arr_mold_time),
                sum(arr_hand_time)/len(arr_hand_time)]
    if std_time[0] < 5000 or std_time[0] > 600000:
        return False  # "注塑时间样本异常，不适合分析"
    if std_time[1] < 2000 or std_time[1] > 300000:
        return False  # "下料时间样本异常，不适合分析"
    return {"data": data, "std_time": std_time, "std_count": len(arr_mold_time), "uptime": uptime}


# 根据设备号和时间段获取并分析节拍数据，并尝试修复遗漏节拍


def fix_broken_cycle(rs, machine_id, start_timestamp, end_timestamp):
    res = get_sample_cycle_data(rs, machine_id, start_timestamp, end_timestamp)
    if res:
        data, std_time = res["data"], res["std_time"]
    else:
        return
    # 遍历时间样本，根据标准时间判断是否存在正常遗漏节拍
    for i in range(len(data)-1):
        inject_ts = data[i][0]  # 静态化初始插入的时间戳
        if data[i][1] != data[i+1][1]:
            if abs(data[i+1][0]-data[i][0] - std_time[int(data[i+1][1] - 1)]) < 1000:
                continue
            else:
                # 补全遗漏节拍
                if data[i+1][1] == 1:  # 前一个节拍是合模，后一个节拍是开模，并且时间超出容差值1秒
                    if abs(data[i+1][0]-data[i][0] -
                           std_time[0]) % sum(std_time) < 2000:
                        # 计算出丢了多少个节拍
                        missing_cycle_count = int(
                            abs(data[i+1][0]-data[i][0] - std_time[0]) // sum(std_time))
                        for inject_count in range(missing_cycle_count):
                            fix_cycle_record(rs,
                                             f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[0])} 1')
                            inject_ts += std_time[0]
                            fix_cycle_record(rs,
                                             f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[1])} 2')
                            inject_ts += std_time[1]
                elif data[i+1][1] == 2:  # 前一个节拍是开模，后一个节拍是合模，并且时间超出容差值1秒
                    if abs(data[i+1][0]-data[i][0] -
                           std_time[1]) % sum(std_time) < 2000:
                        # 计算出丢失了多少个节拍
                        missing_cycle_count = int(
                            abs(data[i+1][0]-data[i][0] - std_time[1]) // sum(std_time))
                        for inject_count in range(missing_cycle_count):
                            fix_cycle_record(rs,
                                             f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[1])} 2')
                            inject_ts += std_time[1]
                            fix_cycle_record(rs,
                                             f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[0])} 1')
                            inject_ts += std_time[0]
        elif data[i][1] == data[i+1][1]:  # 如果相邻两个节拍是同一个类型，中间肯定出现丢失的节拍
            if abs(data[i+1][0]-data[i][0]) <= sum(std_time):
                if abs(abs(data[i+1][0]-data[i][0]) - sum(std_time)) < 2000:  # 如果中间只丢失了1个节拍
                    fix_cycle_record(rs,
                                     f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[dict({1: 1, 2: 0})[int(data[i][1])]])} {dict({1: 2, 2: 1})[int(data[i][1])]}')
            else:
                missing_cycle_count = int(
                    abs(data[i+1][0]-data[i][0]) // sum(std_time))
                if data[i+1][1] == 1:  # 如果相邻两个节拍都是开模
                    if abs(data[i+1][0]-data[i][0]) % sum(std_time) < 2000:
                        for inject_count in range(missing_cycle_count-1):
                            fix_cycle_record(rs,
                                             f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[1])} 2')
                            inject_ts += std_time[1]
                            fix_cycle_record(rs,
                                             f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[0])} 1')
                            inject_ts += std_time[0]
                        fix_cycle_record(rs,
                                         f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[1])} 2')
                elif data[i+1][1] == 2:  # 如果相邻两个节拍都是合模
                    if abs(data[i+1][0]-data[i][0]) % sum(std_time) < 2000:
                        for inject_count in range(missing_cycle_count-1):
                            fix_cycle_record(rs,
                                             f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[0])} 1')
                            inject_ts += std_time[0]
                            fix_cycle_record(rs,
                                             f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[1])} 2')
                            inject_ts += std_time[1]
                        fix_cycle_record(rs,
                                         f'TS.ADD CYCLE:IM{machine_id} {int(inject_ts+std_time[0])} 1')
    return 'Finished'


def fix_cycle_record(rs, cmd):
    rs.execute_command(cmd)
    ...

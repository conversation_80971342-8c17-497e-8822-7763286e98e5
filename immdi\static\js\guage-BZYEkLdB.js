var g=Object.defineProperty;var u=Object.getOwnPropertySymbols;var x=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var f=(l,t,e)=>t in l?g(l,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[t]=e,p=(l,t)=>{for(var e in t||(t={}))x.call(t,e)&&f(l,e,t[e]);if(u)for(var e of u(t))m.call(t,e)&&f(l,e,t[e]);return l};var d=(l,t,e)=>new Promise((c,i)=>{var a=o=>{try{s(e.next(o))}catch(r){i(r)}},n=o=>{try{s(e.throw(o))}catch(r){i(r)}},s=o=>o.done?c(o.value):Promise.resolve(o.value).then(a,n);s((e=e.apply(l,t)).next())});import{d as v,am as y,p as h,n as _,aY as S,q as w,o as C,c as k,e as V,_ as D}from"./index-BnxEuBzx.js";const z={class:"gauge-container"},B=v({__name:"guage",props:{title:{default:"T1"},actualValue:{default:250},stdValue:{default:1},lower:{default:123},upper:{default:200}},setup(l){const{isDark:t}=y(),e=h(()=>t.value?"dark":"light"),c=_(),{setOptions:i}=S(c,{theme:e}),a=l,n=_(16),s=h(()=>({color:"var(--text-color)"}));return w(()=>d(this,null,function*(){i({graphic:[{type:"text",left:"center",top:"86%",style:p({text:"标准范围 "+a.lower+" ℃ ~ "+a.upper+" ℃。",fontSize:16,fill:t.value?"#fff":"#000"},s.value)}],title:{text:a.title,left:"center",top:0,textStyle:{fontSize:18,fontWeight:"bold"}},series:[{type:"gauge",max:300,min:0,axisLine:{lineStyle:{width:n.value,color:[[a.lower/300,"#FAC858"],[a.upper/300,"#91CC75"],[1,"#FD666D"]]}},pointer:{itemStyle:{color:a.actualValue<a.lower?"#FAC858":a.actualValue<a.upper?"#91CC75":"#FD666D"}},axisTick:{distance:-n.value,length:8,lineStyle:{color:"#fff",width:2}},splitLine:{distance:-n.value,length:20,lineStyle:{color:"#fff",width:2}},axisLabel:{color:"inherit",distance:n.value,fontSize:12},detail:{valueAnimation:!0,formatter:"{value} ℃",color:"inherit",offsetCenter:[0,"30%"],fontSize:18},data:[{value:a.actualValue}]}]})})),(o,r)=>(C(),k("div",z,[V("div",{ref_key:"chartRef",ref:c,class:"gauge_chart",style:{width:"250px",height:"240px"}},null,512)]))}}),L=D(B,[["__scopeId","data-v-0da4384d"]]);export{L as default};

<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Refresh from "@iconify-icons/ep/refresh";
import shiftdata from "@/views/components/hourlydata/index.vue";
import { ref, reactive, watch } from "vue";
import moment from "moment";
import { getmachineidbyoutput } from "@/api/dashboard";

const refreshkey = ref(true);

const shortcuts = [
  {
    text: "今天",
    value: new Date()
  },
  {
    text: "昨天",
    value: () => {
      const date = new Date();
      date.setTime(date.getTime() - 3600 * 1000 * 24);
      return date;
    }
  },
  {
    text: "一周前",
    value: () => {
      const date = new Date();
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
      return date;
    }
  }
];

const machines = ref([]);

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const search_condition = reactive({
  selecteddate: moment().format("YYYY-MM-DD"),
  machine: 5
});

watch(
  () => search_condition.selecteddate,
  newValue => {
    getmachineid(newValue);
  }
);

const getmachineid = (querydate: string) => {
  getmachineidbyoutput({ selecteddate: querydate }).then(
    (res: { data: any }) => {
      machines.value = res.data.machine_list;
    }
  );
};
</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" class="search-form bg-bg_color">
      <el-form-item label="日期：">
        <el-date-picker
          v-model="search_condition.selecteddate"
          type="date"
          placeholder="选择生产日期"
          :disabled-date="disabledDate"
          :shortcuts="shortcuts"
          :clearable="false"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          class="selectdate"
        />
      </el-form-item>
      <el-form-item label="机台号">
        <el-select
          class="selectmachine"
          v-model="search_condition.machine"
          placeholder="选择注塑机台"
        >
          <el-option
            v-for="item in machines"
            :key="item"
            :label="item + '号机'"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Refresh)"
          @click="refreshkey = !refreshkey"
          >刷新
        </el-button>
      </el-form-item>
    </el-form>
    <div class="hourlyview">
      <div class="item">
        <shiftdata
          :search_condition="search_condition"
          shift="A"
          :key="refreshkey"
        />
      </div>
      <div class="item">
        <shiftdata
          :search_condition="search_condition"
          shift="B"
          :key="refreshkey"
        />
      </div>
      <div class="item">
        <shiftdata
          :search_condition="search_condition"
          shift="C"
          :key="refreshkey"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.search-form {
  margin-bottom: 6px;
  padding: 6px 0 6px 10px;
}
.el-form {
  display: flex;
}
.el-form-item {
  margin-bottom: 0;
  padding: 0;
}

.hourlyview {
  display: flex;
  flex-direction: row;
  flex: 1;
  justify-content: space-between;
  .item {
    width: 550px;
  }
}

.selectmachine {
  min-width: 150px;
}
</style>

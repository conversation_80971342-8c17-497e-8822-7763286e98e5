from config import config, env
from extensions import db
from itsdangerous import TimedJSONWebSignatureSerializer as Serializer
from functools import wraps
from flask import request
import traceback
from sqlalchemy import func, or_
import os
import shutil
from app.public.functions import responseExpire, responseError
from ultils.log_helper import ProjectLogger
from app.carbonmix.model.models_cm import User
mylogger = ProjectLogger()


def getServer():  # 获取上传文件的访问路径
    urls = {
        'basePath': config[env].localPath+"carbonmix/",
        'baseUrl': config[env].base_url+'carbonmix/',
    }
    return urls


def storeFile(id, new):
    tempFile = getServer()['uploadPath']+'temp/'+new
    storePath = getServer()['uploadPath']+'store/'+str(id)+'/'
    if not os.path.exists(storePath):
        os.mkdir(storePath)
    shutil.move(tempFile, storePath)


def login_required(view_func):  # 通过一个装饰函数来实现登录验证
    @wraps(view_func)
    def verify_token(*args, **kwargs):
        mylogger.info("*"*20+"verify_token start"+"*"*20)
        # mylogger.info("*"*20+request.headers["roles"]+"*"*20)
        try:
            token = request.headers["token"]
            roles = request.headers["roles"].split(',')
            user = db.session.query(User).filter(
                User.token == token).first()
            # mylogger.debug(user.role)
            # mylogger.debug(user.eid)
            if user.role not in roles:
                return responseExpire('没有足够的权限！')
        except Exception:
            traceback.print_exc()
            return responseError('登录失败！')

        s = Serializer(config[env].SECRET_KEY)
        try:
            s.loads(token)
        except Exception:
            return responseExpire('登录已过期！请重新登录！')

        return view_func(*args, **kwargs)

    return verify_token

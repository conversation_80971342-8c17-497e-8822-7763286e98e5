import { addDialog } from "@/components/ReDialog";
import editDataForm from "./editDataForm.vue";
import { updatehourdata } from "@/api/front";
import { useProdStoreHook } from "@/store/modules/prod";
import { message } from "@/utils/message";
import { FormItemProps } from "./types";
import { h, ref, toRaw } from "vue";

const formRef = ref();

type CallbackFunction = () => void;
export function changehourdata(
  action: any,
  machine_id: any,
  row?: any,
  fun_callback?: CallbackFunction
) {
  addDialog({
    title: (action == "edit" ? "调整" : "新增") + machine_id + "#机小时数据",
    props: {
      shiftinfo: {
        selecteddate: useProdStoreHook().selectedDate,
        shift: useProdStoreHook().shift,
        machineid: useProdStoreHook().machineId
      },
      action: action,
      formInline: {
        hourid: action == "edit" ? row?.hourid ?? "" : 0,
        pn: action == "edit" ? row?.pn ?? "" : "",
        output: action == "edit" ? row?.output ?? "" : 0,
        adjustion: action == "edit" ? row?.adjustion ?? "" : 0
      }
    },
    width: "30%",
    draggable: true,
    fullscreenIcon: false,
    closeOnClickModal: false,
    contentRenderer: () => h(editDataForm),
    beforeSure: async (done, { options }) => {
      const raw_data = {
        hourid: action == "edit" ? row?.hourid ?? "" : 0,
        pn: action == "edit" ? row?.pn ?? "" : "",
        output: action == "edit" ? row?.output ?? "" : 0,
        adjustion: action == "edit" ? row?.adjustion ?? "" : 0
      };
      if (
        JSON.stringify(toRaw(options.props.formInline)) !=
        JSON.stringify(raw_data)
      ) {
        console.log(toRaw(options.props));
        updatehourdata(toRaw(options.props)).then((res: { meta: any }) => {
          if (res.meta.status == 201) {
            fun_callback();
            message("变更成功！", { customClass: "el", type: "success" });
            done();
          }
        });
      } else {
        done();
      }
      // if (options.props.formInline != row) {
      //   const res = (await changepn({
      //     selecteddate: options.props.PNData.machine_id
      //   })) as { meta: any };
      //   if (res.meta.status != 201) {
      //     message("变更状态失败", { customClass: "el", type: "error" });
      //   } else {
      //     done();
      //     message("变更状态成功", { customClass: "el", type: "success" });
      //     fun_callback();
      //   }
      // } else {
      //   done();
      // }
    }
  });
}

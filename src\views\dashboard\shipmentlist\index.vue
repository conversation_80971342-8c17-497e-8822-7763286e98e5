<script setup lang="ts">
import { ref, onMounted, toRef } from "vue";
import { useColumns } from "./utils/columns";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useRoute } from "vue-router";
import {
  Plus,
  Minus,
  Refresh,
  CircleCheckFilled
} from "@element-plus/icons-vue";
const tableRef = ref(null);

const formRef = ref();
defineOptions({
  name: "ShipmentList"
});

const rowdata = ref([]);

const {
  operation_flag,
  search_condition,
  loading,
  columns,
  loadingConfig,
  dataList,
  adaptiveConfig,
  getList,
  openReleaseForm,
  showMouseMenu,
  selectSameShipmentAddress
} = useColumns();

const route = useRoute();
onMounted(() => {});

const handleSelect = selection => {
  rowdata.value = selection;
  if (selection.length > 0) {
    // 如果selection数组中的receiver_address都相同，并且status都为1，则可以进行释放操作
    const receiver_address = selection[0].receiver_address;
    const can_release = selection.every(
      item => item.receiver_address === receiver_address && item.status == 1
    );
    operation_flag.can_release = can_release;
  } else {
    operation_flag.can_release = false;
  }
};

const shortcuts = [
  {
    text: "上周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 7);
      return [start, end];
    }
  },
  {
    text: "上月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 1);
      return [start, end];
    }
  },
  {
    text: "过月3个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 3);
      return [start, end];
    }
  }
];
</script>

<template>
  <div>
    <el-form ref="formRef" :inline="true" class="search-form bg-bg_color">
      <el-form-item label="放单时间">
        <el-date-picker
          v-model="search_condition.daterange"
          type="datetimerange"
          :shortcuts="shortcuts"
          range-separator="To"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="DN单号">
        <el-input
          v-model="search_condition.dn_number"
          placeholder="请输入DN单号"
          clearable
        />
      </el-form-item>
      <el-form-item label="工单状态：" prop="status">
        <el-select
          v-model="search_condition.status"
          placeholder="选择工单状态"
          clearable
          class="!w-[180px]"
        >
          <el-option label="新建工单" :value="1" />
          <el-option label="工单释放" :value="2" />
          <el-option label="仓库拣料" :value="3" />
          <el-option label="物流对接" :value="4" />
          <el-option label="工单结束" :value="5" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :icon="Plus"> 录单 </el-button>
      </el-form-item>
      <el-form-item v-show="operation_flag.can_release">
        <el-button
          type="warning"
          :icon="CircleCheckFilled"
          @click="openReleaseForm(rowdata)"
        >
          释放
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" :icon="Minus"> 删除 </el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :icon="Refresh" @click="getList()">
          刷新
        </el-button>
      </el-form-item>
    </el-form>

    <pure-table
      ref="tableRef"
      border
      stripe
      adaptive
      :adaptiveConfig="adaptiveConfig"
      row-key="id"
      alignWhole="center"
      showOverflowTooltip
      :loading="loading"
      :loading-config="loadingConfig"
      :data="dataList"
      :columns="columns"
      @select="handleSelect"
      @select-all="handleSelect"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-table__row) {
  background-color: transparent;
}

:deep(.el-card__body) {
  padding: 0;
}
:deep(td) {
  padding: 4px 0;
}

.search-form {
  padding: 6px 0 6px 10px;
}
.el-form {
  display: flex;
  .el-form-item {
    margin-bottom: 0;
  }
}

:deep(el-dialog) {
  height: "50%";
}
</style>

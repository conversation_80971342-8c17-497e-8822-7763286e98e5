<script setup lang="ts">
import { onMounted } from "vue";
import { useColumns } from "./utils/column";

const {
  search_condition,
  search,
  loading,
  columns,
  loadingConfig,
  dataList,
  adaptiveConfig
} = useColumns();

onMounted(() => {
  loading.value = false;
});
</script>

<template>
  <div>
    <el-form :inline="true" class="search-form bg-bg_color">
      <el-form-item label="处理日期" />
      <el-date-picker
        type="date"
        v-model="search_condition.selected_date"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
        placeholder="点击选择日期"
      />
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
      </el-form-item>
    </el-form>

    <pure-table
      ref="tableRef"
      border
      :adaptiveConfig="adaptiveConfig"
      row-key="id"
      alignWhole="center"
      showOverflowTooltip
      :loading="loading"
      :loading-config="loadingConfig"
      :data="dataList"
      :columns="columns"
    />
  </div>
</template>

<style lang="scss" scoped>
.search-form {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .extra_info {
    font-size: 14px;
  }
  .el-form-item {
    display: flex;
    margin: 8px 10px;
  }
}
</style>

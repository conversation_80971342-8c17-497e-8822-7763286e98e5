	<?php
include("coon.php");
$year=$_GET['year'];
if(!$year){
  $year=2023;
}
$nextyear=$year+1;
$sql3 ="select status,duedate,cfdate, dept1 from wl_suggest left join wl_userinfo
on wl_suggest.exeid=wl_userinfo.eid where dept1 is not null and wl_suggest.plant='SZ' and idate between '{$year}-01-01' and '{$nextyear}-01-01'";
$query=mysqli_query($link, $sql3);
$a=[];
$a['ALL']['closed']=0;
$a['ALL']['ongoing']=0;
$a['ALL']['pastdue']=0;
$a['ALL']['cancel']=0;
if (mysqli_num_rows($query) >= 1) {
    while ($rs = mysqli_fetch_array($query)) {
        $status=$rs['status'];
        $duedate=$rs['duedate'];
        $cfdate=$rs['cfdate'];
        $closed=($status=='closed')?1:0;
        $ongoing=0;
        $pastdue=0;
        $dept=$rs['dept1'];
        $cancel=($status=='cancel')?1:0;
        if (!$cfdate) {
            $cfdate=$duedate;
        }

        if ($status=='ongoing' && $cfdate<date("Y-m-d")) {
            $pastdue=1;
        } elseif ($status=='ongoing' && $cfdate>=date("Y-m-d")) {
            $ongoing=1;
        }

        if (array_key_exists($dept, $a)) {
            $a[$dept]['closed']=$a[$dept]['closed']+$closed;
            $a[$dept]['ongoing']=$a[$dept]['ongoing']+$ongoing;
            $a[$dept]['pastdue']=$a[$dept]['pastdue']+$pastdue;
            $a[$dept]['cancel']=$a[$dept]['cancel']+$cancel;
        } else {
            $a[$dept]=array('closed'=>$closed,'ongoing'=>$ongoing,'pastdue'=>$pastdue,'cancel'=>$cancel);
        }
        $a['ALL']['closed']=$a['ALL']['closed']+$closed;
        $a['ALL']['ongoing']=$a['ALL']['ongoing']+$ongoing;
        $a['ALL']['pastdue']=$a['ALL']['pastdue']+$pastdue;
        $a['ALL']['cancel']=$a['ALL']['cancel']+$cancel;
    }
}
foreach ($a as $key=>$val) {
    $outArr['dept'][]=$key;
    $outArr['closed'][]=$val['closed'];
    $outArr['cancel'][]=$val['cancel'];
    $outArr['ongoing'][]=$val['ongoing'];
    $outArr['pastdue'][]=$val['pastdue'];
    if ($val['closed']+$val['pastdue']>0) {
        $outArr['rate'][]=round($val['closed']/($val['closed']+$val['pastdue'])*100);
    } else {
        $outArr['rate'][]='-';
    }
}

print_r(json_encode($outArr, JSON_UNESCAPED_UNICODE));

?>
	
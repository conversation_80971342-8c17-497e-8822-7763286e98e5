import{d as p,n as c,r as e,o as r,c as f,b as t,h as o,f as d,_ as m}from"./index-BnxEuBzx.js";const u=p({__name:"uploadplan",setup(i){const l=c([]);return(x,b)=>{const a=e("el-button"),s=e("el-upload"),n=e("el-form-item"),_=e("el-form");return r(),f("div",null,[t(_,{ref:"sopFormref",class:"sop_form","label-width":100},{default:o(()=>[t(n,{prop:"pdfnew",label:"上传文件:"},{default:o(()=>[t(s,{ref:"upload",class:"myupload",action:"testtest",accept:".xlsx",limit:1,"file-list":l.value,"auto-upload":!1,"list-type":"text"},{default:o(()=>[t(a,{type:"primary",round:""},{default:o(()=>[d("上传EXCEL计划")]),_:1})]),_:1},8,["file-list"])]),_:1})]),_:1},512)])}}}),v=m(u,[["__scopeId","data-v-b27c1eb7"]]);export{v as default};

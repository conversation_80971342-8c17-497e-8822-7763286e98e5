import sys
import os
import traceback
import pymysql
from datetime import datetime
from apscheduler.schedulers.background import BlockingScheduler
from scripts.excelapi import excelTrans
from scripts.pictoexcel import excelFromPictures
from scripts.taskschedule import createTask,deleteSuggestOverdue
from models.public import session, Scriptlog


def officeTools():
    picToExcel()
    translate()


def myTask():
    status = 'success'
    start = datetime.now()
    fin = 0
    neo = 0
    try:
        status, fin, neo = createTask()
    except Exception:
        status = 'failed'
        traceback.print_exc()
    finish = datetime.now()
    logs = '强制关闭项目类别：'+str(fin)+'个，新建项目：'+str(neo)+'个'
    newlog = Scriptlog(starttime=start, finishtime=finish, due=(finish-start).seconds,
                       script='Layeredjob', status=status, logs=logs)
    session.add(newlog)
    session.commit()

def mydeleteSuggestOverdue():
    status = 'success'
    start = datetime.now()
    try:
        deleteSuggestOverdue()
    except Exception:
        status = 'failed'
        traceback.print_exc()
    finish = datetime.now()
    logs = '清理完成2天以上未结系统问题'
    newlog = Scriptlog(starttime=start, finishtime=finish, due=(finish-start).seconds,
                       script='Weleansystempastdue', status=status, logs=logs)
    session.add(newlog)
    session.commit()


def picToExcel():
    eid = ''
    wxid = ''
    path = getServer()['servicesPath']
    db = pymysql.connect(host=config[myEnv].mysqlDB['host'], port=config[myEnv].mysqlDB['port'], user=config[myEnv].mysqlDB['user'],
                         password=config[myEnv].mysqlDB['password'], database=config[myEnv].mysqlDB['database'], charset=config[myEnv].mysqlDB['charset'])
    cursor = db.cursor()
    cksql = "select isactive from wl_options where cate='services' and title='pics'"
    sql = """select wl_services.Id, urls,options,wl_services.eid,wxid from wl_services left join wl_account
    on wl_services.eid=wl_account.eid where stype='picToExcel' and status=0 order by starttime limit 1"""
    try:
        cursor.execute(cksql)
        isactive = cursor.fetchone()[0]
        if isactive == 1:
            cursor.execute(sql)
            rst = cursor.fetchone()
            if rst:
                Id = rst[0]
                url = path+rst[1]
                eid = rst[3]
                wxid = rst[4]
                locksql = "update wl_options set isactive=0 where cate='services' and title='pics'"
                cursor.execute(locksql)
                excelFromPictures(url)
                resultsql = "update wl_services set finishtime='%s',status=1 where Id=%d" % (
                    datetime.now(), Id)
                cursor.execute(resultsql)
                db.commit()  # 事务提交
    except Exception:
        db.rollback()
        taskBody = {
            'content': '你的图片辨识失败',
            'owner': '工号:'+eid,
            'comments': '只能进行文字的识别'
        }
        subscribeOT(taskBody, wxid, 'pages/login/login')
        cancelsql = "update wl_services set finishtime='%s',status=3 where Id=%d" % (
                    datetime.now(), Id)
        cursor.execute(cancelsql)
    else:
        if eid and wxid:
            taskBody = {
                'content': '你的图片已经辨识完成',
                'owner': '工号:'+eid,
                'comments': '识别内容仅供参考,请自行核对'
            }
            subscribeOT(taskBody, wxid, 'pages/login/login')
    unlocksql = "update wl_options set isactive=1 where cate='services' and title='pics'"
    cursor.execute(unlocksql)
    db.commit()
    cursor.close()
    db.close()


def translate():
    translated = 0
    eid = ''
    wxid = ''
    path = getServer()['servicesPath']
    db = pymysql.connect(host=config[myEnv].mysqlDB['host'], port=config[myEnv].mysqlDB['port'], user=config[myEnv].mysqlDB['user'],
                         password=config[myEnv].mysqlDB['password'], database=config[myEnv].mysqlDB['database'], charset=config[myEnv].mysqlDB['charset'])
    cursor = db.cursor()
    cksql = "select isactive from wl_options where cate='services' and title='trans'"
    sql = """select wl_services.Id, urls,options,wl_services.eid,wxid from wl_services left join wl_account
    on wl_services.eid=wl_account.eid where stype='excelTrans' and status=0 order by starttime limit 1"""
    try:
        cursor.execute(cksql)
        isactive = cursor.fetchone()[0]
        print(isactive)
        if isactive == 1:
            cursor.execute(sql)
            rst = cursor.fetchone()
            print(rst)
            if rst:
                Id = rst[0]
                url = path+rst[1]
                lfrom = rst[2].split(',')[0]
                lto = rst[2].split(',')[1]
                tp = rst[2].split(',')[2]
                eid = rst[3]
                wxid = rst[4]
                locksql = "update wl_options set isactive=0 where cate='services' and title='trans'"
                cursor.execute(locksql)
                translated = excelTrans(url, lfrom, lto, tp)
                resultsql = "update wl_services set finishtime='%s',status=1,countresult=%d where Id=%d" % (
                    datetime.now(), translated, Id)
                cursor.execute(resultsql)
                db.commit()  # 事务提交
    except Exception as e:
        db.rollback()
        traceback.print_exc(e)
    if translated > 0 and eid and wxid:
        taskBody = {
            'content': '你的Excel已经翻译完成',
            'owner': '工号:'+eid,
            'comments': '翻译仅供参考,请自行核对'
        }
        subscribeOT(taskBody, wxid, 'pages/login/login')
    unlocksql = "update wl_options set isactive=1 where cate='services' and title='trans'"
    cursor.execute(unlocksql)
    db.commit()
    cursor.close()
    db.close()


def clearbookorder():
    db = pymysql.connect(host=config[myEnv].mysqlDB['host'], port=config[myEnv].mysqlDB['port'], user=config[myEnv].mysqlDB['user'],
                         password=config[myEnv].mysqlDB['password'], database=config[myEnv].mysqlDB['database'], charset=config[myEnv].mysqlDB['charset'])
    cursor = db.cursor()
    sql1 = "update wl_booksinfo set status='a' where status='o'"
    sql2 = "update wl_bookslog set returndate=curdate() where borrowdate is null"
    try:
        cursor.execute(sql1)
        cursor.execute(sql2)
        db.commit()
        cursor.close()
        db.close()
    except Exception:
        db.rollback()
        traceback.print_exc()


def otMessage():
    opensql = """select wl_appvowners.eid,wxid,count(cname) qty from wl_suggest left join wl_userinfo on wl_suggest.eid=wl_userinfo.eid left join wl_appvowners
            left join wl_account on wl_appvowners.eid=wl_account.eid on (wl_userinfo.dept1=wl_appvowners.dept and wl_userinfo.plant=wl_appvowners.plant)
             where status='open' group by eid"""
    actionsql = """select exeid,wxid,count(wl_suggest.Id) qty from wl_suggest left join wl_account on wl_suggest.exeid=wl_account.eid
                where status='ongoing' and (cfdate is null or cfdate<now()) group by exeid"""
    auditsql = """select eid,wxid,count(eid) qty from wl_tasks left join wl_account on (wl_tasks.owner1=wl_account.eid or wl_tasks.owner2=wl_account.eid)
            where wl_tasks.month<now() and wl_tasks.status=0 group by eid"""
    db = pymysql.connect(host=config[myEnv].mysqlDB['host'], port=config[myEnv].mysqlDB['port'], user=config[myEnv].mysqlDB['user'],
                         password=config[myEnv].mysqlDB['password'], database=config[myEnv].mysqlDB['database'], charset=config[myEnv].mysqlDB['charset'])
    cursor = db.cursor()
    openArr = []
    try:
        cursor.execute(opensql)
        result = cursor.fetchall()
        for r in result:
            openArr.append({
                'eid': r[0],
                'wxid': r[1],
                'qty': r[2],
                'type': '待批准'

            })
        cursor.execute(actionsql)
        result2 = cursor.fetchall()
        for r in result2:
            openArr.append({
                'eid': r[0],
                'wxid': r[1],
                'qty': r[2],
                'type': '提案任务'
            })
        cursor.execute(auditsql)
        result3 = cursor.fetchall()
        for r in result3:
            openArr.append({
                'eid': r[0],
                'wxid': r[1],
                'qty': r[2],
                'type': '稽核任务'
            })

    except Exception:
        db.rollback()
    db.commit()  # 事务提交
    cursor.close()
    db.close()
    openDic = {}
    for o in openArr:
        if o['eid'] in openDic.keys():
            openDic[o['eid']]['comments'] = openDic[o['eid']
                                                    ]['comments']+'/'+o['type']+':'+str(o['qty'])+'条'
        else:
            openDic[o['eid']] = {
                'eid': o['eid'],
                'wxid': o['wxid'],
                'comments': o['type']+':'+str(o['qty'])+'条'
            }
    for od in openDic.values():
        if od['wxid']:
            taskBody = {
                'content': '你有超过时限的任务未处理',
                'owner': '工号:'+od['eid'],
                'comments': od['comments']
            }
            subscribeOT(taskBody, od['wxid'], 'pages/login/login')


def purgeDownloadCache():
    path = getServer()['downloadsPath']
    ls = os.listdir(path)
    for i in ls:
        c_path = os.path.join(path, i)
        if os.path.isfile(c_path):
            abspath = os.path.abspath(c_path)
            print(abspath)
            os.remove(abspath)


if __name__ == '__main__':
    parentdir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.append(parentdir)
    if True:
        from config import config, myEnv
        from app.welean.functions import subscribeOT, getServer
    # myTask()
    # mydeleteSuggestOverdue()
    # print(ck)
    # translate()
    # officeTools()
    # picToExcel()
    # clearbookorder()
    # purgeDownloadCache()
    # otMessage()

    sched = BlockingScheduler(timezone="Asia/Shanghai")
    sched.add_job(otMessage, 'cron',  day_of_week='tue,fri', hour='12', minute='00', second='00')
    sched.add_job(clearbookorder, 'cron',  day_of_week='mon-fri',
                  hour='00', minute='01', second='00')
    sched.add_job(mydeleteSuggestOverdue, 'cron',  day_of_week='mon-sun',
                  hour='07', minute='07', second='07')
    sched.add_job(myTask, 'cron',  day_of_week='mon-fri',
                  hour='10', minute='07', second='00')
    sched.add_job(purgeDownloadCache, 'cron',  day_of_week='tue',
                  hour='15', minute='02', second='02')
    sched.add_job(officeTools, 'interval',  minutes=10)
    sched.start()

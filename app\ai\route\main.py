from flask import Blueprint,request
import requests
from config import config, env
from zhipuai import ZhipuAI
api = Blueprint('ai/generate', __name__)

@api.route('/chat', methods=['GET'])
def chat():
    chat_str=request.args.get('chat_str');
    api_url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
    authorization_token = config[env].ZHIPU_API_KEY
    
    headers = {
        'Authorization': f'Bearer {authorization_token}',
        'Content-Type': 'application/json'
    }    
    data = {
    'model': 'glm-4-flash',
    'messages':[
            {"role": "user", "content": chat_str}
        ],
    'response_format':{
        'type': 'json_object'
    }
    }
    response = requests.post(api_url, json=data,headers=headers)
    if response.status_code == 200:
        # 处理成功响应的数据
        result = response.json()
        print(result['choices'][0]['message']['content'])
        return 'ok'
    else:
        # 处理错误响应
        print(f'Error: {response.status_code} - {response.text}')
        return 'error'
    # client = ZhipuAI(api_key=config[env].ZHIPU_API_KEY)  # 请填写您自己的APIKey
    # chat_str=request.args.get('chat_str');
    # if chat_str is None:
    #     return ("输入不能为空！")
    # response = client.chat.completions.create(
    #     model="glm-4-flash",  # 请填写您要调用的模型名称
    #     messages=[
    #         {"role": "user", "content": chat_str},
    #     ],
    #     response_format={
    #         "type": "json_object" 
    #     }
    # )
    # print(response.choices[0].message.content)
    # return (response.choices[0].message.content)
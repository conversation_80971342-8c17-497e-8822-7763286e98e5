import { addDialog } from "@/components/ReDialog";
import editDataForm from "./editDefectForm.vue";
import { updatedefectdata } from "@/api/front";
import { useProdStoreHook } from "@/store/modules/prod";
import { message } from "@/utils/message";
import { h, ref, toRaw } from "vue";

const formRef = ref();

type CallbackFunction = () => void;
export function changedefectdata(
  action: any,
  machine_id: any,
  row?: any,
  fun_callback?: CallbackFunction
) {
  addDialog({
    title: (action == "edit" ? "调整" : "新增") + machine_id + "#机不良数据",
    props: {
      shiftinfo: {
        selecteddate: useProdStoreHook().selectedDate,
        shift: useProdStoreHook().shift,
        machineid: useProdStoreHook().machineId
      },
      action: action,
      formInline: {
        hourid: row?.hourid ?? "",
        pn: row?.pn ?? "",
        output: row?.output ?? "",
        unit: row?.unit ?? "EA",
        defect_qty: row?.defect_count ?? 0,
        defect_type: row?.defect_type ?? 0
      }
    },
    width: "30%",
    draggable: true,
    fullscreenIcon: false,
    closeOnClickModal: false,
    contentRenderer: () => h(editDataForm),
    beforeSure: async (done, { options }) => {
      const raw_data = {
        selected_date: useProdStoreHook().selectedDate,
        hourid: row?.hourid ?? "",
        machine_id: useProdStoreHook().machineId,
        pn: row?.pn ?? "",
        defect_qty: options.props.formInline.defect_qty,
        defect_type: options.props.formInline.defect_type,
        unit: options.props.formInline.unit
      };
      if (
        JSON.stringify(toRaw(options.props.formInline)) !=
        JSON.stringify(raw_data)
      ) {
        console.log(raw_data);
        updatedefectdata(raw_data).then((res: { meta: any }) => {
          if (res.meta.status == 201) {
            fun_callback();
            message("变更成功！", { customClass: "el", type: "success" });
            done();
          }
        });
      } else {
        done();
      }
      // if (options.props.formInline != row) {
      //   const res = (await changepn({
      //     selecteddate: options.props.PNData.machine_id
      //   })) as { meta: any };
      //   if (res.meta.status != 201) {
      //     message("变更状态失败", { customClass: "el", type: "error" });
      //   } else {
      //     done();
      //     message("变更状态成功", { customClass: "el", type: "success" });
      //     fun_callback();
      //   }
      // } else {
      //   done();
      // }
    }
  });
}

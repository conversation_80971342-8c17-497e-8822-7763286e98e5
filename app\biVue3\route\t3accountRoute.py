from flask import Blueprint,  request
import requests
from config import config, env
from app.public.functions import responseGet,  responseError
import datetime
import os
api = Blueprint('bi3/t3accountAPI', __name__)


def find_file(dept, path, url):
    i = 0
    result = []
    for root, lists, files in os.walk(path):
        for file in files:
            if dept in file:
                i = i + 1
                write = url+file
                result.append(write)
    return result


@ api.route('/getAccounts', methods=['GET'])  # 获得accountbility board的部门分类数据，产线分类
def getAccounts():
    res = request.args
    query = res.get('query')
    line = res.get('line')
    area = ''
    res = requests.get(
        config[env].cloud_url+"welean/bi/issueAPI/getAccountsSZ", params={
            'query': query,
            'line': line,
            'area': ''
        })
    content = res.json()
    issues = []
    if content["meta"]["status"] == 200:
        issues = content['data']['suggests']
    else:
        return responseError('获取问题列表失败，请尝试刷新或联系管理员')
    wkArr = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
    url = config[env].base_url+'BI/boardimg/t3/'
    path = config[env].localPath+'BI/boardimg/t3/'
    boardDept = {
        '其他': 'Site',
        'EHS': 'EHS',
        'Production': '生产',
        'Lean': 'Lean',
        'ME': '工程',
        'R&D': '研发',
        'F&M': '维修',
        'Planning': 'CS',
        'Quality': '质量',
        'M&L': '仓库'
    }
    rowDic = {}
    for b, v in boardDept.items():
        result = find_file(v, path, url)
        rowDic[b] = {
            'dept': v,
            'deptimg': result
        }
    today = datetime.date.today()
    dateArr = []
    weekArr = []
    thisyear = str(today.year)
    for i in range(14):
        dd = today+datetime.timedelta(days=i)
        wd = dd.weekday()
        if wd in [0, 1, 2, 3, 4]:
            dateArr.append(dd.strftime('%m-%d'))
            weekArr.append(wkArr[wd])
    for o in issues:
        if o['cfdate'] == '0000-00-00':
            o['cfdate'] = ''
        if o['dept1'] in rowDic.keys():
            if o['cfdate'] < thisyear+'-'+dateArr[0]:
                if '过期未处理' in rowDic[o['dept1']].keys():
                    rowDic[o['dept1']]['过期未处理'].append(o)
                else:
                    rowDic[o['dept1']]['过期未处理'] = [o]
            elif o['cfdate'] > thisyear+'-'+dateArr[len(dateArr)-1]:
                if '两周后' in rowDic[o['dept1']].keys():
                    rowDic[o['dept1']]['两周后'].append(o)
                else:
                    rowDic[o['dept1']]['两周后'] = [o]
            else:
                if o['cfdate'][5:] in rowDic[o['dept1']].keys():
                    rowDic[o['dept1']][o['cfdate'][5:]].append(o)
                else:
                    rowDic[o['dept1']][o['cfdate'][5:]] = [o]

    dateArr.append('两周后')
    dateArr.insert(0, '过期未处理')
    weekArr.append(' ')
    weekArr.insert(0, ' ')
    dt = getLines(area)
    lines = dt[0]
    stypes = dt[1]
    owners = dt[2]
    rowArr = list(rowDic.values())
    return responseGet('成功', {'data': rowArr, 'lines': lines, 'pp': dateArr, 'ww': weekArr, 'stypes': stypes, 'owners': owners})


def getLines(area):   # 获取产线列表，会根据用户信息的Dept2进行切分，未激活和未注册的用户无法出现在选单
    res = requests.get(
        config[env].cloud_url+"welean/bi/issueAPI/getLinesSZ", params={
            'area': area
        })
    content = res.json()
    lines = []
    if content["meta"]["status"] == 200:
        lines = content['data']['lines']
        owners = content['data']['owners']
        types = content['data']['types']
    lineData = []
    for r in lines:
        lineData.append({'name': r, 'value': r})
    return [lineData, types, owners]

import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export const getDN = (param: any) => {
  return http.request("get", baseUrlApi("/order/getdnlist"), {
    params: {
      user_id: param
    }
  });
};

export const getProductsbyDN = (param: any) => {
  return http.request("get", baseUrlApi("/order/getproductsbydn"), {
    params: {
      dn_numbers: param
    }
  });
};

export const releaseDN = (param: any) => {
  return http.request("post", baseUrlApi("/order/release_dn"), {
    data: {
      search_field: param
    }
  });
};

export const getDNFile = (param: any) => {
  return baseUrlApi("/order/get_dn_file") + "?datestr=" + param;
};

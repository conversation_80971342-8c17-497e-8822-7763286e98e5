from flask import Blueprint, request,send_from_directory
from urllib.parse import quote
from app.dm.model.models_dm import Skuinfo, Shiftinfo, Scaninfo, Scanpack, Scansub,Palletinfo
from extensions import db
from app.public.functions import responseError, responseGet,responsePost
from app.dm.functions import login_required,getServer
from app.dm.schemas import scan_schema
from sqlalchemy import func, desc,or_
import datetime
import hashlib
from openpyxl import load_workbook
import pandas as pd
import traceback
import os
api = Blueprint('dm/queryAPI', __name__)


@api.route('/downloadSAP', methods=['GET'])
@login_required
def downloadSAP():
    res = request.args
    path = getServer()['uploadPath']+'dn/'
    filename = res.get('url')
    if os.path.isfile(os.path.join(path, filename)):
            # print(filename)
            response = send_from_directory(
                path, filename, as_attachment=True)
            # qfilename =quote(filename.encode('utf-8'))
            response.headers["Access-Control-Expose-Headers"] = "fname"
            response.headers['fname'] = filename
            # response.headers['Content-Disposition'] = f'attachment; filename="{qfilename}"; filename*=UTF-8\'\'{qfilename}'
            return response
    return responseError('没有找到下载文件')


@ api.route('/uploadSAP', methods=['POST'])
@ login_required
def uploadSAP():
    file_obj = request.files.get('file')
    em = request.headers["email"]
    mystr = ('SAPUPLOAD' + str(datetime.datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    appendix = file_obj.filename[file_obj.filename.rfind('.'):]
    file_obj.save(getServer()['uploadPath']+name+appendix)
    tag,filename = filetoordertankplan(getServer()['uploadPath']+name+appendix, em)
    if tag == 'fail':
        return responseError('上传失败，请检查模板格式或请联系管理员')
    if tag=='lack':
        text = "注意！注意！注意！数据不完整，请下载后检查缺少哪些包装号的序列号"
    else:
        text = '上传项目信息获取成功,箱号可以全部找到'
    return responsePost(text, {
        'upload_url': filename})


def filetoordertankplan(file, em):
    wb = load_workbook(file)
    ws = wb.active
    skuDic={}
    rows=[]
    skuset=[]
    normsku=[]
    if ws.cell(1, 1).value != 'SHIP.DATE' or ws.cell(1, 2).value != 'DN.NO.' or ws.cell(1, 3).value != 'ITEM.NO.' \
            or ws.cell(1, 4).value != 'SN' or ws.cell(1, 5).value != 'ITEM.NO' or ws.cell(1, 6).value != 'DESC.':
        return 'fail'
    try:
        dn=ws.cell(2, 2).value
        for r in range(2, ws.max_row + 1):
            sku = ws.cell(r, 5).value
            if sku:
                boxsn=ws.cell(r,4).value
                des=ws.cell(r, 6).value
                if boxsn[11]=='P' or boxsn[11]=='T':
                    skuDic[boxsn]={
                        'sku':sku,
                        'des':des
                    }
                else:
                    normsku.append({
                    '料号':sku,
                    '描述':des,
                    '序列号':boxsn,
                    '对应箱号':'独立序列号'
                })
        sns=list(skuDic.keys())
        skuInfo=db.session.query(Scanpack).filter(or_(Scanpack.boxsn.in_(sns),Scanpack.palletsn.in_(sns))).order_by(Scanpack.boxsn).all()
        for s in skuInfo:
            print(111,s)
            rows.append({
                '料号':skuDic[s.boxsn]['sku'] if s.boxsn else skuDic[s.palletsn]['sku'],
                '描述':skuDic[s.boxsn]['des'] if s.boxsn else skuDic[s.palletsn]['des'],
                '序列号':s.sn,
                '对应箱号':s.boxsn if s.boxsn else s.palletsn
            })
            skuset.append(s.boxsn)
        rows=rows+normsku
        df=pd.DataFrame(rows)
        filename='DN'+dn+'.xlsx'
        writer = pd.ExcelWriter(getServer()['uploadPath']+'dn/'+filename, engine='openpyxl')
        df.to_excel(writer, index=False, sheet_name='Sheet1', na_rep='')
        worksheet = writer.sheets['Sheet1']
        worksheet.column_dimensions['A'].width = 15
        worksheet.column_dimensions['B'].width = 25
        worksheet.column_dimensions['C'].width = 18
        worksheet.column_dimensions['D'].width = 18
        writer.close()
        if (len(set(skuset)))!=len(sns):
            return 'lack',filename
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return 'fail',''
    wb.close()
    return 'success',filename


@api.route('/getPackinfo',methods=['GET'])
def getPackinfo():
    res=request.args
    keyword=res.get('whquery')
    boxArr=[]
    palletArr=[]
    if(len(keyword)<13 or keyword==''):
        return responseError('搜索字符需要大于等于13个字符')
    if 'P' in keyword[10:]:
        packs=db.session.query(Scanpack.sn,Scanpack.boxsn,Scanpack.palletsn,Palletinfo.starttime).join(Palletinfo,Palletinfo.palletsn==Scanpack.boxsn).filter(
            Scanpack.boxsn.like('%{0}%'.format(keyword))).all()
    elif 'T' in keyword[10:]:
        packs=db.session.query(Scanpack.sn,Scanpack.boxsn,Scanpack.palletsn,Palletinfo.starttime).join(Palletinfo,Palletinfo.palletsn==Scanpack.palletsn).filter(
            Scanpack.palletsn.like('%{0}%'.format(keyword))).all()
    else:
        packs=db.session.query(Scanpack.sn,Scanpack.boxsn,Scanpack.palletsn,Palletinfo.starttime).join(Palletinfo,or_(Palletinfo.palletsn==Scanpack.boxsn,Palletinfo.palletsn==Scanpack.palletsn)).filter(
            Scanpack.sn.like('%{0}%'.format(keyword))).all()
    packInfo=[]
    for p in packs:
        packInfo.append({
            'sn':p.sn,
            'boxsn':p.boxsn,
            'palletsn':p.palletsn,
            'scantime':datetime.datetime.strftime(p.starttime,'%Y-%m-%d %H:%M:%S')
        })
    return responseGet('获取成功',{'packInfo':packInfo})


@api.route('/getAllsnsku', methods=['GET'])  # 输入：班次号，搜索关键字，输出：料号列表和总数
def getAllsnsku():
    res = request.args
    keyword = res.get('query')
    scandate=res.get('scandate')
    dic = {}
    skus = db.session.query(Scaninfo.sn, Scanpack.palletsn, Scanpack.boxsn, Scaninfo.scanqty, Scaninfo.Id).outerjoin(
        Scanpack, Scaninfo.sn == Scanpack.sn).outerjoin(
        Palletinfo, or_(Scanpack.palletsn == Palletinfo.palletsn, Scanpack.boxsn == Palletinfo.palletsn)).filter(
            or_(Scaninfo.sn.like('%{0}%'.format(keyword)),Scaninfo.sku.like('%{0}%'.format(keyword))))
    if len(keyword)!=15:
        skus=skus.filter(func.date(Scaninfo.scantime) == scandate)
    skus = skus.order_by(desc(Scanpack.palletsn), desc(Scanpack.boxsn), desc(Scaninfo.Id)).all()
    total = len(skus)
    for sku in skus:
        psn = sku.palletsn if sku.palletsn else '000散托000'
        bsn = sku.boxsn if sku.boxsn else ' 000散箱000-按扫码的先后循序排列'
        if psn in dic.keys():
            if bsn in dic[psn].keys():
                dic[psn][bsn].append({
                    'sn': sku.sn,
                    'id': sku.Id,
                    'scanqty': sku.scanqty
                })
            else:
                dic[psn][bsn] = [{
                    'sn': sku.sn,
                    'id': sku.Id,
                    'scanqty': sku.scanqty
                }]
        else:
            dic[psn] = {
                bsn: [{
                    'sn': sku.sn,
                    'id': sku.Id,
                    'scanqty': sku.scanqty
                }]
            }
    # dic[psn][bsn]内按sn排序
    for psn in dic.keys():
        for bsn in dic[psn].keys():
            dic[psn][bsn].sort(key=lambda x: x['id'])
    return responseGet('获取成功', {'snList': dic, 'total': total})


# getAllsn api
@api.route('/queryBox', methods=['GET'])   # 获取所有sn
@login_required
def queryBox():
    res = request.args
    shiftid = res.get('shiftid')
    keyword = res.get('keyword')
    dic = {}
    skus = db.session.query(Scaninfo.sn, Scanpack.palletsn, Scanpack.boxsn, Scaninfo.scanqty, Scaninfo.Id).outerjoin(Scanpack, Scaninfo.sn == Scanpack.sn).filter(
        Scaninfo.shiftid == shiftid)
    total = db.session.query(func.sum(Scaninfo.scanqty)).filter(
        Scaninfo.shiftid == shiftid)
    if keyword:
        skus = skus.filter(Scaninfo.sn.like('%{0}%'.format(keyword)))
        total = total.filter(Scaninfo.sn.like('%{0}%'.format(keyword)))
    skus = skus.order_by(desc(Scanpack.palletsn), desc(Scanpack.boxsn), desc(Scaninfo.Id)).all()
    total = total.scalar()
    for sku in skus:
        psn = sku.palletsn if sku.palletsn else '散托'
        bsn = sku.boxsn if sku.boxsn else '散箱'
        if psn in dic.keys():
            if bsn in dic[psn].keys():
                dic[psn][bsn].append({
                    'sn': sku.sn,
                    'scanqty': sku.scanqty
                })
            else:
                dic[psn][bsn] = [{
                    'sn': sku.sn,
                    'scanqty': sku.scanqty
                }]
        else:
            dic[psn] = {
                bsn: [{
                    'sn': sku.sn,
                    'scanqty': sku.scanqty
                }]
            }
    return responseGet('获取成功', {'snList': dic, 'total': total})


@api.route('/queryBySN', methods=['GET'])   # 根据SN查询班次信息和扫码信息
@login_required
def queryBySN():
    res = request.args
    sn = res.get('sn')
    subArr = []
    if not sn.strip():
        return responseError('没有找到该序列号')
    info = db.session.query(Scaninfo.Id, Scaninfo.sn, Scaninfo.scantime, Scaninfo.scanqty, Skuinfo.des, Scaninfo.sku, Scanpack.palletsn, Scanpack.boxsn,
                            Shiftinfo.lineleader, Shiftinfo.team, Shiftinfo.linename, Shiftinfo.shifttype).outerjoin(
        Shiftinfo, Scaninfo.shiftid == Shiftinfo.Id).outerjoin(Scanpack, Scanpack.sn == Scaninfo.sn).outerjoin(Skuinfo, Skuinfo.sku == Scaninfo.sku).filter(
        Scaninfo.sn == sn).first()
    if not info:
        subinfo = db.session.query(Scansub).filter(Scansub.subsn == sn).first()
        if not subinfo:
            return responseError('没有找到该序列号')
        else:
            sn = subinfo.sn
            info = db.session.query(Scaninfo.Id, Scaninfo.sn, Scaninfo.scantime, Scaninfo.scanqty, Skuinfo.des, Scaninfo.sku, Scanpack.palletsn, Scanpack.boxsn,
                                    Shiftinfo.lineleader, Shiftinfo.team, Shiftinfo.linename, Shiftinfo.shifttype).outerjoin(
                Shiftinfo, Scaninfo.shiftid == Shiftinfo.Id).outerjoin(Scanpack, Scanpack.sn == Scaninfo.sn).outerjoin(Skuinfo, Skuinfo.sku == Scaninfo.sku).filter(
                Scaninfo.sn == sn).first()
    subsns = db.session.query(Scansub).filter(Scansub.sn == sn).all()
    for s in subsns:
        subArr.append(
            {
                'subname': s.subname,
                'subsn': s.subsn
            }
        )
    snInfo = scan_schema.dump(info)
    # snInfo['subsns'] = subArr
    return responseGet('获取成功', {'snInfo': snInfo, 'subArr': subArr})

/**
 * 弹窗管理 Composable
 */
import { h, ref, toRaw } from 'vue';
import { addDialog } from '@/components/ReDialog';
import { message } from '@/utils/message';
import type { DialogConfig, RowData, FeeFormData, VolumeFormData, ReleaseFormData } from '../types';

export function useDialogManager() {
  const formRef = ref();

  // 通用弹窗配置
  const createDialogConfig = (config: DialogConfig) => {
    return {
      draggable: true,
      closeOnClickModal: false,
      ...config
    };
  };

  // 通用成功处理
  const handleSuccess = (message: string, callback?: () => void) => {
    return () => {
      message(message, { type: 'success' });
      callback?.();
    };
  };

  // 通用错误处理
  const handleError = (message: string) => {
    message(message, { type: 'error' });
  };

  // 通用API调用处理
  const handleApiCall = async <T>(
    apiCall: Promise<{ meta: { status: number } }>,
    successMessage: string,
    errorMessage: string,
    onSuccess?: () => void
  ) => {
    try {
      const res = await apiCall;
      if (res.meta.status === 201) {
        handleSuccess(successMessage, onSuccess)();
        return true;
      } else {
        handleError(errorMessage);
        return false;
      }
    } catch (error) {
      console.error('API调用失败:', error);
      handleError(errorMessage);
      return false;
    }
  };

  // 打开运费计算弹窗
  const openFeeDialog = (
    rowData: RowData,
    component: any,
    onSuccess?: (data: FeeFormData) => void
  ) => {
    addDialog(createDialogConfig({
      title: '运费计算',
      width: '65%',
      top: '4vh'
    }), {
      props: { rowdata: rowData },
      contentRenderer: () => h(component, { ref: formRef }),
      beforeSure: async (done) => {
        try {
          // 从组件获取最新数据
          if (formRef.value?.getFeedData) {
            const feeData = formRef.value.getFeedData();
            
            // 验证数据
            if (formRef.value?.validateData) {
              const validation = formRef.value.validateData();
              if (!validation.valid) {
                message(validation.errors.join(', '), { type: 'error' });
                return;
              }
            }

            // 调用成功回调
            onSuccess?.(feeData);
            handleSuccess('运费计算成功！', done)();
          } else {
            handleError('无法获取运费数据');
          }
        } catch (error) {
          console.error('保存运费数据失败:', error);
          handleError('保存运费数据失败');
        }
      }
    });
  };

  // 打开货物量登记弹窗
  const openVolumeDialog = (
    rowData: RowData,
    component: any,
    onSuccess?: (data: VolumeFormData) => void
  ) => {
    addDialog(createDialogConfig({
      title: '登记货物量',
      width: '40%'
    }), {
      props: {
        rowdata: rowData,
        newdata: { weight: 0, volume: 0, warehouse_remark: '' }
      },
      contentRenderer: () => h(component),
      beforeSure: async (done, { options }) => {
        const volumeData = options.props.newdata;
        onSuccess?.(volumeData);
        handleSuccess('货物量登记成功，待物流处理！', done)();
      }
    });
  };

  // 打开释放DN弹窗
  const openReleaseDialog = (
    rowData: RowData,
    component: any,
    onSuccess?: (data: ReleaseFormData) => void
  ) => {
    addDialog(createDialogConfig({
      title: '释放Delivery Note',
      width: '60%'
    }), {
      props: {
        formInline: {
          row: rowData,
          warehouse: '',
          customer_email: '',
          comments: ''
        }
      },
      contentRenderer: () => h(component, { ref: formRef }),
      beforeSure: async (done, { options }) => {
        const releaseData = toRaw(options.props.formInline);
        onSuccess?.(releaseData);
        handleSuccess('DN工单释放成功,已发至工厂处理！', done)();
      }
    });
  };

  // 打开PDF查看弹窗
  const openPdfDialog = (rowData: RowData, component: any) => {
    addDialog(createDialogConfig({
      title: `查看DN附件,${rowData.DN_NO}`,
      width: '62%',
      top: '4vh',
      hideFooter: true,
      fullscreenIcon: true
    }), {
      props: { pdf_url: rowData.release_time },
      contentRenderer: () => h(component)
    });
  };

  return {
    formRef,
    openFeeDialog,
    openVolumeDialog,
    openReleaseDialog,
    openPdfDialog,
    handleApiCall,
    handleSuccess,
    handleError
  };
}

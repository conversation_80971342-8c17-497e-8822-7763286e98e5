from O365 import Account, FileSystemTokenBackend
import os
import datetime
from utils.config import cfg
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.sms.v20190711 import sms_client, models
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.common import credential
from model import MyModel
import pandas as pd
import requests
import re
import traceback
from utils.functions import convert_to_utf8_csv
from utils.mylog import logger
import sys
import pytz


class O365:
    def __init__(self):
        self.accounts = []
        self.warningtimes = 0
        self.smscount = 0
        for user in cfg.o365:
            client_id = user['client_id']
            client_secret = user['client_secret']
            tenant_id = user['tenant_id']
            fname = cfg.flaskPath + 'tokens/o365_token_{}.txt'.format(user['email'])
            token_backend = FileSystemTokenBackend(
                token_path='.', token_filename=fname)
            credentials = (client_id, client_secret)
            account = Account(credentials, tenant_id=tenant_id, token_backend=token_backend)
            if not account.is_authenticated:
                # sendSMS('连接', f'token失效 {user["email"]}')
                account.authenticate(scopes=[
                    'basic',
                    'message_all'
                    # 'Sites.Read.All',  # 读取所有 SharePoint 网站
                    # 'Files.Read.All'   # 读取所有 OneDrive 文件
                ])
            self.accounts.append(account)
        if not os.path.exists('mails'):
            os.makedirs('mails')

    def sendSMS(self, connect, info):
        phone = ["+*************"]
        try:
            cred = credential.Credential(
                "AKIDUL5UeX1zZ4HItGqFLIacP0LQFKGx3RLy", "bxNWLvGOIwKn02qNVLuPgs93mMlu9CxV")
            httpProfile = HttpProfile()
            httpProfile.reqMethod = "POST"  # POST 请求（默认为 POST 请求）
            httpProfile.reqTimeout = 30  # 请求超时时间，单位为秒（默认60秒）
            httpProfile.endpoint = "sms.tencentcloudapi.com"  # 指定接入地域域名（默认就近接入）
            # 实例化一个客户端配置对象，可以指定超时时间等配置
            clientProfile = ClientProfile()
            clientProfile.signMethod = "TC3-HMAC-SHA256"  # 指定签名算法
            clientProfile.language = "en-US"
            clientProfile.httpProfile = httpProfile
            client = sms_client.SmsClient(cred, "ap-guangzhou", clientProfile)
            req = models.SendSmsRequest()
            req.SmsSdkAppid = "1400149076"
            req.Sign = "welean"
            req.SessionContext = "xxx"
            req.PhoneNumberSet = phone
            req.TemplateID = "208852"
            req.TemplateParamSet = ['邮件服务器', connect+"失败", info]
            client.SendSms(req)
            sys.exit(1)
        except TencentCloudSDKException as err:
            logger.critical(str(err))
            return 'fail'
        return 'ok'

    def sendMail(self, tos, ccs, title, body, sender='', bccs=[], attachments=[], aindex=0):
        if sender == 'Error-Report':
            self.warningtimes += 1
        if self.warningtimes > 5 and tos[0] == '<EMAIL>':
            return
        m = self.accounts[aindex].new_message()
        m.subject = f'[{sender}]'+title
        m.body = body
        failArr = []
        for to in tos:
            if re.match(r'^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]+$', to):
                m.to.add(to)
            else:
                failArr.append(to)
        if len(tos) == len(failArr):
            return 'fail'
        if len(ccs) > 0:
            for cc in ccs:
                m.cc.add(cc)
        if len(bccs) > 0:
            for bcc in bccs:
                m.bcc.add(bcc)
        # 添加附件
        if len(attachments) > 0:
            for attachment in attachments:
                m.attachments.add(attachment)
        try:
            m.send()
            logger.info('邮件发送成功'+str(tos))
        except Exception as e:
            logger.critical(str(e))
            sql = "insert into errorlog(app,function,error,recordtime) values ('%s','%s','%s','%s')" % (
                'mailserver', title[:50], str(e).replace('\'', ''), datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            pubModel = MyModel(cfg.emdidb, 'pentair_public')
            pubModel.insSql(sql)
            # sendSMS('发送', title[:5])
            # self.smscount += 1

    def receiveMails(self):
        for i in range(len(self.accounts)):
            logger.info(cfg.o365[i]['email'])
            if cfg.o365[i]['email'] == '<EMAIL>':
                self.receiveMailsLean(self.accounts[i])
                # pass
            elif cfg.o365[i]['email'] == '<EMAIL>':
                self.receiveMailspogr(self.accounts[i])

    def receiveMailspogr(self, account):
        settings = pd.read_json('setting.json')
        sql = "select * from mail_pogr where status=1 order by senddate desc limit 10"
        try:
            pubModel = MyModel(cfg.emdidb, 'pentair_public')
            last_message_ids = []
            row = pubModel.upSql(sql)
            if len(row) > 0:
                start_date = row[0][1].astimezone(pytz.UTC)
            else:
                start_date = datetime.date.today().astimezone(pytz.UTC)
            for r in row:
                last_message_ids.append(r[7])
            end_date = datetime.datetime.now().astimezone(pytz.UTC)
            mailbox = account.mailbox()
            inbox = mailbox.inbox_folder()
            print(start_date, end_date)
            query = inbox.new_query('receivedDateTime').greater(
                start_date).less_equal(end_date)
            messages = inbox.get_messages(query=query, download_attachments=True)
        except Exception as e:
            logger.critical('POGR收件箱失败'+str(e))
            if self.smscount < 10:
                self.smscount += 1
            else:
                self.sendSMS('接收pogr', 'POGR收件箱失败')
            traceback.print_exc()
            return
        for message in messages:
            mid = message.object_id
            print(message)
            # print(last_message_ids)
            if mid in last_message_ids:
                continue
            received = message.received
            if message.sender.address == settings['location']['sap1mail'] and message.subject == settings['location']['sap1subject']:
                fpath = settings['location']['sap1raw']
                tpath = settings['location']['sap1to']
                self.sap1(message, received, mid, fpath, tpath)
            elif message.sender.address == settings['location']['sap1mail'] and message.subject == settings['location']['sap2subject']:
                fpath = settings['location']['sap2raw']
                tpath = settings['location']['sap2to']
                self.sap1(message, received, mid, fpath, tpath)
            else:
                self.dealASN(message, received, mid, settings)

    def pogr_downloadASN(self, fn, topath):
        a = convert_to_utf8_csv(fn, topath, 'ASN')
        return a

    def pogr_downloadSAP1(self, fn, topath):
        a = convert_to_utf8_csv(fn, topath, 'SAP1')
        return a

    def sap1(self, message, received, mid,  fpath, tpath):
        for attachment in message.attachments:
            timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            attachname = timestamp+'----'+attachment.name
            local_path = os.path.join(fpath, attachname)
            rst = 0
            try:
                issave = attachment.save(fpath, attachname)
                if not issave:
                    self.sendMail([cfg.gm1],  [],
                                  'Pentair-Error-Report 邮件保存失败 '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), local_path+"保存失败", 'Error-Report')
                    return
                aname = attachname.split('----')[1].split('.')[0]
                to_path = os.path.join(tpath, aname)+'.csv'
                rst = self.pogr_downloadSAP1(local_path, to_path)
            except Exception as e:
                logger.critical('sap1'+str(e))
                traceback.print_exc()
                self.sendMail([cfg.gm1],  [cfg.gm2],
                              'Pentair-Error-Report SQL-update error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), str(e), 'Error-Report')
                sql = "insert into mail_pogr(senddate,stype,content,status,record,recorddate,mailid) values ('%s','%s','%s',%d,%d,'%s','%s')" % (
                    received, message.sender.address, attachment.name, 0, rst, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), mid)
                pubModel = MyModel(cfg.emdidb, 'pentair_public')
                pubModel.insSql(sql)
            else:
                status = 1 if rst >= 0 else 0
                sql = "insert into mail_pogr(senddate,stype,content,status,record,recorddate,mailid) values ('%s','%s','%s',%d,%d,'%s','%s')" % (
                    received, message.sender.address, attachment.name, status, rst, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), mid)
                pubModel = MyModel(cfg.emdidb, 'pentair_public')
                pubModel.insSql(sql)

    def dealASN(self, message, received, mid, settings):
        for attachment in message.attachments:
            if "ASN" in attachment.name:
                fpath = settings['location']['asnraw']
                tpath = settings['location']['asnto']
                timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
                attachname = timestamp+'----'+attachment.name
                local_path = os.path.join(fpath, attachname)
                # return
                rst = 0
                try:
                    issave = attachment.save(fpath, attachname)
                    if not issave:
                        self.sendMail([cfg.gm1],  [],
                                      'Pentair-Error-Report 邮件保存失败 '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), local_path+"保存失败", 'Error-Report')
                        return
                    aname = attachname.split('----')[1].split('.')[0]
                    to_path = os.path.join(tpath, aname)+'.csv'
                    rst = self.pogr_downloadASN(local_path, to_path)
                except Exception as e:
                    logger.critical('dealASN'+str(e))
                    traceback.print_exc()
                    self.sendMail([cfg.gm1],  [cfg.gm2],
                                  'Pentair-Error-Report SQL-update error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), str(e), 'Error-Report')
                    sql = "insert into mail_pogr(senddate,stype,content,status,record,recorddate,mailid) values ('%s','%s','%s',%d,%d,'%s','%s')" % (
                        received, message.sender.address, attachment.name, 0, rst, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), mid)
                    pubModel = MyModel(cfg.emdidb, 'pentair_public')
                    pubModel.insSql(sql)
                else:
                    status = 1 if rst >= 0 else 0
                    sql = "insert into mail_pogr(senddate,stype,content,status,record,recorddate,mailid) values ('%s','%s','%s',%d,%d,'%s','%s')" % (
                        received, message.sender.address, attachment.name, status, rst, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), mid)
                    pubModel = MyModel(cfg.emdidb, 'pentair_public')
                    pubModel.insSql(sql)

    def receiveMailsLean(self, account):
        subjects = ['Material Location Storage',
                    'Material For IQC',
                    'SO clear and short report', 'IOT SN', 'ASN list in plant 1110']
        sql = "select * from mail_record where stype='receive' and status=1 order by senddate desc limit 10"
        try:
            pubModel = MyModel(cfg.emdidb, 'pentair_public')
            last_message_ids = []
            row = pubModel.upSql(sql)
            start_date = row[0][1]
            for r in row:
                last_message_ids.append(r[7])
            mailbox = account.mailbox()
            inbox = mailbox.inbox_folder()
            end_date = datetime.datetime.now()

            query = inbox.new_query('receivedDateTime').greater(
                start_date).less_equal(end_date)

            # # 获取电子邮件的列表
            messages = inbox.get_messages(query=query, download_attachments=True)
        except Exception:
            traceback.print_exc()
            logger.critical('lean收件箱失败')
            if self.smscount < 10:
                self.smscount += 1
            else:
                self.sendSMS('接收pogr', 'POGR收件箱失败')
            return
        # 使用 ID 筛选电子邮件
        # query = inbox.new_query().on_attribute('id').greater_than(last_message_id)
        # messages = inbox.get_messages(limit=10, start_after=start_after_message_id)
        # 获取更多的邮件，可以适当调整获取的数量，以保证获取到指定邮件ID之后的邮件

        # 遍历邮件并下载附件
        for message in messages:
            mid = message.object_id
            if mid in last_message_ids:
                continue
            if message.subject not in subjects:
                continue
            received = message.received
            for attachment in message.attachments:
                timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
                attachname = timestamp+'----'+attachment.name
                local_path = os.path.join("./mails", attachname)
                # return
                rst = 0
                try:
                    issave = attachment.save("./mails", attachname)
                    if not issave:
                        self.sendMail([cfg.gm1],  [],
                                      'Pentair-Error-Report 邮件保存失败 '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), local_path+'lean mail保存失败', 'Error-Report')
                        return
                    if attachment.name == "MaterialStorage.XLS":
                        rst = self.upIM(local_path)
                    elif attachment.name == "Material.XLS":
                        rst = self.upQC(local_path)
                    elif attachment.name == "SOCelarShort.XLS":
                        rst = self.upCS(local_path)
                    elif attachment.name[:3] == 'IOT' and attachment.name.split('.')[1] == 'txt':
                        rst = self.upIOT(local_path, attachment.name)
                    elif attachment.name == "ASN List.XLS":
                        rst = self.upASN(local_path)
                    else:
                        continue
                except Exception as e:
                    self.sendMail([cfg.gm1],  [cfg.gm2],
                                  'Pentair-Error-Report SQL-update error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), str(e), 'Error-Report')
                    sql = "insert into mail_record(senddate,stype,content,status,record,recorddate,mailid) values ('%s','%s','%s',%d,%d,'%s','%s')" % (
                        received, 'receive', attachment.name, 0, rst, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), mid)
                    pubModel = MyModel(cfg.emdidb, 'pentair_public')
                    a = pubModel.insSql(sql)
                    if a == 0:
                        self.sendMail([cfg.gm1],  [cfg.gm2],
                                      'Pentair-Error-Report Insert-record-fail error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), attachment.name, 'Error-Report')
                else:
                    if rst:
                        # print('rst', rst)
                        sql = "insert into mail_record(senddate,stype,content,status,record,recorddate,mailid) values ('%s','%s','%s',%d,%d,'%s','%s')" % (
                            received, 'receive', attachment.name, 1, rst, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), mid)
                        pubModel = MyModel(cfg.emdidb, 'pentair_public')
                        a = pubModel.insSql(sql)
                        if a == 0:
                            self.sendMail([cfg.gm1],  [cfg.gm2],
                                          'Pentair-Error-Report Insert-record-success error ' +
                                          datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), attachment.name, 'Error-Report')
                    else:
                        sql = "insert into mail_record(senddate,stype,content,status,record,recorddate,mailid) values ('%s','%s','%s',%d,%d,'%s','%s')" % (
                            received, 'receive', attachment.name, 0, rst, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), mid)
                        pubModel = MyModel(cfg.emdidb, 'pentair_public')
                        a = pubModel.insSql(sql)
                        self.sendMail([cfg.gm1],  [cfg.gm2],
                                      'Pentair-Error-Report No-record error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), attachment.name, 'Error-Report')

    def upASN(self, fn):
        f = open(fn, encoding='utf-16', errors='ignore')
        line = f.readline()
        b = []
        while line:
            a = line.split('\t')
            b.append(a)
            line = f.readline()
        f.close()
        b.remove((b[0]))
        arr = []
        asndic = {}
        for i in range(len(b)):
            if (len(b[i]) > 4):
                asnno = b[i][0].replace('\x00', '').replace('\n', '')
                vendorid = b[i][1].replace('\x00', '').replace('\n', '')
                cdate = b[i][3].replace('\x00', '').replace('\n', '')
                adate = b[i][4].replace('\x00', '').replace('\n', '')
                if len(b[i]) >= 10:
                    sku = b[i][7].replace('\x00', '').replace('\n', '')
                    qty = b[i][8].replace('\x00', '').replace('\n', '')
                    unit = b[i][9].replace('\x00', '').replace('\n', '')
                    inspect = b[i][10].replace('\x00', '').replace('\n', '')
                    if asnno in asndic.keys():
                        asndic[asnno].append("('%s','%s','%s','%s','%s')" % (asnno, sku, qty, unit, inspect))
                    else:
                        arr.append("('%s','%s','%s','%s')" % (asnno, vendorid, cdate, adate))
                        asndic[asnno] = ["('%s','%s','%s','%s','%s')" % (asnno, sku, qty, unit, inspect)]
                else:
                    arr.append("('%s','%s','%s','%s')" % (asnno, vendorid, cdate, adate))
        if len(arr) > 0:
            skuarr = []
            for item in list(asndic.values()):
                for subitem in item:
                    skuarr.append(subitem)
            # print(skuarr)
            # return
            sqlasn = "replace into dm_asn(asnno,vendorid,cdate,adate) values"+','.join(arr)
            asncnt = MyModel(cfg.emdidb, 'dm')
            result = asncnt.insSql(sqlasn)
            if len(skuarr) > 0:
                sqlsku = "replace into dm_asnsku(asnno,sku,qty,bin,inspect) values"+','.join(skuarr)
                asncnt.insSql(sqlsku)
            if result:
                return len(b)
            else:
                return result
        return -1

    def upIOT(self, fn, fname):
        try:
            outArr = []
            with open(fn, 'r', encoding='utf-8-sig') as f:
                lines = f.readlines()
            for i, line in enumerate(lines):
                if line != '\n':
                    arr = line.split(',')
                    sn = arr[5]
                    arrsn = []
                    if sn == 'SN':
                        ll = ','.join(arr).strip()+'\n'
                        outArr.append(ll)
                    else:
                        if sn[-4] == 'P':
                            cscnt = MyModel(cfg.emdidb, 'dm')
                            sqlgetsns = "select sn from dm_scanpack where boxsn='%s'" % sn
                            resultssn = cscnt.upSql(sqlgetsns)
                            if len(resultssn) > 0:
                                for rr in resultssn:
                                    arrsn.append(rr[0])
                            else:
                                arrsn.append(sn)
                        elif sn[-4] == 'T':
                            cscnt = MyModel(cfg.emdidb, 'dm')
                            sqlgetsns = "select sn from dm_scanbox where palletsn='%s'" % sn
                            resultssn = cscnt.upSql(sqlgetsns)
                            if len(resultssn) > 0:
                                for rr in resultssn:
                                    arrsn.append(rr[0])
                            else:
                                arrsn.append(sn)
                        else:
                            arrsn.append(sn)
                        for asn in arrsn:
                            sql = "select subname,subsn,sn from dm_scansub where sn='%s'" % asn
                            # print('sql', sql)
                            sncnt = MyModel(cfg.emdidb, 'dm')
                            results = sncnt.upSql(sql)
                            for r in results:
                                arr[5] = r[2]
                                if r[0] == '隐码':
                                    arr[6] = r[1]
                                elif r[0] == 'IMEI':
                                    arr[7] = r[1].replace(';', '')
                            ll = ','.join(arr).strip()+'\n'
                            # print('line', ll)
                            outArr.append(ll)
            # 吧outArr写入fn文件并去除空行

            outArr[len(outArr)-1] = outArr[len(outArr)-1].strip()
            with open(fn, 'w', encoding='utf-8-sig') as f_w:
                for i, line in enumerate(outArr):
                    # print('l', line)
                    line.strip()
                    f_w.write(line)

            with open(fn, 'rb') as f:
                logger.critical('IOT text start')
                res = requests.put(url='https://api.iot.pentair.com.cn/iotbatch/auto/'+fname, data=f)
                logger.critical('IOT text end')
                logger.critical('IOT text'+str(res))
                return 1
        except Exception as e:
            logger.critical('upIOT'+fname+str(e))
            return 0

    def upCS(self, fn):
        f = open(fn, encoding='utf-16', errors='ignore')
        line = f.readline()
        b = []
        while line:
            a = line.split('\t')
            b.append(a)
            line = f.readline()
        f.close()
        b.remove((b[0]))
        sqlplanner = "select listitem from cs_options where name='edi'"
        cscnt = MyModel(cfg.emdidb, 'cs')
        planners = cscnt.upSql(sqlplanner)
        planlist = []
        for p in planners:
            planlist.append(p[0])
        for i in range(len(b) - 1, -1, -1):
            if len(b[i]) == 1:
                b.remove(b[i])
                continue
            if 'FREIGHT' in b[i][6].upper() or 'SERVICE' in b[i][6].upper():
                b.remove(b[i])
                continue
            if b[i][17] in planlist:
                customer = "select planner from cs_edi where customer='%s'" % b[i][2]
                cscnt = MyModel(cfg.emdidb, 'cs')
                result = cscnt.upSql(customer)
                if result:
                    b[i][17] = result[0][0].split('@')[0]
                else:
                    b[i][17] = 'judy.xie'
            for j in range(len(b[i])):
                b[i][j] = b[i][j].replace('\x00', '').replace(
                    '\n', '').replace("'", '"')
                if j == 10 or j == 11:
                    b[i][j] = b[i][j].split('.')[0] + '-' + \
                        b[i][j].split('.')[1] + '-' + b[i][j].split('.')[2]
                b[i][j] = "'" + b[i][j] + "'"
            b[i].append("'" + datetime.date.today().strftime('%Y-%m-%d') + "'")
            b[i].pop(14)
            b[i].pop(14)
            b[i].pop(14)
            b[i] = '(' + ','.join(b[i]) + ')'
        a = ','.join(b)
        # print('aaaaaaaa', a)
        if len(a) > 0:
            sql1 = "insert into cs_orderlist" \
                   "(salesdoc,line,customer,shipto,mgroup,mgroupdesc,sku,skudesc,orderqty,unit,requestdate,orderdate,mprc,ordertype,planner,ponumber,recorddate)" \
                   "values %s" % a
            cscnt = MyModel(cfg.emdidb, 'cs')
            result = cscnt.insSql(sql1)
            if result:
                return len(b)
            else:
                return result
        else:
            return -1

    # IQC来料更新
    # def getItems(self):
    #     yesterday=datetime.date.today()-datetime.timedelta(days=1)
    #     sql="select sku,receive_date,orderno,orderitem from re_inspect where receive_date>='%s'"%(yesterday)
    #     print(sql)
    #     qccnt = MyModel(cfg.emdidb,'receiving')
    #     skus=qccnt.upSql(sql)
    #     arr=[]
    #     for sku in skus:
    #         if sku[3]:
    #             items=sku[3].split(',')
    #             for item in items:
    #                 arr.append(sku[0]+datetime.datetime.strftime(sku[1],"%Y%m%d%H:%M:%S")+item)
    #         else:
    #             arr.append(sku[0]+datetime.datetime.strftime(sku[1],"%Y%m%d%H:%M:%S"))
    #     return arr

    def upQC(self, fn):
        f = open(fn, encoding='utf-16', errors='ignore')
        line = f.readline().replace('\x00', '').replace('\n', '')
        inputArr = line.split('\t')
        outArr = []
        for x in inputArr:
            if x != '':
                outArr.append(x)
        b = {}
        skuArray = []
        supplierArray = []
        columnDic = {}
        while line:
            a = line.split('\t')
            for i in range(len(a)):
                columnDic[a[i]] = i
            if len(a) > 1:
                if a[columnDic['物料号']] in b:
                    b[a[columnDic['物料号']]][columnDic['数量']] = str(
                        round(float(b[a[columnDic['物料号']]][columnDic['数量']]) + float(a[columnDic['数量']]), 3))
                else:
                    b[a[columnDic['物料号']]] = a
            line = f.readline()
        # return
        f.close()
        b = list(b.values())
        # print(b)
        # return
        b.remove((b[0]))
        for i in range(0, len(b)):
            sampleLoc = ''
            sampleSku = ''
            if len(b[i]) == 1:
                b.remove(b[i])
                continue
            for j in range(len(b[i])):
                b[i][j] = b[i][j].replace('\x00', '').replace(
                    '\n', '').replace("'", '"')
                if j == columnDic['过账日期']:
                    b[i][j] = b[i][j][:4] + '-' + b[i][j][4:6] + \
                        '-' + b[i][j][6:] + ' ' + b[i][columnDic['时间']]
                b[i][j] = "'" + b[i][j] + "'"
                if j == columnDic['物料描述']:
                    sku = '(' + b[i][j] + ','
                elif j == columnDic['物料号']:
                    sku = sku + b[i][j] + ','
                    sampleSku = b[i][j]
                elif j == columnDic['物料组']:
                    sku = sku + b[i][j] + ')'
                elif j == columnDic['仓位']:
                    sampleLoc = b[i][j]
                elif j == columnDic['供应商名称']:
                    supplier = '(' + b[i][j] + ','
                elif j == columnDic['供应商编号']:
                    supplier = supplier + b[i][j] + ')'
            if sampleLoc:
                sql4 = "update re_spmanage set locationstock=%s where sku=%s" % (
                    sampleLoc, sampleSku)
                qccnt = MyModel(cfg.emdidb, 'receiving')
                isSample = qccnt.insSql(sql4)
                if isSample == 0:
                    print('issample zero')
            skuArray.append(sku)
            supplierArray.append(supplier)
            # print(b[i],b[i][columnDic['时间']],b[i][columnDic['工厂']])
            b[i].remove(b[i][columnDic['时间']])
            b[i].remove(b[i][columnDic['工厂']])
            b[i] = '(' + ','.join(b[i]) + ')'
        # return
        a = ','.join(b)
        skustr = ','.join(skuArray)
        supplierstr = ','.join(supplierArray)
        if len(a) > 0:
            qccnt = MyModel(cfg.emdidb, 'receiving')
            isQCinspect = 0
            if '仓位' in columnDic.keys():
                sql1 = "insert into re_inspect" \
                    "(sku_desc,supplier_name,sku,receive_qty,unit,orderno,receive_date,location,mgroup,supplier_code,bin)" \
                    "values %s" % a
                isQCinspect = qccnt.insSql(sql1)
            else:
                sql1 = "insert into re_inspect" \
                    "(sku_desc,supplier_name,sku,receive_qty,unit,orderno,receive_date,location,mgroup,supplier_code)" \
                    "values %s" % a
                isQCinspect = qccnt.insSql(sql1)
            # print(a)
            # return
            if isQCinspect == 0:
                self.sendMail([cfg.gm1],  [cfg.gm2],
                              'Pentair-Error-Report QC-ins error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'IQC导入失败', 'Error-Report')
            sql2 = "insert ignore into re_sku" \
                   "(sku_desc,sku,mgroup)" \
                   "values %s" % skustr
            # print(sql2)
            qccnt = MyModel(cfg.emdidb, 'receiving')
            isQCsku = qccnt.insSql(sql2)
            if isQCsku == 0:
                self.sendMail([cfg.gm1],  [cfg.gm2],
                              'Pentair-Error-Report QC-ins error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'IQC-SKU导入失败', 'Error-Report')
            sql3 = "insert ignore into re_supplier" \
                   "(name,code)" \
                   "values %s" % supplierstr
            # print(sql3)
            qccnt = MyModel(cfg.emdidb, 'receiving')
            isQCsupplier = qccnt.insSql(sql3)
            if isQCsupplier == 0:
                self.sendMail([cfg.gm1],  [cfg.gm2],
                              'Pentair-Error-Report QC-ins error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'IQC-Supplier导入失败', 'Error-Report')
            self.checkUrge()
            if isQCinspect:
                return len(b)
            else:
                return isQCinspect
        else:
            return -1

    def checkUrge(self):
        now = datetime.date.today().strftime('%Y-%m-%d')
        urgeSku = "select sku from re_urge where '%s'>=startDate and '%s'<=endDate" % (now, now)
        qccnt = MyModel(cfg.emdidb, 'receiving')
        skus = qccnt.upSql(urgeSku)
        for s in skus:
            sql = "update re_inspect set isurge=1 where status='open' and sku = '%s'" % s
            qccnt = MyModel(cfg.emdidb, 'receiving')
            qccnt.upSql(sql)

            # 三厂库存更新
    def upIM(self, fn):
        f = open(fn, encoding='utf-16', errors='ignore')
        line = f.readline()
        b = []
        dd = {}
        while line:
            a = line.split('\t')
            if len(a) > 1:
                b.append(a)
            line = f.readline()
        f.close()
        for i in range(1, len(b)):
            for j in range(len(b[i])):
                b[i][j] = b[i][j].replace('\x00', '')
                if len(b[i])-j == 3:
                    mo = b[i][j]
                    sku = b[i][0]
                    qty = b[i][j+1].replace('\x00', '')
                    # print(round(float(qty)),len(qty))
                    if mo[2:] not in ['33', '44', '66', '77']:
                        if sku in dd:
                            dd[sku] = dd[sku] + round(float(qty))
                        else:
                            dd[sku] = round(float(qty))
        modate = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if len(dd) > 0:
            sql = "update dm_epeistock set countdate='%s',countstock=%s where area='B3'" % (
                modate, 0)
            imcnt = MyModel(cfg.emdidb, 'dm')
            isim = imcnt.insSql(sql)
            if isim == 0:
                self.sendMail([cfg.gm1],  [cfg.gm2],
                              'IM-ins error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'IM-stock导入失败', 'Error-Report')

            for k in dd:
                sql = "update dm_epeistock set countdate='%s',countstock=%s where sku='%s'" % (
                    modate, dd[k], k)
                # print(sql)
                imcnt = MyModel(cfg.emdidb, 'dm')
                issku = imcnt.insSql(sql)
                # print(issku)
                if issku == 0:
                    self.sendMail([cfg.gm1],  [cfg.gm2],
                                  'IM-ins error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'IM-SKU导入失败'+k, 'Error-Report')
            return len(b)-1
        else:
            return -1


if __name__ == '__main__':
    o365 = O365()
    # o365.upASN('./mails/ASN1.XLS')
    # o365.upQC('./mails/Material.XLS')
    # o365.receiveMailspogr(o365.accounts[1])
    # o365.receiveMailsLean(o365.accounts[0])
    # o365.receiveMails()
    # o365.sendMail(['<EMAIL>'], [],
    #               'title-testdddfdsfsdfdsfdsfdsfdsfdsfdsfdsfdsf', 'msg-test', 'PIMS综合管理程序', aindex=1)

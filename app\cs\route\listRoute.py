from flask import Blueprint, request, send_from_directory
from app.cs.model.models_cs import Orderlist, Planner, Options, User, Edi
from extensions import db
from openpyxl import load_workbook
import datetime
import traceback
from sqlalchemy import func, desc
from app.cs.functions import login_required, responseDelete, responsePost, responsePut, responseGet, responseError, getServer
from app.cs.schemas import orderlists_schema, planners_schema, edis_schema, option_schema
api = Blueprint('cs/listAPI', __name__)


def downloadExcel(query, path, excelFile):
    wb = load_workbook(path+'excelTemplate.xlsx')
    sht = wb.worksheets[0]
    i = 2
    print(query)
    for q in query:
        sht.cell(row=i, column=1).value = q.salesdoc if q.salesdoc else ''
        sht.cell(row=i, column=2).value = q.line if q.line else ''
        sht.cell(row=i, column=3).value = q.customer if q.customer else ''
        sht.cell(row=i, column=4).value = q.shipto if q.shipto else ''
        sht.cell(row=i, column=5).value = q.mgroup if q.mgroup else ''
        sht.cell(row=i, column=6).value = q.mgroupdesc if q.mgroupdesc else ''
        sht.cell(row=i, column=7).value = q.sku if q.sku else ''
        sht.cell(row=i, column=8).value = q.skudesc if q.skudesc else ''
        sht.cell(row=i, column=9).value = q.orderqty if q.orderqty else ''
        sht.cell(row=i, column=10).value = q.unit if q.unit else ''
        sht.cell(row=i, column=11).value = q.requestdate if q.requestdate else ''
        sht.cell(row=i, column=12).value = q.orderdate if q.orderdate else ''
        sht.cell(row=i, column=13).value = q.mprc if q.mprc else ''
        sht.cell(row=i, column=14).value = q.ordertype if q.ordertype else ''
        sht.cell(row=i, column=15).value = q.updatedate if q.updatedate else ''
        sht.cell(row=i, column=16).value = q.updatereason if q.updatereason else ''
        sht.cell(row=i, column=17).value = q.updateremark if q.updateremark else ''
        sht.cell(row=i, column=18).value = q.planner if q.planner else ''
        sht.cell(row=i, column=19).value = q.ponumber if q.ponumber else ''
        sht.cell(row=i, column=20).value = q.confirmer if q.confirmer else ''
        sht.cell(row=i, column=21).value = q.confirmdate if q.confirmdate else ''
        sht.cell(row=i, column=22).value = q.recorddate if q.recorddate else ''
        i += 1
    wb.save(path + excelFile)


@ api.route('/addExcel', methods=['GET'])
@ login_required
def addExcel():
    res = request.args
    print(res)
    isall = res.get("isall")
    dateRange = res.get('dateRange')
    select = res.get('select')
    keywords = res.get('query')
    orderlist = db.session.query(Orderlist.Id, Orderlist.salesdoc, Orderlist.line, Orderlist.customer, Orderlist.shipto,
                                 Orderlist.mgroup, Orderlist.mgroupdesc, Orderlist.sku, Orderlist.skudesc, Orderlist.orderqty, Orderlist.unit, Orderlist.requestdate,
                                 Orderlist.orderdate, Orderlist.ordertype, Orderlist.updatedate, Orderlist.updatereason, Orderlist.updateremark,
                                 Orderlist.planner, Orderlist.ponumber, Planner.planner.label(
                                     'mprc'), Orderlist.confirmer,
                                 Orderlist.confirmdate, Orderlist.recorddate).outerjoin(Planner, Planner.mprc == Orderlist.mprc)
    if dateRange:
        orderlist = orderlist.filter(Orderlist.orderdate.between(
            dateRange.split(',')[0], dateRange.split(',')[1]))
    if select == "salesdoc" and keywords:
        orderlist = orderlist.filter(Orderlist.salesdoc.like('%{0}%'.format(keywords)))
    elif select == "ponumber" and keywords:
        orderlist = orderlist.filter(Orderlist.ponumber.like('%{0}%'.format(keywords)))
    elif select == "skudesc" and keywords:
        orderlist = orderlist.filter(Orderlist.skudesc.like('%{0}%'.format(keywords)))
    for k in res:
        if k not in ['query', 'pagenum', 'pagesize', 'request', 'mprc', 'dateRange', 'select', 't', 'isall'] and res[k]:
            orderlist = orderlist.filter(getattr(Orderlist, k) == res[k])
        elif k == 'mprc' and res[k]:
            orderlist = orderlist.filter(getattr(Planner, 'planner') == res[k])
    if isall:
        orderlist = orderlist.order_by(
            desc(Orderlist.orderdate)).order_by(Orderlist.salesdoc).order_by(Orderlist.line).all()
    else:
        orderlist = orderlist.filter(Orderlist.updatedate.is_(None)).order_by(
            desc(Orderlist.orderdate)).order_by(Orderlist.salesdoc).order_by(Orderlist.line).all()
    path = getServer()['localPath'] + 'downloadExcels/'
    excelFile = 'csOut' + datetime.datetime.now().strftime("%Y-%m-%d %H%M%S") + '.xlsx'
    downloadExcel(orderlist, path, excelFile)
    return send_from_directory(path, excelFile, as_attachment=True)


@api.route('/addoption', methods=['POST'])
@login_required
def addoption():
    res = request.json
    name = res.get('name')
    listitem = res.get('listitem')
    item = db.session.query(Options).filter(Options.name == name).filter(
        Options.listitem == listitem).scalar()
    if item:
        return responseError('系统已经有重复的项目，不允许添加重复的项目！')
    else:
        try:
            myItem = Options(name=name, listitem=listitem)
            db.session.add(myItem)
            db.session.flush()
            db.session.commit()
        except Exception:
            traceback.print_exc()
            return responseError('添加选项失败，请联系管理员')
    return responsePost('添加修改成功！', {'info': 'success'})


@api.route('/optionid/<int:uid>', methods=['DELETE'])
@login_required
def deleteOption(uid):
    user = db.session.query(Options).filter(Options.Id == uid).scalar()
    db.session.delete(user)
    db.session.commit()
    return responseDelete("删除Options成功")


@ api.route('/getoptions', methods=['GET'])
@ login_required
def getoptions():
    names = db.session.query(Options).all()
    dic = {}
    outArray = []
    menus = option_schema.dump(names)
    print(menus)
    for m in menus:
        if m['name'] in dic.keys():
            dic[m['name']]['children'].append(m)
        else:
            dic[m['name']] = {}
            dic[m['name']]['listname'] = m['name']
            dic[m['name']]['children'] = [m]
    for value in dic.values():
        outArray.append(value)
    return responseGet("获取选项列表成功", outArray)


@api.route('/ediid/<int:uid>', methods=['DELETE'])
@login_required
def deleteUser(uid):
    user = db.session.query(Edi).filter(Edi.Id == uid).scalar()
    db.session.delete(user)
    db.session.commit()
    return responseDelete("删除EDI成功")


@ api.route('/getedi', methods=['GET'])
@ login_required
def getedi():
    mprcs = db.session.query(Edi).all()
    mprclist = edis_schema.dump(mprcs)
    pls = db.session.query(User).filter(User.roleid != 3).all()
    planners = []
    for p in pls:
        planners.append(p.email)
    data = {'mprclist': mprclist, 'planners': planners}
    return responseGet("获取edi列表成功", data)


@api.route('/addedi', methods=['POST'])
@login_required
def addedi():
    res = request.json
    id = res.get('Id')
    customer = res.get('customer')
    planner = res.get('planner')
    if id:
        item = db.session.query(Edi).filter(Edi.Id == id).scalar()
        if item:
            item.customer = customer
            item.planner = planner
            db.session.commit()
    else:
        try:
            myItem = Edi(customer=customer, planner=planner)
            db.session.add(myItem)
            db.session.flush()
            db.session.commit()
        except Exception:
            traceback.print_exc()
            return responseError('创建EDI失败，请联系管理员')

    return responsePost('添加修改成功！', {'info': 'success'})


@api.route('/addplanner', methods=['POST'])
@login_required
def addplanner():
    res = request.json
    id = res.get('Id')
    mprc = res.get('mprc')
    planner = res.get('planner')
    plannerbackup = res.get('plannerbackup')
    supervisor = res.get('supervisor')
    if id:
        item = db.session.query(Planner).filter(Planner.Id == id).scalar()
        if item:
            item.mprc = mprc
            item.planner = planner
            item.plannerbackup = plannerbackup
            item.supervisor = supervisor
            db.session.commit()
    else:
        try:
            myItem = Planner(mprc=mprc, planner=planner,
                             plannerbackup=plannerbackup, supervisor=supervisor)
            db.session.add(myItem)
            db.session.flush()
            db.session.commit()
        except Exception:
            traceback.print_exc()
            return responseError('创建MPRC失败，请联系管理员')

    return responsePost('添加修改成功！', {'info': 'success'})


@ api.route('/getplanner', methods=['GET'])
@ login_required
def getplanner():
    mprcs = db.session.query(Planner).all()
    mprclist = planners_schema.dump(mprcs)
    pls = db.session.query(User).filter(User.roleid == 3).all()
    planners = []
    for p in pls:
        planners.append(p.email)
    data = {'mprclist': mprclist, 'planners': planners}
    return responseGet("获取mprc列表成功", data)


@api.route('/returnorder/<int:id>', methods=['DELETE'])
@login_required
def deleteorder(id):
    order = db.session.query(Orderlist).filter(Orderlist.Id == id).scalar()
    if order:
        order.updatedate = None
        db.session.commit()
        return responseDelete('回退成功!')
    else:
        return responseError('回退失败，请联系管理员！')


@api.route('/updatedate', methods=['PUT'])
@login_required
def updatedate():
    res = request.json
    print(res)
    id = res.get('Id')
    updatereason = res.get('updatereason')
    updateremark = res.get('updateremark')
    updatedate = res.get('updatedate')
    confirmer = res.get('confirmer')
    ct = res.get('ct').split(',') if res.get('ct') else []
    confirmdate = datetime.date.today()
    if not updatereason:
        updatereason = ''
    if not updateremark:
        updateremark = ''
    if len(ct) > 0:
        record = db.session.query(Orderlist).filter(Orderlist.Id.in_(ct)).all()
        for r in record:
            r.updatedate = updatedate
            r.updatereason = updatereason
            r.updateremark = updateremark
            r.confirmdate = confirmdate
            r.confirmer = confirmer
    else:
        db.session.query(Orderlist).filter(Orderlist.Id == id).update(
            {Orderlist.updatedate: updatedate, Orderlist.updatereason: updatereason, Orderlist.updateremark: updateremark,
             Orderlist.confirmdate: confirmdate, Orderlist.confirmer: confirmer})
    db.session.commit()
    return responsePut('确认交期成功！')


@api.route('/csupdate', methods=['PUT'])
@login_required
def csupdate():
    res = request.json
    id = res.get('Id')
    updatedate = res.get('updatedate')
    confirmer = res.get('confirmer')
    if updatedate != '1970-01-01':
        db.session.query(Orderlist).filter(Orderlist.Id == id).update(
            {Orderlist.updatedate: updatedate, Orderlist.confirmer: confirmer})
        db.session.commit()
        return responsePut('确认交期成功！')
    else:
        return responseError('确认日期不能改为空，请至少给个日期！')


@api.route('/qtyupdate', methods=['PUT'])
@login_required
def qtyupdate():
    res = request.json
    id = res.get('Id')
    orderqty = res.get('qty')
    db.session.query(Orderlist).filter(Orderlist.Id == id).update({Orderlist.orderqty: orderqty})
    db.session.commit()
    return responsePut('修改订单数量成功！')


@api.route('/orderlist', methods=['GET'])
@login_required
def orderlist():
    res = request.args
    print(res)
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    dateRange = res.get('dateRange')
    select = res.get('select')
    keywords = res.get('query')
    clist = db.session.query(Orderlist.Id, Orderlist.salesdoc, Orderlist.line, Orderlist.customer, Orderlist.shipto,
                             Orderlist.mgroup, Orderlist.mgroupdesc, Orderlist.sku, Orderlist.skudesc, Orderlist.orderqty, Orderlist.unit, Orderlist.requestdate,
                             Orderlist.orderdate, Orderlist.ordertype, Orderlist.updatedate, Orderlist.updatereason, Orderlist.updateremark,
                             Orderlist.planner, Orderlist.ponumber, Planner.planner.label('mprc')).outerjoin(Planner, Planner.mprc == Orderlist.mprc)
    orderlist = db.session.query(Orderlist.Id, Orderlist.salesdoc, Orderlist.line, Orderlist.customer, Orderlist.shipto,
                                 Orderlist.mgroup, Orderlist.mgroupdesc, Orderlist.sku, Orderlist.skudesc, Orderlist.orderqty, Orderlist.unit, Orderlist.requestdate,
                                 Orderlist.orderdate, Orderlist.ordertype, Orderlist.updatedate, Orderlist.updatereason, Orderlist.updateremark,
                                 Orderlist.planner, Orderlist.ponumber, Planner.planner.label('mprc')).outerjoin(Planner, Planner.mprc == Orderlist.mprc)
    tquery = db.session.query(func.count(Orderlist.Id)).outerjoin(
        Planner, Planner.mprc == Orderlist.mprc)
    if dateRange:
        orderlist = orderlist.filter(Orderlist.orderdate.between(
            dateRange.split(',')[0], dateRange.split(',')[1]))
        tquery = tquery.filter(Orderlist.orderdate.between(
            dateRange.split(',')[0], dateRange.split(',')[1]))
        clist = clist.filter(Orderlist.orderdate.between(
            dateRange.split(',')[0], dateRange.split(',')[1]))
    if select == "salesdoc" and keywords:
        orderlist = orderlist.filter(Orderlist.salesdoc.like('%{0}%'.format(keywords)))
        tquery = tquery.filter(Orderlist.salesdoc.like('%{0}%'.format(keywords)))
        clist = clist.filter(Orderlist.salesdoc.like('%{0}%'.format(keywords)))
    elif select == "ponumber" and keywords:
        orderlist = orderlist.filter(Orderlist.ponumber.like('%{0}%'.format(keywords)))
        tquery = tquery.filter(Orderlist.ponumber.like('%{0}%'.format(keywords)))
        clist = clist.filter(Orderlist.ponumber.like('%{0}%'.format(keywords)))
    elif select == "skudesc" and keywords:
        orderlist = orderlist.filter(Orderlist.skudesc.like('%{0}%'.format(keywords)))
        tquery = tquery.filter(Orderlist.skudesc.like('%{0}%'.format(keywords)))
        clist = clist.filter(Orderlist.skudesc.like('%{0}%'.format(keywords)))
    for k in res:
        if k not in ['query', 'pagenum', 'pagesize', 'request', 'mprc', 'dateRange', 'select', 'query'] and res[k]:
            orderlist = orderlist.filter(getattr(Orderlist, k) == res[k])
            tquery = tquery.filter(getattr(Orderlist, k) == res[k])
            clist = clist.filter(getattr(Orderlist, k) == res[k])
        elif k == 'mprc' and res[k]:
            orderlist = orderlist.filter(getattr(Planner, 'planner') == res[k])
            tquery = tquery.filter(getattr(Planner, 'planner') == res[k])
            clist = clist.filter(getattr(Planner, 'planner') == res[k])
    orderlist = orderlist.order_by(desc(Orderlist.orderdate)).order_by(Orderlist.salesdoc).order_by(Orderlist.line).paginate(
        pagenum, pagesize, error_out=False).items
    tquery = tquery.scalar()
    olist = orderlists_schema.dump(orderlist)
    lists = {}
    for k in res:
        if k not in ['query', 'pagenum', 'pagesize', 'request', 'mprc', 'dateRange', 'select', 'query']:
            ds = clist.group_by(getattr(Orderlist, k)).all()
            lists[k] = []
            for d in ds:
                lists[k].append(getattr(d, k))
        elif k == 'mprc':
            ds = clist.group_by(getattr(Planner, 'planner')).all()
            lists[k] = []
            for d in ds:
                lists[k].append(getattr(d, k))
    data = {'orderlist': olist, 'total': tquery, 'lists': lists}
    return responseGet("获取用户列表成功", data)


@api.route('/waitlist', methods=['GET'])
@login_required
def waitlist():
    res = request.args
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    select = res.get('select')
    keywords = res.get('query')
    clist = db.session.query(Orderlist.Id, Orderlist.salesdoc, Orderlist.line, Orderlist.customer, Orderlist.shipto,
                             Orderlist.mgroup, Orderlist.mgroupdesc, Orderlist.sku, Orderlist.skudesc, Orderlist.orderqty, Orderlist.unit, Orderlist.requestdate,
                             Orderlist.orderdate, Orderlist.ordertype, Orderlist.updatedate, Orderlist.updatereason, Orderlist.updateremark,
                             Orderlist.planner, Orderlist.ponumber, Planner.planner.label('mprc')).outerjoin(Planner, Planner.mprc == Orderlist.mprc)
    orderlist = db.session.query(Orderlist.Id, Orderlist.salesdoc, Orderlist.line, Orderlist.customer, Orderlist.shipto,
                                 Orderlist.mgroup, Orderlist.mgroupdesc, Orderlist.sku, Orderlist.skudesc, Orderlist.orderqty, Orderlist.unit, Orderlist.requestdate,
                                 Orderlist.orderdate, Orderlist.ordertype, Orderlist.updatedate, Orderlist.updatereason, Orderlist.updateremark,
                                 Orderlist.planner, Orderlist.ponumber, Planner.planner.label('mprc')).outerjoin(Planner, Planner.mprc == Orderlist.mprc)
    tquery = db.session.query(func.count(Orderlist.Id)).outerjoin(
        Planner, Planner.mprc == Orderlist.mprc)
    if select == "salesdoc" and keywords:
        orderlist = orderlist.filter(Orderlist.salesdoc.like('%{0}%'.format(keywords)))
        tquery = tquery.filter(Orderlist.salesdoc.like('%{0}%'.format(keywords)))
        clist = clist.filter(Orderlist.salesdoc.like('%{0}%'.format(keywords)))
    elif select == "ponumber" and keywords:
        orderlist = orderlist.filter(Orderlist.ponumber.like('%{0}%'.format(keywords)))
        tquery = tquery.filter(Orderlist.ponumber.like('%{0}%'.format(keywords)))
        clist = clist.filter(Orderlist.ponumber.like('%{0}%'.format(keywords)))
    elif select == "skudesc" and keywords:
        orderlist = orderlist.filter(Orderlist.skudesc.like('%{0}%'.format(keywords)))
        tquery = tquery.filter(Orderlist.skudesc.like('%{0}%'.format(keywords)))
        clist = clist.filter(Orderlist.skudesc.like('%{0}%'.format(keywords)))
    for k in res:
        if k not in ['query', 'pagenum', 'pagesize', 'request', 'mprc', 'select', 'query'] and res[k]:
            orderlist = orderlist.filter(getattr(Orderlist, k) == res[k])
            tquery = tquery.filter(getattr(Orderlist, k) == res[k])
            clist = clist.filter(getattr(Orderlist, k) == res[k])
        elif k == 'mprc' and res[k]:
            orderlist = orderlist.filter(getattr(Planner, 'planner') == res[k])
            tquery = tquery.filter(getattr(Planner, 'planner') == res[k])
            clist = clist.filter(getattr(Planner, 'planner') == res[k])
    orderlist = orderlist.filter(Orderlist.updatedate.is_(None)).order_by(Orderlist.salesdoc).order_by(Orderlist.line).paginate(
        pagenum, pagesize, error_out=False).items
    tquery = tquery.filter(Orderlist.updatedate.is_(None)).scalar()
    clist = clist.filter(Orderlist.updatedate.is_(None))
    lists = {}
    for k in res:
        if k not in ['query', 'pagenum', 'pagesize', 'request', 'mprc', 'select', 'query']:
            ds = clist.group_by(getattr(Orderlist, k)).all()
            lists[k] = []
            for d in ds:
                lists[k].append(getattr(d, k))
        elif k == 'mprc':
            ds = clist.group_by(getattr(Planner, 'planner')).all()
            lists[k] = []
            for d in ds:
                lists[k].append(getattr(d, k))
    print('dddddddddddddddd', lists)
    olist = orderlists_schema.dump(orderlist)
    reasons = getReasons()
    data = {'waitlist': olist, 'total': tquery, 'lists': lists,
            'reasons': reasons}
    return responseGet("获取用户列表成功", data)


def getReasons():
    reasonlist = []
    rs = db.session.query(Options).filter(Options.name == 'reasontype').all()
    for r in rs:
        reasonlist.append(r.listitem)
    return reasonlist

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { useColumns } from "./utils/column";
import { getpnbyprefix } from "@/api/operation";

const {
  search_condition,
  search,
  loading,
  columns,
  loadingConfig,
  dataList,
  adaptiveConfig
} = useColumns();

const options = ref([]);
const select_loading = ref(false);
const show_extra = ref(false);

defineOptions({
  name: "Mix"
});

watch(dataList, newvalue => {
  if (newvalue.length > 0) {
    show_extra.value = true;
  } else if (newvalue.length == 0) {
    show_extra.value = false;
  }
});

const remoteMethod = (query: string) => {
  select_loading.value = true;
  if (query) {
    getpnbyprefix({ prefix: query }).then((res: any) => {
      options.value = res.data;
      select_loading.value = false;
    });
  } else {
    options.value = [];
    select_loading.value = false;
  }
};

const reset = () => {
  dataList.value = [];
  search_condition.material = "";
  search_condition.quantity = 0;
};

onMounted(() => {
  loading.value = false;
});
</script>

<template>
  <div>
    <el-form :inline="true" class="search-form bg-bg_color">
      <el-form-item label="成品料号">
        <el-select
          filterable
          clearable
          remote
          reserve-keyword
          v-model="search_condition.material"
          :default-first-option="true"
          :remote-method="remoteMethod"
          :loading="select_loading"
          loading-text="加载料号中..."
          no-data-text="未检索到匹配的料号，联系ME添加！"
          placeholder="输入料号"
        >
          <el-option
            v-for="(item, index) in options"
            :key="item.material"
            :value="item.material"
            :class="{ 'bg-bg_color': index % 2 === 0 }"
            ><el-tag style="margin-right: 10px">{{ item.material }}</el-tag
            >{{ item.desc }}</el-option
          >
        </el-select>
      </el-form-item>
      <el-form-item label="成品数量">
        <el-input-number
          v-model="search_condition.quantity"
          :min="0"
          placeholder="输入数量"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="reset">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-tag size="large" class="extra_info" v-if="show_extra"
          >实际拌料数量：50EA,比计划多出：20EA</el-tag
        >
      </el-form-item>
    </el-form>

    <pure-table
      ref="tableRef"
      border
      :adaptiveConfig="adaptiveConfig"
      row-key="id"
      alignWhole="center"
      showOverflowTooltip
      :loading="loading"
      :loading-config="loadingConfig"
      :data="dataList"
      :columns="columns"
      height="250px"
    />
  </div>
</template>

<style lang="scss" scoped>
.search-form {
  display: flex;
  margin-bottom: 10px;
  .extra_info {
    font-size: 14px;
  }
  .el-form-item {
    display: flex;
    margin: 8px 10px;
  }
}

.pure-table {
  :deep(.el-table .el-table__cell) {
    padding: 6px 0;
  }
  :deep(.el-tag) {
    width: 100%;
    font-size: 14px;
  }
}
</style>

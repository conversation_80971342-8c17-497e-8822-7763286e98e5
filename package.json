{"name": "pure-admin-thin", "version": "5.4.0", "private": true, "type": "module", "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "clean:cache": "rimraf .eslintcache && rimraf pnpm-lock.yaml && rimraf node_modules && pnpm store prune && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky", "preinstall": "npx only-allow pnpm"}, "keywords": ["vue-pure-admin", "element-plus", "tailwindcss", "pure-admin", "typescript", "pinia", "vue3", "vite", "esm"], "homepage": "https://github.com/pure-admin/pure-admin-thin/tree/i18n", "repository": {"type": "git", "url": "git+https://github.com/pure-admin/pure-admin-thin.git"}, "bugs": {"url": "https://github.com/pure-admin/vue-pure-admin/issues"}, "license": "MIT", "author": {"name": "xiaoxian521", "email": "<EMAIL>", "url": "https://github.com/xiaoxian521"}, "dependencies": {"@kjgl77/datav-vue3": "^1.7.2", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^3.1.2", "@pureadmin/utils": "^2.4.7", "@vueuse/core": "^10.9.0", "@vueuse/motion": "^2.1.0", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.6.8", "cropperjs": "^1.6.2", "dayjs": "^1.11.10", "deepmerge": "4.3.1", "echarts": "^5.5.0", "element-plus": "^2.7.1", "js-cookie": "^3.0.5", "localforage": "^1.10.0", "mitt": "^3.0.1", "moment": "^2.29.4", "nprogress": "^0.2.0", "papaparse": "^5.4.1", "path": "^0.12.7", "pinia": "^2.1.7", "pinyin-pro": "^3.20.2", "qs": "^6.12.1", "responsive-storage": "^2.2.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.2", "vue": "^3.4.25", "vue-i18n": "^9.13.1", "vue-router": "^4.3.2", "vue-tippy": "^6.4.1", "vue-types": "^5.1.1"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@commitlint/types": "^19.0.3", "@eslint/js": "^9.1.1", "@faker-js/faker": "^8.4.1", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.1.2", "@intlify/unplugin-vue-i18n": "^4.0.0", "@pureadmin/theme": "^3.2.0", "@types/gradient-string": "^1.1.6", "@types/js-cookie": "^3.0.6", "@types/node": "^20.12.7", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.15", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.7.1", "@typescript-eslint/parser": "^7.7.1", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.19", "boxen": "^7.1.1", "cssnano": "^7.0.0", "eslint": "^9.1.1", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.25.0", "gradient-string": "^2.0.2", "husky": "^9.0.11", "lint-staged": "^15.2.2", "postcss": "^8.4.38", "postcss-html": "^1.6.0", "postcss-import": "^16.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.75.0", "stylelint": "^16.4.0", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.0", "svgo": "^3.2.0", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "vite": "^5.2.10", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^9.4.2", "vue-tsc": "^1.8.27"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0", "pnpm": ">=8.6.10"}, "packageManager": "pnpm@8.6.10", "pnpm": {"allowedDeprecatedVersions": {"sourcemap-codec": "*", "domexception": "*", "w3c-hr-time": "*", "stable": "*", "abab": "*"}, "peerDependencyRules": {"allowedVersions": {"eslint": "9"}}}}
from datetime import timedelta
import os


class Config:  # 公共的配置参数
    SQLALCHEMY_TRACK_MODIFICATIONS = True  # SQLAlchemy连接跟踪
    SQLALCHEMY_POOL_RECYCLE = 1200  # SQLalchemy连接池失效时间
    SECRET_KEY = 'pims'  # md5加密密钥
    JWT_SECRET_KEY = 'pims'  # JWT加密密钥
    ZHIPU_API_KEY = '787047b374d542bfa042972686f15889.gzWbGhg07gjzSjBE'  # 智普AI平台API密钥
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(days=1)  # 设置访问token的有效时间为30分钟
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)  # 设置刷新token的有效时间为1天
    static_path = os.path.join(os.path.dirname(
        __file__), "static", "updoad", "plan")  # 静态文件保存路径

    @staticmethod
    def init_app(app):
        '''初始化配置文件'''
        pass

# 开发环境的配置参数


class DevelopmentConfig(Config):
    # REDIS_URL = "redis://:<EMAIL>:6379/0"
    REDIS_URL = "redis://:welean@***********:6379/0"
    DTU_URL = "tcp://*************:8886"
    SQLALCHEMY_BINDS = {
        'im': 'mysql+pymysql://root:pimslean@***********:3306/im',
        # 'im': 'mysql+pymysql://root:<EMAIL>:3306/im'
    }
    PARAM_IOT = {
        '9#': 'opc.tcp://***********:4840'
    }
    SQLALCHEMY_ECHO = True  # 启用SQL语句日志输出
    DEBUG = True  # 启用错误调试模式

# 生产环境的配置参数


class ProductionConfig(Config):
    REDIS_URL = "redis://:welean@172.19.0.1:6379/0"
    DTU_URL = "tcp://*************:8886"
    SQLALCHEMY_BINDS = {
        'im': 'mysql+pymysql://root:pimslean@172.19.0.1:3306/im'
    }
    PARAM_IOT = {
        '9#': 'opc.tcp://192.168.4.9:4840'
    }


# define the config，默认开发模式，上线更改模式为production
env = 'develop'
config = {'develop': DevelopmentConfig, 'production': ProductionConfig}

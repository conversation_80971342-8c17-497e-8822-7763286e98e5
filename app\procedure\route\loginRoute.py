import traceback
from flask import Blueprint, request
from extensions import db
import requests
from config import config, myEnv
from app.public.functions import login_required, responseError, responsePost, responseDelete, responsePut
import datetime
from app.procedure.functions.jwt import AuthService
from app.procedure.model.models_procedure import User
api = Blueprint('procedure/loginAPI', __name__)


@api.route('/', methods=['GET'])
# @login_required
def DD():
    return responseDelete("测试用户成功", {'data': 33})


@api.route('/changePassword', methods=['PUT'])
@login_required
def changePassword():
    res = request.json
    email = res.get('email')
    oldpass = res.get('oldpass')
    newpass = res.get('newpass1')
    r = requests.post(config[myEnv].api_url+"public/changePassword",
                      {'username': email, 'oldpass': oldpass, 'newpass': newpass})
    vdata = r.json()
    print('aaa', vdata)
    if vdata['meta']['status'] != 202:
        return responseError(vdata['meta']['msg'])
    return responsePut(vdata['meta']['msg'])


@api.route("/getLogin", methods=["POST"])
def getLogin():
    res_dir = request.json
    email = res_dir.get("username")
    password = res_dir.get("password")
    try:
        if not (email and password):
            return responseError('请输入用户名密码')
        else:
            if "@" not in email and "." in email:
                email = email+"@pentair.com"
        try:
            if email.isdigit():
                puser = db.session.query(User).filter(
                    User.eid == email).first()
            else:
                puser = db.session.query(User).filter(
                    User.email == email).first()
            if puser:
                eid = puser.eid
                email = puser.email
        except Exception:
            traceback.print_exc()
            return responseError('查询错误，请联系管理员！')
        if puser is None:
            return responseError('用户名不存在或已被禁用！')
        elif not puser.check_password_hash(password):
            return responseError('密码错误！')
        auth_service = AuthService()
        expiration = datetime.datetime.now() + datetime.timedelta(days=5)
        token = auth_service.create_jwt_token(puser, expiration)
        username = puser.name
        eid = puser.eid
        email = puser.email
        plant = puser.plant
        roles = puser.role.split(',') if puser.role else []
        expires = expiration.strftime("%Y/%m/%d %H:%M:%S")
        permissions = puser.permissions.split(',') if puser.permissions else []
        data = {'username': username, 'roles': roles, 'eid': eid, 'permissions': permissions, 'email': email,
                'accessToken': token,  'refreshToken': token, 'expires': expires, 'plant': plant}
        return responsePost('登录成功', data)
    except Exception:
        return responseError('登录失败，请联系管理员！')

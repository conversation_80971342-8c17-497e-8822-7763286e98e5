import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";

import { ref, onMounted, reactive, h } from "vue";
import { addDialog } from "@/components/ReDialog";
import moment from "moment";
import { watch, toRaw } from "vue";
import { gethourlydata } from "@/api/dashboard";
import moldcycle from "@/views/components/moldcycle/index.vue";
import { message } from "@/utils/message";
export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);
  const query_param = reactive({
    search_condition: {
      selecteddate: moment().format("YYYY-MM-DD"),
      machine: 5,
      shift: null
    }
  });
  const query_cycle_param = reactive({
    hourid: null,
    show_view: false
  });

  const queryCycle = hour_id => {
    addDialog({
      title: "查看" + query_param.search_condition.machine + "#节拍",
      props: {
        search_condition: toRaw(query_param.search_condition),
        hour_id: hour_id
      },
      width: "60%",
      draggable: true,
      fullscreenIcon: false,
      closeOnClickModal: true,
      contentRenderer: () => h(moldcycle)
    });
  };

  const columns: TableColumnList = [
    {
      label: "小时号",
      prop: "hourid",
      formatter(row) {
        return row.hourid + ":00 ~" + (row.hourid + 1) + ":00";
      }
    },
    {
      label: "料号",
      prop: "pn",
      minWidth: "150px"
    },
    {
      label: "产量",
      prop: "",
      cellRenderer({ row }) {
        if (row.output == 0 && row.adjustion == 0) {
          return <div></div>;
        } else {
          return (
            <el-tag
              size="large"
              type={
                row.output + row.adjustion >= row.computed_output
                  ? "success"
                  : "danger"
              }
              style="width:100%"
              onClick={() => {
                queryCycle(row.hourid);
              }}
            >
              {row.output + row.adjustion}
            </el-tag>
          );
        }
      }
    },
    {
      label: "标准产量",
      prop: "computed_output",
      formatter(row) {
        return row.computed_output.toFixed(0);
      }
    },
    {
      label: "产量累积",
      prop: "acumulated_output",
      cellRenderer({ row }) {
        if (row.acumulated_output == 0) {
          return <div></div>;
        } else if (row.acumulated_output >= row.acumulated_computedvalue) {
          return (
            <el-tag size="large" type="success" style="width:60%">
              {row.acumulated_output}
            </el-tag>
          );
        } else if (row.acumulated_output < row.acumulated_computedvalue) {
          return (
            <el-tag size="large" type="danger" style="width:60%">
              {row.acumulated_output}
            </el-tag>
          );
        }
      }
    },
    {
      label: "标准累积",
      prop: "acumulated_computedvalue",
      formatter(row) {
        return row.acumulated_computedvalue.toFixed(0);
      }
    }
  ];

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载小时记录表数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  /** 撑满内容区自适应高度相关配置 */
  const adaptiveConfig: AdaptiveConfig = {
    /** 表格距离页面底部的偏移量，默认值为 `96` */
    // offsetBottom: 12,
    /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
    fixHeader: true
    /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
    // timeout: 60
    /** 表头的 `z-index`，默认值为 `100` */
    // zIndex: 100
  };

  const refreshData = () => {
    gethourlydata(toRaw(query_param.search_condition)).then(
      (res: { data: any }) => {
        dataList.value = res.data;
        loading.value = false;
      }
    );
  };

  watch(query_param, () => {
    refreshData();
  });

  return {
    query_cycle_param,
    refreshData,
    query_param,
    loading,
    columns,
    dataList,
    loadingConfig,
    adaptiveConfig
  };
}

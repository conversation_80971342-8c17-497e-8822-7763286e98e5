from app.dm.functions import getkpi, getproblems
from flask import Blueprint, request
from sqlalchemy import func, not_, and_, or_, cast, Time
import requests
from config import config, env
from app.public.functions import responseGet, responsePost, responseError
from app.dm.model.models_dm import Skuinfo, Shiftinfo, Lineinfo, Probleminfo, Restinfo, Scaninfo, Issuelog
from app.dm.functions import getReqQty, getReqQtyByShift, andonMsg, dmIssueSubmit, getRouting,epeiadjust
from extensions import db
from dateutil import rrule
import traceback
import datetime
import hashlib
api = Blueprint('bi3/t2mbAPI', __name__)


def getShifttype(starttime):
    yeban = '00:00'
    baiban = '08:00'
    zhongban = '16:00'
    if starttime >= yeban and starttime < baiban:
        shifttype = '夜班'
    elif starttime >= baiban and starttime < zhongban:
        shifttype = '白班'
    else:
        shifttype = '中班'
    return shifttype


@api.route("/changeShiftsku",methods=["POST"])
def changeShiftsku():
    res = request.json
    hhid = res.get('shiftid')
    sku = res.get('sku')
    linename=res.get('linename')
    try:
        routing = getRouting(sku, linename, 1)
        currentShift = db.session.query(Shiftinfo).filter(
            Shiftinfo.Id == hhid).first()
        currentShift.sku = sku
        currentShift.routing=routing
        scans=db.session.query(Scaninfo).filter(Scaninfo.shiftid==currentShift.Id).first()
        oldsku=scans.sku
        epeiadjust(linename,oldsku,-scans.scanqty)
        scans.sku=sku
        epeiadjust(linename,sku,scans.scanqty)
        db.session.commit()
        return responsePost('修改成功')
    except:
        db.session.rollback()
        return responseError('修改失败，请联系管理员')

@ api.route("/startShift", methods=["POST"])  # MB确认小时产出信息
def startShift():
    res = request.json
    linename = res.get('linename')
    starttime = res.get('starttime')
    shiftdate = res.get('shiftdate')
    sku = res.get('sku')
    headcount = 1
    openshift = db.session.query(Shiftinfo.Id,Shiftinfo.starttime).filter(
        Shiftinfo.finishtime.is_(None)).filter(Shiftinfo.linename == linename).first()
    if openshift:
        # print((datetime.date.today()+datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S'),openshift.starttime.strftime('%Y-%m-%d %H:%M:%S'))
        if ((datetime.date.today()+datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S'))==openshift.starttime.strftime('%Y-%m-%d %H:%M:%S'):
            db.session.query(Shiftinfo).filter(Shiftinfo.Id==openshift.Id).update({
                'starttime':datetime.date.today()
            })
            db.session.commit()
            return responsePost('开机成功')
        else:
            openstart = datetime.datetime.strftime(openshift.starttime, '%Y-%m-%d')
            return responseError(f'该产线在{openstart}的班次还没有结束，请把日期调整到{openstart}查看状况并操作')
    # 判断starttime是否在0:00和8：00之间
    shifttype = getShifttype(starttime)
    scantemplate = '简单模板'
    lineleader = '领班'
    try:
        routing = getRouting(sku, linename, headcount)
        newShift = Shiftinfo(shifttype=shifttype, scantemplate=scantemplate, lineleader=lineleader,
                             headcount=headcount, starttime=shiftdate+' '+starttime, linename=linename, sku=sku, routing=routing)
        db.session.add(newShift)
        db.session.commit()
        return responsePost('开机成功')
    except:
        return responseError('开机失败，请联系管理员')


@ api.route("/submitShift", methods=["POST"])  # MB开始班次
def submitShift():
    # print(1111111111111111111111)
    res = request.json
    hhisstop = res.get('hhisstop')
    linename = res.get('linename')
    sku = res.get('sku')
    changesku = res.get('changesku')
    hhid = res.get('hhid')
    starttime = res.get('starttime')
    finishtime = res.get('finishtime')
    shiftdate = res.get('shiftdate')
    scanqty = res.get('scanqty')
    ftime = shiftdate+' '+finishtime
    # print(222222222222222222222222222222,hhisstop,hhid)
    try:
        currentShift = db.session.query(Shiftinfo).filter(
            Shiftinfo.Id == hhid).first()
        sftime = currentShift.finishtime
        # print(222222222222222222222222222222,sftime)
        srouting = currentShift.routing
        scaninfo = db.session.query(Scaninfo).filter(
            Scaninfo.shiftid == hhid).first()
        if scaninfo:
            epeiadjust(linename,sku,scanqty-scaninfo.scanqty )
            scaninfo.scanqty = scanqty
        else:
            mystr = (linename + str(datetime.datetime.now())).encode('UTF-8')
            name = sku+hashlib.md5(mystr).hexdigest()[8:-8]
            newscan = Scaninfo(sn=name, shiftid=hhid,
                               scantime=ftime, scanqty=scanqty, sku=sku)
            epeiadjust(linename,sku,scanqty )
            db.session.add(newscan)
        shifttype = getShifttype(finishtime)
        if hhisstop == 1:
            currentShift.finishtime = ftime
        if hhisstop == 2:
            if finishtime == '00:00:00':
                ftime = datetime.datetime.strptime(
                    shiftdate+' 23:00:00', '%Y-%m-%d %H:%M:%S')+datetime.timedelta(hours=1)
            newrouting = getRouting(changesku, linename, 1)
            newshift = Shiftinfo(shifttype=shifttype, scantemplate='简单模板', lineleader='领班', headcount=1,
                                 starttime=ftime, linename=linename, sku=changesku, routing=newrouting)
            if not sftime:
                currentShift.finishtime = ftime
            db.session.add(newshift)
        elif hhisstop == 0 and (not sftime):
            if finishtime == '00:00:00':
                ftime = datetime.datetime.strptime(
                    shiftdate+' 23:00:00', '%Y-%m-%d %H:%M:%S')+datetime.timedelta(hours=1)
            newshift = Shiftinfo(shifttype=shifttype, scantemplate='简单模板', lineleader='领班', headcount=1,
                                 starttime=ftime, linename=linename, sku=sku, routing=srouting)
            db.session.add(newshift)
            # print(*****************,ftime)
            currentShift.finishtime = ftime
        db.session.commit()
        return responsePost('提交成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('提交失败，请联系管理员')


@ api.route("/closeShift", methods=["POST"])  # MB关闭班次
def closeShift():
    res = request.json
    check = ''
    if check:
        return responsePost('插入成功')
    else:
        return responseError('插入失败，请联系管理员')


@ api.route("/changeShift", methods=["POST"])  # MB切换班次
def changeShift():
    res = request.json
    check = ''
    if check:
        return responsePost('插入成功')
    else:
        return responseError('插入失败，请联系管理员')


@ api.route("/submitIssue", methods=["POST"])  # 登记QDC问题
def submitIssue():
    res = request.json
    check = dmIssueSubmit(res)
    if check:
        return responsePost('插入成功')
    else:
        return responseError('插入失败，请联系管理员')


@api.route('/getProblemstype', methods=['GET'])   # 根据产线获取问题类型列表
def getProblemstype():
    res = request.args
    linename = res.get('linename')
    problemtypes = db.session.query(Probleminfo).outerjoin(Lineinfo, Probleminfo.linename == Lineinfo.linegroup).filter(
        Probleminfo.sqdctype.in_(['质量', '交货', '效率'])).filter(
        or_(Lineinfo.linename == linename, Probleminfo.linename == 'ALL')).all()
    pdic = {}
    for p in problemtypes:
        if p.sqdctype in pdic.keys():
            pdic[p.sqdctype]['children'].append({
                'label': p.problemtype,
                'value': p.problemtype
            })
        else:
            pdic[p.sqdctype] = {
                'label': p.sqdctype,
                'value': p.sqdctype,
                'children': [{
                    'label': p.problemtype,
                    'value': p.problemtype
                }]
            }
    return responseGet('成功', {'problems': list(pdic.values())})


@api.route('/getAlllines', methods=['GET'])  # 获取小时记录表
def getAlllines():
    res = request.args
    dt = res.get('dt')
    lines = db.session.query(Lineinfo.linename).filter(
        Lineinfo.linegroup == 'Meltblown').filter(Lineinfo.isactive == 1).order_by(Lineinfo.linename).all()
    lineArr = []
    shiftDic = {}
    shifts = db.session.query(Lineinfo.linename, Shiftinfo.Id, Shiftinfo.shifttype, Shiftinfo.routing, Shiftinfo.sku).join(Shiftinfo, Lineinfo.linename == Shiftinfo.linename).filter(
        func.date(Shiftinfo.starttime) == func.date(dt)).filter(Shiftinfo.finishtime.is_(None)).all()
    for s in shifts:
        shiftDic[s.linename] = {
            'shiftid': s.Id,
            'shifttype': s.shifttype,
            'linestatus': 1,
            'linename': s.linename,
            'routing': s.routing,
            'sku': s.sku
        }
    for ll in lines:
        if ll.linename in shiftDic.keys():
            lineArr.append(shiftDic[ll.linename])
        else:
            lineArr.append({
                'shiftid': 0,
                'shifttype': '',
                'linestatus': 0,
                'linename': ll.linename,
                'routing': 0,
                'sku': ''
            })
    return responseGet('成功', {'linesInfo': lineArr})


def rounduptoevenhour(time_obj):
        # 将分钟部分修改为0，即整点
    time_obj+= datetime.timedelta(hours=1)
    newtime= time_obj.replace(minute=0)
    # 确保小时数为双数
    if time_obj.hour % 2 != 0:
         newtime+= datetime.timedelta(hours=1)

    return newtime

@api.route('/getHHMB', methods=['GET'])  # 获取小时记录表
def getHH():
    res = request.args
    linename = res.get('linename')
    linegroup = db.session.query(Lineinfo.linegroup).filter(
        Lineinfo.linename == linename).scalar()
    dt = res.get('dt')
    scans = []
    try:
        scans = db.session.query(Shiftinfo.sku, Scaninfo.scanqty, Shiftinfo.Id, Shiftinfo.shifttype, Shiftinfo.starttime, Shiftinfo.finishtime, Shiftinfo.headcount,
                                 Shiftinfo.routing, Shiftinfo.linename).outerjoin(Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).filter(
                                     Shiftinfo.linename == linename).filter(func.date(Shiftinfo.starttime) == dt).order_by(Shiftinfo.starttime).all()
    except Exception:
        traceback.print_exc()
        db.session.rollback()
    if len(scans) == 0:
        return responseError('没有生产记录')
    dic = {}
    step = 2
    start = ''
    accumreq = 0
    accumscan = 0
    # print('aaaaaaaaaaaaaaaaaaaaaaaa')
    hhArr = []
    for s in scans:
        start = s.starttime
        hours = step
        if not s.finishtime:
            if s.starttime.minute==0:
                finishtime = s.starttime+datetime.timedelta(hours=step)
            else:
                finishtime=rounduptoevenhour(s.starttime)
                hours = (finishtime-s.starttime).seconds/3600
            # 判断finishtime是否过了0点到第二天了
            if finishtime.day != s.starttime.day:
                finishtime = datetime.datetime.strptime(
                    dt+' 00:00:00', '%Y-%m-%d %H:%M:%S')+datetime.timedelta(days=1)
                hours = (finishtime-s.starttime).seconds/3600
        else:
            finishtime = s.finishtime
        # seconds between start and finishtime
            hours = (finishtime-start).seconds/3600
        scanqty = s.scanqty if s.scanqty else 0
        reqqty = round(hours/float(s.routing), 0)
        hhArr.append({
            'shiftid': s.Id,
            'sku': s.sku,
            'shifttype': s.shifttype,
            'routing': s.routing,
            'scanqty': scanqty,
            'reqqty': reqqty,
            'accumscan': accumscan+scanqty,
            'accumreq': accumreq+reqqty,
            'starttime': datetime.datetime.strftime(start, '%H:%M'),
            'finishtime': datetime.datetime.strftime(finishtime, '%H:%M'),
            'issues': []
        })
        accumreq += reqqty
        accumscan += scanqty
    try:
        suggestArr = getDailyIssues(linegroup, dt)
        for s in suggestArr:
            itime = s['idate']
            qty = 0
            issuemin = 0
            issueid = 0
            sku = ''
            sqdctype = ''
            problemtype = ''
            recordtime = ''
            if s['auditid']:
                dmissue = db.session.query(Issuelog).filter(
                    Issuelog.Id == s['auditid']).filter(Issuelog.linename == linename).first()
                if dmissue:
                    qty = dmissue.qty
                    issuemin = dmissue.issuemin
                    issueid = dmissue.Id
                    sku = dmissue.sku
                    sqdctype = dmissue.sqdctype
                    problemtype = dmissue.problemtype
                    recordtime = dmissue.recordtime
                    info = {
                        'Id': s['Id'],
                        'exeid': s['exeid'],
                        'sku': sku,
                        'auditid': issueid,
                        'sqdctype': sqdctype,
                        'problemtype': problemtype,
                        'recorder': s['cname'],
                        'recordtime': datetime.datetime.strftime(recordtime, "%Y-%m-%d %H:%M:%S") if recordtime else '',
                        'desc': s['content'],
                        'qty': qty,
                        'issuemin': issuemin
                    }
                    av = 1
                    for i in range(len(hhArr)):
                        if hhArr[i]['starttime'] <= itime and hhArr[i]['finishtime'] > itime:
                            hhArr[i]['issues'].append(info)
                            av = 0
                    if av:
                        hhArr[len(hhArr)-1]['issues'].append(info)
    except Exception as e:
        traceback.print_exc()
    outArr = hhArr[::]
    return responseGet('success', {'tableData': outArr})


def getDailyIssues(linename, shiftdate):  # 获取当日的问题清单
    res1 = requests.get(
        config[env].cloud_url +
        "welean/bi/issueAPI/getDailyIssues?linename=%s&shiftdate=%s" % (
            linename, shiftdate)
    )
    content = res1.json()
    # print(content)
    if content and content["meta"]["status"] == 200:
        return content['data']['suggests']
    return []

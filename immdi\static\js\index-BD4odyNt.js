import{d as y,p as b,n as C,aC as c,r as s,o as d,c as m,b as l,h as n,e as o,y as g,F as w,t as k,v as x,O as S,M as V,aD as B}from"./index-BnxEuBzx.js";const P=o("p",{class:"mb-2"}," 模拟后台根据不同角色返回对应路由，观察左侧菜单变化（管理员角色可查看系统管理菜单、普通角色不可查看系统管理菜单） ",-1),N={class:"card-header"},F=y({name:"PermissionPage",__name:"index",setup(U){var t;const i=b(()=>({width:"85vw",justifyContent:"start"})),a=C((t=c())==null?void 0:t.username),p=[{value:"admin",label:"管理员角色"},{value:"common",label:"普通角色"}];function _(){c().loginByUsername({username:a.value,password:"admin123"}).then(r=>{r.success&&(S().removeItem("async-routes"),V().clearAllCachePage(),B())})}return(r,u)=>{const v=s("el-option"),f=s("el-select"),h=s("el-card");return d(),m("div",null,[P,l(h,{shadow:"never",style:x(i.value)},{header:n(()=>[o("div",N,[o("span",null,"当前角色："+g(a.value),1)])]),default:n(()=>[l(f,{modelValue:a.value,"onUpdate:modelValue":u[0]||(u[0]=e=>a.value=e),class:"!w-[160px]",onChange:_},{default:n(()=>[(d(),m(w,null,k(p,e=>l(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1},8,["style"])])}}});export{F as default};

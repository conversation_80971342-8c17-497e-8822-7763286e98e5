<script setup lang="ts">
import { useColumns } from "./utils/columns";
import { computed, toRaw, watch, onMounted } from "vue";
import { getShiftName } from "@/views/functions/shift";
const {
  query_param,
  loading,
  columns,
  loadingConfig,
  dataList,
  adaptiveConfig
} = useColumns();

const props = defineProps<{
  search_condition: { selecteddate: string; machine: number };
  shift: string;
}>();
const shiftname = computed(() => getShiftName(props.shift));

onMounted(() => {
  query_param.search_condition.selecteddate =
    props.search_condition.selecteddate;
  query_param.search_condition.machine = props.search_condition.machine;
  query_param.search_condition.shift = props.shift;
});
</script>

<template>
  <div>
    <div>{{ shiftname }}</div>
    <pure-table
      ref="tableRef"
      border
      adaptive
      :adaptiveConfig="adaptiveConfig"
      row-key="id"
      alignWhole="center"
      showOverflowTooltip
      :loading="loading"
      :loading-config="loadingConfig"
      :data="dataList"
      :columns="columns"
    />
  </div>
</template>

<style lang="scss" scoped></style>

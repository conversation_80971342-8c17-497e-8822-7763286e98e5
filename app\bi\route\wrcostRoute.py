from flask import Blueprint, jsonify, request
import pymysql
from sqlalchemy import func, or_
from config import config, env
from app.public.functions import responseGet, responsePut, responseError, responsePost
from app.bi.model.models_emdi import Issuelog, Lineinfo, Userinfo, Deptinfo, Probleminfo
from app.bi.schemas import Issues_schema
from extensions import db
import datetime
import traceback
from app.bi.functions.ews import sendMailTread
api = Blueprint('bi/wrcostAPI', __name__)


@ api.route('/newBoard', methods=['POST'])
def newBoard():
    res = request.json
    # print('aaaaaaaa', res)
    owner = res.get('owner')[1]
    status = 'open'
    close = 0
    recorder = 'EMDI'
    recordtime = datetime.datetime.now()
    ctmeasure = res.get('ctmeasure')
    desc = res.get('desc')
    duedate = res.get('duedate')
    linename = res.get('linename')
    types = res.get('types')
    if types:
        sqdctype = types[0].split('（')[0]
        problemtype = types[1]
    qty = res.get('qty')
    rootcause = res.get('rootcause')
    if not rootcause:
        rootcause = ''
    if not ctmeasure:
        ctmeasure = ''
    id = res.get('id')
    if id:
        issue = db.session.query(Issuelog).filter(Issuelog.id == id).scalar()
        issue.ctmeasure = ctmeasure
        issue.owner = owner
        issue.pastdue = issue.pastdue+1
        issue.desc = desc
        issue.duedate = duedate
        issue.linename = linename
        if types:
            issue.sqdctype = sqdctype
            issue.problemtype = problemtype
        issue.qty = qty
        issue.rootcause = rootcause
        db.session.commit()
        return responsePost("更新成功", {'info': 'success'})
    else:
        try:
            myItem = Issuelog(owner=owner, status=status, close=close, recorder=recorder, recordtime=recordtime, firstdate=recordtime, ctmeasure=ctmeasure, pastdue=0,
                              desc=desc, duedate=duedate, linename=linename, sqdctype=sqdctype, problemtype=problemtype, qty=qty, rootcause=rootcause, csreason='', inform=0)
            db.session.add(myItem)
            db.session.commit()
            mail = db.session.query(Userinfo).filter(Userinfo.username == owner).first()
            if mail:
                usermail = mail.email
                sendMailTread([usermail], '你收到'+linename + '产线的一个新的问题', '问题描述：'+desc)
            return responsePost("更新成功", {'info': 'success'})
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('更新失败,请联系管理员')


@ api.route('/getSelects', methods=['GET'])
def getSelects():
    res = request.args
    if res:
        linename = res.get('linename')
    else:
        linename = '不存在'
    problems = getProblemlist(linename)
    users = getUserlist()
    lines = getLinelist()
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, users=users, lines=lines, problems=problems)


@ api.route('/getProblem', methods=['GET'])
def getProblem():
    res = request.args
    linename = res.get('linename')
    problems = getProblemlist(linename)
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta,  problems=problems)


def getProblemlist(linename):
    users = db.session.query(Probleminfo.sqdctype, Probleminfo.problemtype).filter(
        or_(Probleminfo.linename == linename, Probleminfo.linename == 'ALL')).all()
    dic = {}
    print(users)
    for m in range(len(users)):
        dept = users[m][0]
        if dept == '质量':
            dept = '质量（FTT)'
        linename = users[m][1]
        if dept in dic.keys():
            dic[dept]['children'].append({'value': linename, 'label': linename})
        else:
            dic[dept] = {
                'label': dept,
                'value': dept,
                'children': [{'value': linename, 'label': linename}]
            }
    outArray = list(dic.values())
    return outArray


def getLinelist():
    users = db.session.query(Lineinfo.VSM, Lineinfo.linename).all()
    outArr = []
    for m in range(len(users)):
        linename = users[m][1]
        outArr.append({'value': linename, 'label': linename})
    return outArr


def getUserlist():
    users = db.session.query(Deptinfo.deptname, Userinfo.username).outerjoin(
        Userinfo, Deptinfo.id == Userinfo.deptid).all()
    dic = {}
    for m in range(len(users)):
        dept = users[m][0]
        linename = users[m][1]
        if dept in dic.keys():
            dic[dept]['children'].append({'value': linename, 'label': linename})
        else:
            dic[dept] = {
                'label': dept,
                'value': dept,
                'children': [{'value': linename, 'label': linename}]
            }
    outArray = list(dic.values())
    return outArray


@ api.route('/acceptBoard', methods=['PUT'])
def acceptBoard():
    res = request.json
    issueid = res.get('id')
    issue = db.session.query(Issuelog).filter(Issuelog.id == issueid).scalar()
    if issue:
        issue.close = 1
        issue.findate = datetime.date.today()
        db.session.commit()
        return responsePut('NCMR取消完成！', {'info': 'success'})
    else:
        return responseError('没有找到该NCMR,请联系管理员')


@ api.route('/getBoards', methods=['GET'])
def getBoards():
    res = request.args
    query = res.get('query')
    line = res.get('line')
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    meta = {'status': 200, 'msg': '成功'}
    issues = db.session.query(Issuelog).outerjoin(Lineinfo, Issuelog.linename == Lineinfo.linename).filter(
        Issuelog.desc.like('%{0}%'.format(query))).filter(Issuelog.close == 0).order_by(Issuelog.duedate)
    total = db.session.query(func.count(Issuelog.id)).outerjoin(Lineinfo, Issuelog.linename == Lineinfo.linename).filter(
        Issuelog.desc.like('%{0}%'.format(query))).filter(Issuelog.close == 0)
    if line:
        issues = issues.filter(Lineinfo.linegroup == line)
        total = total.filter(Lineinfo.linegroup == line)
    issues = issues.paginate(
        pagenum, pagesize, error_out=False).items
    total = total.scalar()
    outlist = Issues_schema.dump(issues)
    for o in outlist:
        if o['duedate'] == '0000-00-00':
            o['duedate'] = ''
    lines = getLines()
    return jsonify(meta=meta, data=outlist, total=total, lines=lines)


@ api.route('/getAccounts', methods=['GET'])
def getAccounts():
    res = request.args
    query = res.get('query')
    line = res.get('line')
    meta = {'status': 200, 'msg': '成功'}
    issues = db.session.query(Issuelog.id, Issuelog.firstdate, Issuelog.linename, Issuelog.recordtime, Issuelog.sqdctype, Issuelog.problemtype,
                              Issuelog.recorder, Issuelog.desc, Issuelog.qty, Issuelog.owner, Issuelog.rootcause, Issuelog.ctmeasure,
                              Issuelog.pastdue, Issuelog.duedate, Deptinfo.deptname).outerjoin(Lineinfo, Issuelog.linename == Lineinfo.linename).outerjoin(
        Userinfo, Userinfo.username == Issuelog.owner).outerjoin(Deptinfo, Deptinfo.id == Userinfo.deptid).filter(
        Issuelog.desc.like('%{0}%'.format(query))).filter(Issuelog.close == 0).order_by(Issuelog.duedate)
    if line:
        issues = issues.filter(Lineinfo.linegroup == line)
    issues = issues.all()
    outlist = Issues_schema.dump(issues)
    wkArr = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
    url = config[env].base_url+'BI/boardimg/'
    rowDic = {'EHS': {'dept': 'EHS', 'deptimg': [url+'EHS.jpg']}, '生产': {'dept': '生产', 'deptimg': [url+'生产.jpg']}, 'Lean': {'dept': 'Lean', 'deptimg': [url+'Lean.jpg']},
              '工程': {'dept': '工程', 'deptimg': [url+'工程.jpg', url+'工程2.jpg']}, '研发': {'dept': '研发', 'deptimg': [url+'研发.jpg']}, '维修': {'dept': '维修', 'deptimg': [url+'维修.jpg']},
              '计划': {'dept': '计划', 'deptimg': [url+'计划.jpg', url+'计划2.jpg']}, '质量': {'dept': '质量', 'deptimg': [url+'质量.jpg']}, '仓库': {'dept': '仓库', 'deptimg': [url+'仓库.jpg']},
              '采购': {'dept': '采购', 'deptimg': [url+'采购.jpg']}}
    today = datetime.date.today()
    dateArr = []
    weekArr = []
    for i in range(14):
        dd = today+datetime.timedelta(days=i)
        wd = dd.weekday()
        if wd in [0, 1, 2, 3, 4]:
            dateArr.append(dd.strftime('%m-%d'))
            weekArr.append(wkArr[wd])
    for o in outlist:
        if o['duedate'] == '0000-00-00':
            o['duedate'] = ''
        if o['deptname'] in rowDic.keys():
            if o['duedate'][5:] < dateArr[0]:
                if '过期未处理' in rowDic[o['deptname']].keys():
                    rowDic[o['deptname']]['过期未处理'].append(o)
                else:
                    rowDic[o['deptname']]['过期未处理'] = [o]
            elif o['duedate'][5:] > dateArr[len(dateArr)-1]:
                if '两周后' in rowDic[o['deptname']].keys():
                    rowDic[o['deptname']]['两周后'].append(o)
                else:
                    rowDic[o['deptname']]['两周后'] = [o]
            else:
                if o['duedate'][5:] in rowDic[o['deptname']].keys():
                    rowDic[o['deptname']][o['duedate'][5:]].append(o)
                else:
                    rowDic[o['deptname']][o['duedate'][5:]] = [o]

    dateArr.append('两周后')
    dateArr.insert(0, '过期未处理')
    weekArr.append(' ')
    weekArr.insert(0, ' ')
    lines = getLines()
    rowArr = list(rowDic.values())
    return jsonify(meta=meta, data=rowArr, lines=lines, pp=dateArr, ww=weekArr)


def getLines():
    connect = pymysql.connect(host=config[env].wrdb['host'], port=config[env].wrdb['port'], user=config[env].wrdb['user'],
                              password=config[env].wrdb['password'], database=config[env].wrdb['database'], charset=config[env].wrdb['charset'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    sql = "select linegroup from mdi_lineinfo where active=1 group by linegroup"
    cursor.execute(sql)
    rst = cursor.fetchall()
    outData = []
    for r in rst:
        outData.append({'name': r[0], 'value': r[0]})
    return outData


@ api.route('/getWR', methods=['GET'])
def getWR():
    connect = pymysql.connect(host=config[env].wrdb['host'], port=config[env].wrdb['port'], user=config[env].wrdb['user'],
                              password=config[env].wrdb['password'], database=config[env].wrdb['database'], charset=config[env].wrdb['charset'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    sql2 = '''select sum(actualout) as produceQty,sum(requireout) as requireQty from mdi_hourinfo where date(mdi_hourinfo.hourstart)=current_date()'''
    cursor.execute(sql2)
    r2 = cursor.fetchone()
    r20 = r2[0]
    r21 = r2[1]
    produceQty = int(r20) if r20 else 0
    requireQty = int(r21) if r21 else 0
    sql4 = '''select count(id) as num, sum(if(currentshift>0,1,0)) as act from mdi_lineinfo where active=1'''
    cursor.execute(sql4)
    r4 = cursor.fetchone()
    r41 = r4[1]
    r41 = r41 if r41 else 0
    aRate = round(r41/int(r4[0])*100, 0)
    sql5 = '''select linename,sum(actualout) as produceQty,sum(requireout) as requireQty from mdi_hourinfo
            left join mdi_shiftinfo on mdi_shiftinfo.id=mdi_hourinfo.shiftid
            left join mdi_lineinfo on mdi_lineinfo.id = mdi_shiftinfo.lineid
            where date(mdi_hourinfo.hourstart)='2022-04-13' group by linename'''
    cursor.execute(sql5)
    rlist = cursor.fetchall()
    linesdata = []
    if rlist:
        for r in rlist:
            if r[2] > 0:
                dic = {}
                dic['name'] = r[0]
                dic['value'] = round((float(r[1])/float(r[2]))*100, 1)
                linesdata.append(dic)
    print(linesdata)
    cRate = round(produceQty/requireQty*100, 2) if requireQty else 0
    return responseGet("获取WR-EMDI数据成功", {'produceQty': str(produceQty), 'requireQty': str(requireQty),
                                         'cRate': str(cRate), 'aRate': str(aRate), 'linesdata': linesdata})


@ api.route('/getCmonth', methods=['GET'])
def getCmonth():
    r = request.args
    print(r)
    stime = r.get('stime')
    etime = r.get('etime')
    area = r.get('area')
    if area:
        sql = """
        select right(date,5),qq*95 red,qq*5 yellow,round(act/req*100,1) ftt from (select date(starttime) as date,ctarget*earnhour qq,earnhour,
        sum(actualout) act,sum(requireout) req,linegroup
        from mdi_hourinfo
        left join mdi_shiftinfo on mdi_hourinfo.shiftid=mdi_shiftinfo.id
        left join mdi_lineinfo on mdi_shiftinfo.lineid=mdi_lineinfo.id
        left join mdi_kpi on mdi_kpi.lineid=mdi_lineinfo.linegroup
        where linegroup='%s' and date(starttime) between '%s' and  '%s'
        group by date(starttime),linegroup) as A
        left join
        (select sum(qty) as defect,linegroup,date(recordtime) as date2 from mdi_issuelog
        left join mdi_lineinfo on mdi_issuelog.linename=mdi_lineinfo.linename
        where sqdctype='效率' and linegroup='%s' and date(recordtime) between '%s' and  '%s'
        group by date(recordtime),linegroup) as B
        on A.date=B.date2 and A.linegroup=B.linegroup
        """ % (area, stime, etime, area, stime, etime)
        sql2 = """select count(abnormalqty) as cqty,round(mdi_probleminfo.trigger,0) as tg,mdi_shiftreport.sqdctype,mdi_shiftreport.problemtype, mdi_lineinfo.linegroup
        from mdi_shiftreport left join mdi_planinfo on mdi_planinfo.id=mdi_shiftreport.planid
        left join mdi_lineinfo on mdi_lineinfo.id=mdi_planinfo.lineid
        left join mdi_probleminfo on mdi_probleminfo.sqdctype=mdi_shiftreport.sqdctype and mdi_probleminfo.problemtype=mdi_shiftreport.problemtype
        where mdi_lineinfo.linegroup='%s' and plandate between '%s' and  '%s'
        group by problemtype""" % (stime, etime, area)
    else:
        sql = """select right(date,5),qq*95 red,qq*5 yellow,round(act/req*100,1) ftt from (select date(starttime) as date,ctarget*earnhour qq,earnhour,
        sum(actualout) act,sum(requireout) req,linegroup
        from mdi_hourinfo left join mdi_shiftinfo on mdi_hourinfo.shiftid=mdi_shiftinfo.id
        left join mdi_lineinfo on mdi_shiftinfo.lineid=mdi_lineinfo.id
        left join mdi_kpi on mdi_kpi.lineid=mdi_lineinfo.linegroup
        group by date(starttime)) as A
        left join
        (select sum(qty) as defect,linegroup,date(recordtime) as date2 from mdi_issuelog
        left join mdi_lineinfo on mdi_issuelog.linename=mdi_lineinfo.linename
        where sqdctype='效率' group by date(recordtime)) as B
        on A.date=B.date2 and A.linegroup=B.linegroup
        where date between '%s' and  '%s'""" % (stime, etime)
        sql2 = """
        select count(abnormalqty) as cqty,round(mdi_probleminfo.trigger,0) as tg,mdi_shiftreport.sqdctype,mdi_shiftreport.problemtype, mdi_lineinfo.linegroup
        from mdi_shiftreport left join mdi_planinfo on mdi_planinfo.id=mdi_shiftreport.planid
        left join mdi_lineinfo on mdi_lineinfo.id=mdi_planinfo.lineid
        left join mdi_probleminfo on mdi_probleminfo.sqdctype=mdi_shiftreport.sqdctype and mdi_probleminfo.problemtype=mdi_shiftreport.problemtype
        where plandate between '%s' and  '%s'
        group by problemtype""" % (stime, etime)
    connect = pymysql.connect(host=config[env].wrdb['host'], port=config[env].wrdb['port'], user=config[env].wrdb['user'],
                              password=config[env].wrdb['password'], database=config[env].wrdb['database'], charset=config[env].wrdb['charset'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    cursor.execute(sql)
    rst = cursor.fetchall()
    outArr = {
        'fttx': [],
        'red': [],
        'yellow': [],
        'green': [],
        'ftt': [],
        'fttmin': 0,
        'fttmax': 100,
        'paretox': [],
        'paretotarget': [],
        'paretodata': []
    }
    for r in rst:
        outArr['fttx'].append(r[0])
        outArr['red'].append(float(r[1]))
        outArr['yellow'].append(float(r[2]))
        outArr['green'].append(100-float(r[1])-float(r[2]))
        outArr['ftt'].append(float(r[3]))
    outArr['fttmin'] = min(outArr['ftt'])-1 if len(outArr['ftt']) > 0 else 50
    outArr['fttmax'] = max(outArr['ftt']) if len(outArr['ftt']) > 0 else 100
    green = outArr['fttmax']-outArr['red'][0]-outArr['yellow'][0]
    for i in range(len(outArr['green'])):
        outArr['green'][i] = green
    cursor.execute(sql2)
    rst2 = cursor.fetchall()
    for r in rst2:
        paretox = r[3] if r[3] else '其他'
        target = int(r[1]) if r[1] else 5
        pdata = int(r[0]) if r[0] else 0
        outArr['paretox'].append(paretox)
        outArr['paretotarget'].append(target)
        outArr['paretodata'].append(
            {
                'value': pdata,
                'itemStyle': {
                    'color': '#A2323D' if pdata > target else '#7CFFB2'
                }
            }
        )
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data=outArr)

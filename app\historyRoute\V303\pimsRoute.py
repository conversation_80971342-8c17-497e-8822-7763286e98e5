from flask import Blueprint, request, json
from app.welean.model.models_welean import Auth, Permission, Getscore, Userinfo, Services
from extensions import db
import hashlib
from datetime import datetime
from sqlalchemy import func, or_, desc
import math
import os
from app.public.functions import responseGet, login_required, responsePost, responseError, responsePut
from app.welean.functions import getServer, defaultAuth, PDFthread
api = Blueprint('welean/V303/pimsAPI', __name__)

# 目前办公两种服务，services里的picToExcel和excelTrans，对应options里分类services的上锁的pics和trans


@ api.route('/delMyPTOE', methods=['PUT'])
@ login_required
def delMyPTOE():
    res = request.json
    id = res.get('id')
    url = res.get('url')
    db.session.query(Services).filter(Services.Id == id).update({'status': 2})
    db.session.commit()
    picname = url[url.rfind('/')+1:]
    excelname = picname.split('.')[0]+'.xlsx'
    p_path = getServer()['servicesPath']+picname
    e_path = getServer()['servicesPath']+excelname
    print(p_path, e_path)
    if os.path.isfile(p_path):
        abspath = os.path.abspath(p_path)
        os.remove(abspath)
    if os.path.isfile(e_path):
        abspath = os.path.abspath(e_path)
        os.remove(abspath)
    return responsePut("取消成功")


@api.route('/getMyPTOE', methods=['GET'])
@login_required
def getMyPTOE():
    res = request.args
    eid = res.get('eid')
    trans = db.session.query(Services).filter(
        Services.stype == 'picToExcel').filter(Services.eid == eid).order_by(desc(Services.starttime)).limit(5).all()
    myPTOE = []
    for t in trans:
        dic = {
            'id': t.Id,
            'starttime': datetime.strftime(t.starttime, "%Y-%m-%d %H:%M:%S"),
            'status': t.status,
            'url': getServer()['servicesUrl']+t.urls,
            'excelurl': getServer()['servicesUrl']+t.urls.split('.')[0]+'.xlsx'
        }
        myPTOE.append(dic)
    drawTimes = getDrawTimes(eid)
    return responseGet("获取列表成功", {'myPTOE': myPTOE,  'drawTimes': drawTimes})


@ api.route('/delMyTrans', methods=['PUT'])
@ login_required
def delMyTrans():
    res = request.json
    id = res.get('id')
    url = res.get('url')
    db.session.query(Services).filter(Services.Id == id).update({'status': 2})
    db.session.commit()
    c_path = getServer()['servicesPath']+url[url.rfind('/')+1:]
    if os.path.isfile(c_path):
        abspath = os.path.abspath(c_path)
        print(abspath)
        os.remove(abspath)
    return responsePut("取消成功")


@api.route('/getMyTrans', methods=['GET'])
@login_required
def getMyTrans():
    res = request.args
    eid = res.get('eid')
    trans = db.session.query(Services).filter(
        Services.stype == 'excelTrans').filter(Services.eid == eid).order_by(desc(Services.starttime)).limit(5).all()
    myTrans = []
    for t in trans:
        dic = {
            'id': t.Id,
            'starttime': datetime.strftime(t.starttime, "%Y-%m-%d %H:%M:%S"),
            'status': t.status,
            'url': getServer()['servicesUrl']+t.urls
        }
        myTrans.append(dic)
    drawTimes = getDrawTimes(eid)
    maxSize = db.session.query(func.sum(Getscore.getscore)).filter(
        Getscore.eid == eid).group_by(Getscore.eid).scalar()
    if maxSize:
        maxSize = maxSize/10
        if maxSize < 50:
            maxSize = 50
    else:
        maxSize = 50
    return responseGet("获取列表成功", {'myTrans': myTrans, 'maxSize': maxSize, 'drawTimes': drawTimes})


def getDrawTimes(eid):
    now = datetime.now()
    mstart = datetime(now.year, now.month, 1)
    drawed = getDrawed(eid, now, mstart)
    totalScore = db.session.query(func.sum(Getscore.getscore)).filter(Getscore.eid == eid).filter(
        Getscore.getdate.between(mstart, now)).group_by(Getscore.eid).scalar()
    if not totalScore:
        totalScore = 0
    myTimes = math.ceil(math.sqrt(totalScore)*0.18)-drawed
    print(totalScore, drawed, myTimes)
    return myTimes


def getDrawed(eid, now, mstart):
    getTimes = db.session.query(func.count(Services.Id)).filter(Services.eid == eid).filter(
        Services.starttime.between(mstart, now)).group_by(Services.eid).scalar()
    if not getTimes:
        getTimes = 0
    return getTimes


@ api.route('/uploadPTOE', methods=['POST'])
@ login_required
def uploadPTOE():
    file_obj = request.files.get('file')
    eid = request.headers["eid"]
    stype = request.headers["stype"]
    mystr = ('picToExcel' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    print(file_obj)
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        try:
            svs = Services(starttime=datetime.now(), urls=name +
                           appendix, eid=eid, stype=stype, status=0)
            db.session.add(svs)
            db.session.commit()
            file_obj.save(getServer()['servicesPath']+name+appendix)
            return responsePost("更新成功", {'upload_url': getServer()['servicesUrl']+name+appendix})
        except Exception:
            db.session.rollback()
    return responseError("上传文件失败，请联系管理员")


@ api.route('/uploadTrans', methods=['POST'])
@ login_required
def uploadTrans():
    file_obj = request.files.get('file')
    eid = request.headers["eid"]
    stype = request.headers["stype"]
    options = request.headers["options"]
    mystr = ('excelTrans' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        if appendix.lower() != '.xlsx':
            return responseError("上传文件失败，请联系管理员")
        try:
            svs = Services(starttime=datetime.now(), urls=name +
                           appendix, eid=eid, stype=stype, status=0, options=options)
            db.session.add(svs)
            db.session.commit()
            file_obj.save(getServer()['servicesPath']+name+appendix)
            return responsePost("更新成功", {'upload_url': getServer()['servicesUrl']+name+appendix})
        except Exception:
            db.session.rollback()
    return responseError("上传文件失败，请联系管理员")


@api.route('/cpdf', methods=['GET'])
def conpdf():
    pdfPath = getServer()['newsPath']+'000/demo.pdf'
    imagePath = getServer()['newsPath']+'000/pics/'
    PDFthread(pdfPath, imagePath)

    return responseGet("成功")


@api.route('/getTools', methods=['GET'])
@login_required
def getTools():
    res = request.args
    user = json.loads(res.get('user'))
    eid = user['eid']
    myAuth = db.session.query(Auth.auth).filter(Auth.eid == eid).scalar()
    if not myAuth:
        myAuth = defaultAuth
    else:
        myAuth = (defaultAuth+','+myAuth).split(',')
    permission = db.session.query(Permission).filter(Permission.ptype == 'pims').filter(Permission.pid >= 0).filter(
        or_(Permission.Id.in_(myAuth), Permission.needauth == 0)).order_by(Permission.pid).order_by(Permission.order).all()
    plist = {}
    pArr = []
    for p in permission:
        if p.pid == 0:
            plist[p.Id] = {'name': p.name, 'children': []}
        else:
            if p.pid in plist.keys():
                plist[p.pid]['children'].append({
                    'icon': getServer()['baseUrl']+'pimstools/'+p.icon,
                    'url': p.url,
                    'name': p.name
                })
    for v in plist.values():
        pArr.append(v)
    return responseGet("获取列表成功", {'myAuth': pArr})


@api.route('/getMyStatus', methods=['GET'])
@login_required
def getMyStatus():
    res = request.args
    eid = res.get('eid')
    plant = res.get('plant')
    trophyArr = []
    outArr = []
    index = 0
    totalLevel = db.session.query(func.sum(Getscore.getscore)).filter(
        Getscore.eid == eid).group_by(Getscore.eid).scalar()
    ot = totalLevel
    for i in range(1, 8):
        trophyArr.insert(0, pow(5, i))
    for j in trophyArr:
        index = totalLevel//j
        totalLevel = totalLevel % j
        outArr.append(index)
    sql = """
    SELECT b.* FROM (SELECT t.*, @rownum := @rownum + 1 AS rownum FROM (SELECT @rownum := 0) r,
    (select wl_userinfo.eid,sum(getscore) from wl_getscore  inner join wl_userinfo on wl_userinfo.eid=wl_getscore.eid
    where year(getdate)=%d and active=1 and plant='%s'  group by eid order by sum(getscore) desc ) AS t) AS b where eid='%s'
    """ % (datetime.now().year, plant, eid)
    cursor = db.session.execute(sql)
    result = cursor.fetchall()
    totalPeople = db.session.query(Getscore.eid).join(Userinfo, Userinfo.eid == Getscore.eid).filter(Userinfo.plant == plant).filter(Userinfo.active == 1).filter(
        func.year(Getscore.getdate) == datetime.now().year).group_by(Getscore.eid).all()
    myStatus = {
        'level': ot,
        'score': int(result[0][1]),
        'rank': int(result[0][2]),
        'total': len(totalPeople)
    }
    return responseGet("获取列表成功", {'myStatus': myStatus, 'tArr': outArr})

from flask import Blueprint, request
from app.welean.model.models_welean import Newspaper, Questiontask, Settings, Questionresult, Questions, Relationquestions
from extensions import db
from datetime import datetime, date
from sqlalchemy import func,  or_, and_, desc
from app.public.functions import responseError, responsePost, responseGet,  login_required
from app.welean.functions import getServer, getScore
api = Blueprint('welean/V302/trainingAPI', __name__)


@ api.route('/submitTask', methods=['POST'])
@ login_required
def submitTask():
    res = request.json
    eid = res.get('eid')
    testdate = date.today()
    taskid = res.get('taskid')
    score = res.get('score')
    passscore = res.get('passscore')
    myresult = db.session.query(Questionresult).filter(
        Questionresult.eid == eid).filter(Questionresult.taskid == taskid).scalar()
    if myresult:
        if myresult.score < passscore and score >= passscore:
            myresult.score = score
            myresult.testdate = testdate
            db.session.commit()
            getScore(eid, '问答', taskid)
        else:
            if score > myresult.score:
                myresult.score = score
                myresult.testdate = testdate
                db.session.commit()
        return responsePost('修改成功')
    else:
        try:
            rst = Questionresult(taskid=taskid, testdate=testdate,
                                 eid=eid, score=score, firstscore=score)
            db.session.add(rst)
            if score >= passscore:
                getScore(eid, '问答', taskid)
            db.session.commit()
        except Exception:
            db.session.rollback()
            return responseError('新增失败')
        return responsePost('新增成功')


@api.route('/getExams', methods=['GET'])
@login_required
def getExams():
    res = request.args
    taskid = int(res.get('id'))
    QuestionList = []
    questions = db.session.query(Questions).outerjoin(
        Relationquestions, Questions.Id == Relationquestions.questionid).filter(Relationquestions.taskid == taskid).all()
    passsore = db.session.query(Questiontask.passscore).filter(
        Questiontask.Id == taskid).scalar()
    for q in questions:
        if q.qtype == 2:
            QuestionOptionList = ''
        else:
            QuestionOptionList = []
            if q.optionA:
                QuestionOptionList.append(
                    {
                        "optionID": "A",
                        "fldOptionText": q.optionA,
                        "checked": False,
                        "fldOptionIndex": 1
                    }
                )
            if q.optionB:
                QuestionOptionList.append(
                    {
                        "optionID": "B",
                        "fldOptionText": q.optionB,
                        "checked": False,
                        "fldOptionIndex": 2
                    }
                )
            if q.optionC:
                QuestionOptionList.append(
                    {
                        "optionID": "C",
                        "fldOptionText": q.optionC,
                        "checked": False,
                        "fldOptionIndex": 3
                    }
                )
            if q.optionD:
                QuestionOptionList.append(
                    {
                        "optionID": "D",
                        "fldOptionText": q.optionD,
                        "checked": False,
                        "fldOptionIndex": 4
                    }
                )
            if q.optionE:
                QuestionOptionList.append(
                    {
                        "optionID": "E",
                        "fldOptionText": q.optionE,
                        "checked": False,
                        "fldOptionIndex": 5
                    }
                )
        dic = {
            'picUrl': getServer()['questionsUrl']+q.picurl if q.picurl else '',
            'checkorder': q.checkorder,
            'answer': q.answer,
            'questionID': str(q.Id),
            'fldName': q.question,
            'fldAnswer': '',
            'questionType': q.qtype,
            'QuestionOptionList': QuestionOptionList
        }
        QuestionList.append(dic)
    return responseGet("获取列表成功", {'qList': QuestionList, 'passscore': passsore})


@api.route('/getMyTasks', methods=['GET'])
@login_required
def getMyTasks():
    res = request.args
    eid = res.get('eid')
    newsid = res.get('newsid')
    tasks = db.session.query(Questiontask.Id, Questiontask.passscore, Questiontask.startdate, Questionresult.testdate, Questionresult.firstscore, Questionresult.score).outerjoin(
        Questionresult, Questionresult.taskid == Questiontask.Id).filter(Questiontask.newsid == newsid).filter(or_(Questionresult.eid == eid, Questionresult.eid.is_(None))).all()
    myTasks = []
    for t in tasks:
        dic = {
            'taskid': str(t.Id),
            'testdate': datetime.strftime(t.startdate, "%Y-%m-%d"),
            'firstscore': int(t.firstscore) if t.firstscore else '未参加',
            'score': int(t.score) if t.score else '未参加',
            'passscore': int(t.passscore)
        }
        myTasks.append(dic)
    return responseGet("获取列表成功", {'myTasks': myTasks})


@api.route('/getAllTasks', methods=['GET'])
@login_required
def getAllTasks():
    res = request.args
    eid = res.get('eid')
    nb = int(res.get('nb'))
    keywords = res.get('keywords')
    myTasks = []
    tasks = db.session.query(Questiontask.Id, Questiontask.tasktitle, Questiontask.newsid, Questiontask.piccover, Questiontask.passscore, Questiontask.startdate, Questionresult.testdate,
                             Questionresult.firstscore, Questionresult.score).outerjoin(
        Questionresult, Questionresult.taskid == Questiontask.Id).filter(Questionresult.score >= Questiontask.passscore).filter(Questionresult.eid == eid)
    if keywords:
        tasks = tasks.filter(Questiontask.tasktitle.like('%{0}%'.format(keywords)))
    tasks = tasks.order_by(desc(Questiontask.startdate)).limit(nb).all()
    for t in tasks:
        dic = {
            'tasktitle': t.tasktitle,
            'id': t.newsid,
            'currenttask': str(t.Id),
            'piccover': getServer()['questionsUrl'] + t.piccover,
            'testdate': datetime.strftime(t.startdate, "%Y-%m-%d"),
            'firstscore': t.firstscore,
            'score': t.score,
            'passscore': t.passscore
        }
        myTasks.append(dic)
    return responseGet("获取列表成功", {'contents': myTasks})


@api.route('/getCourses', methods=['GET'])
@login_required
def getCourses():
    res = request.args
    plant = res.get('plant')
    eid = res.get('eid')
    subcate = res.get('subcate')
    current = int(res.get('current'))  # 0未完成，1已完成，2全部
    coursesArr = []
    mycourses = db.session.query(Newspaper.Id, Newspaper.currenttask, Newspaper.title, Newspaper.subcate, Newspaper.postdate, Newspaper.reservedate, Newspaper.piccover, Questionresult.eid
                                 ).outerjoin(Questiontask, Newspaper.currenttask == Questiontask.Id).outerjoin(
        Questionresult, Questionresult.taskid == Questiontask.Id).filter(or_(Newspaper.plant == plant, Newspaper.plant == 'ALL')).filter(
            Newspaper.cate == 'training').filter(Newspaper.subcate == subcate).filter(Questionresult.eid == eid).filter(Questionresult.score >= Questiontask.passscore).all()
    blankcourses = db.session.query(Newspaper).outerjoin(Questiontask, Newspaper.currenttask == Questiontask.Id).outerjoin(
        Questionresult, Questionresult.taskid == Questiontask.Id).filter(or_(Newspaper.plant == plant, Newspaper.plant == 'ALL')).filter(
            Newspaper.cate == 'training').filter(Newspaper.subcate == subcate).group_by(Newspaper.Id).all()
    fcoursesDic = {}
    coursesDic = {}
    for mc in mycourses:
        fcoursesDic[mc.Id] = mc
    if current == 1:
        coursesDic = fcoursesDic
    elif current == 0:
        for bc in blankcourses:
            if bc.Id not in fcoursesDic.keys():
                coursesDic[bc.Id] = bc
    elif current == 2:
        for bc in blankcourses:
            coursesDic[bc.Id] = bc
    courses = coursesDic.values()

    for c in courses:
        dic = {
            'id': c.Id,
            'currenttask': c.currenttask,
            'name': c.title,
            'subcate': c.subcate,
            'postdate': datetime.strftime(c.postdate, "%Y-%m-%d"),
            'updatedate': datetime.strftime(c.reservedate, "%Y-%m-%d"),
            'src': getServer()['newsUrl']+str(c.Id)+'/'+c.piccover,
        }
        coursesArr.append(dic)
    item = {
        'qty': len(blankcourses),
        'nfqty': len(mycourses),
        'rate': round(len(mycourses)/len(blankcourses)*100, 0)
    }
    return responseGet("获取列表成功", {'courses': coursesArr, 'item': item})


@api.route('/getTrainingMenu', methods=['GET'])
@login_required
def getTrainingMenu():
    res = request.args
    plant = res.get('plant')
    eid = res.get('eid')
    today = date.today()
    tasks = db.session.query(Questiontask.Id, Questiontask.passscore, Questiontask.piccover, Questiontask.tasktitle, Questiontask.duedate, Questiontask.newsid
                             ).filter(or_(Questiontask.plant == plant, Questiontask.plant == 'ALL')).filter(and_(
                                 today >= Questiontask.startdate, today <= Questiontask.duedate)).all()
    tasksArr = []
    for t in tasks:
        mytask = db.session.query(Questionresult).filter(
            Questionresult.taskid == t.Id).filter(Questionresult.eid == eid).filter(Questionresult.score >= t.passscore).scalar()
        if not mytask:
            dic = {
                'id': t.Id,
                'newsid': t.newsid,
                'image': getServer()['questionsUrl']+t.piccover,
                'title': t.tasktitle+' 截止：'+datetime.strftime(t.duedate, "%Y-%m-%d")
            }
            tasksArr.append(dic)
    courses = db.session.query(Settings.Id, Settings.name2, Settings.name1, func.count(Newspaper.subcate).label('qty')).outerjoin(
        Newspaper, Settings.name1 == Newspaper.subcate).group_by(Settings.name1).order_by(Settings.name3).filter(
            Settings.cate == 'trainingtypes').filter(Newspaper.cate == 'training').filter(or_(Newspaper.plant == plant, Newspaper.plant == 'ALL'))
    coursesArr = []
    myDic = {}
    myCourses = db.session.query(Newspaper.subcate, func.count(Questionresult.taskid).label('fqty')).join(Questiontask, Newspaper.currenttask == Questiontask.Id).join(
        Questionresult, Questionresult.taskid == Questiontask.Id).filter(Questionresult.eid == eid).filter(Questionresult.score >= Questiontask.passscore).group_by(Newspaper.subcate).all()
    for mc in myCourses:
        myDic[mc.subcate] = mc.fqty
    for c in courses:
        if c.qty > 0:
            if c.name1 in myDic.keys():
                fqty = myDic[c.name1]
            else:
                fqty = 0
            dic = {
                'name': c.name1,
                'src': getServer()['coursesUrl']+c.name2,
                'id': c.Id,
                'qty': c.qty,
                'nfqty': c.qty-fqty
            }
            coursesArr.append(dic)
    notice = [
        '新的限时问答已经发布',
        '请点击下方图片进行培训和答题,获取精益积分参与抽奖',
        '如果一直能看到此滚动条和下方的大图',
        '说明没有完成或答题未达到及格要求',
        '请继续答题直到此滚动条消失即可获得积分'
    ],
    return responseGet("获取列表成功", {'tasksArr': tasksArr, 'coursesArr': coursesArr, 'notice': notice})

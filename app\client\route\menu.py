from flask import Blueprint, request, jsonify
from app.client.model.models_client import User,<PERSON>u,Role
from extensions import db
import requests
from config import config, env
from app.public.functions import create_token, responseError, responsePost, responseGet
from app.tms.functions import login_required
import datetime
from ultils.log_helper import ProjectLogger

mylogger = ProjectLogger()
api = Blueprint('client/menu', __name__)


@api.route('/getAsyncRoutes', methods=['GET'])
def getAsyncRoutes():
    user_id = request.args.get("user_id")
    print(user_id)
    # 查询用户的角色id列表
    user_roles = User.query.filter_by(eid=user_id).first().role_id.split(',')
    roles = Role.query.filter(Role.id.in_(
        user_roles)).all()
    role_name = [item.name for item in roles]
    # 查询该用户具备权限的菜单id列表
    menu_ids = list(set([int(menu_id)
                    for item in roles for menu_id in item.auth.split(",")]))
    # 查询所有父节点
    menus = db.session.query(Menu).filter(
        Menu.id.in_(menu_ids), Menu.parent_id == 0).order_by(Menu.rank.asc()).all()
    menu_tree = []
    for m in menus:
        # 将父节点转化为字典
        menu_dict = parse_node(m)
        child_node = get_children(menu_dict, m.id, role_name, menu_ids)
        if child_node:
            menu_dict["children"] = child_node
        menu_dict["meta"]["roles"] = role_name
        menu_tree.append(menu_dict)
    data = {
        "success": True,
        "data": menu_tree
    }
    return jsonify(data)


def get_children(pnode, parent_id, roles, menu_ids):
    # 查询所有子节点并按照 rank 排序
    children = db.session.query(Menu).filter_by(
        parent_id=parent_id).filter(Menu.id.in_(menu_ids)).order_by(Menu.rank).all()
    if not children:
        return []
    child_list = []
    for c in children:
        # 将子节点转化为字典
        child_dict = parse_node(c, pnode)
        # 如果子节点有子节点，递归查询并添加到 children 中
        child_node = get_children(child_dict, c.id, roles, menu_ids)
        if child_node:
            child_dict["children"] = child_node
        if child_dict:
            child_dict["meta"]["roles"] = roles
            child_list.append(child_dict)
    return child_list


def parse_node(node, pnode=None):
    # 解析目录节点
    if node.node_type == 1:
        menu_dict = {
            "path": node.path,
            "meta": {
                "title": node.title,
                "showLink": bool(node.show_link)
            }
        }
        if node.name is not None:
            menu_dict["name"] = node.name
        if node.redirect:
            menu_dict["redirect"] = node.redirect
        if node.icon is not None:
            menu_dict['meta']['icon'] = node.icon
        if node.show_link is not None:
            menu_dict['meta']['showLink'] = bool(node.show_link)
        if node.rank is not None:
            menu_dict['meta']['rank'] = node.rank
        return menu_dict

    # 解析菜单节点
    elif node.node_type == 2:
        menu_dict = {
            "path": node.path,
            "name": node.name,
            "meta": {
                "title": node.title,
                "showLink": bool(node.show_link)
            }
        }
        if node.redirect is not None:
            menu_dict["redirect"] = node.redirect
        if node.icon is not None:
            menu_dict['meta']['icon'] = node.icon
        if node.extra_icon is not None:
            menu_dict['meta']['extraIcon'] = node.extra_icon
        if node.show_link is not None:
            menu_dict['meta']['showLink'] = bool(node.show_link)
        if node.show_parent is not None:
            menu_dict['meta']['showParent'] = bool(node.show_parent)
        if node.keep_alive is not None:
            menu_dict['meta']['keepAlive'] = bool(node.keep_alive)
        if node.hidden_tag is not None:
            menu_dict['meta']['hiddenTag'] = bool(node.hidden_tag)
        return menu_dict

    # 解析按钮节点
    elif node.node_type == 3:
        pnode['meta'].setdefault('auths', [])
        pnode['meta']['auths'].append(node.path)
        return
        ...
    # 解析外链节点
    elif node.node_type == 4:
        menu_dict = {
            "path": node.path,
            "name": node.name,
            "meta": {
                "title": node.title,
                "showLink": bool(node.show_link)
            }
        }
        return menu_dict
        ...
    # 解析IFrame节点
    elif node.node_type == 5:
        menu_dict = {
            "path": node.path,
            "name": node.name,
            "meta": {
                "title": node.title,
                "frameSrc": node.frame,
                "showLink": bool(node.show_link)
            }
        }
        return menu_dict
        ...


@api.route('/getFullMenu', methods=['GET'])
def getFullMenu():
    tree = build_menu_tree(parent_id=0)
    return responseGet("菜单生成成功！", tree)


def build_menu_tree(parent_id=0):
    menu_items = Menu.query.filter_by(
        parent_id=parent_id).order_by(Menu.rank.asc()).all()
    tree = []
    for item in menu_items:
        node = {
            'id': item.id,
            'name': item.name,
            'node_type': item.node_type,
            'icon': item.icon,
            'extra_icon': item.extra_icon,
            'path': item.path,
            'redirect': item.redirect,
            'show_parent': item.show_parent,
            'show_link': item.show_link,
            'hidden_tag': item.hidden_tag,
            'title': item.title,
            'keep_alive': item.keep_alive,
            'rank': item.rank,
            'frame': item.frame,
            'frame_loading': item.frame_loading,
            'created_time': item.created_time,
            'updated_time': item.updated_time,
            'children': build_menu_tree(item.id)
        }
        tree.append(node)
    return tree


@api.route('/getRoles', methods=['GET'])
def getRoles():
    roles = db.session.query(Role).all()
    result = []
    for role_item in roles:
        result.append({
            'id': role_item.id,
            'name': role_item.name,
            'auth': role_item.auth,
            'rank': role_item.rank,
            'active': role_item.active,
            'remark': role_item.remark,
            'create_time': role_item.create_time,
            'update_time': role_item.update_time
        })
    return responseGet("角色生成成功！", result)


@api.route("/getAuthbyRoleid")
def getAuthbyRoleid():
    id = request.args.get("id", type=int)
    role_obj = Role.query.filter_by(id=id).first()
    auth = role_obj.auth if role_obj else None
    return responseGet("返回角色ID组成功！", auth)


@api.route("/assign", methods=['POST'])
def assign():
    args = request.get_json()
    menu_ids = args.get('menu_ids')
    role_id = args.get('id')
    if role_id is None:
        return responseError("参数错误")
    menu_ids = ','.join(str(item) for item in sorted(menu_ids))
    db.session.query(Role).filter(
        Role.id == role_id).update({Role.auth: menu_ids})
    db.session.commit()
    return responsePost("角色权限修改成功！")



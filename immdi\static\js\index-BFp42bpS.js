import{useColumns as p}from"./column-DC2FtDTr.js";import{d as u,n as f,r as t,o as m,c as d,b as e,h as o,f as r,_ as h}from"./index-BnxEuBzx.js";import"./operation-DCG_Ggeh.js";import"./uploadplan-Ceuk2SqS.js";import"./moment-C3TZ8gAF.js";const x=u({__name:"index",setup(C){p();const a=f([]),s=()=>{},_=()=>{};return(b,v)=>{const l=t("el-button"),c=t("el-upload"),n=t("el-form-item"),i=t("el-form");return m(),d("div",null,[e(i,{inline:!0,class:"search-form bg-bg_color"},{default:o(()=>[e(n,null,{default:o(()=>[e(c,{action:"http://10.76.1.239","file-list":a.value,"show-file-list":!1,"auto-upload":!0,"on-error":s},{default:o(()=>[e(l,{type:"primary"},{default:o(()=>[r("上传EXCEL计划")]),_:1})]),_:1},8,["file-list"])]),_:1}),e(n,null,{default:o(()=>[e(l,{type:"primary",onClick:_},{default:o(()=>[r("显示上传url")]),_:1})]),_:1})]),_:1})])}}}),B=h(x,[["__scopeId","data-v-5404ff6d"]]);export{B as default};

import functools
from apscheduler.schedulers.background import BlockingScheduler
from o365pentairnew import O365
from sendmail import Sendmail
from localscript import startDaily, startMonthly, dmDailyTask, clearFiles
from backupScript import backupMysql,  backupMssqlVOC, update_libs
from utils.mylog import logger
from datetime import datetime
import multiprocessing


def run_with_timeout(func, timeout):
    process = multiprocessing.Process(target=func)  # 使用 Process
    process.start()
    process.join(timeout)  # 设置超时
    if process.is_alive():
        logger.critical("收件任务超时，强制结束。")
        process.terminate()  # 强制终止进程
        process.join()  # 等待进程结束


class Questions():
    def __init__(self):
        self.mail = O365()
        self.sendmail = Sendmail()
        self.sched = BlockingScheduler(timezone='Asia/Shanghai')
        self.sched.add_job(clearFiles, 'cron',  day=1,
                           hour='00', minute='00', second='00')
        self.sched.add_job(lambda: run_with_timeout(self.mail.receiveMails, 250), 'cron', minute='*/5')
        self.sched.add_job(dmDailyTask, 'cron',
                           hour='00', minute='02', second='02', misfire_grace_time=300)
        self.sched.add_job(startDaily, 'cron',
                           hour='00', minute='11', second='10', misfire_grace_time=300)
        self.sched.add_job(startMonthly, 'cron', day=25,
                           hour='00', minute='15', second='30', misfire_grace_time=300)

        self.sched.add_job(backupMssqlVOC, 'cron',
                           hour='01', minute='00', second='30', misfire_grace_time=300)
        # self.sched.add_job(backupStatic, 'cron',
        #                    hour='01', minute='05', second='30', misfire_grace_time=300)
        self.sched.add_job(backupMysql, 'cron',
                           hour='01', minute='15', second='30', misfire_grace_time=300)
        self.sched.add_job(update_libs, 'cron',  day=1, hour='00', minute='00', second='30', misfire_grace_time=300)

        self.sched.add_job(self.sendmail.sendCalMonth, 'cron',  day=25,
                           hour='06', minute='30', second='30', misfire_grace_time=300)
        self.sched.add_job(self.sendmail.sendcal2, 'cron',  day_of_week='mon-fri',
                           hour='06', minute='30', second='30', misfire_grace_time=300)

        self.sched.add_job(self.sendmail.sendWeleanlayered, 'cron',  day_of_week='mon',
                           hour='08', minute='00', second='30', misfire_grace_time=300)
        self.sched.add_job(self.sendmail.sendMROsafety, 'cron',  day_of_week='mon',
                           hour='08', minute='08', second='08', misfire_grace_time=300)
        self.sched.add_job(self.sendmail.sendTPchart, 'cron',  day_of_week='wed',
                           hour='09', minute='00', second='30', misfire_grace_time=300)
        self.sched.add_job(self.sendmail.sendWeleanpassdue, 'cron',  day_of_week='mon',
                           hour='10', minute='00', second='30', misfire_grace_time=300)

        self.sched.add_job(self.sendmail.sendtp, 'cron',
                           hour='14', minute='48', second='30', misfire_grace_time=300)
        self.sched.add_job(self.sendmail.sendcs, 'cron',  day_of_week='mon-fri',
                           hour='15', minute='00', second='30', misfire_grace_time=300)
        self.sched.add_job(self.sendmail.sendreceiving, 'cron',  day_of_week='mon-fri',
                           hour='15', minute='30', second='30', misfire_grace_time=300)
        self.sched.add_job(self.sendmail.sendvoc, 'cron',  day_of_week='mon-fri',
                           hour='16', minute='00', second='30', misfire_grace_time=300)
        logger.info('mailmain.py - scheduler started======'+datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        self.mail.receiveMails()
        self.sched.start()


def just_one_instance(func):
    @functools.wraps(func)
    def f(*args, **kwargs):
        import socket
        try:
            global s
            s = socket.socket()
            host = socket.gethostname()
            s.bind((host, 60333))
        except Exception:
            print('already has an instance')
            return None
        return func(*args, **kwargs)
    return f


@just_one_instance
def mainProgram():
    Questions()


if __name__ == '__main__':
    mainProgram()

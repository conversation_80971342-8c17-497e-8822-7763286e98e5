import{useColumns as l}from"./columns-DU4NCeYN.js";import{d as p,r,o as _,c as m,e,b as f,u as o,ak as g,al as u,_ as h}from"./index-BnxEuBzx.js";import"./front-CiGk0t8u.js";import"./prod-CmDsiAIL.js";import"./moment-C3TZ8gAF.js";const v=a=>(g("data-v-975c4aa7"),a=a(),u(),a),C={class:"main"},b=v(()=>e("div",{class:"header"},[e("span",{class:"lightfont"},"质量信息")],-1)),w={class:"content"},x=p({__name:"index",setup(a){const{dataList:t,columns:s,loadingConfig:n,adaptiveConfig:i,loading:c}=l();return(k,I)=>{const d=r("pure-table");return _(),m("div",C,[b,e("div",w,[f(d,{ref:"tableRef",border:"",stripe:"",adaptive:"",adaptiveConfig:o(i),"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:o(c),"loading-config":o(n),data:o(t),columns:o(s),style:{width:"100%"},"max-height":"100%",height:"100%"},null,8,["adaptiveConfig","loading","loading-config","data","columns"])])])}}}),q=h(x,[["__scopeId","data-v-975c4aa7"]]);export{q as default};

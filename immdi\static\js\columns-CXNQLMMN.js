import{g as p}from"./runlog-CNDP2hwV.js";import{u as e}from"./prod-CmDsiAIL.js";import{h as n}from"./moment-C3TZ8gAF.js";import{n as m,G as h,a2 as r,S as f,q as u}from"./index-BnxEuBzx.js";function D(){const o=m([]),i=m(!0);h([()=>e().selectedDate,()=>e().shift,()=>e().machineId],(t,d)=>{a.selecteddate=t[0],a.shift=t[1],a.machine=t[2],s()});const a=r({selecteddate:e().selectedDate,machine:e().machineId,shift:e().shift}),s=()=>{p(f(a)).then(t=>{o.value=t.data,i.value=!1})};u(()=>{s(),i.value=!1});const l=r({text:"正在加载不良记录数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `});return{dataList:o,columns:[{label:"开始时间",prop:"start_time",minWidth:50},{label:"结束时间",prop:"end_time",minWidth:50},{label:"生产料号",prop:"pn",minWidth:50},{label:"状态",prop:"machine_state",minWidth:30},{label:"状态描述",prop:"machine_state_des",minWidth:40},{label:"持续时间",prop:"unit",minWidth:40,formatter(t,d,b,x){let c=n(t.start_time,"HH:mm:ss");return n(t.end_time,"HH:mm:ss").diff(c,"seconds")}}],loadingConfig:l,adaptiveConfig:{offsetBottom:20,fixHeader:!0},loading:i,refreshData:s}}export{D as useColumns};

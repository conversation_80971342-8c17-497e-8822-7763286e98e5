import{a2 as g,d as y,n as m,r,o as b,g as x,h as u,b as a,u as U,_ as w}from"./index-BnxEuBzx.js";const q=g({pn:[{required:!0,message:"料号为必填项",trigger:"blur"}],pn_des:[{required:!0,message:"料号描述为必填项",trigger:"blur"}],cavity:[{required:!0,message:"模穴数为必填项",trigger:"blur"}],machine:[{required:!0,message:"注塑机编号为必填项",trigger:"blur"}],moldingcycle:[{required:!0,message:"成型周期为必填项",trigger:"blur"}]}),F=y({__name:"item",props:{formInline:{default:()=>({pn:"",pn_des:"",cavity:1,machine:"",moldingcycle:0,manualcycle:0,unload:0,sap_value:0})},ops:{default:()=>({action_type:0})}},setup(s,{expose:i}){const d=s,e=m(d.formInline),c=m(d.ops),p=m();function v(){return p.value}return i({getRef:v}),(I,l)=>{const t=r("el-input"),n=r("el-form-item"),f=r("el-input-number"),_=r("el-switch"),V=r("el-form");return b(),x(V,{ref_key:"ruleFormRef",ref:p,model:e.value,rules:U(q),"label-width":"120px"},{default:u(()=>[a(n,{label:"生产料号",prop:"pn"},{default:u(()=>[a(t,{modelValue:e.value.pn,"onUpdate:modelValue":l[0]||(l[0]=o=>e.value.pn=o),readonly:c.value.action_type==1},null,8,["modelValue","readonly"])]),_:1}),a(n,{label:"料号描述",prop:"pn_des"},{default:u(()=>[a(t,{modelValue:e.value.pn_des,"onUpdate:modelValue":l[1]||(l[1]=o=>e.value.pn_des=o)},null,8,["modelValue"])]),_:1}),a(n,{label:"模穴数",prop:"cavity"},{default:u(()=>[a(f,{modelValue:e.value.cavity,"onUpdate:modelValue":l[2]||(l[2]=o=>e.value.cavity=o),min:1,max:12},null,8,["modelValue"])]),_:1}),a(n,{label:"机台号",prop:"machine"},{default:u(()=>[a(t,{modelValue:e.value.machine,"onUpdate:modelValue":l[3]||(l[3]=o=>e.value.machine=o)},null,8,["modelValue"])]),_:1}),a(n,{label:"成型周期",prop:"moldingcycle"},{default:u(()=>[a(t,{modelValue:e.value.moldingcycle,"onUpdate:modelValue":l[4]||(l[4]=o=>e.value.moldingcycle=o)},null,8,["modelValue"])]),_:1}),a(n,{label:"上下料时间",prop:"manualcycle"},{default:u(()=>[a(t,{modelValue:e.value.manualcycle,"onUpdate:modelValue":l[5]||(l[5]=o=>e.value.manualcycle=o)},null,8,["modelValue"])]),_:1}),a(n,{label:"自动下料",prop:"unload"},{default:u(()=>[a(_,{modelValue:e.value.unload,"onUpdate:modelValue":l[6]||(l[6]=o=>e.value.unload=o),style:{"--el-switch-on-color":"#13ce66","--el-switch-off-color":"#ff4949"},"inline-prompt":"","active-text":"是","inactive-text":"否","active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),a(n,{label:"SAP系统工时",prop:"sap_value"},{default:u(()=>[a(t,{modelValue:e.value.sap_value,"onUpdate:modelValue":l[7]||(l[7]=o=>e.value.sap_value=o)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])}}}),k=w(F,[["__scopeId","data-v-23d98e7b"]]);export{k as default};

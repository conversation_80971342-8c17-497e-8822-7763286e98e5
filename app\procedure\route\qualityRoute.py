from flask import Blueprint, request
from app.procedure.functions import download_img
from app.public.functions import responseError, responseGet
import requests
from config import config, myEnv
import datetime

api = Blueprint('procedure/qualityAPI', __name__)


@api.route('/getQmonth', methods=['GET'])  # 获取EHS统计和分类
def getQmonth():
    r = request.args
    stime = r.get('stime')
    etime = r.get('etime')
    ttime = datetime.datetime.strptime(
        etime, '%Y-%m-%d')+datetime.timedelta(days=1)
    days = (datetime.datetime.strptime(etime, '%Y-%m-%d') -
            datetime.datetime.strptime(stime, '%Y-%m-%d')).days
    linegroup = r.get('area')
    area = r.get('group')
    typeline = []
    # alllines = db.session.query(linfo.linegroup).filter(
    #     linfo.area == area).all()
    # kpi = getkpi(stime.split('-')[0], linegroup)['qtarget']
    # shifts = db.session.query(func.date(Shiftinfo.starttime).label('shiftdate'), func.sum(Scaninfo.scanqty).label('pqty')).join(
    #     Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(linfo, Shiftinfo.linename == linfo.linename).filter(and_(
    #         Shiftinfo.starttime >= stime, Shiftinfo.finishtime <= ttime)).group_by(func.date(Shiftinfo.starttime))
    # if linegroup:
    #     shifts = shifts.filter(linfo.linegroup == linegroup)
    #     typeline.append(linegroup)
    # else:
    #     shifts = shifts.filter(linfo.area == area)
    #     for a in alllines:
    #         typeline.append(a[0])
    # shifts = shifts.all()
    # print('sssssssssssss', typeline)
    data = {
        'ftt': [],
        'pd': [],
        'fttmin': 50,
        'fttx': [],
        'green': [],
        'red': [],
        'yellow': [],
        'listData': [],
        'paretodata': [],
        'paretotarget': [],
        'paretox': [],
        'defect': []
    }
    # for p in shifts:
    #     sdate = datetime.datetime.strftime(p.shiftdate, '%m-%d')
    #     data['fttx'].append(sdate)
    #     data['pd'].append(p.pqty)
    #     data['ftt'].append(100)
    #     data['defect'].append(0)
    #     data['green'].append(100-kpi*100)
    #     data['red'].append(kpi*99)
    #     data['yellow'].append(kpi*1)

    # problems = getproblems(typeline, '质量', days)
    # for p in problems:
    #     data['paretox'].append(p['ptype'])
    #     data['paretotarget'].append(p['trigger'])
    #     data['paretodata'].append(0)
    # data['paretox'].append('未分类')
    # data['paretotarget'].append(round(5/7*days))
    # data['paretodata'].append(0)

    # issues = db.session.query(Issuelog).join(linfo, Issuelog.linename == linfo.linename).filter(
    #     Issuelog.shiftdate.between(stime, etime)).filter(Issuelog.sqdctype == '质量')
    # if linegroup:
    #     issues = issues.filter(linfo.linegroup == linegroup)
    # else:
    #     issues = issues.filter(linfo.area == area)
    # issues = issues.order_by(desc(Issuelog.recordtime)).all()
    # for i in issues:
    #     if i.problemtype in data['paretox']:
    #         data['paretodata'][data['paretox'].index(i.problemtype)] += i.qty
    #     else:
    #         data['paretodata'][len(data['paretodata'])-1] += i.qty
    #     data['listData'].append({
    #         'recorddate': datetime.datetime.strftime(i.shiftdate, "%Y-%m-%d"),
    #         'desc': i.desc,
    #         'linename': i.linename,
    #         'qty': i.qty,
    #         'problemtype': i.problemtype if i.problemtype else '其他'
    #     })
    #     sftdate = datetime.datetime.strftime(i.shiftdate, '%m-%d')
    #     if sftdate in data['fttx']:
    #         idx = data['fttx'].index(sftdate)
    #         data['defect'][idx] += i.qty
    # for i in range(len(data['defect'])):
    #     if data['defect'][i] > 0:
    #         data['ftt'][i] = round(
    #             (data['pd'][i]-data['defect'][i])/data['pd'][i]*100, 1)
    # print(data)
    # maxqty = 100
    # if len(data['ftt']) > 0:
    #     data['fttmin'] = min(data['ftt']) if min(data['ftt']) < 100 else 80
    #     maxqty = max(data['ftt']) if max(data['ftt']) > 100 else 100
    # for j in range(len(data['green'])):
    #     data['green'][j] = int(maxqty)-kpi*100 if (kpi*100 < int(maxqty)) else 1
    # indexArr = []
    # for i in range(len(data['paretodata'])):
    #     if data['paretodata'][i] == 0:
    #         indexArr.append(i)
    # # print(111111111111, indexArr)
    # data['paretodata'] = [data['paretodata'][i]
    #                       for i in range(len(data['paretodata'])) if (i not in indexArr)]
    # data['paretox'] = [data['paretox'][i]
    #                    for i in range(len(data['paretox'])) if (i not in indexArr)]
    # data['paretotarget'] = [data['paretotarget'][i]
    #                         for i in range(len(data['paretotarget'])) if (i not in indexArr)]
    # pareto_data = list(zip(data['paretodata'], data['paretox'], data['paretotarget']))

    # # 按照 paretodata 从大到小排序
    # pareto_data.sort(key=lambda x: x[0], reverse=True)

    # # 将排序后的数据重新解包到 data 中
    # data['paretodata'] = [item[0] for item in pareto_data]
    # data['paretox'] = [item[1] for item in pareto_data]
    # data['paretotarget'] = [item[2] for item in pareto_data]
    return responseGet('获取成功', data)

from flask import jsonify

# API格式化


def responseGet(msg, data={}):  # get方法返回成功格式化
    meta = {'status': 200, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responsePost(msg, data={}):  # post方法返回成功格式化
    meta = {'status': 201, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responsePut(msg, data={}):  # put方法返回成功格式化
    meta = {'status': 202, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responseDelete(msg, data={}):  # delete方法返回成功格式化
    meta = {'status': 203, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responseError(msg):  # request失败返回格式化
    meta = {'status': 204, 'msg': msg}
    return jsonify(meta=meta)


def responseExpire(msg):  # request授权超时返回格式化
    meta = {'status': 205, 'msg': msg}
    return jsonify(meta=meta)

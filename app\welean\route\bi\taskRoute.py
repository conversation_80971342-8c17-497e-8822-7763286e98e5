from flask import Blueprint, request
from app.welean.model.models_welean import Userinfo, Layeredlist, Layeredresult, Suggest
from extensions import db
import datetime
from sqlalchemy import func, not_, case
from sqlalchemy.orm import aliased
from app.public.functions import responseGet
from app.welean.functions import managers, resultStatus, getServer
api = Blueprint('welean/bi/taskAPI', __name__)
Userinfo2 = aliased(Userinfo)


@ api.route('/getLayeredratebyeid', methods=['GET'])
def getLayeredratebyeid():
    res = request.args
    eid = res.get('eid')
    year = int(res.get('year'))

    myaudit = db.session.query(
        func.month(Layeredresult.starttime).label('month'),
        func.sum(case(
            (Layeredresult.status.in_([1, 2]), 1),  # 使用元组作为位置参数
            else_=0
        )).label('ontime'),
        func.count(Layeredresult.Id).label('total')
    ).join(
        Layeredlist, Layeredresult.listid == Layeredlist.Id
    ).filter(
        func.year(Layeredresult.starttime) == year,
        Layeredresult.eid == eid,
        Layeredlist.plant == 'SZ',
        Layeredlist.tasktype == 'layered'
    ).group_by(
        func.month(Layeredresult.starttime)
    ).all()
    outDic = {1: [0, 1, 0, 0], 2: [0, 2, 0, 0], 3: [0, 3, 0, 0], 4: [0, 4, 0, 0], 5: [0, 5, 0, 0], 6: [0, 6, 0, 0],
              7: [0, 7, 0, 0], 8: [0, 8, 0, 0], 9: [0, 9, 0, 0], 10: [0, 10, 0, 0], 11: [0, 11, 0, 0], 12: [0, 12, 0, 0]}
    for r in myaudit:
        outDic[r.month] = [0, r.month, 80, round((r.ontime/r.total)*100, 0)]
    return responseGet('成功', list(outDic.values()))


@ api.route('/getLayeredPastdue', methods=['GET'])
def getLayeredPastdue():
    results = db.session.query(Layeredresult.eid, Layeredlist.subitem, func.datediff(Layeredresult.starttime, datetime.datetime.now()).label('gap')).join(
        Layeredlist, Layeredresult.listid == Layeredlist.Id).filter(Layeredlist.plant == 'SZ',
                                                                    Layeredlist.tasktype == 'layered', Layeredresult.status == 0).filter(
                                                                        Layeredresult.starttime < datetime.datetime.now()).group_by(Layeredresult.eid).all()
    arr = []
    for r in results:
        arr.append({
            'eid': r.eid,
            'subitem': r.subitem,
            'gap': r.gap
        })
    return responseGet('成功', {'results': arr})


@ api.route('/getLayeredDetail', methods=['GET'])
def getLayeredDetail():
    res = request.args
    eid = res.get('eid')
    weeknum = res.get('weeknum')
    year = res.get('year')
    results = db.session.query(Layeredresult.Id, Layeredresult.eid, Layeredlist.subitem, Layeredlist.area, Layeredlist.name, Layeredresult.sugid,
                               Layeredlist.des, Layeredresult.result, Layeredresult.comments, Layeredresult.status, Userinfo.cname, Layeredresult.pics,
                               Userinfo2.cname.label('exename'), Suggest.content.label(
                                   'sugcontent'), Suggest.comments.label('sugcomments'), Suggest.beforepic, Suggest.afterpic,
                               Suggest.linename, Suggest.duedate).join(
        Layeredlist, Layeredresult.listid == Layeredlist.Id).join(
        Userinfo, Layeredresult.eid == Userinfo.eid).outerjoin(Suggest, Layeredresult.sugid == Suggest.Id).outerjoin(
        Userinfo2, Suggest.exeid == Userinfo2.eid).filter(Layeredresult.eid == eid).filter(
        func.week(Layeredresult.starttime) == weeknum).filter(Layeredresult.status.in_([1, 2])).filter(func.year(Layeredresult.starttime) == year).all()
    outArr = []
    for r in results:
        picsArr = []
        pics = getServer()['baseUrl']+'jobs/'+r.pics.split(',')[0] if r.pics else ''
        if pics:
            picsArr.append(pics)
        outArr.append({
            'Id': r.Id,
            'eid': r.eid,
            'subitem': r.subitem,
            'area': r.area,
            'pics': pics,
            'picsArr': picsArr,
            'name': r.name,
            'sugid': r.sugid,
            'des': r.des,
            'result': r.result,
            'comments': r.comments,
            'status': resultStatus[r.status],
            'cname': r.cname,
            'exename': r.exename,
            'sugcontent': r.sugcontent,
            'sugcomments': r.sugcomments,
            'linename': r.linename,
            'beforepic': getServer()['suggestionUrl']+r.beforepic.split(',')[0] if r.beforepic else '',
            'afterpic': getServer()['suggestionUrl']+r.afterpic.split(',')[0] if r.afterpic else '',
            'duedate': datetime.datetime.strftime(r.duedate, "%Y-%m-%d") if r.duedate else ''
        })
    print(outArr, weeknum)
    return responseGet('成功', {'outArr': outArr})


@ api.route('/getLayeredresult', methods=['GET'])
def getLayeredresult():
    res = request.args
    dept = res.get('dept')
    plant = res.get('plant')
    year = res.get('year')
    results = db.session.query(func.week(Layeredresult.starttime).label('weeknum'),
                               Layeredresult.eid, Userinfo.dept1, Layeredresult.starttime, Userinfo.cname, func.count(
        Layeredresult.Id).label('total'),
        func.sum(func.if_(Layeredresult.status.in_([1, 2]), 1, 0)).label('ontime')).join(
        Layeredlist, Layeredresult.listid == Layeredlist.Id).join(Userinfo, Layeredresult.eid == Userinfo.eid).filter(
        Userinfo.plant == plant).filter(Layeredlist.tasktype == 'layered').filter(func.year(Layeredresult.starttime) == year)
    if dept == 'Manager':
        results = results.filter(Layeredresult.eid.in_(managers))
    else:
        results = results.filter(Userinfo.dept1 == dept).filter(
            not_(Layeredresult.eid.in_(managers)))
    results = results.group_by(Layeredresult.eid, Layeredresult.starttime).order_by(
        Layeredresult.starttime).all()
    # get weeknum from a date
    # weeknum = datetime.strptime('2020-01-01', '%Y-%m-%d').isocalendar()[1]
    outDic = {}
    wknumArr = []
    for r in results:
        weeknum = 'WK'+str(r.weeknum+1).zfill(2)
        wknumArr.append(weeknum)
        if r.eid in outDic.keys():
            outDic[r.eid][weeknum] = {
                'weeknum': r.weeknum,
                'eid': r.eid,
                'total': r.total,
                'ontime': r.ontime
            }
        else:
            outDic[r.eid] = {
                'cname': r.cname,
                weeknum: {
                    'weeknum': r.weeknum,
                    'eid': r.eid,
                    'total': r.total,
                    'ontime': r.ontime
                }
            }
    return responseGet("获取列表成功", {'outDic': outDic, 'weeknumArr': list(set(wknumArr))})

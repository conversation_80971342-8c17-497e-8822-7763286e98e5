var d=Object.defineProperty;var u=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var c=(e,n,f)=>n in e?d(e,n,{enumerable:!0,configurable:!0,writable:!0,value:f}):e[n]=f,t=(e,n)=>{for(var f in n||(n={}))i.call(n,f)&&c(e,f,n[f]);if(u)for(var f of u(n))m.call(n,f)&&c(e,f,n[f]);return e};import{d as s,aj as o,aS as p,aT as l,aU as y}from"./index-BnxEuBzx.js";function g(e,n){const f=/^IF-/;if(f.test(e)){const r=e.split(f)[1],I=r.slice(0,r.indexOf(" ")==-1?r.length:r.indexOf(" ")),a=r.slice(r.indexOf(" ")+1,r.length);return s({name:"FontIcon",render(){return o(p,t({icon:I,iconType:a},n))}})}else return typeof e=="function"||typeof(e==null?void 0:e.render)=="function"?n?o(e,t({},n)):e:typeof e=="object"?s({name:"OfflineIcon",render(){return o(l,t({icon:e},n))}}):s({name:"Icon",render(){const r=e&&e.includes(":")?y:l;return o(r,t({icon:e},n))}})}export{g as u};

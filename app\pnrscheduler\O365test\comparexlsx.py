import openpyxl
from openpyxl.utils import get_column_letter
import datetime


def compare_excel_files(file1, file2, output_file):
    # 记录开始时间
    start_time = datetime.datetime.now()

    # 打开两个Excel文件
    wb1 = openpyxl.load_workbook(file1)
    wb2 = openpyxl.load_workbook(file2)

    with open(output_file, 'w', encoding='utf-8') as f:
        # 比较sheet名称
        sheets1 = set(wb1.sheetnames)
        sheets2 = set(wb2.sheetnames)

        # 找出不同的sheet
        diff_sheets = sheets1.symmetric_difference(sheets2)
        if diff_sheets:
            f.write("# Sheet名称差异\n")
            f.write("2024: " + ", ".join(sheets1 - sheets2) + "\n")
            f.write("2025: " + ", ".join(sheets2 - sheets1) + "\n\n")

        # 找出共同的sheet
        common_sheets = sheets1 & sheets2
        total_sheets = len(common_sheets)

        f.write("# 单元格内容差异\n")

        # 比较每个共同的sheet
        for i, sheet_name in enumerate(common_sheets, 1):
            print(f"正在处理第{i}/{total_sheets}个sheet: {sheet_name}")

            ws1 = wb1[sheet_name]
            ws2 = wb2[sheet_name]

            max_row = max(ws1.max_row, ws2.max_row)
            max_col = max(ws1.max_column, ws2.max_column)

            differences = []

            # 比较每个单元格
            for row in range(1, max_row + 1):
                for col in range(1, max_col + 1):
                    cell1 = ws1.cell(row=row, column=col).value
                    cell2 = ws2.cell(row=row, column=col).value

                    if cell1 != cell2:
                        cell_ref = f"{get_column_letter(col)}{row}"
                        differences.append([cell_ref, cell1, cell2])

            # 如果有差异，写入文件
            if differences:
                f.write(f"## Sheet: {sheet_name}\n")
                f.write("| 单元格 | 2024内容 | 2025内容 |\n")
                f.write("|--------|----------|----------|\n")
                for diff in differences:
                    # 处理None值
                    val1 = str(diff[1]) if diff[1] is not None else "空"
                    val2 = str(diff[2]) if diff[2] is not None else "空"
                    f.write(f"| {diff[0]} | {val1} | {val2} |\n")
                f.write("\n")

    # 计算并打印总耗时
    end_time = datetime.datetime.now()
    print(f"比较完成，耗时: {end_time - start_time}")


if __name__ == "__main__":
    file1 = "app/pnrscheduler/O365test/2024.xlsx"
    file2 = "app/pnrscheduler/O365test/2025.xlsx"
    output_file = "app/pnrscheduler/O365test/log.md"

    compare_excel_files(file1, file2, output_file)

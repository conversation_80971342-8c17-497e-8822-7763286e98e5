from extensions import db
class AppInfo(db.Model):
    __bind_key__ = "client"
    __tablename__ = "client_app"
    Id = db.Column(db.Integer, primary_key=True)
    app_name = db.Column(db.String(50))
    plantform = db.Column(db.String(16))
    version = db.Column(db.String(16))
    change_log = db.Column(db.String(255))
    file_md5 = db.Column(db.String(32))
    filesize = db.Column(db.Float())
    is_active = db.Column(db.SmallInteger)
    downloads = db.Column(db.SmallInteger)
    release_time = db.Column(db.DateTime())


class ClientInfo(db.Model):
    __bind_key__ = "client"
    __tablename__ = "client_info"
    mac_addr = db.Column(db.String(18), primary_key=True)
    hostname = db.Column(db.String(32))
    model = db.Column(db.String(64))
    unique_id = db.Column(db.String(32))
    ip_addr = db.Column(db.String(16))
    area = db.Column(db.String(16))
    linename = db.Column(db.String(32))
    location = db.Column(db.String(32))
    client_type = db.Column(db.String(16))
    app_version = db.Column(db.String(16))
    display_type = db.Column(db.String(16))
    remark = db.Column(db.String(32))
    keeper = db.Column(db.String(16))
    nav_url = db.Column(db.String(255))
    boot_count = db.Column(db.SmallInteger)
    connect_count = db.Column(db.Integer)
    refresh_time = db.Column(db.DateTime())
    last_boot_time = db.Column(db.DateTime())
    boot_duration = db.Column(db.SmallInteger)
    is_active = db.Column(db.SmallInteger)
    os_name = db.Column(db.String(32))
    plantform = db.Column(db.String(32))
    os_version = db.Column(db.String(255))
    cpu_model = db.Column(db.String(64))
    cpu_load = db.Column(db.String(16))
    memory_capacity = db.Column(db.SmallInteger)
    memory_usage_percent = db.Column(db.SmallInteger)
    disk_name = db.Column(db.String(32))
    disk_type = db.Column(db.String(16))
    disk_capacity = db.Column(db.Float())
    disk_temperature = db.Column(db.SmallInteger)
    disk_usage_percent = db.Column(db.SmallInteger)
    battery_status = db.Column(db.Integer)
    graphics = db.Column(db.String(32))
    monitor_resolution = db.Column(db.String(16))
    is_touchscreen = db.Column(db.SmallInteger)


class Config(db.Model):
    __bind_key__ = "client"
    __tablename__ = "client_config"
    Id = db.Column(db.Integer, primary_key=True)
    reconnect_interval = db.Column(db.Integer)


class User(db.Model):
    __bind_key__ = "client"
    __tablename__ = "client_user"
    eid = db.Column(db.String(16), primary_key=True)
    cname = db.Column(db.String(16))
    ename = db.Column(db.String(255))
    auth = db.Column(db.String(255))
    is_active = db.Column(db.SmallInteger)
    email=db.Column(db.String(64))
    role_id=db.Column(db.String(255))


class Menu(db.Model):
    __bind_key__ = "client"
    __tablename__ = "client_menu"
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(16))
    node_type = db.Column(db.SmallInteger)
    icon = db.Column(db.String(32))
    extra_icon = db.Column(db.String(32))
    path = db.Column(db.String(64))
    redirect = db.Column(db.String(64))
    parent_id = db.Column(db.SmallInteger)
    show_parent = db.Column(db.SmallInteger)
    show_link = db.Column(db.SmallInteger)
    hidden_tag = db.Column(db.SmallInteger)
    rank = db.Column(db.SmallInteger)
    title = db.Column(db.String(32))
    keep_alive = db.Column(db.SmallInteger)
    frame = db.Column(db.String(64))
    frame_loading = db.Column(db.SmallInteger)
    created_time = db.Column(db.DateTime())
    updated_time = db.Column(db.DateTime())


class Role(db.Model):
    __bind_key__ = "client"
    __tablename__ = "client_role"
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(32))
    auth = db.Column(db.String(255))
    init_url = db.Column(db.SmallInteger)
    active = db.Column(db.SmallInteger)
    remark = db.Column(db.String(32))
    create_time = db.Column(db.DateTime())
    update_time = db.Column(db.DateTime())
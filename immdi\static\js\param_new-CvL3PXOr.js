import{_ as b}from"./basic_info.vue_vue_type_script_setup_true_lang-B0_oAY14.js";import k from"./temperature-aD9bVYTe.js";import v from"./plasticization-Dvr3DwwB.js";import y from"./injection_presure-B7jORALD.js";import h from"./hot_runner-DgrpLbW9.js";import C from"./close_mould-DI0mUbn7.js";import g from"./open_mould-DNT6m56T.js";import j from"./ejector-c21b3fpd.js";import w from"./core-CJM-StND.js";import V from"./blow-BWXrC31z.js";import x from"./alarm-D8paOfgU.js";import I from"./param_change_log-DHc86Jii.js";import{d as B,n as N,r as _,o,g as l,h as e,b as t,e as c,C as u,f as n,ak as z,al as H,_ as P}from"./index-BnxEuBzx.js";import"./guage-BZYEkLdB.js";const S=m=>(z("data-v-0cbd70d9"),m=m(),H(),m),T={class:"custom-tabs-label"},q=S(()=>c("span",null,"基本信息",-1)),M=B({__name:"param_new",props:{queryinfo:{type:Object}},setup(m){const a=N("basic_info");return(O,s)=>{const p=_("House"),d=_("el-icon"),r=_("el-tab-pane"),i=_("el-tabs");return o(),l(i,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=f=>a.value=f),type:"card",class:"demo-tabs",stretch:!0,"tab-position":"bottom"},{default:e(()=>[t(r,{name:"basic_info"},{label:e(()=>[c("span",T,[t(d,null,{default:e(()=>[t(p)]),_:1}),q])]),default:e(()=>[a.value=="basic_info"?(o(),l(b,{key:0,sku:m.queryinfo.sku},null,8,["sku"])):u("",!0)]),_:1}),t(r,{label:"温度",name:"temperature"},{default:e(()=>[a.value=="temperature"?(o(),l(k,{key:0})):u("",!0)]),_:1}),t(r,{label:"熔胶",name:"plasticization"},{default:e(()=>[a.value=="plasticization"?(o(),l(v,{key:0})):u("",!0)]),_:1}),t(r,{label:"注塑保压",name:"injection_presure"},{default:e(()=>[a.value=="injection_presure"?(o(),l(y,{key:0})):u("",!0)]),_:1}),t(r,{label:"热浇道",name:"hot_runner"},{default:e(()=>[a.value=="hot_runner"?(o(),l(h,{key:0})):u("",!0)]),_:1}),t(r,{label:"合模",name:"close_mould"},{default:e(()=>[a.value=="close_mould"?(o(),l(C,{key:0},{default:e(()=>[n("合模")]),_:1})):u("",!0)]),_:1}),t(r,{label:"开模",name:"open_mould"},{default:e(()=>[a.value=="open_mould"?(o(),l(g,{key:0},{default:e(()=>[n("开模")]),_:1})):u("",!0)]),_:1}),t(r,{label:"顶出",name:"ejector"},{default:e(()=>[a.value=="ejector"?(o(),l(j,{key:0},{default:e(()=>[n("顶出")]),_:1})):u("",!0)]),_:1}),t(r,{label:"抽芯",name:"core"},{default:e(()=>[a.value=="core"?(o(),l(w,{key:0},{default:e(()=>[n("抽芯")]),_:1})):u("",!0)]),_:1}),t(r,{label:"吹气",name:"blow"},{default:e(()=>[a.value=="blow"?(o(),l(V,{key:0},{default:e(()=>[n("吹气")]),_:1})):u("",!0)]),_:1}),t(r,{label:"报警履历",name:"alarm"},{default:e(()=>[a.value=="alarm"?(o(),l(x,{key:0},{default:e(()=>[n("报警履历")]),_:1})):u("",!0)]),_:1}),t(r,{label:"操作记录",name:"param_change_log"},{default:e(()=>[a.value=="param_change_log"?(o(),l(I,{key:0},{default:e(()=>[n("参数修改记录")]),_:1})):u("",!0)]),_:1})]),_:1},8,["modelValue"])}}}),Y=P(M,[["__scopeId","data-v-0cbd70d9"]]);export{Y as default};

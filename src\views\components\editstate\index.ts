import { addDialog } from "@/components/ReDialog";
import editStateForm from "./editStateForm.vue";
import { addstate, updatestate } from "@/api/dashboard";
import { message } from "@/utils/message";
import { h } from "vue";

const updateState = async (data: any) => {
  let res = null;
  if (data.action == "update") {
    console.log(data);
    res = await updatestate(data);
  } else {
    res = await addstate(data);
  }

  return res;
};

type CallbackFunction = () => void;
export function changeState(
  machine_id: any,
  shift_date?: any,
  shift?: any,
  state_id?: any,
  start_time?: any,
  end_time?: any,
  row_id?: any,
  action?: string,
  fun_callback?: CallbackFunction
) {
  addDialog({
    title: machine_id + "#注塑机运行状态编辑",
    props: {
      stateData: {
        machine_id: machine_id,
        shift_date: shift_date,
        shift: shift,
        state_id: state_id,
        start_time: start_time,
        end_time: end_time,
        row_id: row_id,
        action: action
      }
    },
    width: "50%",
    draggable: true,
    fullscreenIcon: false,
    closeOnClickModal: false,
    contentRenderer: () => h(editStateForm),
    beforeSure: async (done, { options }) => {
      if (options.props.stateData.state_id != state_id) {
        const res = (await updateState({
          machine_id: options.props.stateData.machine_id,
          state_id: options.props.stateData.state_id,
          start_time: options.props.stateData.start_time,
          end_time: options.props.stateData.end_time,
          row_id: options.props.stateData.row_id,
          action: action
        })) as { meta: any };
        if (res.meta.status != 201) {
          message(res.meta.msg, { customClass: "el", type: "error" });
        } else {
          done();
          message(res.meta.msg, { customClass: "el", type: "success" });
          fun_callback();
        }
      } else {
        done();
      }
    }
  });
}

import{aN as U,d as J,n as u,a2 as K,p as L,G as Q,q as X,c as Z,b as s,h as i,w as $,D as E,Y as I,r as c,o as N,f as m,C as O,u as R,g as ee,y as ae,aR as T,_ as le}from"./index-BnxEuBzx.js";import{g as te,a as oe}from"./dashboard-dtTxmf4X.js";import{h as A}from"./moment-C3TZ8gAF.js";const ne=U("plan/upload"),re=U("plan/packing_upload"),se={class:"main"},ie=J({__name:"index",setup(ue){const v=u([]),d=u(!0),p=u("plan_im"),g=u([]),y=u([]),w=u(""),D=u(""),h=u(!1),V=u(""),M={offsetBottom:12,fixHeader:!0},x=K({text:"正在加载生产计划数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `}),l=L(()=>h.value?g.value:g.value.filter(a=>Object.keys(a).filter(n=>n.match(/^\d{4}-\d{2}-\d{2}$/)).map(n=>a[n]).reduce((n,o)=>n+(o?parseInt(o,10):0),0)!==0)),C=L(()=>{const a=[];for(let t=0;t<27;t++){const e=new Date(w.value);e.setDate(e.getDate()+t),a.push(e.toLocaleDateString())}return a});function S(a,t="YYYY-MM-DD"){const e=new Date(a),n=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0");return t==="YYYY-MM-DD"?`${n}-${o}-${r}`:t==="MM/DD"?`${o}/${r}`:a}const f=u([]);Q(C,(a,t)=>{f.value=[{label:"注塑机台",prop:"machine",width:"120px",fixed:"left",class:"header-td",cellRenderer:({row:e})=>e.machine.split(`
`).map((n,o)=>s("div",{key:o},[n]))},{label:"料号",prop:"pn",width:"180px",fixed:"left",class:"material"},{label:"描述",prop:"remark",width:"250px",fixed:"left"}],C.value.forEach(e=>{let n=new Date(e).toISOString();f.value.push({label:S(n,"MM/DD"),prop:S(n),width:"80px"})}),f.value.push({label:"类型",prop:"type",width:"80px"})});const q=({row:a,column:t,rowIndex:e,columnIndex:n})=>{if(n===0)if(e==0){if(l.value[0].machine==l.value[1].machine){let o=1;for(let r=0;r<l.value.length-1&&l.value[r].machine==l.value[r+1].machine;r++)o++;return{rowspan:o,colspan:1}}}else if(e>0&&e<l.value.length-1){let o=1;if(l.value[e-1].machine!=l.value[e].machine&&l.value[e].machine==l.value[e+1].machine){for(let r=e;r<l.value.length-1&&l.value[r].machine==l.value[r+1].machine;r++)o++;return{rowspan:o,colspan:1}}else return l.value[e-1].machine==l.value[e].machine&&l.value[e].machine==l.value[e+1].machine?{rowspan:0,colspan:0}:l.value[e-1].machine==l.value[e].machine&&l.value[e].machine!=l.value[e+1].machine?{rowspan:0,colspan:0}:{rowspan:1,colspan:1}}else return{rowspan:1,colspan:1}},W=[{label:"工单",prop:"mo",width:"100px",sortable:!0},{label:"料号",prop:"sku",sortable:!0},{label:"数量",prop:"qty",width:"80px",sortable:!0},{label:"交期",prop:"due",width:"110px",sortable:!0},{label:"类别",prop:"category",width:"140px"},{label:"盖子/棉/碳棒",prop:"lid"},{label:"罐子",prop:"sump"},{label:"备注",prop:"remark"}],j=I();X(()=>{j.fullPath=="/plan"&&document.documentElement.classList.add("dark"),b(),k()});const b=()=>{d.value=!0,te().then(a=>{g.value=a.data.res,w.value=a.data.start_date,D.value=a.data.upload_time}),d.value=!1},k=()=>{d.value=!0,oe().then(a=>{y.value=a.data.res,V.value=a.data.upload_time}),d.value=!1},z=a=>(v.value=[],p.value=="plan_im"?b():k(),T({duration:2e4,type:a.meta.status!==201?"error":"success",message:a.meta.msg})),F=a=>(v.value=[],T({duration:2e3,type:"error",message:"系统出错，上传失败！"})),Y=({column:a})=>{if(a.label.split("/").length>1){const t=A(a.property);if(t.isValid())return t.isSame(A(),"day")?{backgroundColor:"var(--el-color-success-light-5)",color:"var(--el-color-white)"}:t.day()===0||t.day()===6?{backgroundColor:"rgba(128, 128, 128, 0.8)",color:"var(--el-color-white)"}:{}}};return(a,t)=>{const e=c("el-radio"),n=c("el-radio-group"),o=c("el-form-item"),r=c("el-button"),G=c("el-upload"),H=c("el-checkbox"),P=c("el-form"),B=c("pure-table");return N(),Z("div",se,[s(P,{ref:"formRef",inline:!0,class:"search-form bg-bg_color"},{default:i(()=>[s(o,null,{default:i(()=>[s(n,{modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=_=>p.value=_)},{default:i(()=>[s(e,{value:"plan_im",label:"plan_im"},{default:i(()=>[m("注塑计划")]),_:1}),O("",!0)]),_:1},8,["modelValue"])]),_:1}),s(o,null,{default:i(()=>[s(G,{ref:"upload","file-list":v.value,"on-success":z,"on-error":F,action:p.value=="plan_im"?R(ne):R(re),class:"upload-box",limit:1,"show-file-list":!0,"auto-upload":!0},{trigger:i(()=>[s(r,{type:"primary"},{default:i(()=>[m("上传文件更新")]),_:1})]),_:1},8,["file-list","action"])]),_:1}),p.value=="plan_im"?(N(),ee(o,{key:0},{default:i(()=>[s(H,{modelValue:h.value,"onUpdate:modelValue":t[1]||(t[1]=_=>h.value=_),label:"包含空计划",size:"large"},null,8,["modelValue"])]),_:1})):O("",!0),s(o,null,{default:i(()=>[s(r,{type:"primary",onClick:t[2]||(t[2]=_=>p.value=="plan_im"?b():k())},{default:i(()=>[m(" 刷新 ")]),_:1})]),_:1}),s(o,null,{default:i(()=>[m(" 更新时间:"+ae(p.value=="plan_im"?D.value:V.value),1)]),_:1}),s(o,null,{default:i(()=>[s(r,{type:"primary"},{default:i(()=>[m("显示全部")]),_:1})]),_:1})]),_:1},512),$(s(B,{ref:"table_im",border:"",stripe:"",adaptive:"","header-cell-style":Y,"cell-style":Y,adaptiveConfig:M,"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:d.value,"loading-config":x,data:l.value,columns:f.value,"span-method":q},null,8,["loading","loading-config","data","columns"]),[[E,p.value=="plan_im"]]),$(s(B,{ref:"table_packing",class:"table_packing",border:"",stripe:"",adaptive:"",adaptiveConfig:M,"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:d.value,"loading-config":x,data:y.value,columns:W},null,8,["loading","loading-config","data"]),[[E,p.value=="plan_packing"]])])}}}),me=le(ie,[["__scopeId","data-v-15f88914"]]);export{me as default};

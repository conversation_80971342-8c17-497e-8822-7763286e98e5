from flask_sqlalchemy import SQLAlchemy
from flask_marshmallow import Marshmallow
from flask_cors import CORS
from flask_redis import FlaskRedis
import redis
from flask_jwt_extended import JWTManager

from flask_apscheduler import APScheduler
from apscheduler.schedulers.background import BackgroundScheduler

db = SQLAlchemy()  # 数据库ORM，用于操作数据库
ma = Marshmallow()  # 数据库序列化工具，可以把数据库对象方便的转为JSON输出
cors = CORS()  # 跨域配置，允许前端在不同的域名或端口下访问后端服务器
scheduler = APScheduler(BackgroundScheduler(
    timezone="Asia/Shanghai"))  # 定时任务调度器，用于执行后台任务
rs = FlaskRedis()  # Redis 客户端，用于与 Redis 数据库进行交互
jwt = JWTManager()  # JWT 认证管理器，用于处理 JSON Web Tokens 的生成和验证


def init_ext(app):
    db.init_app(app)  # 初始化数据库 ORM
    scheduler.init_app(app)  # 初始化定时任务调度器
    scheduler.start()  # 启动定时任务调度器
    ma.init_app(app)  # 初始化数据库序列化工具
    cors.init_app(app, supports_credentials=True)  # 允许跨域请求
    rs.init_app(app, decode_responses=True)  # 初始化 Redis 客户端
    jwt.init_app(app)  # 初始化 JWT 认证管理器

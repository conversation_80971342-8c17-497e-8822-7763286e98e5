from flask import Blueprint, request
from app.carbonmix.model.models_cm import User
from extensions import db
import requests
from config import config, env
from app.public.functions import create_token, responseError, responsePost, responseDelete, responsePut
from app.carbonmix.functions import login_required
import datetime
from ultils.log_helper import ProjectLogger

mylogger = ProjectLogger()
api = Blueprint('carbonmix/loginAPI', __name__)


@api.route('/', methods=['GET'])
# @login_required
def DD():
    return responseDelete("测试用户成功", {'data': 33})


@api.route('/changePassword', methods=['PUT'])
@login_required
def changePassword():
    res = request.json
    email = res.get('email')
    oldpass = res.get('oldpass')
    newpass = res.get('newpass1')
    r = requests.post(config[env].api_url+"public/changePassword",
                      {'username': email, 'oldpass': oldpass, 'newpass': newpass})
    vdata = r.json()
    print('aaa', vdata)
    if vdata['meta']['status'] != 202:
        return responseError(vdata['meta']['msg'])
    return responsePut(vdata['meta']['msg'])


@api.route("/getLogin", methods=["POST"])
def getLogin():
    mylogger.info("*"*10 + "getLogin" + "*"*10)
    # testuser = db.session.query(Duser).filter(Duser.eid == '1238034').first()
    res_dir = request.json
    mylogger.debug(res_dir.get('username'))
    if res_dir is None:
        res_dir = request.form
    if not len(res_dir):
        return responseError('请输入用户名密码')
    # 获取前端传过来的参数
    eid = res_dir.get('username')
    password = res_dir.get('password')
    r = requests.post(config[env].api_url+"/public/login",
                      {'username': eid, 'password': password})
    vdata = r.json()
    mylogger.debug(vdata)
    if vdata['meta']['status'] != 201:
        data = {'data': vdata['meta']['msg'], 'success': False}
        return responsePost('error', data)
    else:
        email = vdata['data']['email']
    mylogger.debug(email)
    mylogger.debug(333)
    omsuser = db.session.query(User).filter(
        User.email == email, User.status == 1).first()
    now = datetime.datetime.now()
    if omsuser:
        # future_date = now + datetime.timedelta(seconds=2)
        future_date = now + datetime.timedelta(days=30)
        username = omsuser.name
        roles = [omsuser.role]
        accessToken = create_token(omsuser.Id)
        refreshToken = create_token(omsuser.eid)
        expires = future_date.strftime("%Y/%m/%d %H:%M:%S")
        db.session.query(User).filter(User.eid == omsuser.eid
                                      ).update({"token": accessToken})
        db.session.commit()
        data = {'username': username, 'roles': roles, 'eid': omsuser.eid,
                'accessToken': accessToken, 'refreshToken': refreshToken, 'expires': expires}
        data = {'data': data, 'success': True}
        return responsePost('登录成功', data)
    else:
        data = {'data': '登陆失败，账号状态为已关闭', 'success': False}
        return responsePost('error', data)

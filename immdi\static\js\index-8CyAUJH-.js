import{au as m,b2 as u}from"./index-BnxEuBzx.js";var s=function(r){return o(r)&&!y(r)};function o(e){return!!e&&typeof e=="object"}function y(e){var r=Object.prototype.toString.call(e);return r==="[object RegExp]"||r==="[object Date]"||d(e)}var g=typeof Symbol=="function"&&Symbol.for,O=g?Symbol.for("react.element"):60103;function d(e){return e.$$typeof===O}function j(e){return Array.isArray(e)?[]:{}}function l(e,r){return r.clone!==!1&&r.isMergeableObject(e)?c(j(e),e,r):e}function v(e,r,t){return e.concat(r).map(function(a){return l(a,t)})}function E(e,r){if(!r.customMerge)return c;var t=r.customMerge(e);return typeof t=="function"?t:c}function M(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(r){return Object.propertyIsEnumerable.call(e,r)}):[]}function i(e){return Object.keys(e).concat(M(e))}function f(e,r){try{return r in e}catch(t){return!1}}function A(e,r){return f(e,r)&&!(Object.hasOwnProperty.call(e,r)&&Object.propertyIsEnumerable.call(e,r))}function S(e,r,t){var a={};return t.isMergeableObject(e)&&i(e).forEach(function(n){a[n]=l(e[n],t)}),i(r).forEach(function(n){A(e,n)||(f(e,n)&&t.isMergeableObject(r[n])?a[n]=E(n,t)(e[n],r[n],t):a[n]=l(r[n],t))}),a}function c(e,r,t){t=t||{},t.arrayMerge=t.arrayMerge||v,t.isMergeableObject=t.isMergeableObject||s,t.cloneUnlessOtherwiseSpecified=l;var a=Array.isArray(r),n=Array.isArray(e),b=a===n;return b?a?t.arrayMerge(e,r,t):S(e,r,t):l(r,t)}c.all=function(r,t){if(!Array.isArray(r))throw new Error("first argument should be an array");return r.reduce(function(a,n){return c(a,n,t)},{})};var h=c,D=h;const F=m(D),I=e=>{const r=n=>{e.formVisible.value=!0,n!=null&&n.id?(e.title.value="编辑"+e.titleExt,e.formData.value=u(n,!0),e.isAdd.value=!1):(e.formData.value=u(e.defaultFormData.value,!0),n&&(e.formData.value=F(e.defaultFormData.value,n)),e.title.value="添加"+e.titleExt,e.isAdd.value=!0)},t=n=>{n&&n.resetFields()};return{showEdit:r,closeDialog:()=>{e.formVisible.value=!1,t(e.ruleFormRef.value),e.doneFn&&e.doneFn()}}};export{I as R};

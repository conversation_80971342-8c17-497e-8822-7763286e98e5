import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export const getroutinglist = (data: object) => {
  return http.request("get", baseUrlApi("setting/getroutinglist"), {
    params: data
  });
};

export const updaterouting = (data: object) => {
  return http.request("post", baseUrlApi("setting/updaterouting"), {
    data
  });
};

export const addrouting = (data: object) => {
  return http.request("post", baseUrlApi("setting/addrouting"), {
    data
  });
};

export const syncrouting = () => {
  return http.request("post", baseUrlApi("setting/syncrouting"));
};

export const deleteRouting = (data: object) => {
  return http.request("post", baseUrlApi("setting/deleterouting"), {
    data
  });
};

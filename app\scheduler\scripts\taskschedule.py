
from datetime import datetime
import os
import sys
from sqlalchemy import create_engine, and_, or_, func, not_
from sqlalchemy.orm import sessionmaker
import traceback
import random


def createSession():
    parentdir = os.path.dirname(os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    sys.path.append(parentdir)
    from config import config, myEnv
    constr = 'mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8mb4'.format(config[myEnv].mysqlDB['user'], config[myEnv].mysqlDB['password'],
                                                                     config[myEnv].mysqlDB['host'], config[myEnv].mysqlDB['port'], config[myEnv].mysqlDB['database'])
    con = create_engine(constr)
    Session = sessionmaker(con)
    db_session = Session()
    return db_session


def createTask():
    db_session = createSession()
    ttype = {
        'training': '培训任务',
        'layered': '层级审核',
        'PM': 'PM维护'
    }
    audittypslist = ['5S稽核']  # , 'TPM稽核', '厂内稽核'暂时不放入，需亚奥建立格子的standard，然后判断那种再绑定人和设备或产线
    auditnumbers = []  # 自动建立稽核对比项目的编号
    from app.welean.model.models_welean import Layeredlist, Layeredresult, Layered, Account, Userinfo, Settings, FiveSaudit, FiveSstandard
    from app.welean.functions import subscribeOT,  subscribeTASK
    currentTime = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
    tasks = db_session.query(Layeredlist.Id, Layered.eid, Layered.items, Layeredlist.name, Layeredlist.tasktype, Layeredlist.subitem, Layeredlist.isarea).outerjoin(Layered, and_(
        Layeredlist.tasktype == Layered.tasktype, Layeredlist.subitem == Layered.subitem)).outerjoin(
        Layeredresult, and_(Layeredlist.Id == Layeredresult.listid, Layered.eid == Layeredresult.eid)).filter(not_(Layered.eid.is_(None))).filter(
            or_(Layeredlist.lastfinish > '2024-01-01', Layeredlist.lastfinish.is_(None))).filter(
        or_(and_(Layeredresult.eid.is_(None), Layeredlist.freq == 0),
            Layeredlist.lastfinish.is_(None), and_(func.datediff(currentTime, Layeredlist.lastfinish) >= Layeredlist.freq,
                                                   Layeredlist.freq > 0))).group_by(Layeredlist.Id, Layered.eid).all()
    areas = db_session.query(Settings.name2).filter(Settings.cate == 'areas', Settings.name1.in_(['VSM1', 'VSM2', 'Warehouse']),
                                                    Settings.isactive == 1, Settings.plant == 'SZ').group_by(Settings.name2).all()
    areaArr = []
    for a in areas:
        areaArr.append(a.name2)
    audits_list = db_session.query(Layeredlist).filter(Layeredlist.name.in_(audittypslist)).all()
    for aaa in audits_list:
        auditnumbers.append(aaa.Id)
    tasksArr = []
    ids = []
    eidset = {}
    # print('taskxs',tasks)
    # return
    for t in tasks:
        area = ''
        if t.isarea == 1:
            shuffled_area_arr = areaArr[:]
            random.shuffle(shuffled_area_arr)
            area = random.choice(shuffled_area_arr)
        activeitems = t.items.split(',') if t.items else []
        if len(activeitems) == 0 or (str(t.Id) in activeitems):
            tasksArr.append(Layeredresult(listid=t.Id, starttime=currentTime,
                            eid=t.eid, status=0, area=area))
            if t.eid not in eidset.keys():
                eidset[t.eid] = {
                    'area': area,
                    'linename': t.subitem[:20],
                    'jobtype': ttype[t.tasktype]
                }
        ids.append(t.Id)
    for e, v in eidset.items():
        try:
            dealer = db_session.query(Account.wxid, Userinfo.cname).join(
                Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == e).first()
            if dealer:
                taskBody = {
                    'idate': datetime.strftime(datetime.now(), '%Y-%m-%d %H:%M:%S'),
                    'linename': v['linename'],
                    'suggester': '系统工作任务'[:10],
                    'jobtype': v['jobtype'],
                    'content': '请进入微信小程序查看工作任务并按时完成'[:20]
                }
                dwxid = dealer.wxid
                dhref = 'pages/login/login'
                subscribeTASK(taskBody, dwxid, dhref)
        except Exception:
            traceback.print_exc()
            pass
    try:
        linesDic = {}
        fives = db_session.query(Settings.name2, FiveSstandard.linename, FiveSstandard.station).join(
            Settings, Settings.name3 == FiveSstandard.linename).all()
        stationsDic = {}
        for f in fives:
            if f.linename in stationsDic.keys():
                stationsDic[f.linename].append(f.station)
            else:
                stationsDic[f.linename] = [f.station]
            if f.name2 in linesDic.keys():
                if f.linename not in linesDic[f.name2]:
                    linesDic[f.name2].append(f.linename)
            else:
                linesDic[f.name2] = [f.linename]
        ids = set(ids)
        db_session.query(Layeredlist).filter(
            Layeredlist.Id.in_(ids)).update({'lastfinish': currentTime}, synchronize_session=False)
        db_session.query(Layeredresult).filter(Layeredresult.listid.in_(ids)
                                               ).filter(Layeredresult.status == 0).update({'status': 3}, synchronize_session=False)
        db_session.add_all(tasksArr)
        db_session.flush()
        audit5seidsArr = []
        for t in tasksArr:
            if t.listid in auditnumbers:
                audit5seidsArr.append({
                    'layeredid': t.Id, 'eid': t.eid
                })
        linesArr = shuffleArrlefttwo(list(linesDic.values()))
        if len(audit5seidsArr) > 0:
            crr = assign_elements(linesArr, audit5seidsArr)
            auditArr = []
            for c in crr:
                stations = stationsDic[c[0]]
                for station in stations:
                    auditArr.append(FiveSaudit(linename=c[0], station=station, eid=c[1]['eid'], layeredid=c[1]['layeredid'], isaudit=1))
                # auditArr.append(FiveSaudit(linename=c[0], eid=c[1]['eid'], station='任意场所自由稽核', layeredid=c[1]['layeredid'], isaudit=1))
            print(auditArr)
            db_session.add_all(auditArr)
        db_session.commit()
        return 'success', len(ids), len(tasksArr)
    except Exception:
        taskBody = {
            'content': '更新工作任务出错',
            'owner': '管理员',
            'comments': '请及时查看系统问题'
        }
        subscribeOT(taskBody, 'ozo5a5GrffZZLy2NuvjLRHz2AL7c', 'pages/login/login')
        db_session.rollback()
        traceback.print_exc()
        return 'failed', 0, 0


def deleteSuggestOverdue():
    db_session = createSession()
    from app.welean.model.models_welean import Suggest
    currentTime = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
    db_session.query(Suggest).filter(Suggest.eid == '9999999', Suggest.exeid == '9999999', Suggest.status == 'ongoing').filter(
        func.datediff(currentTime, Suggest.idate) > 2).update(
            {'status': 'cancel', 'mdi': '结', 'comments': '过期两天系统类别问题自动完结'}, synchronize_session=False)
    db_session.commit()


def shuffleArrlefttwo(arr):
    print('arr', arr)
    brr = []
    for ar in arr:
        random.shuffle(ar)
        for a in ar[:2]:
            brr.append(a)
    return brr


def assign_elements(arr, brr):
    crr = []
    random.shuffle(arr)
    random.shuffle(brr)
    max_len = max(len(arr), len(brr))
    # min_len = min(len(arr), len(brr))

    # 对于长的数组，重复匹配短的数组
    if len(arr) > len(brr):
        brr *= -(-len(arr) // len(brr))  # 这样确保即使 len(arr) // len(brr) 不整除也能取到上限
    elif len(brr) > len(arr):
        arr *= -(-len(brr) // len(arr))

    for i in range(max_len):
        crr.append([arr[i % len(arr)], brr[i % len(brr)]])
    return crr


# createTask()

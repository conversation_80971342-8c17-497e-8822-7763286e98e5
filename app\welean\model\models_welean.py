from extensions import db


class Location(db.Model):
    __tablename__ = "wl_location"
    Id = db.Column(db.Integer, primary_key=True)
    plant = db.Column(db.String(10))
    area = db.Column(db.String(20))
    line = db.Column(db.String(20))
    pos1 = db.Column(db.String(60))
    pos2 = db.Column(db.String(60))
    pos3 = db.Column(db.String(60))
    pos4 = db.Column(db.String(60))


class Cardlog(db.Model):
    __tablename__ = "wl_cardlog"
    Id = db.Column(db.Integer, primary_key=True)
    pid = db.Column(db.Integer)
    cardtype = db.Column(db.String(10))
    cardtypesub = db.Column(db.String(10))
    eid = db.Column(db.String(7))
    qty = db.Column(db.SmallInteger)
    carddate = db.Column(db.DateTime)


class Givescore(db.Model):
    __tablename__ = "wl_givescore"
    Id = db.Column(db.Integer, primary_key=True)
    giveyear = db.Column(db.SmallInteger)
    givetype = db.Column(db.String(10))
    desc = db.Column(db.String(50))
    scoremin = db.Column(db.SmallInteger)
    scoremax = db.Column(db.SmallInteger)
    scoreid = db.Column(db.String(16))
    expiretime = db.Column(db.DateTime)
    plant = db.Column(db.String(10))
    location = db.Column(db.String(20))


class Videocheck(db.Model):
    __tablename__ = "wl_videocheck"
    Id = db.Column(db.Integer, primary_key=True)
    videourl = db.Column(db.String(50))
    title = db.Column(db.String(50))
    options = db.Column(db.String(100))
    answer = db.Column(db.String(10))
    btime = db.Column(db.String(8))


class Videorecord(db.Model):
    __tablename__ = "wl_videorecord"
    Id = db.Column(db.Integer, primary_key=True)
    videourl = db.Column(db.String(50))
    eid = db.Column(db.String(7))


class Layered(db.Model):
    __tablename__ = "wl_layered"
    Id = db.Column(db.Integer, primary_key=True)
    eid = db.Column(db.String(7))
    spv = db.Column(db.String(7))
    tasktype = db.Column(db.String(10))
    subitem = db.Column(db.String(50))
    items = db.Column(db.String(100))


class Layeredlist(db.Model):
    __tablename__ = "wl_layeredlist"
    Id = db.Column(db.Integer, primary_key=True)
    tasktype = db.Column(db.String(10))
    subitem = db.Column(db.String(50))
    area = db.Column(db.String(50))
    name = db.Column(db.String(200))
    des = db.Column(db.String(255))
    credittype = db.Column(db.String(10))
    low = db.Column(db.Float)
    high = db.Column(db.Float)
    freq = db.Column(db.SmallInteger)
    stdtime = db.Column(db.Float)
    plant = db.Column(db.String(2))
    content = db.Column(db.String(50))
    lastfinish = db.Column(db.DateTime())
    isarea = db.Column(db.SmallInteger)


class Layeredresult(db.Model):
    __tablename__ = "wl_layeredresult"
    Id = db.Column(db.Integer, primary_key=True)
    listid = db.Column(db.Integer)
    result = db.Column(db.String(500))
    eid = db.Column(db.String(7))
    status = db.Column(db.SmallInteger)
    sugid = db.Column(db.Integer)
    finishtime = db.Column(db.DateTime())
    starttime = db.Column(db.DateTime())
    pics = db.Column(db.String(150))
    comments = db.Column(db.String(255))
    spv = db.Column(db.String(7))
    area = db.Column(db.String(20))


class Mroaddress(db.Model):
    __tablename__ = "wl_mroaddress"
    Id = db.Column(db.Integer, primary_key=True)
    address = db.Column(db.String(10))
    sku = db.Column(db.String(20))
    stock = db.Column(db.Float)


class Mrolog(db.Model):
    __tablename__ = "wl_mrolog"
    Id = db.Column(db.Integer, primary_key=True)
    address = db.Column(db.String(25))
    transtime = db.Column(db.DateTime())
    logtype = db.Column(db.String(10))
    logid = db.Column(db.Integer)
    transqty = db.Column(db.Float)
    owner = db.Column(db.String(7))
    sku = db.Column(db.String(20))


class Mrobill(db.Model):
    __tablename__ = "wl_mrobill"
    Id = db.Column(db.Integer, primary_key=True)
    initdate = db.Column(db.DateTime())
    appvdate = db.Column(db.DateTime())
    finishdate = db.Column(db.DateTime())
    billtype = db.Column(db.String(10))
    reason = db.Column(db.String(255))
    owner = db.Column(db.String(7))
    sender = db.Column(db.String(7))
    receiver = db.Column(db.String(7))
    status = db.Column(db.String(10))
    negreason = db.Column(db.String(255))
    approver = db.Column(db.String(7))
    bindtype = db.Column(db.String(10))
    bindid = db.Column(db.Integer)


class Mrosku(db.Model):
    __tablename__ = "wl_mrosku"
    Id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(20))
    safetystock = db.Column(db.SmallInteger)
    brand = db.Column(db.String(50))
    category = db.Column(db.String(10))
    desc = db.Column(db.String(100))
    unit = db.Column(db.String(10))
    price = db.Column(db.String(255))
    supplier = db.Column(db.String(50))
    mrotype = db.Column(db.String(10))
    isactive = db.Column(db.SmallInteger)
    coverpic = db.Column(db.String(50))
    csku = db.Column(db.String(50))


class Mrosupplier(db.Model):
    __tablename__ = "wl_mrosupplier"
    Id = db.Column(db.Integer, primary_key=True)
    suppliercode = db.Column(db.String(10))
    suppliername = db.Column(db.String(50))
    suppliertype = db.Column(db.String(10))
    suppliercomments = db.Column(db.String(50))
    contactors = db.Column(db.String(100))
    tels = db.Column(db.String(100))
    mails = db.Column(db.String(200))
    plant = db.Column(db.String(2))
    isactive = db.Column(db.SmallInteger)


class RSpareparts(db.Model):
    __tablename__ = "wl_r_spareparts"
    Id = db.Column(db.Integer, primary_key=True)
    subpart = db.Column(db.String(20))
    sku = db.Column(db.String(20))


class Tpmmachine(db.Model):
    __tablename__ = "wl_tpmmachine"
    Id = db.Column(db.Integer, primary_key=True)
    area = db.Column(db.String(20))
    linename = db.Column(db.String(20))
    machine = db.Column(db.String(20))
    isactive = db.Column(db.SmallInteger)


class Tpmrepair(db.Model):
    __tablename__ = "wl_tpmrepair"
    Id = db.Column(db.Integer, primary_key=True)
    machineid = db.Column(db.Integer)
    sugid = db.Column(db.Integer)
    mrobillid = db.Column(db.Integer)
    finishtime = db.Column(db.DateTime())
    owner = db.Column(db.String(7))
    confirmtime = db.Column(db.DateTime())
    approver = db.Column(db.String(7))
    status = db.Column(db.String(10))


class Scan(db.Model):
    __tablename__ = "wl_scan"
    Id = db.Column(db.Integer, primary_key=True)
    stype = db.Column(db.String(20))
    scankey = db.Column(db.String(64))
    status = db.Column(db.SmallInteger)
    scantime = db.Column(db.DateTime)
    eid = db.Column(db.String(7))


class Booksaddress(db.Model):
    __tablename__ = "wl_booksaddress"
    Id = db.Column(db.Integer, primary_key=True)
    bin = db.Column(db.String(20))
    booksn = db.Column(db.String(20))


class Bookslog(db.Model):
    __tablename__ = "wl_bookslog"
    Id = db.Column(db.Integer, primary_key=True)
    booksn = db.Column(db.String(20))
    eid = db.Column(db.String(7))
    orderdate = db.Column(db.Date())
    borrowdate = db.Column(db.Date())
    returndate = db.Column(db.Date())
    rate = db.Column(db.SmallInteger)


class Booksinfo(db.Model):
    __tablename__ = "wl_booksinfo"
    Id = db.Column(db.Integer, primary_key=True)
    bookname = db.Column(db.String(20))
    adddate = db.Column(db.Date())
    author = db.Column(db.String(20))
    category = db.Column(db.String(4))
    coverpic = db.Column(db.String(50))
    brief = db.Column(db.String(255))
    sn = db.Column(db.String(20))
    label = db.Column(db.String(50))
    status = db.Column(db.String(1))
    borrowed = db.Column(db.SmallInteger)
    rate = db.Column(db.Float(3, 2))


class FiveSgemba(db.Model):
    __tablename__ = "wl_5sgemba"
    Id = db.Column(db.Integer, primary_key=True)
    owner = db.Column(db.String(7))
    plant = db.Column(db.String(10))
    area = db.Column(db.String(20))
    season = db.Column(db.String(6))
    score = db.Column(db.SmallInteger)


class FiveSkaizen(db.Model):
    __tablename__ = "wl_5skaizen"
    Id = db.Column(db.Integer, primary_key=True)
    owner = db.Column(db.String(7))
    projectid = db.Column(db.Integer)
    credit = db.Column(db.SmallInteger)


class FiveSprojects(db.Model):
    __tablename__ = "wl_5sprojects"
    Id = db.Column(db.Integer, primary_key=True)
    plant = db.Column(db.String(10))
    area = db.Column(db.String(20))
    season = db.Column(db.String(6))
    suggestid = db.Column(db.Integer)


class FiveSaudit(db.Model):
    __tablename__ = "wl_5saudit"
    Id = db.Column(db.Integer, primary_key=True)
    linename = db.Column(db.String(20))
    station = db.Column(db.String(20))
    eid = db.Column(db.String(7))
    auditpic = db.Column(db.String(50))
    benchpic = db.Column(db.String(50))
    marks = db.Column(db.String(255))
    layeredid = db.Column(db.Integer)
    comment = db.Column(db.String(255))
    isaudit = db.Column(db.SmallInteger)
    markqty = db.Column(db.SmallInteger)
    spoint = db.Column(db.String(10))


class FiveSstandard(db.Model):
    __tablename__ = "wl_5sstandard"
    Id = db.Column(db.Integer, primary_key=True)
    linename = db.Column(db.String(20))
    station = db.Column(db.String(20))
    duration = db.Column(db.SmallInteger)
    picurl = db.Column(db.String(50))
    requirement = db.Column(db.String(50))
    comment = db.Column(db.String(50))
    plant = db.Column(db.String(10))


class FiveSdaily(db.Model):
    __tablename__ = "wl_5sdaily"
    Id = db.Column(db.Integer, primary_key=True)
    linename = db.Column(db.String(20))
    station = db.Column(db.String(20))
    eid = db.Column(db.String(7))
    checktime = db.Column(db.DateTime)
    picurl = db.Column(db.String(50))


class Services(db.Model):
    __tablename__ = "wl_services"
    Id = db.Column(db.Integer, primary_key=True)
    starttime = db.Column(db.DateTime)
    finishtime = db.Column(db.DateTime)
    eid = db.Column(db.String(7))
    stype = db.Column(db.String(20))
    status = db.Column(db.SmallInteger)
    urls = db.Column(db.String(225))
    options = db.Column(db.String(100))
    countresult = db.Column(db.Integer)


class Rates(db.Model):
    __tablename__ = "wl_rates"
    Id = db.Column(db.Integer, primary_key=True)
    cate = db.Column(db.String(10))
    eid = db.Column(db.String(7))
    sid = db.Column(db.Integer)
    score = db.Column(db.SmallInteger)


class Newspaper(db.Model):
    __tablename__ = "wl_newspaper"
    Id = db.Column(db.Integer, primary_key=True)
    cate = db.Column(db.String(20))
    subcate = db.Column(db.String(20))
    title = db.Column(db.String(30))
    eid = db.Column(db.String(7))
    postdate = db.Column(db.Date())
    reservedate = db.Column(db.Date())
    plant = db.Column(db.String(10))
    piccover = db.Column(db.String(100))
    currenttask = db.Column(db.Integer)
    videourl = db.Column(db.String(50))
    content = db.Column(db.Text)


class Questionresult(db.Model):
    __tablename__ = "wl_questionresult"
    Id = db.Column(db.Integer, primary_key=True)
    taskid = db.Column(db.Integer)
    testdate = db.Column(db.Date())
    eid = db.Column(db.String(7))
    score = db.Column(db.SmallInteger)
    firstscore = db.Column(db.SmallInteger)


class Questiontask(db.Model):
    __tablename__ = "wl_questiontask"
    Id = db.Column(db.Integer, primary_key=True)
    tasktitle = db.Column(db.String(10))
    tasktype = db.Column(db.String(5))
    duedate = db.Column(db.Date())
    startdate = db.Column(db.Date())
    passscore = db.Column(db.SmallInteger)
    piccover = db.Column(db.String(100))
    plant = db.Column(db.String(10))
    newsid = db.Column(db.Integer)
    randnb = db.Column(db.SmallInteger)
    randcate = db.Column(db.String(20))


class Relationquestions(db.Model):
    __tablename__ = "wl_r_questions"
    Id = db.Column(db.Integer, primary_key=True)
    taskid = db.Column(db.Integer)
    questionid = db.Column(db.Integer)


class Questions(db.Model):
    __tablename__ = "wl_questions"
    Id = db.Column(db.Integer, primary_key=True)
    trainingtypes = db.Column(db.String(20))
    question = db.Column(db.String(200))
    checkorder = db.Column(db.SmallInteger)
    answer = db.Column(db.String(20))
    optionA = db.Column(db.String(50))
    optionB = db.Column(db.String(50))
    optionC = db.Column(db.String(50))
    optionD = db.Column(db.String(50))
    optionE = db.Column(db.String(50))
    picurl = db.Column(db.String(200))
    qtype = db.Column(db.SmallInteger)
    dept = db.Column(db.String(255))


class Options(db.Model):
    __tablename__ = "wl_options"
    Id = db.Column(db.Integer, primary_key=True)
    cate = db.Column(db.String(10))
    title = db.Column(db.String(20))
    content = db.Column(db.String(255))
    plant = db.Column(db.String(10))
    isactive = db.Column(db.SmallInteger)


class Settings(db.Model):
    __tablename__ = "wl_settings"
    Id = db.Column(db.Integer, primary_key=True)
    cate = db.Column(db.String(20))
    name1 = db.Column(db.String(20))
    name2 = db.Column(db.String(20))
    name3 = db.Column(db.String(20))
    plant = db.Column(db.String(10))
    isactive = db.Column(db.SmallInteger)


class Account(db.Model):
    __tablename__ = "wl_account"
    Id = db.Column(db.Integer, primary_key=True)
    wxid = db.Column(db.String(100))
    eid = db.Column(db.String(7))
    loginauth = db.Column(db.String(10))
    isactive = db.Column(db.SmallInteger)
    avatar = db.Column(db.String(300))


class Appvowners(db.Model):
    __tablename__ = "wl_appvowners"
    Id = db.Column(db.Integer, primary_key=True)
    dept = db.Column(db.String(30))
    eid = db.Column(db.String(7))
    plant = db.Column(db.String(10))
    mroauth = db.Column(db.SmallInteger)


class Auth(db.Model):
    __tablename__ = "wl_auth"
    Id = db.Column(db.Integer, primary_key=True)
    eid = db.Column(db.String(7))
    auth = db.Column(db.String(255))


class Getscore(db.Model):
    __tablename__ = "wl_getscore"
    Id = db.Column(db.Integer, primary_key=True)
    eid = db.Column(db.String(7))
    getdate = db.Column(db.Date())
    gettime = db.Column(db.DateTime())
    gettype = db.Column(db.String(10))
    getid = db.Column(db.Integer)
    getscore = db.Column(db.SmallInteger)
    geid = db.Column(db.String(7))


class Lotteryget(db.Model):
    __tablename__ = "wl_lotteryget"
    Id = db.Column(db.Integer, primary_key=True)
    eid = db.Column(db.String(7))
    getprize = db.Column(db.String(7))
    getdate = db.Column(db.Date())
    paiddate = db.Column(db.Date())
    paid = db.Column(db.SmallInteger)
    prizeid = db.Column(db.Integer)


class Lotteryprize(db.Model):
    __tablename__ = "wl_lotteryprize"
    Id = db.Column(db.Integer, primary_key=True)
    plant = db.Column(db.String(10))
    item = db.Column(db.String(7))
    totalqty = db.Column(db.SmallInteger)
    getqty = db.Column(db.SmallInteger)
    weight = db.Column(db.SmallInteger)
    url = db.Column(db.String(100))
    purchasedate = db.Column(db.Date())
    price = db.Column(db.Float(5, 1))
    status = db.Column(db.String(10))


class Permission(db.Model):
    __tablename__ = "wl_permission"
    Id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(10))
    ptype = db.Column(db.String(10))
    ptitle = db.Column(db.String(10))
    pid = db.Column(db.Integer)
    icon = db.Column(db.String(100))
    url = db.Column(db.String(100))
    order = db.Column(db.SmallInteger)
    needauth = db.Column(db.SmallInteger)
    plant = db.Column(db.String(20))


class Suggest(db.Model):
    __tablename__ = "wl_suggest"
    Id = db.Column(db.Integer, primary_key=True)
    idate = db.Column(db.Date())
    acdate = db.Column(db.Date())
    cfdate = db.Column(db.Date())
    appvdate = db.Column(db.Date())
    duedate = db.Column(db.Date())
    content = db.Column(db.String(255))
    eid = db.Column(db.String(7))
    exeid = db.Column(db.String(7))
    status = db.Column(db.String(10))
    stype = db.Column(db.String(10))
    comments = db.Column(db.String(300))
    beforepic = db.Column(db.String(150))
    afterpic = db.Column(db.String(150))
    linename = db.Column(db.String(25))
    auditid = db.Column(db.Integer)
    audittype = db.Column(db.SmallInteger)
    type2 = db.Column(db.String(20))
    fifi = db.Column(db.String(2))
    plant = db.Column(db.String(10))
    mdi = db.Column(db.String(2))
    pastdue = db.Column(db.SmallInteger)
    repairid = db.Column(db.Integer)
    machine = db.Column(db.String(30))
    missqty = db.Column(db.SmallInteger)
    missunit = db.Column(db.String(10))


class Tasks(db.Model):
    __tablename__ = "wl_tasks"
    Id = db.Column(db.Integer, primary_key=True)
    ttype = db.Column(db.String(10))
    month = db.Column(db.Date())
    content = db.Column(db.String(50))
    owner1 = db.Column(db.String(7))
    owner2 = db.Column(db.String(7))
    status = db.Column(db.SmallInteger)
    plant = db.Column(db.String(10))


class Userinfo(db.Model):
    __tablename__ = "wl_userinfo"
    cate = db.Column(db.String(10))
    dept1 = db.Column(db.String(30))
    dept2 = db.Column(db.String(30))
    dept3 = db.Column(db.String(30))
    eid = db.Column(db.String(7), primary_key=True)
    cname = db.Column(db.String(30))
    ename = db.Column(db.String(30))
    position = db.Column(db.String(50))
    lastdate = db.Column(db.Date())
    spv = db.Column(db.String(30))
    mail = db.Column(db.String(50))
    active = db.Column(db.SmallInteger)
    plant = db.Column(db.String(10))


class Logs(db.Model):
    __tablename__ = "wl_logs"
    Id = db.Column(db.Integer, primary_key=True)
    updatedate = db.Column(db.Date())
    version = db.Column(db.String(10))
    versionlogs = db.Column(db.Text)

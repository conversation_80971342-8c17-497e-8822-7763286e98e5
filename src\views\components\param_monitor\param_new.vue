<script setup lang="ts">
import BasicInfo from "./components/basic_info.vue";
import Temperature from "./components/temperature.vue";
import Plasticization from "./components/plasticization.vue";
import InjectionPresure from "./components/injection_presure.vue";
import HotRunner from "./components/hot_runner.vue";
import CloseMould from "./components/close_mould.vue";
import OpenMould from "./components/open_mould.vue";
import Ejector from "./components/ejector.vue";
import Core from "./components/core.vue";
import Blow from "./components/blow.vue";
import Alarm from "./components/alarm.vue";
import ParamChangeLog from "./components/param_change_log.vue";
import { onMounted, ref } from "vue";
const activeTab = ref("basic_info");
const props = defineProps({
  queryinfo: {
    type: Object
  }
});
</script>

<template>
  <el-tabs
    v-model="activeTab"
    type="card"
    class="demo-tabs"
    :stretch="true"
    tab-position="bottom"
  >
    <el-tab-pane name="basic_info">
      <template #label>
        <span class="custom-tabs-label">
          <el-icon><House /></el-icon>
          <span>基本信息</span>
        </span>
      </template>
      <BasicInfo v-if="activeTab == 'basic_info'" :sku="queryinfo.sku" />
    </el-tab-pane>
    <el-tab-pane label="温度" name="temperature">
      <Temperature v-if="activeTab == 'temperature'" />
    </el-tab-pane>
    <el-tab-pane label="熔胶" name="plasticization"
      ><Plasticization v-if="activeTab == 'plasticization'"
    /></el-tab-pane>
    <el-tab-pane label="注塑保压" name="injection_presure"
      ><InjectionPresure v-if="activeTab == 'injection_presure'"
    /></el-tab-pane>
    <el-tab-pane label="热浇道" name="hot_runner"
      ><HotRunner v-if="activeTab == 'hot_runner'"
    /></el-tab-pane>
    <el-tab-pane label="合模" name="close_mould">
      <CloseMould v-if="activeTab == 'close_mould'"
        >合模</CloseMould
      ></el-tab-pane
    >
    <el-tab-pane label="开模" name="open_mould">
      <OpenMould v-if="activeTab == 'open_mould'">开模</OpenMould>
    </el-tab-pane>
    <el-tab-pane label="顶出" name="ejector">
      <Ejector v-if="activeTab == 'ejector'">顶出</Ejector>
    </el-tab-pane>
    <el-tab-pane label="抽芯" name="core">
      <Core v-if="activeTab == 'core'">抽芯</Core>
    </el-tab-pane>
    <el-tab-pane label="吹气" name="blow">
      <Blow v-if="activeTab == 'blow'">吹气</Blow>
    </el-tab-pane>
    <el-tab-pane label="报警履历" name="alarm">
      <Alarm v-if="activeTab == 'alarm'">报警履历</Alarm>
    </el-tab-pane>
    <el-tab-pane label="操作记录" name="param_change_log">
      <ParamChangeLog v-if="activeTab == 'param_change_log'"
        >参数修改记录</ParamChangeLog
      >
    </el-tab-pane>
  </el-tabs>
</template>

<style lang="scss" scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
.demo-tabs .custom-tabs-label .el-icon {
  vertical-align: middle;
}
.demo-tabs .custom-tabs-label span {
  vertical-align: middle;
  margin-left: 4px;
}
.content {
  border: 1px solid red;
}

:deep(.el-tabs__content) {
  height: 500px;
  padding: 5px 5px 0px 5px;
}
</style>

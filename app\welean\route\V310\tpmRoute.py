from flask import Blueprint, request
from extensions import db
import traceback
from datetime import datetime, timedelta
from sqlalchemy import func, desc
from sqlalchemy.orm import aliased
from app.public.functions import responseError, responsePost, responseGet,  responsePut, login_required
from app.welean.functions import getServer
from app.welean.model.models_welean import Tpmrepair, Userinfo, Tpmmachine, Suggest, Layeredlist, Layered
api = Blueprint('welean/V310/tpmAPI', __name__)
Userinfo1 = aliased(Userinfo)
Userinfo2 = aliased(Userinfo)

# submitRepair api


@ api.route('/getPMSchedule', methods=['GET'])
@ login_required
def getPMSchedule():
    res = request.args
    plant = res.get('plant')
    outDic = {}
    subq = db.session.query(Layeredlist.subitem, Layeredlist.lastfinish, Layeredlist.freq, Userinfo.cname).join(
        Layered, Layered.subitem == Layeredlist.subitem).join(Userinfo, Layered.eid == Userinfo.eid).filter(
        Layeredlist.plant == plant, Layeredlist.tasktype == 'PM').filter(Layeredlist.freq > 0).group_by(Layeredlist.subitem,Layeredlist.lastfinish,Layeredlist.freq).all()
    for s in subq:
        lastfinish = s.lastfinish if s.lastfinish else datetime.now()
        nday = datetime.strftime(lastfinish+timedelta(days=s.freq), '%Y-%m-%d')
        if nday > datetime.strftime(datetime.now(), '%Y-%m-%d'):
            if nday in outDic.keys():
                outDic[nday].append(
                    {
                        'subitem': s.subitem,
                        'cname': s.cname,
                        'nextdate': nday
                    }
                )
            else:
                outDic[nday] = [
                    {
                        'subitem': s.subitem,
                        'cname': s.cname,
                        'nextdate': nday
                    }
                ]
    return responseGet('获取列表成功', {'schedule': outDic})


@ api.route('/negConfirm', methods=['PUT'])
@ login_required
def negConfirm():
    res = request.json
    sugid = res.get('sugid')
    try:
        suggest = Suggest.query.filter(Suggest.Id == sugid).first()
        suggest.repairid = 0
        db.session.query(Tpmrepair).filter(Tpmrepair.sugid == sugid).delete()
        db.session.commit()
        return responsePut('操作成功')
    except Exception:
        print(traceback.format_exc())
        return responseError('操作失败')


@ api.route('/appvRepair', methods=['POST'])
@ login_required
def appvRepair():
    res = request.json
    eid = res.get('eid')
    machineid = res.get('machineid')
    mrobillid = res.get('mrobillid') if res.get('mrobillid') else 0
    Id = res.get('Id')
    confirmtime = res.get('confirmtime')
    finishtime = res.get('finishtime')
    try:
        repair = Tpmrepair.query.filter(Tpmrepair.Id == Id).first()
        repair.approver = eid
        repair.machineid = machineid
        repair.mrobillid = mrobillid
        repair.confirmtime = confirmtime
        repair.finishtime = finishtime
        repair.status = 'closed'
        db.session.commit()
        return responsePost('操作成功')
    except Exception:
        print(traceback.format_exc())
        return responseError('操作失败')


@ api.route('/submitRepair', methods=['POST'])
@ login_required
def submitRepair():
    res = request.json
    sugid = res.get('sugid')
    eid = res.get('eid')
    machineid = res.get('machineid')
    mrobillid = res.get('mrobillid') if res.get('mrobillid') else 0
    Id = res.get('Id')
    confirmtime = res.get('confirmtime')
    finishtime = res.get('finishtime')
    try:
        if Id:
            repair = Tpmrepair.query.filter(Tpmrepair.Id == Id).first()
            repair.owner = eid
            repair.machineid = machineid
            repair.mrobillid = mrobillid
            repair.confirmtime = confirmtime
            repair.finishtime = finishtime
        else:
            repair = Tpmrepair(sugid=sugid, owner=eid, machineid=machineid,
                               mrobillid=mrobillid, confirmtime=confirmtime, finishtime=finishtime, status='open')
            db.session.add(repair)
            db.session.flush()
            suggest = db.session.query(Suggest).filter(Suggest.Id == sugid).first()
            suggest.repairid = repair.Id
        db.session.commit()
        return responsePost('操作成功')
    except Exception:
        print(traceback.format_exc())
        return responseError('操作失败')


@ api.route('/notRepair', methods=['PUT'])
@ login_required
def notRepair():
    res = request.json
    id = res.get('id')
    try:
        suggest = Suggest.query.filter(Suggest.Id == id).first()
        suggest.repairid = 0
        db.session.commit()
        return responsePut('操作成功')
    except Exception:
        print(traceback.format_exc())
        return responseError('操作失败')

# getRepairByid api


@ api.route('/getRepairByid', methods=['GET'])
@ login_required
def getRepairByid():
    res = request.args
    # repair = []
    machines = []
    id = res.get('id')
    suggest = getSuggestByid(id)
    repair = getRepairid(id)
    machines = getMachineTreeMap()
    print(machines)
    return responseGet("获取列表成功", {'suggest': suggest, 'repair': repair, 'machines': machines})


@ api.route('/getMachines', methods=['GET'])
@ login_required
def getMachines():
    machines = []
    machines = getMachineTreeMap()
    return responseGet("获取列表成功", {'machines': machines})


def getMachineTreeMap():
    dic = {}
    indexarea = 0
    indexlinename = 0
    indexmachine = 0
    machines = db.session.query(Tpmmachine).filter(Tpmmachine.isactive == 1).order_by(
        Tpmmachine.area, Tpmmachine.linename, Tpmmachine.machine).all()
    for machine in machines:
        area = machine.area
        linename = machine.linename
        machinename = machine.machine
        machineid = machine.Id
        # create treemap from area to linename to machine, each layer contain value, label and children key
        if area not in dic.keys():
            dic[area] = {'value': area, 'label': area, 'extra': indexarea, 'children': {}}
            indexarea += 1
            indexmachine = 0
            indexlinename = 0
        if linename not in dic[area]['children'].keys():
            dic[area]['children'][linename] = {
                'value': linename, 'label': linename, 'extra': indexlinename, 'children': {}}
            indexlinename += 1
            indexmachine = 0
        if machine not in dic[area]['children'][linename]['children'].keys():
            dic[area]['children'][linename]['children'][machinename] = {
                'value': machineid, 'label': machinename, 'extra': indexmachine}
            indexmachine += 1
    for k, v in dic.items():
        for kk, vv in v['children'].items():
            dic[k]['children'][kk]['children'] = list(vv['children'].values())
        dic[k]['children'] = list(v['children'].values())
    return list(dic.values())


def getRepairid(id):
    outDic = {
        'Id': '',
        'machine': '',
        'machineid': 0,
        'mrobillid': 0,
        'confirmtime': '',
        'finishtime': '',
        'approver': '',
        'status': ''
    }
    r = db.session.query(Tpmrepair.Id, Tpmrepair.sugid, Tpmrepair.machineid, Tpmrepair.mrobillid, Tpmmachine.machine,
                         Tpmrepair.confirmtime, Tpmrepair.finishtime, Tpmrepair.approver, Tpmrepair.status).join(
        Tpmmachine, Tpmmachine.Id == Tpmrepair.machineid).filter(Tpmrepair.sugid == id).first()
    if r:
        outDic['Id'] = r.Id
        outDic['sugid'] = r.sugid
        outDic['machineid'] = r.machineid
        outDic['machine'] = r.machine
        outDic['mrobillid'] = r.mrobillid
        outDic['confirmtime'] = datetime.strftime(
            r.confirmtime, "%Y-%m-%d %H:%M:%S") if r.confirmtime else ''
        outDic['finishtime'] = datetime.strftime(
            r.finishtime, "%Y-%m-%d %H:%M:%S") if r.finishtime else ''
        outDic['approver'] = r.approver
        outDic['status'] = r.status
    return outDic


def getSuggestByid(id):
    outDic = {}
    s = db.session.query(Suggest.Id, Suggest.duedate, Suggest.idate, Suggest.acdate, Suggest.beforepic, Suggest.afterpic, Suggest.status,
                         Suggest.stype, Suggest.type2, Suggest.comments, Suggest.content, Suggest.appvdate,
                         Suggest.cfdate, Userinfo1.cname.label(
                             'scname'), Userinfo1.dept1.label('sdept'),
                         Userinfo2.cname.label('dcname'), Userinfo2.dept1.label('ddept'), Suggest.comments).join(
        Userinfo1, Suggest.eid == Userinfo1.eid).outerjoin(Userinfo2, Suggest.exeid == Userinfo2.eid).filter(Suggest.Id == id).first()
    if s:
        if s.beforepic:
            newArr = []
            for ss in s.beforepic.split(','):
                newArr.append(getServer()['suggestionUrl']+ss)
            outbeforepic = ','.join(newArr)
        else:
            outbeforepic = 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png'
        if s.afterpic:
            newArr = []
            for ss in s.afterpic.split(','):
                newArr.append(getServer()['suggestionUrl']+ss)
            outafterpic = ','.join(newArr)
        else:
            outafterpic = 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png'
        outDic = {
            '01_提案编号/状态/类型': str(s.Id)+'/已完成/'+((s.stype+'/'+s.type2) if s.type2 else s.stype),
            '02_批准日期': datetime.strftime(s.appvdate, "%Y-%m-%d") if s.appvdate else datetime.strftime(s.idate,  "%Y-%m-%d"),
            '03_完成日期': datetime.strftime(s.acdate, "%Y-%m-%d") if s.acdate else '',
            '04_提案人': s.sdept+'/'+s.scname,
            '05_提案内容': s.content,
            '06_执行人': s.ddept+'/'+s.dcname,
            '07_完成说明': s.comments,
            '10_改善前图片': outbeforepic,
            '11_改善后图片': outafterpic,
        }
    return outDic


@ api.route('/getMyOpenRepairs', methods=['GET'])
@ login_required
def getMyOpenRepairs():
    res = request.args
    eid = res.get('eid')
    all = db.session.query(func.count(Suggest.Id)).filter(
        Suggest.exeid == eid).filter(Suggest.repairid.is_(None)).filter(Suggest.status == 'closed').filter(func.datediff(datetime.now(), Suggest.acdate) < 100).scalar()
    myList = [
        {
            'name': '可转维修提案',
            'count': all if all else 0
        },
        {
            'name': '我的维修'
        }
    ]
    return responseGet("获取列表成功", {'list': myList})


@ api.route('/getMyRepairs', methods=['GET'])
@ login_required
def getMyRepairs():
    res = request.args
    eid = res.get('eid')
    keywords = res.get('keywords')
    astatus = int(res.get('astatus'))  # 0待确认=cfdate为空且ongoing，1待完成=有cfdate且ongoing，2已完成closed,3为all
    nb = res.get('nb')
    suggests = db.session.query(Suggest.Id, Suggest.duedate, Suggest.acdate, Suggest.cfdate, Suggest.beforepic,  Suggest.status, Suggest.linename, Suggest.type2, Suggest.content,
                                Tpmrepair.machineid, Tpmrepair.mrobillid, Tpmrepair.finishtime, Tpmrepair.confirmtime, Tpmrepair.status.label(
                                    'tpmstatus'),
                                Userinfo.cname.label('scname'), Userinfo.dept1.label('sdept'), Userinfo.cname.label('dcname'), Userinfo.dept1.label('ddept')).join(
        Userinfo, Suggest.exeid == Userinfo.eid).filter(Suggest.exeid == eid).outerjoin(Tpmrepair, Suggest.repairid == Tpmrepair.Id).filter(
        Suggest.status == 'closed')
    if astatus == 0:
        if keywords != '':
            suggests = suggests.filter(Suggest.content.like('%{0}%'.format(keywords)))
        suggests = suggests.filter(Suggest.repairid.is_(None)).filter(func.datediff(
            datetime.now(), Suggest.acdate) < 100).order_by(desc(Suggest.acdate))
    elif astatus == 1:
        if keywords != '':
            suggests = suggests.filter(Suggest.content.like('%{0}%'.format(keywords)))
        suggests = suggests.filter(Tpmrepair.sugid.isnot(None))
    suggests = suggests.limit(nb).all()
    cardsData = []
    for s in suggests:
        dic = {
            'status': s.status,
            'dealer': s.ddept+'/'+s.dcname,
            'id': s.Id,
            'title': '|No.'+str(s.Id),
            'subTitle': '提案完成日期:'+datetime.strftime(s.acdate, "%Y-%m-%d"),
            'thumb': '/static/icon/'+s.status+'.png',
            'content': s.content,
            'url': getServer()['suggestionUrl'] + s.beforepic.split(',')[0] if s.beforepic else 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png',
            'suggester': s.sdept+'/'+s.scname,
            'linename': s.linename
        }
        cardsData.append(dic)
    return responseGet("获取列表成功", {'cardsData': cardsData})


@ api.route('/getAllOpenRepairs', methods=['GET'])
@ login_required
def getAllOpenRepairs():
    all = db.session.query(func.count(Tpmrepair.Id)).filter(Tpmrepair.status == 'open').filter(
        func.datediff(datetime.now(), Tpmrepair.finishtime) < 31).scalar()
    myList = [
        {
            'name': '待确认维修',
            'count': all if all else 0
        },
        {
            'name': '所有维修'
        }
    ]
    return responseGet("获取列表成功", {'list': myList})


@ api.route('/getAllRepairs', methods=['GET'])
@ login_required
def getAllRepairs():
    res = request.args
    keywords = res.get('keywords')
    astatus = int(res.get('astatus'))  # 0待确认=cfdate为空且ongoing，1待完成=有cfdate且ongoing，2已完成closed,3为all
    nb = res.get('nb')
    suggests = db.session.query(Suggest.Id, Suggest.duedate, Suggest.acdate, Suggest.cfdate, Suggest.beforepic,  Suggest.status, Suggest.linename, Suggest.type2, Suggest.content,
                                Tpmrepair.machineid, Tpmrepair.mrobillid, Tpmrepair.finishtime, Tpmrepair.confirmtime, Tpmrepair.status.label(
                                    'tpmstatus'),
                                Userinfo.cname.label('scname'), Userinfo.dept1.label('sdept'), Userinfo.cname.label('dcname'), Userinfo.dept1.label('ddept')).join(
        Userinfo, Suggest.exeid == Userinfo.eid).outerjoin(Tpmrepair, Suggest.repairid == Tpmrepair.Id)
    if astatus == 0:
        if keywords != '':
            suggests = suggests.filter(Suggest.content.like('%{0}%'.format(keywords)))
        suggests = suggests.filter(Tpmrepair.status == 'open').filter(
            func.datediff(datetime.now(), Tpmrepair.finishtime) < 31).order_by(desc(Tpmrepair.finishtime))
    elif astatus == 1:
        if keywords != '':
            suggests = suggests.filter(Suggest.content.like('%{0}%'.format(keywords)))
        suggests = suggests.filter(Tpmrepair.status == 'closed').order_by(
            desc(Tpmrepair.finishtime))
    suggests = suggests.limit(nb).all()
    cardsData = []
    for s in suggests:
        dic = {
            'status': s.status,
            'dealer': s.ddept+'/'+s.dcname,
            'id': s.Id,
            'title': '|No.'+str(s.Id),
            'subTitle': '承诺日期:'+datetime.strftime(s.acdate, "%Y-%m-%d"),
            'thumb': '/static/icon/'+s.status+'.png',
            'content': s.content,
            'url': getServer()['suggestionUrl'] + s.beforepic.split(',')[0] if s.beforepic else 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png',
            'suggester': s.sdept+'/'+s.scname,
            'linename': s.linename
        }
        cardsData.append(dic)
    return responseGet("获取列表成功", {'cardsData': cardsData})

from flask import Blueprint, request
from app.tms.model.models import Customer,Province,City,District
from extensions import db, ma
from app.public.functions import responseError, responsePost, responseGet, responsePut, responseDelete
import traceback
from ultils.log_helper import ProjectLogger

mylogger = ProjectLogger()
api = Blueprint('tms/customer', __name__)

# Customer序列化器
class CustomerSchema(ma.Schema):
    class Meta:
        fields = ('id', 'company', 'code', 'province', 'city', 'district', 'address', 'email')


@api.route('/getlist', methods=['GET'])
def getlist():
    """获取客户列表 - 前端分页模式"""
    try:
        # 获取搜索参数
        company = request.args.get('company', '').strip()
        address_code = request.args.get('address_code', '').strip()

        # 构建查询
        query = Customer.query

        # 如果有搜索条件，添加搜索过滤
        if company:
            query = query.filter(Customer.company.like(f'%{company}%'))

        if address_code:
            address_code_array=address_code.split('/')
            if len(address_code_array)==1:
                query = query.filter(Customer.province==db.session.query(Province.province_name).filter(Province.province_code==address_code_array[0]).first()[0])
            elif len(address_code_array)==2:
                query = query.filter(Customer.province==db.session.query(Province.province_name).filter(Province.province_code==address_code_array[0]).first()[0],Customer.city==db.session.query(City.city_name).filter(City.city_code==address_code_array[1]).first()[0])
            elif len(address_code_array)==3:
                query = query.filter(Customer.province==db.session.query(Province.province_name).filter(Province.province_code==address_code_array[0]).first()[0],Customer.city==db.session.query(City.city_name).filter(City.city_code==address_code_array[1]).first()[0],Customer.district==db.session.query(District.district_name).filter(District.district_code==address_code_array[2]).first()[0])
        query = query.order_by(Customer.id.desc())

        # 获取所有数据（前端分页）
        customers = query.all()

        # 序列化数据
        data = CustomerSchema(many=True).dump(customers)

        return responseGet('获取客户列表成功', data)

    except Exception as e:
        traceback.print_exc()
        return responseError('获取客户列表失败')


@api.route('/getcustomerbycompany', methods=['GET'])
def getcustomerbyname(customer_id):
    # 根get传过来的company模糊检索出所有客户
    try:
        # 获取搜索参数
        company = request.args.get('company', '').strip()
        # 构建查询
        query = Customer.query
        # 如果有搜索条件，添加搜索过滤
        if company:
            query = query.filter(Customer.company.like(f'%{company}%'))
        # 按ID倒序排列
        query = query.order_by(Customer.id.desc())
        # 获取所有数据（前端分页）
        customers = query.all()
        # 序列化数据
        data = CustomerSchema(many=True).dump(customers)
        return responseGet('获取客户列表成功', data)

    except Exception as e:
        traceback.print_exc()
        return responseError('获取客户列表失败')


@api.route('/createcustomer', methods=['POST'])
def createcustomer():
    """创建新客户"""
    try:
        # 获取请求数据
        data = request.json
        if not data:
            data = request.form.to_dict()

        if not data:
            return responseError('请提供客户数据')

        # 验证必填字段
        required_fields = ['company', 'code']
        for field in required_fields:
            if not data.get(field):
                return responseError(f'缺少必填字段: {field}')

        # 检查客户代码是否已存在
        existing_customer = Customer.query.filter(Customer.code == data.get('code')).first()
        if existing_customer:
            return responseError('客户代码已存在')

        # 只保留模型中存在的字段
        allowed_fields = ['company', 'code', 'province', 'city', 'district', 'address', 'email']
        filtered_data = {k: v for k, v in data.items() if k in allowed_fields}

        # 创建新客户
        new_customer = Customer(**filtered_data)
        db.session.add(new_customer)
        db.session.commit()

        # 返回创建的客户数据
        result_data = CustomerSchema().dump(new_customer)
        return responsePost('创建客户成功', result_data)

    except Exception as e:
        db.session.rollback()
        traceback.print_exc()
        return responseError('创建客户失败')


@api.route('/updatecustomer/<int:customer_id>', methods=['PUT'])
def updatecustomer(customer_id):
    """更新客户信息"""
    try:
        # 获取要更新的客户
        customer = Customer.query.filter(Customer.id == customer_id).first()

        if not customer:
            return responseError('客户不存在')

        # 获取请求数据
        data = request.json
        if not data:
            data = request.form.to_dict()

        if not data:
            return responseError('请提供要更新的数据')

        # 如果要更新客户代码，检查是否与其他客户冲突
        if 'code' in data and data['code'] != customer.code:
            existing_customer = Customer.query.filter(
                Customer.code == data['code'],
                Customer.id != customer_id
            ).first()
            if existing_customer:
                return responseError('客户代码已存在')

        # 只更新允许的字段
        allowed_fields = ['company', 'code', 'province', 'city', 'district', 'address', 'email']
        for key, value in data.items():
            if key in allowed_fields and hasattr(customer, key):
                setattr(customer, key, value)

        db.session.commit()

        # 返回更新后的客户数据
        result_data = CustomerSchema().dump(customer)
        return responsePut('更新客户成功', result_data)

    except Exception as e:
        db.session.rollback()
        traceback.print_exc()
        return responseError('更新客户失败')


@api.route('/deletecustomer', methods=['DELETE'])
def deletecustomer():
    try:
        mylogger.info(f"开始删除客户，ID: {customer_id}")
        customer_id = request.args.get('customer_id', '').strip()
        if not customer_id:
            return responseError('请提供客户ID')
        # 查询要删除的客户
        customer = Customer.query.filter(Customer.id == customer_id).first()

        if not customer:
            mylogger.warning(f"客户不存在，ID: {customer_id}")
            return responseError('客户不存在')

        # 保存客户信息用于日志和返回
        customer_info = {
            'id': customer.id,
            'company': customer.company,
            'code': customer.code
        }

        # 物理删除
        db.session.delete(customer)
        db.session.commit()

        mylogger.info(f"成功删除客户: {customer_info}")
        return responseDelete('删除客户成功', customer_info)

    except Exception as e:
        db.session.rollback()
        mylogger.error(f"删除客户失败，ID: {customer_id}, 错误: {str(e)}")
        traceback.print_exc()
        return responseError('删除客户失败')


@api.route('/batchdeletecustomers', methods=['DELETE'])
def batchdeletecustomers():
    """批量删除客户（物理删除）"""
    try:
        # 获取请求数据
        data = request.json
        if not data:
            return responseError('请提供要删除的客户ID列表')

        customer_ids = data.get('customer_ids', [])
        if not customer_ids or not isinstance(customer_ids, list):
            return responseError('客户ID列表格式错误')

        mylogger.info(f"开始批量删除客户，IDs: {customer_ids}")

        # 查询要删除的客户
        customers = Customer.query.filter(Customer.id.in_(customer_ids)).all()

        if not customers:
            mylogger.warning(f"未找到要删除的客户，IDs: {customer_ids}")
            return responseError('未找到要删除的客户')

        # 保存删除的客户信息
        deleted_customers = []

        # 批量物理删除
        for customer in customers:
            deleted_customers.append({
                'id': customer.id,
                'company': customer.company,
                'code': customer.code
            })
            db.session.delete(customer)

        db.session.commit()

        mylogger.info(f"成功批量删除 {len(customers)} 个客户: {deleted_customers}")
        return responseDelete(f'成功删除 {len(customers)} 个客户', {
            'count': len(customers),
            'deleted_customers': deleted_customers
        })

    except Exception as e:
        db.session.rollback()
        mylogger.error(f"批量删除客户失败，IDs: {customer_ids}, 错误: {str(e)}")
        traceback.print_exc()
        return responseError('批量删除客户失败')


@api.route('/getcustomerstatistics', methods=['GET'])
def getcustomerstatistics():
    """获取客户统计信息"""
    try:
        # 总客户数
        total_customers = Customer.query.count()

        # 按省份统计
        province_stats = db.session.query(
            Customer.province,
            db.func.count(Customer.id).label('count')
        ).group_by(Customer.province).all()

        # 按城市统计
        city_stats = db.session.query(
            Customer.city,
            db.func.count(Customer.id).label('count')
        ).group_by(Customer.city).all()

        result = {
            'total_customers': total_customers,
            'province_statistics': [{'province': p[0] or '未知', 'count': p[1]} for p in province_stats],
            'city_statistics': [{'city': c[0] or '未知', 'count': c[1]} for c in city_stats]
        }

        return responseGet('获取客户统计信息成功', result)

    except Exception as e:
        mylogger.error(f"获取客户统计信息失败，错误: {str(e)}")
        traceback.print_exc()
        return responseError('获取客户统计信息失败')


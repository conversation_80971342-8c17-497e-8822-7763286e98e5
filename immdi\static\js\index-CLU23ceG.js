import{useColumns as C}from"./column-C0Rjcnma.js";import{d as v,q as Y,r as t,o as h,c as k,b as o,h as n,u as e,f as x,_ as M}from"./index-BnxEuBzx.js";import"./operation-DCG_Ggeh.js";import"./moment-C3TZ8gAF.js";const V=v({__name:"index",setup(w){const{search_condition:a,search:d,loading:l,columns:i,loadingConfig:s,dataList:_,adaptiveConfig:m}=C();return Y(()=>{l.value=!1}),(y,r)=>{const c=t("el-form-item"),p=t("el-date-picker"),f=t("el-button"),u=t("el-form"),g=t("pure-table");return h(),k("div",null,[o(u,{inline:!0,class:"search-form bg-bg_color"},{default:n(()=>[o(c,{label:"处理日期"}),o(p,{type:"date",modelValue:e(a).selected_date,"onUpdate:modelValue":r[0]||(r[0]=b=>e(a).selected_date=b),format:"YYYY/MM/DD","value-format":"YYYY-MM-DD",placeholder:"点击选择日期"},null,8,["modelValue"]),o(c,null,{default:n(()=>[o(f,{type:"primary",onClick:e(d)},{default:n(()=>[x("查询")]),_:1},8,["onClick"])]),_:1})]),_:1}),o(g,{ref:"tableRef",border:"",adaptiveConfig:e(m),"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:e(l),"loading-config":e(s),data:e(_),columns:e(i)},null,8,["adaptiveConfig","loading","loading-config","data","columns"])])}}}),q=M(V,[["__scopeId","data-v-9f03cfb4"]]);export{q as default};

import datetime
import requests
import hashlib
import openpyxl


def uploadIMEI(imei, sn, markid, snverify):
    content = [
        [imei, sn, markid]
    ]
    now = datetime.datetime.now()
    td = datetime.datetime.strftime(now, '%Y-%m-%d %H:%M:%S')
    mystr = (sn + str(datetime.datetime.now())).encode('UTF-8')
    id = snverify[:5]+hashlib.md5(mystr).hexdigest()[9:18]
    myJson = {
        "gmt_create": td,
        "id": id,
        "count": 1,
        "gmt_submit": td,
        "sn_code": snverify,
        "content": content
    }
    headers = {'x-pentair-application-token': 'c2ad5d337db248c09088ae7c791e77ef'}
    res1 = requests.post(
        "https://api.iot.pentair.com.cn/scan/upload", json=myJson,
        headers=headers
    )
    content1 = res1.json()
    return str(content1)


def readXLSX(file):
    wb = openpyxl.load_workbook(file, read_only=True)
    sheet = wb['Sheet1']
    maxrow = sheet.max_row+1
    print(maxrow)
    outDic = {}
    for i in range(2, maxrow):
        sn = sheet.cell(row=i, column=1).value
        subname = sheet.cell(row=i, column=2).value
        subsn = sheet.cell(row=i, column=3).value
        if sn not in outDic:
            outDic[sn] = {
                'sn': sn
            }
        if subname == '4G':
            outDic[sn]['IMEI'] = subsn
        elif subname == '防伪':
            outDic[sn]['markid'] = subsn
        outDic[sn]['snverify'] = '11182'
    return outDic


if __name__ == '__main__':
    dic = readXLSX(r'E:\DEV\flaskserver\app\dm\functions\SA11182.xlsx')
    log = r'E:\DEV\flaskserver\app\dm\functions\log.txt'
    for item in dic.values():
        ctx = uploadIMEI(item['IMEI'], item['sn'], item['markid'], item['snverify'])
        print(ctx)
        with open(log, 'a') as f:
            f.write(ctx+'\n')

        # print(ctx)

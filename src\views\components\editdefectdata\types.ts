interface FormItemProps {
  /** 小时号 */
  hourid: number;
  /** 料号 */
  pn: string;
  /** 产出数量 */
  output: number;
  /** 不良类型 */
  defect_type: number;
  /** 计量单位 */
  unit: string;
  /** 调整数量 */
  defect_qty: number;
}
interface ShiftInfoProps {
  /**日期 */
  selecteddate: string;
  /**班次 */
  shift: string;
  /**机台 */
  machineid: string;
}
interface FormProps {
  shiftinfo: ShiftInfoProps;
  action: string;
  formInline: FormItemProps;
}

export type { FormItemProps, FormProps };

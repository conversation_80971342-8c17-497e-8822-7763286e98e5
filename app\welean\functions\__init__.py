import fitz
import os
import requests
import json
import threading
import shutil
from extensions import db
from app.welean.model.models_welean import Getscore, Options
from datetime import date, datetime
from config import config, myEnv
defaultAuth = "0"
resultStatus = ['待完成', '完成-没问题', '完成-有问题', '已错过', '补录-没问题', '补录-有问题']  # layeredaudit
defaultScore = {
    '问答': 2,
    '批准': 5,
    '结束': 5,
    '帮助': 5
}
managers = ['1121072',
            '1121103',
            '1121129',
            '1191069',
            '1200365',
            '1226691',
            '1228072']


def deletePicthread(url):
    print('ttttt', url)
    t = threading.Thread(deletePic(url))
    t.setDaemon(True)
    t.start()


def deletePic(url):
    if os.path.isfile(url):
        abspath = os.path.abspath(url)
        os.remove(abspath)


def moveFile(src_path, dst_path, file):
    try:
        # cmd = 'chmod -R +x ' + src_path
        # os.popen(cmd)
        f_src = os.path.join(src_path, file)
        if not os.path.exists(dst_path):
            os.makedirs(dst_path)
        f_dst = os.path.join(dst_path, file)
        shutil.move(f_src, f_dst)
        return True
    except Exception:
        return False


def copyFile(src_path, dst_path, file):
    try:
        f_src = os.path.join(src_path, file)
        if not os.path.exists(dst_path):
            os.makedirs(dst_path)
        f_dst = os.path.join(dst_path, file)
        shutil.copy(f_src, f_dst)
        return True
    except Exception:
        return False


def moveFolder(src_path, dst_path, del_path='', del_src=''):
    try:
        # cmd = 'chmod -R +x ' + src_path
        # os.popen(cmd)
        if os.path.exists(del_path):
            shutil.rmtree(del_path)
        shutil.move(src_path, dst_path)
        if os.path.exists(del_src):
            shutil.rmtree(del_src)
        return True
    except Exception:
        return False


def mkdir(path):  # escalate after v304
    isExists = os.path.exists(path)
    if not isExists:
        abspath = os.path.abspath(path)
        # print('starcreat', abspath)
        os.makedirs(abspath, mode=0o777)
        # print('endcreate')
        return True
    else:
        return False


def createdir(path):
    isExists = os.path.exists(path)
    if not isExists:
        abspath = os.path.abspath(path)
        # print('starcreat', abspath)
        os.makedirs(abspath, mode=0o777)
        # print('endcreate')
        return True
    else:
        return False


def getAdmins(plant, title):
    admin = db.session.query(Options).filter(Options.cate == 'admin').filter(Options.plant == plant).filter(
        Options.title == title).all()
    adminArr = []
    for a in admin:
        dic = {
            'wxid': a.content
        }
        adminArr.append(dic)
    return adminArr


def getScore(eid, gettype, getid, audittype=0):
    if not audittype:
        today = datetime.strftime(date.today(), '%Y-%m-%d')
        score = Getscore(eid=eid, gettype=gettype, getid=getid,
                         getdate=today, getscore=defaultScore[gettype])
        db.session.add(score)
        db.session.commit()


def getServer():  # 获取上传文件的访问路径
    urls = {
        'servicesPath': config[myEnv].local_path+'static/welean/services/',
        'servicesUrl': config[myEnv].base_url+'static/welean/services/',
        'downloadsPath': config[myEnv].local_path+'static/welean/downloads/',
        'downloadsUrl': config[myEnv].base_url+'static/welean/downloads/',
        'coursesPath': config[myEnv].local_path+'static/welean/courses/',
        'coursesUrl': config[myEnv].base_url+'static/welean/courses/',
        'questionsPath': config[myEnv].local_path+'static/welean/questions/',
        'questionsUrl': config[myEnv].base_url+'static/welean/questions/',
        'prizesPath': config[myEnv].local_path+'static/welean/prizes/',
        'prizesUrl': config[myEnv].base_url+'static/welean/prizes/',
        'suggestionPath': config[myEnv].local_path+'static/welean/suggestions/',
        'suggestionUrl': config[myEnv].base_url+'static/welean/suggestions/',
        'newsPath': config[myEnv].local_path+'static/welean/news/',
        'newsUrl': config[myEnv].base_url+'static/welean/news/',
        'basePath': config[myEnv].local_path+'static/welean/',
        'baseUrl': config[myEnv].base_url+'static/welean/',
        'booksPath': config[myEnv].local_path+'static/welean/books/',
        'booksUrl': config[myEnv].base_url+'static/welean/books/',
        'fivesauditsPath': config[myEnv].local_path+'static/welean/fivesaudits/',
        'fivesauditsUrl': config[myEnv].base_url+'static/welean/fivesaudits/',
    }
    return urls


def PDFthread(pdfPath, imagePath):
    t = threading.Thread(target=pyMuPDF_fitz, args=(pdfPath, imagePath))
    t.setDaemon(True)
    t.start()


def OTthread(dt, openid, href):
    t = threading.Thread(target=subscribeOT, args=(dt, openid, href))
    t.setDaemon(True)
    t.start()


def PROGRESSthread(dt, openid, href):
    t = threading.Thread(target=subscribePROGRESS, args=(dt, openid, href))
    t.setDaemon(True)
    t.start()


def TASKthread(dt, openid, href):
    t = threading.Thread(target=subscribeTASK, args=(dt, openid, href))
    t.setDaemon(True)
    t.start()


def sendScribe(body):
    # 服务端token
    appid = 'wx120b5bd72ac43ab9'
    sessionKey = '31ef616d928d24184583d8f80790704f'
    wxurl = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' + \
        appid + '&secret=' + sessionKey
    tokenRes = requests.get(wxurl)
    serverToken = json.loads(tokenRes.text).get('access_token')
    # 要请求的微信API
    url_msg = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send'
    res = requests.post(url=url_msg, params={
        'access_token': serverToken  # 这里是我们上面获取到的token
    }, data=json.dumps(body, ensure_ascii=False).encode('utf-8'))
    # 打印结果
    print(res.text)


def subscribeOT(dt, openid, href):
    body = {
        # 用户的openId
        "touser": openid,
        # 订阅消息模板id
        "template_id": "nE_oJaWIlQhBwiBvn-4Nbs7oMqump6AJA9hkMs40fGw",
        # 要跳转的页面
        "page": href,
        # 模板消息对应的内容设置 任务描述 责任人 备注
        "data": {
            "thing2": {
                "value": dt['content']
            },
            "thing4": {
                "value": dt['owner']
            },
            "thing5": {
                "value": dt['comments']
            }
        }
    }
    sendScribe(body)


def subscribePROGRESS(dt, openid, href):
    body = {
        # 用户的openId
        "touser": openid,
        # 订阅消息模板id
        "template_id": "gtLgTSfmyq91RiGikOxyXrsFJVN6DqWodAF5JsA6zmM",
        # 要跳转的页面
        "page": href,
        # 模板消息对应的内容设置 进度备注 项目名称 发布时间 负责人 完成情况
        "data": {
            "thing1": {
                "value": dt['content']
            },
            "time13": {
                "value": dt['idate']
            },
            "name5": {
                "value": dt['owner']
            },
            "thing2": {
                "value": dt['status']
            },
            "thing3": {
                "value": dt['comments']
            }
        }
    }
    sendScribe(body)


def subscribeTASK(dt, openid, href):
    body = {
        # 用户的openId
        "touser": openid,
        # 订阅消息模板id
        "template_id": "40Em0uzz3uekoKyl8lJ65yQpHvzv3vcjvPj6Z4sqb38",
        # 要跳转的页面
        "page": href,
        # 任务接收通知  发布人 发布时间  工作地点 工作类型 备注
        "data": {
            "time3": {
                "value": dt['idate']
            },
            "thing1": {
                "value": dt['linename']
            },
            "name9": {
                "value": dt['suggester']
            },
            "phrase6": {
                "value": dt['jobtype']
            },
            "thing10": {
                "value": dt['content']
            }
        }
    }
    sendScribe(body)


def pyMuPDF_fitz(pdfPath, imagePath):
    # startTime_pdf2img = datetime.now()  # 开始时间
    print("imagePath="+imagePath)
    pdfDoc = fitz.open(pdfPath)
    for pg in range(pdfDoc.page_count):
        page = pdfDoc[pg]
        # rotate = int(0)
        # 每个尺寸的缩放系数为1.3，这将为我们生成分辨率提高2.6的图像。
        # 此处若是不做设置，默认图片大小为：792X612, dpi=72
        zoom_x = 0.8  # (1.33333333-->1056x816)   (2-->1584x1224)
        zoom_y = 0.8
        mat = fitz.Matrix(zoom_x, zoom_y)
        pix = page.get_pixmap(matrix=mat, alpha=False)

        if not os.path.exists(imagePath):  # 判断存放图片的文件夹是否存在
            os.makedirs(imagePath)  # 若图片文件夹不存在就创建

        pix.save(imagePath+'/'+'images_%s.png' % str(pg).zfill(3))  # 将图片写入指定的文件夹内

    # endTime_pdf2img = datetime.now()  # 结束时间
    return pdfDoc.page_count

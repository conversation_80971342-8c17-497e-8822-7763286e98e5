FROM python:3.10-slim
LABEL author="boots.tian"
LABEL purpose = 'Injection Molding API Server'
WORKDIR /apiserver
ENV PYTHONIOENCODING=utf-8
ENV TZ Asia/Shanghai
RUN mkdir -p flask data/redis data/mysql log
COPY requirements.txt flask/requirements.txt 
RUN apt update && \
    apt install -y --allow-remove-essential libtinfo5 nginx-full supervisor net-tools vim nano&& \
    pip3 install -r flask/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple && \
    rm /etc/nginx/sites-enabled/default
RUN mkdir -p /etc/nginx/stream-enabled
CMD ["/bin/bash"]

from o365pentairnew import O365
import pymysql
import datetime
from utils.config import cfg
import requests
import pandas as pd
import os


class Tplogs(object):
    def __init__(self):
        self.mailserver = O365()
        self.db = pymysql.connect(host=cfg.emdidb['host'], port=cfg.emdidb['port'], user=cfg.emdidb['user'], password=cfg.emdidb['password'],
                                  database='transformationplan',
                                  charset=cfg.emdidb['charset'])
        self.cursor = self.db.cursor()
        self.td = datetime.datetime.strftime(datetime.date.today(), '%Y-%m-%d')
        self.md = self.td.split('-')[0]+'-'+self.td.split('-')[1]+'-25'

    def dailyUpdate(self):
        self.createStart()
        self.createPastdue()
        self.cursor.close()
        self.db.close()

    def monthlyUpdate(self):
        self.createMonthly()
        self.createClosed()
        self.cursor.close()
        self.db.close()

    def createMonthly(self):
        sql = "select Id,fstatus,annualized from tp_funnel  where (status='ongoing' or status='late') "
        self.cursor.execute(sql)
        results = self.cursor.fetchall()
        if len(results) > 0:
            for r in results:
                funnelid = r[0]
                self.insertLog(funnelid, self.md, 'Monthly')

    def createClosed(self):
        sql = "select Id,fstatus,annualized from tp_funnel  where (status='closed' or status='reported') and fstatus<360"
        self.cursor.execute(sql)
        results = self.cursor.fetchall()
        if len(results) > 0:
            for r in results:
                funnelid = r[0]
                fstatus = r[1]
                if fstatus < 90:
                    self.insertLog(funnelid, self.md, '369', fstatus)
                else:
                    if r[2]:
                        self.insertLog(funnelid, self.md, 'Funnel', fstatus)

    def createStart(self):
        sql = "select Id, planstart,status,lastupdate from tp_funnel  where status='open' and planstart<='%s' and lastupdate is Null" % self.td
        self.cursor.execute(sql)
        results = self.cursor.fetchall()
        if len(results) > 0:
            for r in results:
                funnelid = r[0]
                self.insertLog(funnelid, self.td, 'Start')

    def createPastdue(self):
        sql = " select Id from tp_funnel  where (status='ongoing' or status='late') and duedate<='%s'" % self.td
        # print(sql)
        # return
        self.cursor.execute(sql)
        results = self.cursor.fetchall()
        if len(results) > 0:
            for r in results:
                funnelid = r[0]
                sql2 = "select Id,requiredate,recorddate from tp_funnellog where logtype='Pastdue' and funnelid=%d order by requiredate desc limit 1" % funnelid
                self.cursor.execute(sql2)
                rst = self.cursor.fetchall()
                if len(rst) > 0:
                    recorddate = rst[0][2]
                    if recorddate:
                        self.insertLog(funnelid, self.td, 'Pastdue')
                elif len(rst) == 0:
                    self.insertLog(funnelid, self.td, 'Pastdue')

    def insertLog(self, funnelid, requiredate, logtype, fstatus=0):
        sql = "insert into tp_funnellog (funnelid,requiredate,logtype,saving) values (%d,'%s','%s',0)" % (
            funnelid, requiredate, logtype)
        if logtype == '369' or logtype == 'Funnel':
            sql2 = "update tp_funnel set lastupdate='%s', fstatus=%d where Id=%d" % (
                requiredate, fstatus+30, funnelid)
        else:
            sql2 = "update tp_funnel set lastupdate='%s' where Id=%d" % (
                requiredate, funnelid)
        try:
            print(sql, sql2)
            self.cursor.execute(sql)
            self.cursor.execute(sql2)
        except Exception as e:
            # 发生错误时回滚
            self.db.rollback()
            self.mailserver.sendMail([cfg.gm1],  [cfg.gm2],
                                     'Pentair-Mail-Server TP insert error '+datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), str(e), 'Mail-Server')
            return 0
        else:
            self.db.commit()  # 事务提交


class Dmlogs(object):
    def __init__(self):
        self.db = pymysql.connect(host=cfg.emdidb['host'], port=cfg.emdidb['port'], user=cfg.emdidb['user'], password=cfg.emdidb['password'],
                                  database='dm',
                                  charset=cfg.emdidb['charset'])
        self.cursor = self.db.cursor()
        self.mailserver = O365()

    def snapEPEI(self):
        sql = "select sku,countstock,cyclestock,varstock,safetystock from dm_epeistock where countstock>0 and av=1"
        self.cursor.execute(sql)
        results = self.cursor.fetchall()
        if not results:
            return 0

        snaptime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        insert_values = []

        for r in results:
            sku = r[0]
            countstock = r[1]
            totalstock = r[2]+r[3]+r[4]
            insert_values.append("('%s', %d, '%s','%s')" % (sku, countstock, snaptime, totalstock))

        sql2 = "INSERT INTO dm_epeihistory (sku, stock, snaptime,setstock) VALUES " + ", ".join(insert_values)
        # print(sql2)
        try:
            self.cursor.execute(sql2)
        except Exception as e:
            self.db.rollback()
            self.mailserver.sendMail([cfg.gm1], [cfg.gm2],
                                     'Pentair-Mail-Server DM snapEPEI error ' + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                     str(e), 'Mail-Server')
        else:
            self.db.commit()

    def closeShift(self):
        sql = "select dm_shiftinfo.Id from dm_shiftinfo join dm_lineinfo on dm_shiftinfo.linename=dm_lineinfo.linename  where finishtime is null and autoclose=1"
        self.cursor.execute(sql)
        results = self.cursor.fetchall()
        try:
            if len(results) > 0:
                for r in results:
                    shiftid = r[0]
                    res = requests.post(cfg.flaskurl+"dm/shiftAPI/closeShiftwithoutplan", json={
                        'shiftid': shiftid
                    })
                    content = res.json()
                    cc = []
                    if content["meta"]["status"] == 201:
                        return 1
        except Exception:
            to = ['<EMAIL>']
            title = 'DM自动结束班次失败'
            msg = """
                请查看原因，DM自动结束班次失败
                """
            self.mailserver.sendMail(to, cc, title, msg, 'Mail-Server')


class Carbonlogs(object):
    def __init__(self):
        self.db = pymysql.connect(host=cfg.emdidb['host'], port=cfg.emdidb['port'], user=cfg.emdidb['user'], password=cfg.emdidb['password'],
                                  database='carbonmix',
                                  charset=cfg.emdidb['charset'])
        self.cursor = self.db.cursor()
        self.mailserver = O365()

    def savemixed(self):
        td = datetime.datetime.today()
        ckdate = td.strftime('%Y-%m-%d')
        settings = pd.read_json('setting.json')
        filename = settings['location']['carboncsv']+td.strftime('%Y%m%d')+'.csv'
        # 判断是否存在filename的文件
        if os.path.exists(filename):
            print('exists')
            return
        sql = "select mixno,sum(itemweight) from cm_mixrecord where date(mixdate)='%s' group by mixno" % ckdate
        data = pd.read_sql(sql, self.db)
        if len(data) > 0:
            data.to_csv(filename, encoding='utf-8', index=False, header=None)
        print(settings['location']['carboncsv'], td)


class Locals(object):
    def __init__(self):
        settings = pd.read_json('setting.json')
        self.asnraw = settings['location']['asnraw']
        self.sap1raw = settings['location']['sap1raw']
        self.leanraw = settings['location']['leanraw']

    def clearLean(self):
        # 删除self.leanraw目录下的文件名包含 ASN的文件
        for file in os.listdir(self.leanraw):
            if 'ASN List' in file:
                os.remove(os.path.join(self.leanraw, file))
        for file in os.listdir(self.sap1raw):
            if not os.path.isdir(os.path.join(self.sap1raw, file)):
                os.remove(os.path.join(self.sap1raw, file))
        for file in os.listdir(self.asnraw):
            if not os.path.isdir(os.path.join(self.asnraw, file)):
                os.remove(os.path.join(self.asnraw, file))


def startDaily():
    tp = Tplogs()
    tp.dailyUpdate()


def startMonthly():
    tp = Tplogs()
    tp.monthlyUpdate()


def dmDailyTask():
    dm = Dmlogs()
    dm.closeShift()
    dm.snapEPEI()


def saveCarbon():
    carbon = Carbonlogs()
    carbon.savemixed()


def clearFiles():
    local = Locals()
    local.clearLean()


# dmDailyTask()

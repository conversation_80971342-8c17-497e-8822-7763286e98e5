	<?php
include("coon.php");
$idate=$_GET['idate'];
$linename=$_GET['linename'];
if ($linename) {
   $sql3 ="select id,date(idate) idate,`type2` as `stype` ,content,status,beforepic,afterpic,duedate,comments,acdate,u1.cname suggester,u2.cname executer from wl_suggest 
  left join wl_userinfo u1 on u1.eid=wl_suggest.eid left join wl_userinfo u2 on u2.eid=wl_suggest.exeid
  where date(idate) ='{$idate}' and linename='{$linename}' and wl_suggest.plant='SZ' and stype in ('安全', '安全事件') and u1.cname!='EMDI'";
} else {
    $sql3 ="select id,date(idate) idate,`type2` as `stype` ,content,status,beforepic,afterpic,duedate,comments,acdate,u1.cname suggester,u2.cname executer from wl_suggest 
    left join wl_userinfo u1 on u1.eid=wl_suggest.eid left join wl_userinfo u2 on u2.eid=wl_suggest.exeid
    where date(idate) ='{$idate}' and wl_suggest.plant='SZ' and stype in ('安全', '安全事件') and u1.cname!='EMDI'";
}

$query=mysqli_query($link, $sql3);
if (mysqli_num_rows($query) >= 1) {
    while ($rs = mysqli_fetch_array($query)) {
        $output[]=array('idate'=>$rs['idate'],'acdate'=>$rs['acdate'],'content'=>$rs['content'],
        'type'=>$rs['stype'],'status'=>$rs['status'],'duedate'=>$rs['duedate'],
        'beforepic'=>'https://welean.pentair.cn/flaskserver/static/welean/suggestions/'.explode(',', $rs['beforepic'])[0],
        'afterpic'=>'https://welean.pentair.cn/flaskserver/static/welean/suggestions/'.explode(',', $rs['afterpic'])[0],
        'suggester'=>$rs['suggester'],'executer'=>$rs['executer']);
    }
}
echo(json_encode($output,JSON_UNESCAPED_SLASHES));

?>
	
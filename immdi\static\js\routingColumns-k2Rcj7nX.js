var _=(a,s,l)=>new Promise((m,o)=>{var g=n=>{try{c(l.next(n))}catch(r){o(r)}},f=n=>{try{c(l.throw(n))}catch(r){o(r)}},c=n=>n.done?m(n.value):Promise.resolve(n.value).then(g,f);c((l=l.apply(a,s)).next())});import{aM as p,aN as d,n as i,a2 as b,q as $,b as v,r as x,f as I,S as C,aE as u,aW as O,aX as X,aj as G,W as J}from"./index-BnxEuBzx.js";import{h as T}from"./moment-C3TZ8gAF.js";import K from"./item-CclCd0Fm.js";const Q=a=>p.request("get",d("setting/getroutinglist"),{params:a}),Z=a=>p.request("post",d("setting/updaterouting"),{data:a}),w=a=>p.request("post",d("setting/addrouting"),{data:a}),ee=()=>p.request("post",d("setting/syncrouting")),te=a=>p.request("post",d("setting/deleterouting"),{data:a});function ie(){const a=i([]),s=i(!0),l=i(!0),m=i(0),o=i(0),g=b({pn:""}),f=i(),c=[{label:"料号",prop:"pn",width:"200px"},{label:"料号描述",width:"360px",prop:"pn_des"},{label:"模穴数",width:"70px",prop:"cavity"},{label:"机台号",width:"80px",prop:"machine"},{label:"自动下料",prop:"unload",width:"90px",cellRenderer:({row:t})=>v(x("el-switch"),{modelValue:t.unload,"onUpdate:modelValue":e=>t.unload=e,style:{"--el-switch-on-color":"#13ce66","--el-switch-off-color":"#ff4949",height:"16px"},"inline-prompt":!0,disabled:!0,"active-text":"是","inactive-text":"否","active-value":1,"inactive-value":0},null)},{label:"成型周期",prop:"moldingcycle",formatter(t){const e=t.moldingcycle;return e.toFixed(e%1===0?0:1)}},{label:"上下料时间",prop:"manualcycle"},{label:"SAP工时",prop:"sap_value"},{label:"创建时间",prop:"create_time",formatter(t){if(t.create_time)return T(t.create_time).format("YYYY/MM/DD HH:mm")}},{label:"更新时间",prop:"update_time",formatter(t){if(t.update_time)return T(t.update_time).format("YYYY/MM/DD HH:mm")}},{label:"操作",cellRenderer:({row:t})=>v("div",{style:"display:flex;justify-content:space-around"},[v(x("el-link"),{type:"primary",underline:!0,onClick:()=>R("change",t)},{default:()=>[I("编辑")]}),v(x("el-link"),{type:"danger",underline:!0,onClick:()=>N(t)},{default:()=>[I("删除")]})])}],n=b({text:"正在加载生产采集数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `}),r={offsetBottom:70};function V(t){}const h=()=>{s.value=!0,Q(C(g)).then(t=>{a.value=t.data.res,M.total=t.data.sql_count,m.value=t.data.sql_count,o.value=t.data.redis_count,s.value=!1})},z=()=>_(this,null,function*(){const t=yield ee();t.meta.status!=201?u(t.meta.msg,{customClass:"el",type:"error"}):u(t.meta.msg,{customClass:"el",type:"success"})});$(()=>{h()});function N(t){O.confirm("确认删除该routing吗，删除时将调整相邻状态时间，请确认?","系统提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0,draggable:!0}).then(()=>{te({id:t.id}).then(e=>{e.meta.status==201&&h(),u(e.meta.msg,{customClass:"el",type:e.meta.status==201?"success":"error",duration:2e3})})}).catch(()=>{})}const R=(t,e)=>{var q,k,Y,S,D,F,B,H;X({title:t=="change"?"修改Routing":"新增routing",props:{formInline:{pn:(q=e==null?void 0:e.pn)!=null?q:"",pn_des:(k=e==null?void 0:e.pn_des)!=null?k:"",cavity:(Y=e==null?void 0:e.cavity)!=null?Y:1,machine:(S=e==null?void 0:e.machine)!=null?S:"",moldingcycle:(D=e==null?void 0:e.moldingcycle)!=null?D:0,manualcycle:(F=e==null?void 0:e.manualcycle)!=null?F:0,unload:(B=e==null?void 0:e.unload)!=null?B:0,sap_value:(H=e==null?void 0:e.sap_value)!=null?H:0},ops:{action_type:t=="change"?1:0}},width:"30%",draggable:!0,fullscreenIcon:!1,closeOnClickModal:!1,contentRenderer:()=>G(K,{ref:f}),beforeSure:(ce,re)=>_(this,[ce,re],function*(j,{options:E}){const P=f.value.getRef();function L(){u(`${t=="change"?"修改":"新增"}数据成功！`,{type:"success"}),j(),h()}const A=E.props.formInline;P.validate(W=>{W&&(t=="change"?Z(C(A)).then(y=>{y.meta.status==201?L():u("Routing变更失败",{type:"error"})}):w(C(A)).then(y=>{y.meta.status==201?L():u(y.meta.msg,{type:"error"})}))})})})},M=b({pageSize:15,currentPage:1,pageSizes:[10,15,20,50],total:0,align:"right",background:!0,small:!1});function U(t){n.text=`正在加载第${t}页...`,s.value=!0,J(200).then(()=>{s.value=!1})}return{search_condition:g,sql_count:m,redis_count:o,asyncRouting:z,openItem:R,is_current:l,loading:s,pagination:M,columns:c,dataList:a,loadingConfig:n,getRoutingList:h,adaptiveConfig:r,onSizeChange:V,onCurrentChange:U}}export{ie as useColumns};

<script setup lang="ts">
import { useDark, useECharts } from "@pureadmin/utils";
import { color, number } from "echarts";
import { computed, nextTick, onMounted, ref } from "vue";

const { isDark } = useDark();

const theme = computed(() => (isDark.value ? "dark" : "light"));

const chartRef = ref();
const { setOptions } = useECharts(chartRef, {
  theme
});
interface Props {
  title: string;
  actualValue: number;
  stdValue: number;
  lower: number;
  upper: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "T1", // 默认值为 "Default Title"
  actualValue: 250, // 默认值为 0
  stdValue: 1, // 默认值为 1
  lower: 123, // 默认值为 0
  upper: 200 // 默认值为 100
});

const chart_thick = ref(16);

const textStyle = computed(() => ({
  color: "var(--text-color)" // 使用 CSS 变量
}));

onMounted(async () => {
  // await nextTick(); // 确保DOM更新完成后再执行
  setOptions({
    graphic: [
      {
        type: "text",
        left: "center", // 水平居中
        top: "86%", // 距离顶部 96%（比第二行低一些）
        style: {
          text: "标准范围 " + props.lower + " ℃ ~ " + props.upper + " ℃。", // 动态文本内容
          fontSize: 16,
          fill: isDark.value ? "#fff" : "#000", // 动态绑定颜色
          ...textStyle.value // 动态绑定颜色
        }
      }
    ],
    title: {
      text: props.title, // 表名
      left: "center", // 水平居中
      top: 0, // 距离顶部 20px
      textStyle: {
        fontSize: 18, // 字体大小
        fontWeight: "bold" // 字体加粗
      }
    },
    series: [
      {
        type: "gauge",
        max: 300,
        min: 0,
        axisLine: {
          lineStyle: {
            width: chart_thick.value,
            color: [
              [props.lower / 300, "#FAC858"],
              [props.upper / 300, "#91CC75"],
              [1, "#FD666D"]
            ]
          }
        },
        pointer: {
          itemStyle: {
            // 动态设置指针颜色
            color:
              props.actualValue < props.lower
                ? "#FAC858" // 低于 lower 时的颜色
                : props.actualValue < props.upper
                  ? "#91CC75" // 在 lower 和 upper 之间的颜色
                  : "#FD666D" // 高于 upper 时的颜色
          }
        },
        axisTick: {
          distance: -chart_thick.value,
          length: 8,
          lineStyle: {
            color: "#fff",
            width: 2
          }
        },
        splitLine: {
          distance: -chart_thick.value,
          length: 20,
          lineStyle: {
            color: "#fff",
            width: 2
          }
        },
        axisLabel: {
          color: "inherit",
          distance: chart_thick.value,
          fontSize: 12
        },
        detail: {
          valueAnimation: true,
          formatter: "{value} ℃",
          color: "inherit",
          offsetCenter: [0, "30%"],
          fontSize: 18
        },
        data: [
          {
            value: props.actualValue
          }
        ]
      }
    ]
  });
});
</script>

<template>
  <div class="gauge-container">
    <div
      ref="chartRef"
      class="gauge_chart"
      style="width: 250px; height: 240px"
    />
  </div>
</template>

<style lang="scss" scoped>
.gauge-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .gauge_chart {
    padding: 0;
  }
}
</style>

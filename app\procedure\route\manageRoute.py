from flask import Blueprint, request
from app.procedure.model.models_procedure import User
from extensions import db
from datetime import datetime
from app.public.functions import responseGet, responseError, responsePost
from app.procedure.functions.jwt import login_required
from app.procedure.functions.mylog import MyLogger


# import datetime

mylogger = MyLogger()

api = Blueprint('procedure/manageAPI', __name__)


@api.route('/getUserList', methods=['GET'])
@login_required
def getUserList():
    mylogger.debug('getcmmUserList start=====')
    res = request.args
    mylogger.debug(res)
    pageSize = int(res.get('pagination[pageSize]'))
    currentPage = int(res.get('pagination[currentPage]'))
    name = res.get('form[username]')
    plant = res.get('form[plant]')
    eid = res.get('form[eid]')
    status = res.get('form[status]')
    users = db.session.query(User).filter(User.plant == plant)
    if name != '':
        users = users.filter(User.name.like('%{0}%'.format(name)))
    if eid != '':
        users = users.filter(User.eid.like('%{0}%'.format(eid)))
    if status != '':
        status = int(status)
        users = users.filter(User.status.like('%{0}%'.format(status)))
    total = users.count()
    users = users.order_by(User.Id).paginate(
        page=currentPage, per_page=pageSize, error_out=False).items
    userlist = []
    for i in users:
        dic = {}
        dic['username'] = i.name
        dic['email'] = i.email
        dic['eid'] = i.eid
        dic['role'] = i.role
        dic['status'] = i.status
        dic['createTime'] = i.createtime
        userlist.append(dic)
    result = {'success': True, 'data': {'list': userlist, 'total': total}}
    return responseGet('data', result)


@api.route('/postAddUser', methods=['POST'])
@login_required
def postAddUser():
    mylogger.debug('*'*10+'postAddUser'+'*'*10)
    res = request.json
    mylogger.debug(res)
    eid = res.get('eid')
    username = res.get('username')
    role = res.get('role')
    email = res.get('email')
    status = res.get('status')
    tag = res.get('tag')
    plant = res.get('plant')
    current_time = datetime.now()
    createtime = current_time.strftime("%Y-%m-%d %H:%M:%S")
    if tag == 0:  # 添加
        r = db.session.query(User).filter(
            User.eid == eid).first()
        if r:
            return responseError('已经存在此eid，请勿重复添加', {'re': '失败'})
        useradd = User(email=email, eid=eid, name=username, role=role, createtime=createtime,
                       status=status, plant=plant)
        db.session.add(useradd)
        db.session.commit()
        return responsePost('新建成功', {'re': 'success'})
    elif tag == 1:  # 修改
        db.session.query(User).filter(User.eid == eid).update(
            {"name": username, "role": role, "email": email, "status": status, "createtime": createtime})
        db.session.commit()
        return responsePost('success', {'re': 'success'})
    elif tag == 2:  # 删除
        db.session.query(User).filter(
            User.eid == eid).delete()
        db.session.commit()
        return responsePost('success', {'re': 'success'})
    elif tag == 3:  # 重置密码
        db.session.query(User).filter(User.eid == eid).update(
            {"password_hash": 'pbkdf2:sha256:150000$h9gxdDZG$1059e40336183840eaef7ba82c34acb1d0d85bc9945e531dbfad5f389aa5e854'})
        db.session.commit()
        return responsePost('success', {'re': 'success'})
    elif tag == 4:  # 修改状态
        db.session.query(User).filter(User.eid == eid).update(
            {"status": status})
        db.session.commit()
        return responsePost('success', {'re': 'success'})
    return responsePost('成功修改权限', {'data': 'success', 'msg': ''})


@api.route('/getReleases', methods=['GET'])
def getReleases():
    mylogger.debug('getReleases start=====')
    list = [
        {
            "version": "1.0.0",
            "published_at": "2023-10-28",
            "body": [
                "### 🎫 更新内容 ###",
                "-初始功能完成",
                "-原Excel记录导入此系统"]
        },
        {
            "version": "1.0.1",
            "published_at": "2023-10-30",
            "body": [
                "### 🎫 更新内容 ###",
                "-修改计算器字体为30px，增大显示效果",
                "-打印完成后可以按键继续打印"]
        },
        {
            "version": "1.0.2",
            "published_at": "2023-11-01",
            "body": [
                "### 🎫 更新内容 ###",
                "-修改记录里的重量为拌料号的总重量",
                "-记录现在可以导出Excel",
                "-主页增加了常见问题和解决方式说明"]
        },
        {
            "version": "1.0.3",
            "published_at": "2024-06-17",
            "body": [
                "### 🎫 更新内容 ###",
                "-增加了备注项，可以添加需要备注的内容，比例只能为0"]
        }
    ]
    problemlist = [
        {
            "version": "🔍点击按钮无反应或软件打不开",
            "body": [
                "# 🛠解决建议🛠 #",
                "1. 点击桌面右下方WIFI图标,连接的网络是PNRCNLEAN",
                "2. 点击断开连接按钮断开后再点击连接按钮重新连接网络",
                "3. 等待个5秒左右重新点击按钮看是否网络已经正常了"]
        },
        {
            "version": "🔍弹出错误，登录已经过期，请重新登录",
            "body": [
                "# 🛠解决建议🛠 #",
                "点击右上方用户图标后，点击退出登录按钮，重新登录",
                "或者也可以直接关闭软件后再打开"]
        },
        {
            "version": "🔍打印机无法打印",
            "body": [
                "# 🛠解决建议🛠 #",
                "1.首先看打印机电源灯是否是绿色，有无卡纸",
                "2.如果绿灯且没法打印可能断连了，拔掉打印机的USB线重新插入",
                "3.等待个5~10秒后再重试打印功能"]
        }
    ]
    result = {'success': True, 'data': {
        'list': list, 'problemlist': problemlist}}
    return responseGet('data', result)

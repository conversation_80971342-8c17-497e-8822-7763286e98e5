import{useSysRoleManagement as C}from"./hook-BWfQX1Id.js";import{d as M,n as f,r as a,j as x,o as p,c as H,b as e,h as l,e as R,u as t,b3 as $,f as B,w as V,g as z,b6 as N,b4 as E,b5 as F}from"./index-BnxEuBzx.js";import{_ as S}from"./edit.vue_vue_type_script_setup_true_lang-DvEqgJhI.js";import{_ as Z}from"./assign.vue_vue_type_script_setup_true_lang-CUK29SY4.js";import{h as u}from"./moment-C3TZ8gAF.js";import"./system-DzNitOCO.js";import"./index-8CyAUJH-.js";const j={class:"main-content"},A={class:"mb-2"},U=M({name:"SysRoleManagement",__name:"index",setup(L){const r=f(),d=f(),{dataList:b,loading:h,handleEdit:c,handleAssign:k,handleDelete:v,fetchData:g}=C(r,d);return(T,m)=>{const s=a("el-button"),n=a("el-table-column"),y=a("el-popconfirm"),D=a("el-table"),Y=a("el-card"),w=x("loading");return p(),H("div",j,[e(Y,{shadow:"never"},{default:l(()=>[R("div",A,[e(s,{icon:t($),type:"primary",onClick:m[0]||(m[0]=o=>t(c)(o))},{default:l(()=>[B(" 添加 ")]),_:1},8,["icon"])]),V((p(),z(D,{border:"",data:t(b),"row-key":"id"},{default:l(()=>[e(n,{label:"角色名称",prop:"name","show-overflow-tooltip":""}),e(n,{label:"排序",prop:"rank","show-overflow-tooltip":""}),e(n,{label:"角色描述",prop:"remark","show-overflow-tooltip":""}),e(n,{label:"创建时间",prop:"create_time","show-overflow-tooltip":"",formatter:(o,i,_,q)=>t(u)(_,"ddd, DD MMM YYYY HH:mm:ss Z").isValid()?t(u)(_,"ddd, DD MMM YYYY HH:mm:ss Z").format("YYYY/MM/DD HH:mm:ss"):""},null,8,["formatter"]),e(n,{fixed:"right",label:"操作",width:"160",align:"center"},{default:l(({row:o})=>[e(s,{title:"角色指派权限",disabled:o.name=="admin",icon:t(N),size:"small",type:"success",onClick:i=>t(k)(o)},null,8,["disabled","icon","onClick"]),e(s,{title:"编辑角色",disabled:o.name=="admin",icon:t(E),size:"small",type:"primary",onClick:i=>t(c)(o)},null,8,["disabled","icon","onClick"]),e(y,{title:"是否确认删除?",onConfirm:i=>t(v)(o)},{reference:l(()=>[e(s,{title:"删除角色",disabled:o.name=="admin",icon:t(F),size:"small",type:"danger"},null,8,["disabled","icon"])]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[w,t(h)]]),e(S,{ref_key:"editRef",ref:r,onFetchData:t(g)},null,8,["onFetchData"]),e(Z,{ref_key:"assignRef",ref:d},null,512)]),_:1})])}}});export{U as default};

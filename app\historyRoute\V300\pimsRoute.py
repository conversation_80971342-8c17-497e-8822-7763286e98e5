from flask import Blueprint, request, json
from app.welean.model.models_welean import Auth, Permission, Getscore, Userinfo
from extensions import db
from datetime import datetime
from sqlalchemy import func, or_
from app.public.functions import responseGet, login_required
from app.welean.functions import getServer, defaultAuth, PDFthread
api = Blueprint('welean/V300/pimsAPI', __name__)


@api.route('/cpdf', methods=['GET'])
def conpdf():
    pdfPath = getServer()['newsPath']+'000/demo.pdf'
    imagePath = getServer()['newsPath']+'000/pics/'
    PDFthread(pdfPath, imagePath)

    return responseGet("成功")


@api.route('/getTools', methods=['GET'])
@login_required
def getTools():
    res = request.args
    user = json.loads(res.get('user'))
    eid = user['eid']
    myAuth = db.session.query(Auth.auth).filter(Auth.eid == eid).scalar()
    if not myAuth:
        myAuth = defaultAuth
    else:
        myAuth = (defaultAuth+','+myAuth).split(',')
    permission = db.session.query(Permission).filter(Permission.ptype == 'pims').filter(Permission.pid >= 0).filter(
        or_(Permission.Id.in_(myAuth), Permission.needauth == 0)).order_by(Permission.pid).order_by(Permission.order).all()
    plist = {}
    pArr = []
    for p in permission:
        if p.pid == 0:
            plist[p.Id] = {'name': p.name, 'children': []}
        else:
            if p.pid in plist.keys():
                plist[p.pid]['children'].append({
                    'icon': getServer()['baseUrl']+'pimstools/'+p.icon,
                    'url': p.url,
                    'name': p.name
                })
    for v in plist.values():
        pArr.append(v)
    return responseGet("获取列表成功", {'myAuth': pArr})


@api.route('/getMyStatus', methods=['GET'])
@login_required
def getMyStatus():
    res = request.args
    eid = res.get('eid')
    plant = res.get('plant')
    trophyArr = []
    outArr = []
    index = 0
    totalLevel = db.session.query(func.sum(Getscore.getscore)).filter(
        Getscore.eid == eid).group_by(Getscore.eid).scalar()
    ot = totalLevel
    for i in range(1, 8):
        trophyArr.insert(0, pow(5, i))
    for j in trophyArr:
        index = totalLevel//j
        totalLevel = totalLevel % j
        outArr.append(index)
    sql = """
    SELECT b.* FROM (SELECT t.*, @rownum := @rownum + 1 AS rownum FROM (SELECT @rownum := 0) r,
    (select wl_userinfo.eid,sum(getscore) from wl_getscore  inner join wl_userinfo on wl_userinfo.eid=wl_getscore.eid
    where year(getdate)=%d and active=1 and plant='%s'  group by eid order by sum(getscore) desc ) AS t) AS b where eid='%s'
    """ % (datetime.now().year, plant, eid)
    cursor = db.session.execute(sql)
    result = cursor.fetchall()
    totalPeople = db.session.query(Getscore.eid).join(Userinfo, Userinfo.eid == Getscore.eid).filter(Userinfo.plant == plant).filter(Userinfo.active == 1).filter(
        func.year(Getscore.getdate) == datetime.now().year).group_by(Getscore.eid).all()
    myStatus = {
        'level': ot,
        'score': int(result[0][1]),
        'rank': int(result[0][2]),
        'total': len(totalPeople)
    }
    return responseGet("获取列表成功", {'myStatus': myStatus, 'tArr': outArr})

from flask import Blueprint, jsonify, request
import datetime
import pymysql
from config import config, env

api = Blueprint('bi/wrdeliveryAPI', __name__)


@api.route('/getOrder', methods=['GET'])
def getOrder():
    sql = """
    select mo,mdi_planinfo.shifttype,mdi_shiftinfo.id sftid,linename,s1.sku sku,s2.sku currentsku,
    sequence,mdi_planinfo.headcount,s1.routing,planner,qty,plandate,status from mdi_planinfo
    left join mdi_lineinfo on mdi_planinfo.lineid=mdi_lineinfo.id
    left join mdi_shiftinfo on mdi_planinfo.plandate= date(mdi_shiftinfo.starttime) and mdi_planinfo.lineid=mdi_shiftinfo.lineid
    left join mdi_skuinfo s2 on mdi_shiftinfo.currentsku=s2.id
    left join mdi_skuinfo s1 on mdi_planinfo.skuid=s1.id
    where plandate>=curdate()
    order by linename,shifttype desc,sequence
    """
    connect = pymysql.connect(host=config[env].wrdb['host'], port=config[env].wrdb['port'], user=config[env].wrdb['user'],
                              password=config[env].wrdb['password'], database=config[env].wrdb['database'], charset=config[env].wrdb['charset'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    cursor.execute(sql)
    rst = cursor.fetchall()
    dic = {}
    for r in rst:
        card = {}
        card['mo'] = r[0]
        card['shifttype'] = r[1]
        card['shiftid'] = r[2]
        card['linename'] = r[3]
        card['sku'] = r[4]
        card['currentsku'] = r[5]
        card['sequence'] = r[6]
        card['headcount'] = r[7]
        card['routing'] = r[8]
        card['planner'] = r[9]
        card['planqty'] = int(r[10]) if r[10] else 0
        card['plandate'] = str(r[11])
        card['status'] = r[12]
        card['scanqty'] = 0
        if card['shiftid']:
            sql2 = """
          select sum(scanqty) qty from mdi_scaninfo where shiftid=%d and sku='%s'
          """ % (card['shiftid'], card['sku'])
            cursor2 = connect.cursor()
            a = cursor2.execute(sql2)
            scanqty = cursor2.fetchone()[0]
            if a:
                card['scanqty'] = int(scanqty) if scanqty else 0
        if card['sku'] == card['currentsku'] and card['scanqty'] > 0:
            card['resttime'] = card['routing'] * \
                (card['planqty']-card['scanqty'])/card['headcount']*3600
        if card['linename'] in dic.keys():
            dic[card['linename']]['children'].append(card)
        else:
            print(dic, card)
            dic[card['linename']] = {
                'linename': card['linename'],
                'children': [card]
            }
    outArr = list(dic.values())
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data=outArr)


@api.route('/getDay', methods=['GET'])
def getDay():
    r = request.args
    dt = r.get('dt')
    sql = """
      select cat,line,sum(pqty) as pqty, sum(sqty) as sqty,sum(scanqty) as scanqty from mdi_lineinfo as K
      left join
      ((select IFNULL(plandate,scandate) as ssdate,IFNULL(linename,scanline) as line,pqty,sqty,sum(scanqty) as scanqty from
      (select sku,date(starttime) as scandate,linename  as scanline, sum(scanqty) as scanqty from mdi_scaninfo
      left join mdi_shiftinfo on mdi_shiftinfo.id=mdi_scaninfo.shiftid
      left join mdi_lineinfo on mdi_shiftinfo.lineid=mdi_lineinfo.id
      where date(starttime)='%s'
      group by date(starttime), linename,scanline) as k
      left join
      (select starttime,scansku,plandate,linename,sum(planqty) as pqty,sum(IFNULL(scanqty,0)) as sqty from
                  (select plandate,linename,sku,sum(qty) as planqty from mdi_planinfo left join mdi_skuinfo on mdi_planinfo.skuid=mdi_skuinfo.id
      left join mdi_lineinfo on mdi_planinfo.lineid=mdi_lineinfo.id
      where plandate='%s'
      group by plandate,linename,sku ) as A
      left join
                  (select sku as scansku,date(starttime) as starttime,scantime,linename  as scanline, sum(scanqty) as scanqty from mdi_scaninfo
      left join mdi_shiftinfo on mdi_shiftinfo.id=mdi_scaninfo.shiftid
      left join mdi_lineinfo on mdi_shiftinfo.lineid=mdi_lineinfo.id
      where date(starttime)='%s'
      group by date(starttime),sku, linename) as B
      on A.sku=B.scansku and A.linename=B.scanline and A.plandate=B.starttime
      where plandate='%s' group by plandate,linename) as t
      on k.sku=t.scansku and k.scanline=t.linename group by line)
      union
      (select IFNULL(plandate,scandate) as ssdate,IFNULL(linename,scanline) as line,pqty,sqty,sum(scanqty) as scanqty from
      (select sku,date(starttime) as scandate,linename  as scanline, sum(scanqty) as scanqty from mdi_scaninfo
      left join mdi_shiftinfo on mdi_shiftinfo.id=mdi_scaninfo.shiftid
      left join mdi_lineinfo on mdi_shiftinfo.lineid=mdi_lineinfo.id
      where date(starttime)='%s'
      group by date(starttime), linename,scanline) as k
      right join
      (select starttime,scansku,plandate,linename,sum(planqty) as pqty,sum(IFNULL(scanqty,0)) as sqty from
                  (select plandate,linename,sku,sum(qty) as planqty from mdi_planinfo left join mdi_skuinfo on mdi_planinfo.skuid=mdi_skuinfo.id
      left join mdi_lineinfo on mdi_planinfo.lineid=mdi_lineinfo.id
      where plandate='%s'
      group by plandate,linename,sku) as A
      left join
                  (select sku as scansku,date(starttime) as starttime,scantime,linename  as scanline, sum(scanqty) as scanqty from mdi_scaninfo
      left join mdi_shiftinfo on mdi_shiftinfo.id=mdi_scaninfo.shiftid
      left join mdi_lineinfo on mdi_shiftinfo.lineid=mdi_lineinfo.id
      where date(starttime)='%s'
      group by date(starttime),sku, linename) as B
      on A.sku=B.scansku and A.linename=B.scanline and A.plandate=B.starttime
      where plandate='%s' group by plandate,linename) as t
      on k.sku=t.scansku and k.scanline=t.linename group by line)) as T
      ON k.linename=T.line
      where line!='' group by linename
      order by cat
    """ % (dt, dt, dt, dt, dt, dt, dt, dt)
    connect = pymysql.connect(host=config[env].wrdb['host'], port=config[env].wrdb['port'], user=config[env].wrdb['user'],
                              password=config[env].wrdb['password'], database=config[env].wrdb['database'], charset=config[env].wrdb['charset'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    cursor.execute(sql)
    rst = cursor.fetchall()
    outArr = []
    for r in rst:
        cat = r[0]
        line = r[1]
        planQty = r[2]
        finishQty = r[3]
        scanQty = r[4]
        reqQty = 0
        issue = ''
        sql2 = """
        select round(sum(requireout),0) from mdi_hourinfo
        left join mdi_shiftinfo ON mdi_hourinfo.shiftid = mdi_shiftinfo.id
        left join mdi_lineinfo ON mdi_shiftinfo.lineid = mdi_lineinfo.id
        where linename='%s' and date(starttime)='%s' group by linename
        """ % (line, dt)
        cursor2 = connect.cursor()
        a = cursor2.execute(sql2)
        if a:
            reqQty = int(cursor2.fetchone()[0])
        sql3 = """
        select `desc` from mdi_issuelog where date(recordtime)='%s' and linename='%s'
        """ % (dt, line)
        cursor3 = connect.cursor()
        b = cursor3.execute(sql3)
        if b:
            issues = cursor3.fetchall()
            for i in issues:
                issue = issue+i[0]+','
        outArr.append(
            {
                'cat': cat,
                'line': line,
                'planQty': int(planQty) if planQty else 0,
                'finishQty': int(finishQty) if finishQty else 0,
                'requireQty': int(reqQty) if reqQty else 0,
                'scanQty': int(scanQty) if scanQty else 0,
                'issue': issue
            }
        )
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data=outArr)


@api.route('/getDmonth', methods=['GET'])
def getCross():
    r = request.args
    stime = r.get('stime')
    etime = r.get('etime')
    area = r.get('area')
    today = datetime.date.today()
    if str(today) == etime:
        etime = str(today-datetime.timedelta(days=1))
    if area:
        sql = """
        select right(plandate,5),avg(dtarget)*95 as red,avg(dtarget)*5 as yellow,round((count(status)-sum(if(status=2,1,0)))/count(status)*100,1) as ftt from
        mdi_planinfo
        left join mdi_lineinfo on mdi_planinfo.lineid=mdi_lineinfo.id
        left join mdi_kpi on mdi_kpi.lineid=mdi_lineinfo.linegroup
        where linegroup= '%s' and plandate between '%s' and  '%s'
        group by plandate
        """ % (area, stime, etime)
        sql2 = """select count(abnormalqty) as cqty,round(mdi_probleminfo.trigger,0) as tg,mdi_shiftreport.sqdctype,mdi_shiftreport.problemtype, mdi_lineinfo.linegroup
        from mdi_shiftreport left join mdi_planinfo on mdi_planinfo.id=mdi_shiftreport.planid
        left join mdi_lineinfo on mdi_lineinfo.id=mdi_planinfo.lineid
        left join mdi_probleminfo on mdi_probleminfo.sqdctype=mdi_shiftreport.sqdctype and mdi_probleminfo.problemtype=mdi_shiftreport.problemtype
        where mdi_lineinfo.linegroup='%s' and plandate between '%s' and  '%s'
        group by problemtype""" % (area, stime, etime)
        sql3 = """
        select plandate as recorddate,problemdes as descd,linename,abnormalqty as qty,problemtype
        from mdi_shiftreport left join mdi_planinfo on mdi_planinfo.id=mdi_shiftreport.planid
        left join mdi_lineinfo on mdi_lineinfo.id=mdi_planinfo.lineid
        where plandate between '%s' and  '%s' and linegroup='%s' order by plandate desc""" % (stime, etime, area)
    else:
        sql = """select right(plandate,5),avg(dtarget)*95 as red,avg(dtarget)*5 as yellow,round((count(status)-sum(if(status=2,1,0)))/count(status)*100,1) as ftt  from
        mdi_planinfo
        left join mdi_kpi on mdi_kpi.lineid= 'All'
        where  plandate between '%s' and  '%s'
        group by plandate""" % (stime, etime)
        sql2 = """
        select count(abnormalqty) as cqty,round(mdi_probleminfo.trigger,0) as tg,mdi_shiftreport.sqdctype,mdi_shiftreport.problemtype, mdi_lineinfo.linegroup
        from mdi_shiftreport left join mdi_planinfo on mdi_planinfo.id=mdi_shiftreport.planid
        left join mdi_lineinfo on mdi_lineinfo.id=mdi_planinfo.lineid
        left join mdi_probleminfo on mdi_probleminfo.sqdctype=mdi_shiftreport.sqdctype and mdi_probleminfo.problemtype=mdi_shiftreport.problemtype
        where plandate between '%s' and  '%s'
        group by problemtype""" % (stime, etime)
        sql3 = """
        select plandate as recorddate,problemdes as descd,linename,abnormalqty as qty,problemtype
        from mdi_shiftreport left join mdi_planinfo on mdi_planinfo.id=mdi_shiftreport.planid
        left join mdi_lineinfo on mdi_lineinfo.id=mdi_planinfo.lineid
        where plandate between '%s' and  '%s' order by plandate desc""" % (stime, etime)
    connect = pymysql.connect(host=config[env].wrdb['host'], port=config[env].wrdb['port'], user=config[env].wrdb['user'],
                              password=config[env].wrdb['password'], database=config[env].wrdb['database'], charset=config[env].wrdb['charset'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    cursor.execute(sql)
    rst = cursor.fetchall()
    outArr = {
        'fttx': [],
        'red': [],
        'yellow': [],
        'green': [],
        'ftt': [],
        'fttmin': 0,
        'paretox': [],
        'paretotarget': [],
        'paretodata': [],
        'listData': []
    }
    for r in rst:
        outArr['fttx'].append(r[0])
        outArr['red'].append(float(r[1]))
        outArr['yellow'].append(float(r[2]))
        outArr['green'].append(100-float(r[1])-float(r[2]))
        outArr['ftt'].append(float(r[3]))
    outArr['fttmin'] = min(outArr['ftt'])-1 if len(outArr['ftt']) > 0 else 50

    cursor.execute(sql2)
    rst2 = cursor.fetchall()
    for r in rst2:
        paretox = r[3] if r[3] else '其他'
        target = int(r[1]) if r[1] else 5
        pdata = int(r[0]) if r[0] else 0
        outArr['paretox'].append(paretox)
        outArr['paretotarget'].append(target)
        outArr['paretodata'].append(
            {
                'value': pdata,
                'itemStyle': {
                    'color': '#A2323D' if pdata > target else '#7CFFB2'
                }
            }
        )
    cursor.execute(sql3)
    rst3 = cursor.fetchall()
    for r in rst3:
        outArr['listData'].append(
            {
                'recorddate': datetime.datetime.strftime(r[0], "%Y-%m-%d"),
                'desc': r[1],
                'linename': r[2],
                'qty': r[3],
                'problemtype': r[4] if r[4] else '其他'
            }
        )
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data=outArr)

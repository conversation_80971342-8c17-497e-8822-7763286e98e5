from datetime import datetime, timedelta, date
import dateparser

# 根据班次，返回相应的开始小时号和结束小时号


def get_shift_hour(shift):
    shift_mapping = {
        'A': {'start_hour': 8, 'end_hour': 16},
        'B': {'start_hour': 16, 'end_hour': 24},
        'C': {'start_hour': 0, 'end_hour': 8}
    }
    return shift_mapping.get(shift, {'start_hour': None, 'end_hour': None})

# 根据日期和班次，返回相应班次的开始日期时间和结束日期时间,offset为班次偏移量，-1为上一班，1为下一班


def get_shift_time(query_date, shift, offset=0):
    query_date = dateparser.parse(str(query_date))
    if offset == -1:
        if shift == 'A':
            query_date = query_date - timedelta(days=1)
        shift_mapping = {"A": "C", "B": "A", "C": "B"}
        shift = shift_mapping.get(shift)
    elif offset == 1:
        if shift == 'C':
            query_date = query_date + timedelta(days=1)
        shift_mapping = {"A": "B", "B": "C", "C": "A"}
        shift = shift_mapping.get(shift)
    if shift == 'A':
        start_time = query_date + timedelta(hours=8)
        end_time = query_date + timedelta(hours=16)
    elif shift == 'B':
        start_time = query_date + timedelta(hours=16)
        end_time = query_date + timedelta(days=1)
    elif shift == 'C':
        start_time = query_date+timedelta(days=1)
        end_time = query_date + timedelta(days=1, hours=8)
    return {'shift': shift, 'shift_date': query_date, 'start_time': start_time, 'end_time': end_time}

# 根据日期、班次及小时号返回时间区间


def get_ts(query_date, shift, hourid, offset=0):
    res = get_shift_time(query_date, shift, offset)
    delta_hour = int(hourid) - get_shift_hour(shift)['start_hour']
    return {'shift': res['shift'],
            "shift_date": res['shift_date'],
            "start_time": res['start_time']+timedelta(hours=delta_hour),
            'end_time': res['start_time']+timedelta(hours=delta_hour+1)}

# 查询指定时间submit_time是否在指定query_date日期的shift班次时间范围内


def is_in_shift(query_date, shift, submit_time):
    submit_time = dateparser.parse(str(submit_time))
    return submit_time >= get_shift_time(query_date, shift)['start_time'] and submit_time < get_shift_time(query_date, shift)['end_time']

# 返回当前班次的代号，A:早班，B:中班，C：夜班


def current_shiftcode():
    return get_shiftcode_bytime(datetime.now())


def get_shiftcode_bytime(selectedtime):
    if not isinstance(selectedtime, datetime):
        selectedtime = dateparser.parse(selectedtime)
    hourid = selectedtime.hour
    current_shift = ''
    if hourid in range(8, 16):
        current_shift = 'A'
    elif hourid in range(16, 24):
        current_shift = 'B'
    elif hourid in range(0, 8):
        current_shift = 'C'
    return current_shift
    ...

# 根据日期和班次校验是否为当前班次


def check_current_shift(selecteddate, shift):
    if current_shiftcode() in ['A', 'B'] and shift == current_shiftcode() and selecteddate == str(datetime.now().date()):
        is_current_shift = True
    elif current_shiftcode() == 'C' and shift == current_shiftcode() and selecteddate == str(datetime.now().date()-timedelta(days=1)):
        is_current_shift = True
    else:
        is_current_shift = False
    return is_current_shift


# 根据所给的时间，返回该时间所在的班次的起始时间、班次代号、班次日期
def find_shift_bytime(selectedtime):
    selectedtime = dateparser.parse(str(selectedtime))
    shift_code = get_shiftcode_bytime(selectedtime)
    if shift_code in ['A', 'B']:
        selecteddate = selectedtime.date()
    elif shift_code in ['C']:
        selecteddate = selectedtime.date()-timedelta(days=1)
    res = get_shift_time(selecteddate, shift_code, offset=0)
    return {'shift': shift_code, 'shift_date': selecteddate, 'start_time': res['start_time'], 'end_time': res['end_time']}


# 将毫秒时间戳转换为北京时间
def timestamp_to_bjtime(timestamp):
    timestamp = int(timestamp) / 1000
    time_local = datetime.fromtimestamp(timestamp)
    time_bj = time_local + timedelta(hours=8)
    return time_bj.strftime('%Y-%m-%d %H:%M:%S')


# 将北京时间转换为毫秒时间戳
def bjtime_to_timestamp(bjtime):
    bjtime = datetime.strptime(bjtime, '%Y-%m-%d %H:%M:%S')
    timestamp = int(bjtime.timestamp() * 1000)
    return timestamp

# 根据日期和小时号返回对应的毫秒时间戳


def get_ts_by_hourid(date_obj, hour_index):
    if hour_index == 24:
        date_obj = date_obj+timedelta(days=1)
        hour_index = 0
    dt = datetime(date_obj.year, date_obj.month, date_obj.day, hour_index)
    timestamp = dt.timestamp() * 1000
    return int(timestamp)


# 将redis中的节拍数据结合班次料号信息，刷入hourly_output表中


# 班次状态履历智能纠正

var f=(o,a,r)=>new Promise((n,l)=>{var i=e=>{try{t(r.next(e))}catch(s){l(s)}},p=e=>{try{t(r.throw(e))}catch(s){l(s)}},t=e=>e.done?n(e.value):Promise.resolve(e.value).then(i,p);t((r=r.apply(o,a)).next())});import{aE as u,aX as m,aj as c}from"./index-BnxEuBzx.js";import d from"./param-DuQmBhrx.js";function b(o,a){if(o!=9){u("该台设备未部署采集模块，无法查看实时参数",{customClass:"el",type:"error"});return}m({title:"查看"+o+"#机实时参数"+a,hideFooter:!0,props:{queryinfo:{machineid:o,sku:a}},width:"60%",draggable:!0,fullscreenIcon:!1,closeOnClickModal:!1,contentRenderer:()=>c(d),footerButtons:[],beforeSure:(l,i)=>f(this,[l,i],function*(r,{options:n}){})})}export{b as show_param};

import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";
import { ref, onMounted, reactive } from "vue";
import { watch, toRaw } from "vue";
import { getdefectlist } from "@/api/front";
import { useProdStoreHook } from "@/store/modules/prod";

export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);

  watch(
    [
      () => useProdStoreHook().selectedDate,
      () => useProdStoreHook().shift,
      () => useProdStoreHook().machineId
    ],
    (newvalue: any, _) => {
      query_param.selecteddate = newvalue[0];
      query_param.shift = newvalue[1];
      query_param.machine = newvalue[2];
      refreshData();
    }
  );

  const query_param = reactive({
    selecteddate: useProdStoreHook().selectedDate,
    machine: useProdStoreHook().machineId,
    shift: useProdStoreHook().shift
  });

  const refreshData = () => {
    getdefectlist(toRaw(query_param)).then((res: { data: any }) => {
      dataList.value = res.data;
      loading.value = false;
    });
  };

  onMounted(() => {
    refreshData();
    loading.value = false;
  });

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载不良记录数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  const adaptiveConfig: AdaptiveConfig = {
    // offsetBottom: 300,
    fixHeader: true
  };
  const columns: TableColumnList = [
    {
      label: "小时",
      prop: "hourid",
      minWidth: 60,
      cellRenderer: ({ row }) => (
        <div>{`${row.hourid}:00~${row.hourid + 1}:00`}</div>
      )
    },
    { label: "料号", prop: "pn", minWidth: 80 },
    { label: "类型", prop: "defect_type", minWidth: 60 },
    { label: "数量", prop: "quantity", minWidth: 40 },
    { label: "单位", prop: "unit", minWidth: 40 }
  ];

  return {
    dataList,
    columns,
    loadingConfig,
    adaptiveConfig,
    loading,
    refreshData
  };
}

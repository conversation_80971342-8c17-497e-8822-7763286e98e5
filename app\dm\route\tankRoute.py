from flask import Blueprint, request
from app.dm.model.models_dm import Tankplan, Tankplanact, Shiftinfo, Lineinfo, Skuinfo, Scaninfo, Issuelog, Routing, Probleminfooee, Restinfo, \
    Tankplan2, Tankrotoarm, Tankrotomold, Tankrotosku
from extensions import db
import datetime
from config import config, env
from sqlalchemy import func, desc, or_, not_, and_,text
import traceback
import hashlib
import requests
import math
from openpyxl import load_workbook
from app.public.functions import responseError, responsePost, responseGet, responsePut
from app.dm.functions import login_required, getServer,oeeArr
from app.dm.functions.roto import Roto
api = Blueprint('dm/tankAPI', __name__)

tankMgroup = ['Roto molding', 'Blow molding - 250L',
              'Blow molding - 80L', 'Winding - 4#', 'Winding - 3#', 'Winding - 1#/2#']
tankLinegroup = ['Winding1', 'Winding2', 'Winding3',
                 'Winding4', '滚塑', '吹塑大', '吹塑小', 'Tank包装']


@api.route('/getRotosku', methods=['GET'])  # 重置密码
@login_required
def getRotosku():
    skus = db.session.query(Tankrotomold).all()
    dic = {}
    for s in skus:
        dic[s.sku] = s.mold
    return responseGet('成功', {'skuDic': dic})


@api.route('/addrotosku', methods=['POST'])
@login_required
def addrotosku():
    res = request.json
    sku = res.get('sku').upper()
    Id = res.get('Id')
    mold = res.get('mold')
    unload = res.get('unload')
    load = res.get('load')
    bake = res.get('bake')
    cool = res.get('cool')
    try:
        if Id:
            db.session.query(Tankrotosku).filter(Tankrotosku.Id == Id).update({
                'sku': sku,
                'unload': unload,
                'load': load,
                'bake': bake,
                'cool': cool
            })
            db.session.query(Tankrotomold).filter(
                Tankrotomold.sku == sku).delete()
            for m in mold:
                newmold = Tankrotomold(sku=sku, mold=m, co=60)
                db.session.add(newmold)
        else:
            newsku = Tankrotosku(sku=sku, unload=unload,
                                 load=load, bake=bake, cool=cool)
            db.session.add(newsku)
            for m in mold:
                newmold = Tankrotomold(sku=sku, mold=m, co=60)
                db.session.add(newmold)
        db.session.commit()
        return responsePost('添加成功')
    except Exception:
        db.session.rollback()
        return responseError('添加失败')


@api.route('/getTankrotosku', methods=['GET'])  # 重置密码
@login_required
def getTankrotosku():
    res = request.args
    keywords = res.get('keywords')
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    skus = db.session.query(Tankrotosku.Id, Tankrotosku.sku, Tankrotosku.bake, Tankrotosku.cool, Tankrotosku.unload, Tankrotosku.load, Tankrotomold.mold).outerjoin(
        Tankrotomold, Tankrotomold.sku == Tankrotosku.sku)
    total = db.session.query(Tankrotosku.sku).outerjoin(
        Tankrotomold, Tankrotomold.sku == Tankrotosku.sku)
    if keywords:
        skus = skus.filter(or_(Tankrotosku.sku.like(
            '%'+keywords+'%'), Tankrotomold.mold.like('%'+keywords+'%')))
        total = total.filter(or_(Tankrotosku.sku.like(
            '%'+keywords+'%'), Tankrotomold.mold.like('%'+keywords+'%')))
    skus = skus.all()
    total = total.group_by(Tankrotosku.sku).all()
    total = len(total)
    outDic = {}
    for s in skus:
        # print(111, s.sku)
        if s.sku in outDic.keys():
            outDic[s.sku]['mold'].append(s.mold)
        else:
            outDic[s.sku] = {
                'Id': s.Id,
                'sku': s.sku,
                'unload': s.unload,
                'load': s.load,
                'bake': s.bake,
                'cool': s.cool,
                'mold': [s.mold]
            }
    molds = db.session.query(Tankrotomold.mold).group_by(
        Tankrotomold.mold).all()
    moldList = []
    for m in molds:
        moldList.append({
            'label': m.mold,
            'value': m.mold
        })
    # print(pagenum, pagesize, list(outDic.values())[1:10])
    return responseGet('成功', {'skuList': list(outDic.values())[(pagenum-1)*pagesize:pagenum*pagesize], 'total': total, 'moldList': moldList})


def getRotosetting():
    containersDic = {}
    containers = db.session.query(Tankrotomold).all()
    for c in containers:
        if c.mold in containersDic.keys():
            containersDic[c.mold]['products'].append(c.sku)
        else:
            containersDic[c.mold] = {
                'products': [c.sku],
                'name': c.mold,
                'status': 'empty',
                'changeovertime': c.co
            }
    arms = db.session.query(Tankrotoarm).order_by(Tankrotoarm.arm).all()
    armsDic = {}
    for a in arms:
        if a.arm in armsDic.keys():
            armsDic[a.arm]['containers'].append(a.mold)
        else:
            armsDic[a.arm] = {
                'containers': [a.mold],
                'name': a.arm,
                'time': 0,
                'product': '',
                'container': ''
            }
    armsDic = list(armsDic.values())
    # print(armsDic)
    return containersDic, armsDic


@ api.route('/mockList', methods=['POST'])  # 重置密码
@ login_required
def mockList():
    res = request.json
    mocklist = res.get('mocklist')
    thisstart = res.get('thisstart')
    rotoskus = db.session.query(Tankrotosku).all()
    rotoskuDic = {}
    for r in rotoskus:
        rotoskuDic[r.sku] = {
            'sku': r.sku,
            'unload': r.unload,
            'load': r.load,
            'bake': r.bake,
            'cool': r.cool
        }
    dic = {}
    newlist = []
    processTimeDic = {}
    for item in mocklist:
        sku = item.split('#')[0]
        mold = int(item.split('#')[1])
        processTimeDic[sku] = {
            'load': rotoskuDic[sku]['load']*mold,
            'unload': rotoskuDic[sku]['unload']*mold,
            'bake': rotoskuDic[sku]['bake'],
            'cool': rotoskuDic[sku]['cool']
        }
        if item in dic.keys():
            dic[item].append(item+'$'+str(len(dic[item])).zfill(3))
            newlist.append(item+'$'+str(len(dic[item])).zfill(3))
        else:
            dic[item] = [item+'$000']
            newlist.append(item+'$000')
    # print(111, processTimeDic)
    containersDic, armsDic = getRotosetting()
    roto = Roto(newlist, processTimeDic, containersDic, armsDic)
    tag, myData = roto.getTimeinseq(newlist, thisstart)
    if tag:
        return responsePost('成功', {'data': myData})
    return responseError('fail')


@ api.route('/submitMock', methods=['POST'])  # 重置密码
@ login_required
def submitMock():
    res = request.json
    scaninfo = res.get('scaninfo')
    processTimeDic = {}
    pdList = []
    rotoskuDic = {}
    rotoskus = db.session.query(Tankrotosku).all()
    for r in rotoskus:
        rotoskuDic[r.sku] = {
            'sku': r.sku,
            'unload': r.unload,
            'load': r.load,
            'bake': r.bake,
            'cool': r.cool
        }
    for ss in scaninfo:
        if ss['sku'] not in rotoskuDic.keys():
            return responseError(ss['sku']+'的滚塑基本信息不存在，请完善后再试')
        for i in range(math.ceil(int(ss['scanqty'])/ss['mold'])):
            pdList.append(ss['sku']+'#'+str(ss['mold'])+'$'+str(i+1).zfill(3))
            processTimeDic[ss['sku']] = {
                'load': rotoskuDic[ss['sku']]['load']*ss['mold'],
                'unload': rotoskuDic[ss['sku']]['unload']*ss['mold'],
                'bake': rotoskuDic[ss['sku']]['bake'],
                'cool': rotoskuDic[ss['sku']]['cool']
            }

    containersDic, armsDic = getRotosetting()
    # print(111, armsDic)
    roto = Roto(pdList, processTimeDic, containersDic, armsDic)
    armNames = []
    for arm in armsDic:
        armNames.append(arm['name'])
    # print(pdList)
    # print(processTimeDic)
    # print(containersDic)
    # print(armsDic)
    tag, myData = roto.getShortestTime_dp(1000)
    if tag:
        return responsePost('成功', {'data': myData, 'cate': armNames})
    return responseError(myData[0])


@api.route('/getModetail', methods=['GET'])  # 重置密码
@login_required
def getModetail():
    res = request.args
    mo = res.get('mo')
    detail = db.session.query(Tankplanact.mo, Shiftinfo.shifttype, Shiftinfo.lineleader, func.sum(Tankplanact.qty).label('scanqty'), Shiftinfo.starttime,
                              Shiftinfo.sku).outerjoin(Shiftinfo, Tankplanact.shiftid == Shiftinfo.Id).outerjoin(
        Scaninfo, Scaninfo.shiftid == Shiftinfo.Id).group_by(Shiftinfo.Id).filter(Tankplanact.mo == mo).all()
    outArr = []
    for d in detail:
        outArr.append({
            'starttime': datetime.datetime.strftime(d.starttime, '%Y-%m-%d %H:%M:%S'),
            'sku': d.sku,
            'mo': d.mo,
            'shifttype': d.shifttype,
            'lineleader':  d.lineleader.split('@')[0],
            'scanqty': d.scanqty
        })
    return responseGet('成功', {'modetail': outArr})


@api.route('/getSkudetail', methods=['GET'])
@login_required
def getSkudetail():
    res = request.args
    sku = res.get('sku')
    detail = db.session.query(Scaninfo.sku, Shiftinfo.shifttype, func.sum(Tankplanact.qty).label('scanqty'), Shiftinfo.starttime, Tankplanact.mo,
                              Shiftinfo.lineleader, Shiftinfo.starttime).join(Shiftinfo, Scaninfo.shiftid == Shiftinfo.Id).join(
        Tankplanact, Tankplanact.shiftid == Shiftinfo.Id).filter(Scaninfo.sku == sku).order_by(desc(Scaninfo.scantime)).group_by(Shiftinfo.Id).limit(20).all()
    outArr = []
    for d in detail:
        outArr.append({
            'starttime': datetime.datetime.strftime(d.starttime, '%Y-%m-%d %H:%M:%S'),
            'sku': d.sku,
            'mo': d.mo,
            'shifttype': d.shifttype,
            'lineleader': d.lineleader.split('@')[0],
            'scanqty': d.scanqty
        })
    return responseGet('成功', {'skudetail': outArr})


@api.route('/changeShift', methods=['PUT'])  # 重置密码
@login_required
def changeShift():
    res = request.json
    starttime = res.get('starttime')
    finishtime = res.get('finishtime')
    stime = res.get('stime')
    ftime = res.get('ftime')
    linename = res.get('linename')
    shifttype = res.get('shifttype')
    team = res.get('team')
    # currentDate = res.get('currentDate')
    try:
        db.session.query(Shiftinfo).filter(Shiftinfo.linename == linename, Shiftinfo.shifttype == shifttype,
                                           Shiftinfo.starttime == stime,
                                           Shiftinfo.finishtime == ftime).update({
                                               'starttime': starttime,
                                               'finishtime': finishtime,
                                               'team': team
                                           }, synchronize_session=False)
        db.session.commit()
        return responsePut('班次修改成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('修改失败，请联系管理员')


@api.route('/removeScan', methods=['POST'])  # 重置密码
@login_required
def premoveScan():
    res = request.json
    shiftid = res.get('shiftid')
    sku = res.get('sku')
    scanqty = int(res.get('scanqty'))
    tag = removeScan(shiftid, sku, scanqty)
    if tag:
        return responsePost('删除成功')
    return responseError('删除失败，请联系管理员')


def removeScan(shiftid, sku, scanqty):
    try:
        db.session.query(Scaninfo).filter(Scaninfo.shiftid == shiftid).delete()
        sft = db.session.query(Shiftinfo).filter(
            Shiftinfo.Id == shiftid).first()
        linename = sft.linename
        if linename == 'Tank包装':
            plans = db.session.query(Tankplan).filter(
                Tankplan.sku == sku, Tankplan.packed > 0).order_by(desc(Tankplan.modate)).all()
            for p in plans:
                if p.packed >= scanqty:
                    p.packed -= scanqty
                    scanqty = 0
                    break
                else:
                    scanqty -= p.packed
                    p.packed = 0
        db.session.query(Shiftinfo).filter(Shiftinfo.Id == shiftid).delete()
        mo = db.session.query(Tankplanact).filter(
            Tankplanact.shiftid == shiftid).all()
        for m in mo:
            currentmo = db.session.query(Tankplan).filter(
                Tankplan.mo == m.mo).first()
            if currentmo.status != 'open':
                currentmo.status = 'open'
        db.session.query(Tankplanact).filter(
            Tankplanact.shiftid == shiftid).delete()
        db.session.commit()
        return True
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return False


@api.route('/changeQty', methods=['PUT'])  # 重置密码
@login_required
def changeQty():
    res = request.json
    shiftInfo = res.get('shiftInfo')
    sourcescanInfo = res.get('scanInfo')
    shiftid = sourcescanInfo[0]['shiftid']
    sku = sourcescanInfo[0]['sku']
    linename = shiftInfo['linename']
    scanqty = int(sourcescanInfo[0]['scanqty'])
    oscanqty = int(sourcescanInfo[0]['oscanqty'])
    newscanInfo = [{
        'shiftid': shiftid,
        'sku': sku,
        'scanqty': scanqty,
        'oscanqty': oscanqty
    }]
    if linename == 'Tank包装':
        lineArr = ['Winding1', 'Winding2', 'Winding3', 'Winding4']
        openorders = db.session.query(Tankplan.moqty, Tankplan.mo, Skuinfo.sku, Skuinfo.typegroup, Skuinfo.color, Tankplan.packed.label('fqty')).join(
            Skuinfo, Tankplan.sku == Skuinfo.sku).filter(func.datediff(datetime.date.today(), Tankplan.modate) < 30).filter(
            Tankplan.status == 'closed').filter((Tankplan.moqty-Tankplan.packed) > 0).filter(Tankplan.linename.in_(lineArr)).order_by(Tankplan.duedate).all()
        totalOpenqty = 0
        for o in openorders:
            fqty = o.fqty if o.fqty else 0
            totalOpenqty += (o.moqty-fqty)
        if totalOpenqty < scanqty-oscanqty:
            return responseError('修改失败，30天内可包装数量不足，无法修改')
        try:
            tag = removeScan(shiftid, sku, oscanqty)
            if not tag:
                return responseError('删除了修改前的数量，但是添加失败，请重新手动添加')
            else:
                tag = addShiftpack(shiftInfo, newscanInfo)
                if not tag:
                    return responseError('修改失败，请联系管理员')
            return responsePut('修改成功')
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('修改失败，请联系管理员')
    else:
        if linename == 'Winding1' or linename == 'Winding2':
            lineArr = ['Winding1', 'Winding2']
        else:
            lineArr = [linename]
        openorders = db.session.query(Tankplan.moqty, Tankplan.mo, Skuinfo.sku, Skuinfo.typegroup, Skuinfo.color, func.sum(Tankplanact.qty).label('fqty')).join(
            Skuinfo, Tankplan.sku == Skuinfo.sku).outerjoin(
            Tankplanact, Tankplanact.mo == Tankplan.mo).group_by(Tankplan.mo).filter(Tankplan.sku == sku).filter(
            Tankplan.status == 'open').filter(Tankplan.linename.in_(lineArr)).order_by(Tankplan.duedate).all()
        totalOpenqty = 0
        for o in openorders:
            fqty = o.fqty if o.fqty else 0
            totalOpenqty += (o.moqty-fqty)
        if totalOpenqty < scanqty-oscanqty:
            return responseError('修改数量大于了剩余的未完成工单总数，请重新输入不大于工单剩余数量的数字')
        try:
            tag = removeScan(shiftid, sku, oscanqty)
            if not tag:
                return responseError('删除了修改前的数量失败，请联系管理员')
            else:
                tag = addShift(shiftInfo, newscanInfo, linename)
                if not tag:
                    return responseError('删除了修改前的数量，但是添加失败，请重新手动添加')
            return responsePut('数量修改成功')
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('修改失败，请联系管理员')


@api.route('/removeIssue', methods=['POST'])  # 重置密码
@login_required
def removeIssue():
    res = request.json
    issueid = res.get('issueid')
    try:
        db.session.query(Issuelog).filter(Issuelog.Id == issueid).delete()
        db.session.commit()
        return responsePost('删除成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('删除失败')


@api.route('/getIssuetypes', methods=['GET'])  # 重置密码
@login_required
def getIssuetypes():
    res = request.args
    linename = res.get('linename')
    if linename:
        issues = db.session.query(Probleminfooee).join(
            Lineinfo, Probleminfooee.linename == Lineinfo.linegroup).filter(Lineinfo.linename == linename).all()
        issuesDic = {}
        for i in issues:
            if i.sqdctype in issuesDic.keys():
                issuesDic[i.sqdctype]['children'].append({
                    'label': i.problemtype,
                    'value': i.problemtype
                })
            else:
                issuesDic[i.sqdctype] = {
                    'label': i.sqdctype,
                    'value': i.sqdctype,
                    'children': [{
                        'label': i.problemtype,
                        'value': i.problemtype
                    }]
                }
        return responseGet('成功', {'issueTypes': list(issuesDic.values())})
    else:
        return responseError('参数错误')


@api.route('/addIssue', methods=['POST'])  # 重置密码
@login_required
def addIssue():
    res = request.json
    linename = res.get('linename')
    shiftdate = res.get('shiftdate')
    shifttype = res.get('shifttype')
    sqdctype = res.get('problemtype')[0]
    problemtype = res.get('problemtype')[1]
    recorder = res.get('recorder')
    desc = res.get('desc') if res.get('desc') else ''
    recordtime = datetime.datetime.now()
    qty = res.get('qty')
    sku = res.get('sku') if res.get('sku') else ''
    issuemin = res.get('issuemin')
    linegroup = db.session.query(Lineinfo.linegroup).filter(
        Lineinfo.linename == linename).first()[0]
    try:
        issue = Issuelog(linename=linename, shiftdate=shiftdate, shifttype=shifttype, sqdctype=sqdctype, problemtype=problemtype,
                         recorder=recorder, desc=desc, recordtime=recordtime, qty=qty, sku=sku, issuemin=issuemin)
        db.session.add(issue)
        db.session.flush()
        aid = issue.Id
        requests.post(
            config[env].cloud_url+"welean/bi/issueAPI/newSuggest", json={
                'auditid': aid,
                'cfdate': datetime.datetime.strftime(recordtime, '%Y-%m-%d'),
                'idate': shiftdate,
                'comments': '',
                'content': '班次问题-'+sqdctype+'/'+problemtype+'\n'+desc,
                'exeid': 9999999,
                'linename': linegroup,
                'stype': sqdctype,
                'type2': problemtype,
                'plant': 'SZ',
                'mdi': '是'
            })
        db.session.commit()
        return responsePost('添加成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('添加失败')


@api.route('/addShift', methods=['POST'])  # 重置密码
@login_required
def paddShift():
    res = request.json
    shiftInfo = res.get('shiftInfo')
    sourcescanInfo = res.get('scanInfo')
    linename = shiftInfo['linename']
    tag = addShift(shiftInfo, sourcescanInfo, linename)
    if tag:
        return responsePost('添加成功')
    else:
        return responseError('添加失败')


def addShift(shiftInfo, sourcescanInfo, linename):
    if linename == 'Winding1' or linename == 'Winding2':
        lineArr = ['Winding1', 'Winding2']
    else:
        lineArr = [linename]
    openorders = db.session.query(Tankplan.moqty, Tankplan.duedate, Tankplan.mo, Skuinfo.sku,
                                  Skuinfo.typegroup, Skuinfo.color, func.sum(Tankplanact.qty).label('fqty')).join(
        Skuinfo, Tankplan.sku == Skuinfo.sku).outerjoin(
        Tankplanact, Tankplanact.mo == Tankplan.mo).group_by(Tankplan.mo).filter(
        Tankplan.status == 'open').filter(Tankplan.linename.in_(lineArr)).order_by(Tankplan.duedate).all()
    scanInfo = []
    for item in sourcescanInfo:
        for o in openorders:
            restqty = o.moqty-(o.fqty if o.fqty else 0)
            if item['sku'] == o.sku:
                # print('item', item[])
                if item['scanqty'] >= restqty:
                    scanInfo.append({
                        'sku': item['sku'],
                        'scanqty': restqty,
                        'mo': o.mo,
                    })
                    sourcescanInfo.append({
                        'sku': item['sku'],
                        'scanqty': item['scanqty']-restqty
                    })
                    openorders.remove(o)
                    break
                else:
                    scanInfo.append({
                        'sku': item['sku'],
                        'scanqty': item['scanqty'],
                        'mo': o.mo,
                    })
                    break
    # return responseError(11)
    routingDic = {}
    now = datetime.datetime.now()
    routings = db.session.query(Routing).join(Skuinfo, Routing.sku == Skuinfo.sku).filter(
        Skuinfo.mgroup.in_(tankMgroup)).all()
    for r in routings:
        routingDic[r.sku] = r.routing
    try:
        i = 1
        for item in scanInfo:
            if item['scanqty'] > 0:
                sameshift = db.session.query(Scaninfo).join(Shiftinfo, Scaninfo.shiftid == Shiftinfo.Id).filter(
                    Shiftinfo.linename == shiftInfo['linename'], Shiftinfo.starttime == shiftInfo['starttime'],
                    Shiftinfo.finishtime == shiftInfo['finishtime'], Shiftinfo.sku == item['sku'],
                    Shiftinfo.shifttype == shiftInfo['shifttype']).first()
                if sameshift:
                    sameshift.scanqty += item['scanqty']
                    sameshift.sn = shiftInfo['linename'] + \
                        str(datetime.datetime.now().timestamp())
                    sameshift.scantime = now
                    newtanplan = Tankplanact(
                        shiftid=sameshift.shiftid, qty=item['scanqty'], mo=item['mo'])
                    db.session.add(newtanplan)
                else:
                    shift = Shiftinfo(linename=shiftInfo['linename'], starttime=shiftInfo['starttime'], finishtime=shiftInfo['finishtime'],
                                      lineleader=shiftInfo['lineleader'], team=shiftInfo['team'], shifttype=shiftInfo['shifttype'],
                                      headcount=1, sku=item['sku'], routing=routingDic[item['sku']], scantemplate='简单模板')
                    db.session.add(shift)
                    db.session.flush()
                    scan = Scaninfo(shiftid=shift.Id, scantime=now+datetime.timedelta(seconds=i),
                                    sn=shiftInfo['linename'] +
                                    str(int(now.timestamp()*1000000)) +
                                    str(i).zfill(4),
                                    scanqty=item['scanqty'], sku=item['sku'])
                    db.session.add(scan)
                    scanact = Tankplanact(
                        mo=item['mo'], shiftid=shift.Id, qty=item['scanqty'])
                    db.session.add(scanact)
                i += 1
        db.session.commit()
        return True
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return False
# current time to timestamp in string


@api.route('/addShiftpack', methods=['POST'])  # 重置密码
@login_required
def paddShiftpack():
    res = request.json
    shiftInfo = res.get('shiftInfo')
    sourcescanInfo = res.get('scanInfo')
    tag = addShiftpack(shiftInfo, sourcescanInfo)
    if tag:
        return responsePost('添加成功')
    else:
        return responseError('添加失败')


def addShiftpack(shiftInfo, sourcescanInfo):
    lineArr = ['Winding1', 'Winding2', 'Winding3', 'Winding4']
    openorders = db.session.query(Tankplan.moqty, Tankplan.mo, Skuinfo.sku, Skuinfo.typegroup, Skuinfo.color, Tankplan.packed.label('fqty')).join(
        Skuinfo, Tankplan.sku == Skuinfo.sku).filter(func.datediff(datetime.date.today(), Tankplan.modate) < 30).filter(
        Tankplan.status == 'closed').filter((Tankplan.moqty-Tankplan.packed) > 0).filter(Tankplan.linename.in_(lineArr)).order_by(Tankplan.duedate).all()
    scanInfo = []
    for item in sourcescanInfo:
        # print(1111111, item)
        for o in openorders:
            restqty = o.moqty-(o.fqty if o.fqty else 0)
            if item['sku'] == o.sku:
                if item['scanqty'] >= restqty:
                    scanInfo.append({
                        'sku': item['sku'],
                        'scanqty': restqty,
                        'mo': o.mo,
                    })
                    sourcescanInfo.append({
                        'sku': item['sku'],
                        'scanqty': item['scanqty']-restqty
                    })
                    openorders.remove(o)
                    break
                else:
                    scanInfo.append({
                        'sku': item['sku'],
                        'scanqty': item['scanqty'],
                        'mo': o.mo,
                    })
                    break
    # return responseError(11)
    routingDic = {}
    now = datetime.datetime.now()
    routings = db.session.query(Routing).join(Skuinfo, Routing.sku == Skuinfo.sku).filter(
        Skuinfo.mgroup.in_(tankMgroup)).all()
    for r in routings:
        routingDic[r.sku] = r.routing
    try:
        i = 1
        for item in scanInfo:
            if item['scanqty'] > 0:
                sameshift = db.session.query(Scaninfo).join(Shiftinfo, Scaninfo.shiftid == Shiftinfo.Id).filter(
                    Shiftinfo.linename == shiftInfo['linename'], Shiftinfo.starttime == shiftInfo['starttime'],
                    Shiftinfo.finishtime == shiftInfo['finishtime'], Shiftinfo.sku == item['sku'],
                    Shiftinfo.shifttype == shiftInfo['shifttype']).first()
                if sameshift:
                    sameshift.scanqty += item['scanqty']
                    sameshift.sn = shiftInfo['linename'] + \
                        str(datetime.datetime.now().timestamp())
                    sameshift.scantime = now
                else:
                    shift = Shiftinfo(linename=shiftInfo['linename'], starttime=shiftInfo['starttime'], finishtime=shiftInfo['finishtime'],
                                      lineleader=shiftInfo['lineleader'], team=shiftInfo['team'], shifttype=shiftInfo['shifttype'],
                                      headcount=1, sku=item['sku'], routing=routingDic[item['sku']], scantemplate='简单模板')
                    db.session.add(shift)
                    db.session.flush()
                    scan = Scaninfo(shiftid=shift.Id, scantime=now+datetime.timedelta(seconds=i),
                                    sn=shiftInfo['linename'] +
                                    str(int(now.timestamp()*1000000)) +
                                    str(i).zfill(4),
                                    scanqty=item['scanqty'], sku=item['sku'])
                    db.session.add(scan)
                tplan = db.session.query(Tankplan).filter(
                    Tankplan.mo == item['mo']).first()
                tplan.packed += item['scanqty']
                i += 1
        db.session.commit()
        return True
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return False


def getTimestamp():
    return str(int(datetime.datetime.now().timestamp() * 1000))


@api.route('/getTANKOEE', methods=['GET'])
def getTANKOEE():
    res = request.args
    year = res.get('year')
    area = res.get('area')
    group = res.get('group')
    if group=='TANK':
        aArr=['LargeTank', 'SmallTank']
    elif group=='CAN':
        aArr=['CAN']
    else:
        return responseError('该产线未设置OEE')
    areas = db.session.query(Lineinfo.linename).filter(
            Lineinfo.linegroup.in_(aArr)).all()
    options = []
    for a in areas:
        options.append({
            'value': a.linename,
            'label': a.linename
        })
    results = db.session.query(func.date_format(Shiftinfo.starttime,'%x%v'),
                               func.sum(Scaninfo.scanqty*Shiftinfo.routing*60),func.sum(Scaninfo.scanqty)).join(Shiftinfo,Scaninfo.shiftid==Shiftinfo.Id).join(
                                   Lineinfo,Shiftinfo.linename==Lineinfo.linename).filter(
        Lineinfo.linegroup.in_(aArr)).filter(not_(Shiftinfo.finishtime.is_(None))).filter(
            func.year(Shiftinfo.starttime)==year).group_by(func.date_format(Shiftinfo.starttime,'%x%v'))
    workmins=db.session.query(func.date_format(Shiftinfo.starttime,'%x%v').label('iweek'),
                              func.timestampdiff(text('MINUTE'),Shiftinfo.starttime,Shiftinfo.finishtime).label('workmin')).join(
                                   Lineinfo,Shiftinfo.linename==Lineinfo.linename).filter(
        Lineinfo.linegroup.in_(aArr)).filter(not_(Shiftinfo.finishtime.is_(None))).filter(
            func.year(Shiftinfo.starttime)==year).group_by(Shiftinfo.starttime)
    issues=db.session.query(Issuelog.sqdctype,Issuelog.problemtype,func.date_format(Issuelog.shiftdate,'%x%v').label('iweek'),
                            func.sum(Issuelog.issuemin).label('issuemin'),func.sum(Issuelog.qty).label('qty'),
                            func.count(Issuelog.qty).label('count')).filter(
        func.year(Issuelog.shiftdate)==year).filter(Issuelog.sqdctype.in_(['停机损失','效率损失','质量不良'])).group_by(func.date_format(Issuelog.shiftdate,'%x%v'),Issuelog.sqdctype,Issuelog.problemtype)       
    results=results.filter(Shiftinfo.linename==area)
    workmins=workmins.filter(Shiftinfo.linename==area)
    issues=issues.filter(Issuelog.linename==area)
    results=results.all()
    workmins=workmins.all()
    issues=issues.all()
    issueDic={}
    allIssueDic={}
    for i in issues:
        if i.sqdctype in allIssueDic:
            if i.problemtype in allIssueDic[i.sqdctype]:
                allIssueDic[i.sqdctype][i.problemtype]['issuemin']+=(i.issuemin if i.issuemin else 0)
                allIssueDic[i.sqdctype][i.problemtype]['qty']+=(i.qty if i.qty else 0)
                allIssueDic[i.sqdctype][i.problemtype]['count']+=(i.count if i.count else 0)
            else:
                allIssueDic[i.sqdctype][i.problemtype]={
                    'issuemin':i.issuemin if i.issuemin else 0,
                    'qty':i.qty if i.qty else 0,
                    'count':i.count if i.count else 0
                }
        else:
            allIssueDic[i.sqdctype]={
                i.problemtype:{
                    'issuemin':i.issuemin if i.issuemin else 0,
                    'qty':i.qty if i.qty else 0,
                    'count':i.count if i.count else 0
                }
            }
        if i.iweek in issueDic:
            if i.sqdctype in issueDic[i.iweek]:
                issueDic[i.iweek][i.sqdctype][i.problemtype]={
                    'issuemin':i.issuemin if i.issuemin else 0,
                    'qty':i.qty if i.qty else 0,
                    'count':i.count if i.count else 0
                }
            else:
                issueDic[i.iweek][i.sqdctype]={
                    i.problemtype:{
                        'issuemin':i.issuemin if i.issuemin else 0,
                        'qty':i.qty if i.qty else 0,
                        'count':i.count if i.count else 0
                    }
                }
        else:
            issueDic[i.iweek]={
                i.sqdctype:{
                    i.problemtype:{
                        'issuemin':i.issuemin if i.issuemin else 0,
                        'qty':i.qty if i.qty else 0,
                        'count':i.count if i.count else 0
                    }
                }
            } 
    print(1111111,issueDic)
    resultsDic={}
    workminDic={}
    totalWorkmin=0
    totalAllmin=0
    oeeDic={
        'xAxis':[],
        'availability':[],
        'performance':[],
        'quality':[],
        'oee':[]
    }
    for r in results:
        mymin=round(r[1],0)
        resultsDic[r[0]]={
            'min':mymin,
            'qty':r[2]
        }
        totalWorkmin+=mymin
    for w in workmins:
        realWorkmin=w.workmin-w.workmin//300*30
        if w.iweek in workminDic.keys():
            workminDic[w.iweek]+=realWorkmin
        else:
            workminDic[w.iweek]=realWorkmin
        totalAllmin+=realWorkmin
    # print(22222,workminDic,totalAllmin)
    # print(3333,resultsDic,totalWorkmin)
    totalStopmin=0
    totalDefect=0
    totalWorkqty=0
    for k,v in workminDic.items():
        oeeDic['xAxis'].append(k)
        allmin=v
        workmin=resultsDic[k]['min'] if k in resultsDic.keys() else 0
        workqty=resultsDic[k]['qty'] if k in resultsDic.keys() else 0
        totalWorkqty+=workqty
        stopmin=0
        defect=0
        if k in issueDic.keys():
            for kk,vv in issueDic[k].items():
                if kk=='停机损失':
                    for kkk,vvv in vv.items():
                        stopmin+=vvv['issuemin']
                elif kk=='质量不良':
                    for kkk,vvv in vv.items():
                        defect+=vvv['qty']
                        totalDefect+=defect
        totalStopmin+=stopmin
        avail=float((allmin-stopmin))/allmin if allmin!=0 else 0
        perf=workmin/float((allmin-stopmin)) if allmin!=0 else 0
        qual=(workqty-defect)/workqty if workqty!=0 else 0
        # print(allmin,workmin,stopmin)
        oeeDic['availability'].append(round(avail*100,1))
        oeeDic['performance'].append(round(perf*100,1))
        oeeDic['quality'].append(round(qual*100,1))
        oeeDic['oee'].append(round(avail*perf*float(qual)*100,1))
    totalAvail= float((totalAllmin-totalStopmin))/totalAllmin if totalAllmin!=0 else 0
    totalPerf = totalWorkmin/float((totalAllmin-totalStopmin)) if totalAllmin!=0 else 0
    totalQual= (totalWorkqty-totalDefect)/totalWorkqty if totalWorkqty!=0 else 0
    oeeDic['xAxis'].insert(0,'Total')
    oeeDic['availability'].insert(0,round(totalAvail*100,1))
    oeeDic['performance'].insert(0,round(totalPerf*100,1))
    oeeDic['quality'].insert(0,round(totalQual*100,1))
    oeeDic['oee'].insert(0,round(totalAvail*totalPerf*float(totalQual)*100,1))
    qualityPie=[]
    availablePie=[]
    performancePie=[]
    for k,v in allIssueDic.items():
        if k=='质量不良':
            for kk,vv in allIssueDic[k].items():
                qualityPie.append({
                    'name':kk,
                    'value':vv['qty']
                })
        elif k=='停机损失':
              for kk,vv in allIssueDic[k].items():
                availablePie.append({
                    'name':kk,
                    'value':vv['issuemin']
                })
        elif k=='效率损失':
              for kk,vv in allIssueDic[k].items():
                performancePie.append({
                    'name':kk,
                    'value':vv['count']
                })
    return responseGet('查询成功', {'data': {}, 'options': options,'oeeDic':oeeDic,'issueDic':issueDic,'qualityPie':qualityPie,'availablePie':availablePie,'performancePie':performancePie})


@ api.route('/getShifts', methods=['GET'])  # 重置密码
@ login_required
def getShifts():
    res = request.args
    linename = res.get('linename')
    dt = res.get('dt')
    tm = datetime.datetime.strptime(dt, '%Y-%m-%d')+datetime.timedelta(days=1)
    dtime = datetime.datetime.strptime(dt+' 08:00:00', '%Y-%m-%d %H:%M:%S')
    ttime = tm+datetime.timedelta(hours=8)
    shifts = db.session.query(Shiftinfo.linename, Shiftinfo.Id, Shiftinfo.starttime, Tankplanact.mo, Shiftinfo.finishtime, Shiftinfo.routing, Shiftinfo.lineleader,
                              Shiftinfo.team, Shiftinfo.sku, Skuinfo.typegroup, Shiftinfo.shifttype, func.sum(
                                  Tankplanact.qty).label('scanqty'),
                              func.sum(Scaninfo.scanqty).label('packqty')).outerjoin(
        Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).outerjoin(Tankplanact, Tankplanact.shiftid == Shiftinfo.Id).join(Skuinfo, Shiftinfo.sku == Skuinfo.sku).filter(
        or_(and_(func.date(Shiftinfo.starttime) == dt, Shiftinfo.finishtime > dtime),
            and_(func.date(Shiftinfo.starttime) == tm, Shiftinfo.starttime <= ttime, Shiftinfo.finishtime <= ttime))).filter(
        Skuinfo.mgroup.in_(tankMgroup)).group_by(Shiftinfo.Id)
    if linename:
        shifts = shifts.filter(Shiftinfo.linename == linename)
    shifts = shifts.all()
    shiftDic = {}
    lineArr = getTankareas()
    lastteamDic = {}
    lastteam = ''
    lastteaminfo = db.session.query(Shiftinfo.linename, Shiftinfo.team).filter(
        not_(Shiftinfo.team.is_(None))).order_by(desc(Shiftinfo.starttime)).group_by(Shiftinfo.linename).all()
    restDic = {}
    resttime = db.session.query(Restinfo.resttime, Restinfo.restmin, Lineinfo.linename).join(
        Lineinfo, Restinfo.linegroup == Lineinfo.linegroup)
    if linename:
        resttime = resttime.filter(Lineinfo.linename == linename)
    resttime = resttime.all()
    for r in resttime:
        if r.linename not in restDic.keys():
            restDic[r.linename] = {
                r.resttime: r.restmin
            }
        else:
            restDic[r.linename][r.resttime] = r.restmin
    for la in lastteaminfo:
        lastteamDic[la.linename] = la.team
    for s in shifts:
        if s.linename == 'Tank包装':
            scanqty = s.packqty
            routing = 0
        else:
            scanqty = s.scanqty
            routing = s.routing
        ttrest = 0
        for k, v in restDic[s.linename].items():
            ss = datetime.datetime.strftime(s.starttime, '%H:%M:%S')
            ff = datetime.datetime.strftime(s.finishtime, '%H:%M:%S')
            kk = datetime.time.strftime(k, '%H:%M:%S')
            if (ss > ff):
                if ss <= kk < '24:00:00' or '00:00:00' <= kk < ff:
                    ttrest += v
            else:
                if ss <= kk < ff:
                    ttrest += v
        if s.linename in shiftDic.keys():
            if s.shifttype in shiftDic[s.linename].keys():
                shiftDic[s.linename][s.shifttype]['scanInfo'].append({
                    'shiftid': s.Id,
                    'routing': routing,
                    'scanqty': scanqty,
                    'oscanqty': scanqty,
                    'linename': s.linename,
                    'mgroup': s.typegroup,
                    'sku': s.sku,
                    'mo': s.mo
                })
                shiftDic[s.linename][s.shifttype]['oeeInfo']['totalactmin'] += routing*scanqty*60
                shiftDic[s.linename][s.shifttype]['oeeInfo']['totalQty'] += scanqty
            else:
                shiftDic[s.linename][s.shifttype] = {
                    'shiftInfo': {
                        'starttime': datetime.datetime.strftime(s.starttime, '%Y-%m-%d %H:%M:%S') if s.starttime else '',
                        'finishtime': datetime.datetime.strftime(s.finishtime, '%Y-%m-%d %H:%M:%S') if s.finishtime else '',
                        'stime': datetime.datetime.strftime(s.starttime, '%Y-%m-%d %H:%M:%S') if s.starttime else '',
                        'ftime': datetime.datetime.strftime(s.finishtime, '%Y-%m-%d %H:%M:%S') if s.finishtime else '',
                        'linename': s.linename,
                        'shifttype': s.shifttype,
                        'lineleader': s.lineleader,
                        'team': s.team
                    },
                    'scanInfo': [{
                        'shiftid': s.Id,
                        'routing': routing,
                        'scanqty': scanqty,
                        'oscanqty': scanqty,
                        'linename': s.linename,
                        'mgroup': s.typegroup,
                        'sku': s.sku,
                        'mo': s.mo
                    }],
                    'issueInfo': [],
                    'oeeInfo': {
                        'totalmin': (s.finishtime-s.starttime).seconds/60-ttrest,
                        'totalQty': scanqty,
                        'routing':routing,
                        'defectQty': 0,
                        'stopmin': 0,
                        'totalactmin': routing*scanqty*60
                    }
                }
        else:
            shiftDic[s.linename] = {
                s.shifttype: {
                    'shiftInfo': {
                        'starttime': datetime.datetime.strftime(s.starttime, '%Y-%m-%d %H:%M:%S') if s.starttime else '',
                        'finishtime': datetime.datetime.strftime(s.finishtime, '%Y-%m-%d %H:%M:%S') if s.finishtime else '',
                        'stime': datetime.datetime.strftime(s.starttime, '%Y-%m-%d %H:%M:%S') if s.starttime else '',
                        'ftime': datetime.datetime.strftime(s.finishtime, '%Y-%m-%d %H:%M:%S') if s.finishtime else '',
                        'linename': s.linename,
                        'shifttype': s.shifttype,
                        'lineleader': s.lineleader,
                        'team': s.team
                    },
                    'scanInfo': [{
                        'shiftid': s.Id,
                        'routing': routing,
                        'scanqty': scanqty,
                        'oscanqty': scanqty,
                        'linename': s.linename,
                        'mgroup': s.typegroup,
                        'sku': s.sku,
                        'mo': s.mo
                    }],
                    'issueInfo': [],
                    'oeeInfo': {
                        'totalmin': (s.finishtime-s.starttime).seconds/60-ttrest,
                        'totalQty': scanqty,
                        'routing':routing,
                        'defectQty': 0,
                        'stopmin': 0,
                        'totalactmin': routing*scanqty*60
                    }
                }
            }

    if linename:
        if linename in lastteamDic.keys():
            lastteam = lastteamDic[linename]
        else:
            lastteam = ''
        if linename not in shiftDic.keys():
            shiftDic[linename] = {
                '白班': {
                    'shiftInfo': {
                        'team': lastteam
                    },
                    'scanInfo': [],
                    'issueInfo': []
                },
                '夜班': {
                    'shiftInfo': {
                        'team': lastteam
                    },
                    'scanInfo': [],
                    'issueInfo': []
                }
            }
        else:
            for st in ['白班', '夜班']:
                if st not in shiftDic[linename].keys():
                    shiftDic[linename][st] = {
                        'shiftInfo': {
                            'team': lastteam
                        },
                        'scanInfo': [],
                        'issueInfo': []
                    }
    else:
        for ll in lineArr:
            if ll in lastteamDic.keys():
                lastteam = lastteamDic[ll]
            else:
                lastteam = ''
            if ll not in shiftDic.keys():
                shiftDic[ll] = {
                    '白班': {
                        'shiftInfo': {
                            'team': lastteam
                        },
                        'scanInfo': [],
                        'issueInfo': []
                    },
                    '夜班': {
                        'shiftInfo': {
                            'team': lastteam
                        },
                        'scanInfo': [],
                        'issueInfo': []
                    }
                }
            else:
                for st in ['白班', '夜班']:
                    if st not in shiftDic[ll].keys():
                        shiftDic[ll][st] = {
                            'shiftInfo': {
                                'team': lastteam
                            },
                            'scanInfo': [],
                            'issueInfo': []
                        }
    issues = db.session.query(Issuelog).filter(
        Issuelog.shiftdate == dt).order_by(Issuelog.sqdctype).all()
    for line in shiftDic.keys():
        for shift in shiftDic[line].keys():
            if 'linename' in shiftDic[line][shift]['shiftInfo']:
                for i in issues:
                    if i.linename == line and i.shifttype == shift:
                        shiftDic[line][shift]['issueInfo'].append({
                            'issueid': i.Id,
                            'sqdctype': i.sqdctype,
                            'problemtype': i.problemtype,
                            'desc': i.desc,
                            'qty': i.qty,
                            'sku': i.sku,
                            'issuemin': i.issuemin
                        })
                        if 'oeeInfo' in shiftDic[line][shift].keys():
                            shiftDic[line][shift]['oeeInfo']['defectQty'] += i.qty
                            shiftDic[line][shift]['oeeInfo']['totalQty'] += i.qty
                            shiftDic[line][shift]['oeeInfo']['stopmin'] += i.issuemin
            # if 'oeeInfo' in shiftDic[line][shift].keys():
            # #  print(shiftDic[line][shift]['oeeInfo'])
            #  shiftDic[line][shift]['oeeInfo']['totalactmin'] += shiftDic[line][shift]['oeeInfo']['routing']*shiftDic[line][shift]['oeeInfo']['totalQty']*60
    return responseGet('获取成功', {'shiftsInfo': shiftDic, 'lineArr': lineArr})


@api.route('/getAreas', methods=['GET'])  # 重置密码
@login_required
def getAreas():
    lineArr = getTankareas()
    return responseGet('获取成功', {'areas': lineArr})


def getTankareas():
    lines = db.session.query(Lineinfo).filter(
        Lineinfo.area == 'B1Tank').filter(Lineinfo.isactive == 1).order_by(Lineinfo.linename).all()
    lineArr = []
    for ll in lines:
        lineArr.append(ll.linename)
    return lineArr


@api.route('/getOpenorder', methods=['POST'])  # 重置密码
def getOpenorder():
    res = request.json
    linename = res.get('linename')
    scaninfo = res.get('scanInfo')
    scanDic = {}
    if scaninfo:
        for s in scaninfo:
            scanDic[s['sku']] = s['scanqty']
    if linename == 'Winding1' or linename == 'Winding2':
        lineArr = ['Winding1', 'Winding2']
    else:
        lineArr = [linename]
    openorders = db.session.query(Tankplan.moqty, Tankplan.mo, Skuinfo.sku, Skuinfo.typegroup, Skuinfo.color, func.sum(Tankplanact.qty).label('fqty')).join(
        Skuinfo, Tankplan.sku == Skuinfo.sku).outerjoin(
        Tankplanact, Tankplanact.mo == Tankplan.mo).group_by(Tankplan.mo).filter(
        Tankplan.status == 'open').filter(Tankplan.linename.in_(lineArr)).order_by(Tankplan.duedate).all()
    openArr = []
    openDic = {}
    for oo in openorders:
        moqty = oo.moqty if oo.moqty else 0
        fqty = oo.fqty if oo.fqty else 0
        if oo.sku in openDic.keys():
            openDic[oo.sku]['moqty'] = openDic[oo.sku]['moqty']+moqty
            openDic[oo.sku]['fqty'] = openDic[oo.sku]['fqty']+fqty
        else:
            openDic[oo.sku] = {
                'moqty': moqty,
                'fqty': fqty,
                'sku': oo.sku,
                'typegroup': oo.typegroup,
                'color': oo.color
            }
    for oo in openDic.values():
        fqty = oo['fqty'] if oo['fqty'] else 0
        tempqty = scanDic[oo['sku']] if (oo['sku'] in scanDic.keys()) else 0
        if oo['moqty']-fqty-tempqty > 0:
            openArr.append({
                'moqty': oo['moqty']-fqty-tempqty,
                'label': oo['typegroup']+'('+oo['sku']+'-'+oo['color']+')-（'+str(oo['moqty']-fqty-tempqty)+"）",
                'value': [oo['sku'], oo['moqty']-fqty, oo['typegroup']]
            })
    return responseGet('获取成功', {'openorders': openArr})


@api.route('/getOpenorderpack', methods=['POST'])  # 重置密码
def getOpenorderpack():
    res = request.json
    scaninfo = res.get('scanInfo')
    scanDic = {}
    if scaninfo:
        for s in scaninfo:
            scanDic[s['sku']] = s['scanqty']
    lineArr = ['Winding1', 'Winding2', 'Winding3', 'Winding4']
    openorders = db.session.query(Tankplan.moqty, Tankplan.mo, Skuinfo.sku, Skuinfo.typegroup, Skuinfo.color, Tankplan.packed.label('fqty')).join(
        Skuinfo, Tankplan.sku == Skuinfo.sku).filter(func.datediff(datetime.date.today(), Tankplan.modate) < 30).filter(
        Tankplan.status == 'closed').filter((Tankplan.moqty-Tankplan.packed) > 0).filter(Tankplan.linename.in_(lineArr)).order_by(Tankplan.duedate).all()
    openArr = []
    openDic = {}
    for oo in openorders:
        moqty = oo.moqty if oo.moqty else 0
        fqty = oo.fqty if oo.fqty else 0
        if oo.sku in openDic.keys():
            openDic[oo.sku]['moqty'] = openDic[oo.sku]['moqty']+moqty
            openDic[oo.sku]['fqty'] = openDic[oo.sku]['fqty']+fqty
        else:
            openDic[oo.sku] = {
                'moqty': moqty,
                'fqty': fqty,
                'sku': oo.sku,
                'typegroup': oo.typegroup,
                'color': oo.color
            }
    for oo in openDic.values():
        fqty = oo['fqty'] if oo['fqty'] else 0
        tempqty = scanDic[oo['sku']] if (oo['sku'] in scanDic.keys()) else 0
        if oo['moqty']-fqty-tempqty > 0:
            openArr.append({
                'moqty': oo['moqty']-fqty-tempqty,
                'label': oo['typegroup']+'('+oo['sku']+'-'+oo['color']+')-（'+str(oo['moqty']-fqty-tempqty)+"）",
                'value': [oo['sku'], oo['moqty']-fqty, oo['typegroup']]
            })
    return responseGet('获取成功', {'openorders': openArr})


@api.route("/closePlan", methods=["POST"])  # 添加或修改SKU
@login_required
def closePlan():
    res = request.json
    mo = res.get('mo')
    try:
        db.session.query(Tankplan).filter(Tankplan.mo == mo).update({
            'status': 'cancel'
        })
        db.session.commit()
        return responsePost('取消成功')
    except Exception:
        db.session.rollback()
        return responseError('操作失败，请联系管理员')


@api.route("/finishOrder", methods=["POST"])  # 添加或修改SKU
# @login_required
def finishOrder():
    res = request.json
    mo = res.get('mo')
    try:
        db.session.query(Tankplan).filter(Tankplan.mo == mo).update({
            'status': 'closed',
            'finishdate': datetime.datetime.now()
        })
        db.session.commit()
        return responsePost('成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('操作失败，请联系管理员')


@api.route("/addPlan", methods=["POST"])  # 添加或修改SKU
@login_required
def addPlan():
    res = request.json
    mo = res.get('mo')  # 根据mo判断是否是增加
    linename = res.get('linename')
    sku = res.get('sku')
    modate = res.get('modate')
    duedate = res.get('duedate')
    moqty = res.get('moqty')
    id = res.get('Id')
    if(id):
        try:
            db.session.query(Tankplan).filter(Tankplan.mo == mo).update({
                'linename': linename,
                'sku': sku,
                'modate': modate,
                'duedate': duedate,
                'moqty': moqty
            })
            db.session.commit()
            return responsePost('编辑成功')
        except Exception:
            db.session.rollback()
            return responseError('编辑工单'+mo+'信息失败，请联系管理员')
    else:
        try:
            newmo = Tankplan(mo=mo, linename=linename, sku=sku, modate=modate,
                             duedate=duedate, moqty=moqty, status='open', packed=0)
            db.session.add(newmo)
            db.session.commit()
            return responsePost('新增成功')
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('新建工单'+mo+'信息失败，请联系管理员')


@ api.route('/uploadTKPLAN', methods=['POST'])
@ login_required
def uploadTKPLAN():
    file_obj = request.files.get('file')
    em = request.headers["email"]
    mystr = ('tankplanUPLOAD' + str(datetime.datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    appendix = file_obj.filename[file_obj.filename.rfind('.'):]
    file_obj.save(getServer()['uploadPath']+name+appendix)
    moarr = filetoordertankplan(getServer()['uploadPath']+name+appendix, em)
    if moarr == 'fail':
        return responseError('上传失败，请检查模板格式或请联系管理员')
    if len(moarr) > 0:
        text = "部分更新成功，失败工单号如下："+','.join(moarr)+'请检查产线或料号是否存在且内容完整'
    else:
        text = '上传项目全部更新成功'
    return responsePost(text, {
        'upload_url': getServer()['uploadUrl']+name+appendix})


def filetoordertankplan(file, em):
    wb = load_workbook(file)
    ws = wb.active
    if ws.cell(1, 1).value != 'linename' or ws.cell(1, 2).value != 'mo' or ws.cell(1, 3).value != 'sku' \
            or ws.cell(1, 4).value != 'modate' or ws.cell(1, 5).value != 'duedate' or ws.cell(1, 6).value != 'moqty':
        return 'fail'
    skus = db.session.query(Skuinfo).filter(
        Skuinfo.mgroup.in_(tankMgroup)).all()
    skuArr = []
    moArr = []
    for s in skus:
        skuArr.append(s.sku)
    try:
        for r in range(2, ws.max_row + 1):
            mo = ws.cell(r, 2).value
            linename = ws.cell(r, 1).value
            sku = ws.cell(r, 3).value
            modate = ws.cell(r, 4).value
            duedate = ws.cell(r, 5).value
            moqty = ws.cell(r, 6).value
            if mo and linename and sku and modate and duedate and moqty:
                if sku in skuArr and linename in tankLinegroup:
                    currItem = db.session.query(Tankplan).filter(
                        Tankplan.mo == mo).first()
                    if currItem:
                        currItem.linename = linename
                        currItem.sku = sku
                        currItem.modate = modate
                        currItem.duedate = duedate
                        currItem.moqty = moqty
                    else:
                        newItem = Tankplan(sku=sku, linename=linename, mo=mo, status='open',
                                           modate=modate, duedate=duedate, moqty=moqty, packed=0)
                        db.session.add(newItem)
                else:
                    moArr.append(str(mo))
            else:
                moArr.append(str(mo))
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return 'fail'
    wb.close()
    return moArr


@ api.route('/uploadTKPLAN2', methods=['POST'])
def uploadTKPLAN2():
    file_obj = request.files.get('file')
    mystr = ('tankplan2UPLOAD' + str(datetime.datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    appendix = file_obj.filename[file_obj.filename.rfind('.'):]
    file_obj.save(getServer()['uploadPath']+name+appendix)
    tag = filetoordertankplan2(getServer()['uploadPath']+name+appendix)
    if tag == 'fail':
        return responseError('上传失败，请检查模板格式或请联系管理员')
    return responsePost(tag, {
        'upload_url': getServer()['uploadUrl']+name+appendix})


def filetoordertankplan2(file):
    wb = load_workbook(file, data_only=True)
    ws = wb.active
    if ws.cell(1, 1).value != '区域' or ws.cell(3, 1).value != '滚塑Roto molding':
        return 'fail'
    try:
        for r in range(3, ws.max_row + 1):
            for c in range(2, ws.max_column + 1):
                machine = ws.cell(r, 1).value
                plandate = ws.cell(2, c).value
                # get value of plandate, not function
                plan = ws.cell(r, c).value
                existplan = db.session.query(Tankplan2).filter(
                    Tankplan2.machine == machine).filter(Tankplan2.pdate == plandate).first()
                if existplan:
                    existplan.plan = plan
                else:
                    if plan:
                        newplan = Tankplan2(
                            machine=machine, pdate=plandate, plan=plan)
                        db.session.add(newplan)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return 'fail'
    wb.close()
    return 'success'


@api.route('/getTankplan', methods=['GET'])  # 重置密码
# @login_required
def getTankplan():
    res = request.args
    plant = res.get('plant')
    linenames = res.get('linenames')
    isall = res.get('isall')
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    keywords = res.get('keywords')
    lines = db.session.query(Lineinfo).filter(
        Lineinfo.area == 'B1Tank').filter(Lineinfo.plant == plant).order_by(Lineinfo.linename).all()
    lineArr = []
    skuArr = []
    for ll in lines:
        lineArr.append(ll.linename)
    skus = db.session.query(Skuinfo).filter(
        Skuinfo.mgroup.in_(tankMgroup)).all()
    for s in skus:
        skuArr.append(s.sku)
    plans = db.session.query(Tankplan.mo, Tankplan.Id, Tankplan.sku, Skuinfo.color, Skuinfo.typegroup, Tankplan.packed,
                             Tankplan.linename, Tankplan.status, Tankplan.modate, Tankplan.duedate, Tankplan.finishdate, Tankplan.moqty,
                             func.sum(Tankplanact.qty).label('actqty')).join(Skuinfo, Tankplan.sku == Skuinfo.sku).outerjoin(
        Tankplanact, Tankplan.mo == Tankplanact.mo).group_by(Tankplan.mo)
    total = db.session.query(func.count(Tankplan.mo))
    if isall == 'no':
        plans = plans.filter(Tankplan.status == 'open').order_by(Tankplan.Id)
        total = total.filter(Tankplan.status == 'open')
    else:
        plans = plans.order_by(desc(Tankplan.modate))
    if linenames:
        plans = plans.filter(Tankplan.linename.in_(
            linenames.split(','))).order_by(desc(Tankplan.Id))
        total = total.filter(Tankplan.linename.in_(linenames.split(',')))
    # if colnum:
    #     if colorder == 'descending':
    #         plans = plans.order_by(desc(getattr(Tankplan, colnum)))
    #     else:
    #         plans = plans.order_by(getattr(Tankplan, colnum))
    if keywords:
        plans = plans.filter(or_(Tankplan.mo.like('%{0}%'.format(
            keywords)), Tankplan.sku.like('%{0}%'.format(keywords))))
        total = total.filter(or_(Tankplan.mo.like('%{0}%'.format(
            keywords)), Tankplan.sku.like('%{0}%'.format(keywords))))
    planArr = []
    plans = plans.paginate(pagenum, pagesize, error_out=False).items
    total = total.scalar()
    for p in plans:
        if p.moqty == p.actqty and p.status != 'closed':
            db.session.query(Tankplan).filter(
                Tankplan.mo == p.mo).update({'status': 'closed'})
            db.session.commit()
        else:
            planArr.append({
                'Id': p.Id,
                'color': p.color,
                'typegroup': p.typegroup,
                'mo': p.mo,
                'sku': p.sku,
                'status': p.status,
                'linename': p.linename,
                'modate': datetime.datetime.strftime(p.modate, '%Y-%m-%d'),
                'duedate': datetime.datetime.strftime(p.duedate, '%Y-%m-%d'),
                'finishdate': datetime.datetime.strftime(p.finishdate, '%Y-%m-%d') if p.finishdate else '',
                'moqty': p.moqty,
                'actqty': p.actqty if p.actqty else 0,
                'packed': p.packed
            })
    return responseGet('获取成功', {'planList': planArr, 'total': total, 'lineList': lineArr, 'skuList': skuArr})


@api.route('/getTankplan2', methods=['GET'])  # 重置密码
# @login_required
def getTankplan2():
    res = request.args
    sdate = res.get('sdate').split('T')[0]
    # 返回一个送sdate开始的数组，一共14天
    dateArr = []
    for i in range(14):
        dateArr.append((datetime.datetime.strptime(
            sdate, '%Y-%m-%d')+datetime.timedelta(days=i)).strftime('%Y-%m-%d'))
    plans = db.session.query(Tankplan2).filter(
        Tankplan2.pdate >= sdate).order_by(Tankplan2.pdate).all()
    outArr = []
    outDic = {}
    for p in plans:
        pdate = datetime.datetime.strftime(p.pdate, '%Y-%m-%d')
        if pdate in dateArr:
            if p.machine not in outDic.keys():
                outDic[p.machine] = {
                    'machine': p.machine,
                    'plans': {
                        pdate: {
                            'machine': p.machine,
                            'pdate': pdate,
                            'plan': p.plan
                        }
                    }
                }

            else:
                outDic[p.machine]['plans'][pdate] = {
                    'machine': p.machine,
                    'pdate': pdate,
                    'plan': p.plan
                }
    outArr = list(outDic.values())
    return responseGet('获取成功', {'arr': outArr, 'headers': dateArr})

from flask import Blueprint, request, jsonify
from app.public.functions import responseError, responseGet, responsePost
from extensions import db
from app.carbonextr.model.models_b2extr import feed
from marshmallow import Schema, fields
from marshmallow.validate import Range, Length

api = Blueprint('carbonextr/feedingAPI', __name__)


class validate_feedingdata(Schema):
    machine = fields.Int(
        Required=True, validate=Range(1, 9, error="机台号需要在1-9之间"))
    mixsn = fields.String(Required=True, validate=Length(
        8, 13, error="搅拌序列号长度不正确"), error_messages={"required": "搅拌序列号不能为空"})
    feedingdate = fields.DateTime(Required=True)


@api.route('/test', methods=['GET'])
def test():
    print(request.args)
    return responseGet("接口测试成功")


@api.route('/insertrecord', methods=['POST'])
def insertrecord():
    validate_data = validate_feedingdata()
    if validate_data.validate(request.json):
        return responseError("参数不正确")
    machine = int(request.json['machine'])
    mixsn = request.json['mixsn']
    feedingdate = request.json['feedingdate']
    feeding_item = feed(machine=machine, mix_sn=mixsn,
                        feeding_date=feedingdate)
    db.session.add(feeding_item)
    db.session.commit()
    return responsePost("提交成功")


@api.route('getlast10', methods=['GET'])
def getlast10():
    datares = db.session.query(feed).order_by(
        feed.feeding_date.desc()).limit(10).all()
    res = []
    for item in datares:
        res.append(item.to_json())
    return jsonify(res), 200


@api.route('deletebyid', methods=['POST'])
def deletebyid():
    id = request.json['feedid']
    print(id)
    item = db.session.query(feed).filter(feed.id == id).one()
    db.session.delete(item)
    db.session.commit()
    db.session.close()
    return responsePost("删除成功")

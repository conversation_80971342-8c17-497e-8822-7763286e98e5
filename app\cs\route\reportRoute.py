from flask import Blueprint, request
from chinese_calendar import get_workdays
import datetime
from sqlalchemy import func
from app.cs.model.models_cs import User, Orderlist, Planner
from extensions import db
from app.receiving.functions import responseGet
api = Blueprint('cs/reportAPI', __name__)


@ api.route('/getReport', methods=['GET'])
def getReport():
    dateRange = request.args.get('dateRange')
    startdate = dateRange.split(',')[0]
    enddate = dateRange.split(',')[1]
    pls = db.session.query(User.email).filter(User.roleid == 3).all()
    options = []
    dataX = []
    for pl in pls:
        options.append(pl.email)
        dataX.append(pl.email.split('.')[0])
    allArr = []
    ontimeArr = []
    delayArr = []
    rateArr = []
    for i in options:
        query = db.session.query(Orderlist.confirmdate, Orderlist.recorddate).outerjoin(Planner, Orderlist.mprc == Planner.mprc).filter(
            Planner.planner == i).filter(Orderlist.recorddate.between(startdate, enddate)).all()
        z = 0
        b = 0
        ll = 0
        if query:
            for q in query:
                z += 1
                recorddate = q.recorddate
                if q.confirmdate:
                    confirmdate = q.confirmdate
                else:
                    confirmdate = datetime.date.today()
                if len(get_workdays(recorddate, confirmdate)) > 2:
                    ll += 1
                else:
                    b += 1

        # query = db.session.query(func.count(Orderlist.Id),
        #                          func.sum(func.if_(func.datediff(func.if_(Orderlist.confirmdate.is_(None), datetime.date.today().strftime("%Y-%m-%d"), Orderlist.confirmdate),
        #                                                          Orderlist.recorddate) < 2, 1, 0)),
        #                          func.sum(func.if_(func.datediff(func.if_(Orderlist.confirmdate.is_(None), datetime.date.today().strftime("%Y-%m-%d"), Orderlist.confirmdate),
        #                                                          Orderlist.recorddate) >= 2, 1, 0))
        #                          ).outerjoin(Planner, Orderlist.mprc == Planner.mprc).filter(Planner.planner == i).filter(Orderlist.recorddate.between(startdate, enddate)).all()
        # print('dddddddddddddd', z, b, ll)
        # z = 0 if query[0][0] is None else int(query[0][0])
        # b = 0 if query[0][1] is None else int(query[0][1])
        # ll = 0 if query[0][2] is None else int(query[0][2])
        if z > 0:
            r = round(b/z*100, 2)
        else:
            r = ''
        allArr.append(z)
        ontimeArr.append(b)
        delayArr.append(ll)
        rateArr.append(r)
    query2 = db.session.query(func.sum(func.if_(func.datediff(func.if_(Orderlist.confirmdate.is_(None), datetime.date.today().strftime("%Y-%m-%d"), Orderlist.confirmdate),
                                                              Orderlist.recorddate) < 2, 1, 0)),
                              func.sum(func.if_(func.datediff(func.if_(Orderlist.confirmdate.is_(None), datetime.date.today().strftime("%Y-%m-%d"), Orderlist.confirmdate),
                                                              Orderlist.recorddate) >= 2, 1, 0))).filter(Orderlist.recorddate.between(startdate, enddate)).all()
    pie = []
    d = {}
    d['name'] = '未超时'
    d['value'] = 0 if query2[0][0] is None else int(query2[0][0])
    pie.append(d)
    d2 = {}
    d2['name'] = '已超时'
    d2['value'] = 0 if query2[0][1] is None else int(query2[0][1])
    pie.append(d2)

    date = {'dataX': dataX, 'allArr': allArr, 'ontimeArr': ontimeArr, 'delayArr': delayArr,
            'rateArr': rateArr, 'pie': pie}
    return responseGet("获取数据成功", date)

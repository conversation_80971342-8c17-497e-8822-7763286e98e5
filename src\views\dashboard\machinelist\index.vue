<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useColumns } from "./utils/columns";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Refresh from "@iconify-icons/ep/refresh";
import { useRoute } from "vue-router";
const tableRef = ref();

const formRef = ref();
defineOptions({
  name: "MachineList"
});

const {
  refreshData,
  search_condition,
  reset_condition,
  is_current,
  loading,
  columns,
  loadingConfig,
  dataList,
  adaptiveConfig
} = useColumns();

const shortcuts = [
  {
    text: "今天",
    value: new Date()
  },
  {
    text: "昨天",
    value: () => {
      const date = new Date();
      date.setTime(date.getTime() - 3600 * 1000 * 24);
      return date;
    }
  },
  {
    text: "一周前",
    value: () => {
      const date = new Date();
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
      return date;
    }
  }
];
const route = useRoute();
onMounted(() => {
  if (route.fullPath == "/machinelist") {
    document.documentElement.classList.add("dark");
  }
});

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};
</script>

<template>
  <div>
    <el-form
      ref="formRef"
      :inline="true"
      :model="search_condition"
      class="search-form bg-bg_color"
    >
      <el-form-item label="日期：" prop="selecteddate">
        <el-date-picker
          v-model="search_condition.selecteddate"
          type="date"
          placeholder="选择生产日期"
          :disabled-date="disabledDate"
          :shortcuts="shortcuts"
          :clearable="false"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="班次：" prop="status">
        <el-select
          v-model="search_condition.shift"
          placeholder="请选择班次"
          :clearable="false"
          class="!w-[180px]"
        >
          <el-option label="早班(8点~16点)" value="A" />
          <el-option label="中班(16点~24点)" value="B" />
          <el-option label="夜班(次日0点~8点)" value="C" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="search_condition.status"
          placeholder="请选择状态"
          :clearable="false"
          class="!w-[180px]"
        >
          <el-option label="ALL" :value="2" />
          <el-option label="运行" :value="1" />
          <el-option label="停止" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Refresh)"
          @click="reset_condition"
          v-show="!is_current"
        >
          返回当前班次
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="refreshData"> 刷新 </el-button>
      </el-form-item>
    </el-form>

    <pure-table
      ref="tableRef"
      border
      stripe
      adaptive
      :adaptiveConfig="adaptiveConfig"
      row-key="id"
      alignWhole="center"
      showOverflowTooltip
      :loading="loading"
      :loading-config="loadingConfig"
      :data="dataList"
      :columns="columns"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-table__row) {
  background-color: transparent;
}

:deep(.el-card__body) {
  padding: 0;
}
:deep(td) {
  padding: 4px 0;
}

.search-form {
  padding: 6px 0 6px 10px;
}
.el-form {
  display: flex;
  .el-form-item {
    margin-bottom: 0;
  }
}

:deep(el-dialog) {
  height: "50%";
}
</style>

from flask import jsonify, request
from config import config, env
from itsdangerous import TimedJSONWebSignatureSerializer as Serializer
from functools import wraps
import traceback


def getServer():  # 获取上传文件的访问路径
    urls = {
        'localPath': config[env].localPath+"cs/uploads/"
    }
    return urls


def responseGet(msg, data):  # get方法返回成功格式化
    meta = {'status': 200, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responsePost(msg, data):  # post方法返回成功格式化
    meta = {'status': 201, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responsePut(msg):  # put方法返回成功格式化
    meta = {'status': 202, 'msg': msg}
    return jsonify(meta=meta)


def responseDelete(msg, *args):  # delete方法返回成功格式化
    meta = {'status': 203, 'msg': msg}
    dt = {}
    if len(args) == 1:
        dt = args[0]
    return jsonify(meta=meta, data=dt)


def responseError(msg):  # request失败返回格式化
    meta = {'status': 204, 'msg': msg}
    return jsonify(meta=meta)


def responseExpire(msg):  # request超时返回格式化
    meta = {'status': 205, 'msg': msg}
    return jsonify(meta=meta)


def create_token(api_user):  # 创建token
    s = Serializer(config[env].SECRET_KEY, expires_in=14400)
    token = s.dumps({"id": api_user}).decode("ascii")
    return token


def login_required(view_func):  # 通过一个装饰函数来实现登录验证
    @wraps(view_func)
    def verify_token(*args, **kwargs):
        try:
            token = request.headers["token"]
        except Exception:
            traceback.print_exc()
            return responseError('登录失败！')

        s = Serializer(config[env].SECRET_KEY)
        try:
            s.loads(token)
        except Exception:
            return responseExpire('登录已过期！请重新登录！')

        return view_func(*args, **kwargs)

    return verify_token

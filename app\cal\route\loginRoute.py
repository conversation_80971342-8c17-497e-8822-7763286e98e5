from flask import Blueprint, request
from app.voc.model.models_voc import Du<PERSON>
from extensions import db
import requests
from config import config, env
import traceback
from app.public.functions import create_token, responseError, responsePost, responseGet, responseDelete, responsePut
from app.voc.functions import login_required, getMytasks
from app.cal.model.models_cal import Dcal, Dcal_instep, Dcal_standcaleq, Dcal_user
from app.voc.schemas import roles_schema
from ultils.log_helper import ProjectLogger
mylogger = ProjectLogger()
api = Blueprint('cal/loginAPI', __name__)


@api.route('/', methods=['GET'])
# @login_required
def DD():
    return responseDelete("测试用户成功", {'data': 33})


# @api.route('/userid/<int:uid>', methods=['DELETE'])
# @login_required
# def deleteUser(uid):
#     user = db.session.query(User).filter(User.Id == uid).scalar()
#     db.session.delete(user)
#     db.session.commit()
#     return responseDelete("删除用户成功")


# @api.route('/resetPassword', methods=['PUT'])
# @login_required
# def resetPassword():
#     res = request.json
#     uid = res.get('id')
#     user = db.session.query(User).filter(User.Id == uid).scalar()
#     email = user.email
#     r = requests.post(config[env].api_url+"public/resetPassword",
#                       {'username': email})
#     vdata = r.json()
#     if vdata['meta']['status'] != 202:
#         return responseError(vdata['meta']['msg'])
#     return responsePut(vdata['meta']['msg'])


@api.route('/changePassword', methods=['PUT'])
@login_required
def changePassword():
    res = request.json
    email = res.get('email')
    oldpass = res.get('oldpass')
    newpass = res.get('newpass1')
    r = requests.post(config[env].api_url+"public/changePassword",
                      {'username': email, 'oldpass': oldpass, 'newpass': newpass})
    vdata = r.json()
    print('aaa', vdata)
    if vdata['meta']['status'] != 202:
        return responseError(vdata['meta']['msg'])
    return responsePut(vdata['meta']['msg'])


# @api.route('/getUsers', methods=['GET'])
# @login_required
# def getUsers():
#     res = request.args
#     query = res.get('query')
#     pagenum = int(res.get('pagenum'))
#     pagesize = int(res.get('pagesize'))
#     users = db.session.query(User.Id, User.email, User.auth, User.isactive, Role.name.label(
#         'rolename')).outerjoin(Role, User.roleid == Role.Id).filter(User.email.like('%{0}%'.format(query))).order_by(
#         User.roleid).paginate(pagenum, pagesize, error_out=False).items
#     total = db.session.query(func.count(User.Id)).filter(
#         User.email.like('%{0}%'.format(query))).scalar()
#     userlist = users_schema.dump(users)
#     for u in userlist:
#         if u['isactive']:
#             u['isactive'] = True
#         else:
#             u['isactive'] = False
#         auth = u['auth']
#         if auth:
#             myAuth = gemAuth(auth)
#             u['children'] = myAuth
#     data = {'total': total, 'userlist': userlist}
#     return responseGet("获取用户列表成功", data)


# @api.route('/getRoles', methods=['GET'])
# @login_required
# def getRoles():
#     roles = db.session.query(Role).all()
#     rolelist = roles_schema.dump(roles)
#     return responseGet("获取用户列表成功", rolelist)


# @api.route('/addUser', methods=['POST'])
# def addUser():
#     res = request.json
#     email = res.get("email").lower()
#     eid = res.get("eid")
#     roleid = res.get("roleid")
#     auth = db.session.query(Role).filter(Role.Id == roleid).scalar()
#     try:
#         user1 = User(email=email,
#                      password='123',
#                      roleid=roleid,
#                      auth=auth.default_auth,
#                      isactive=1)
#         print(user1)
#         db.session.add(user1)
#         r = requests.post(config[env].api_url+"/public/addUser",
#                           {'email': email, 'eid': eid})
#         vdata = r.json()
#         print(vdata)
#         if vdata['meta']['status'] != 201:
#             return responseError(vdata['meta']['msg'])
#         else:
#             db.session.commit()
#             return responsePost('成功建立新用户', {'data': 'success'})
#     except Exception:
#         return responseError('建立新用户失败！')


# @api.route('/editRoles', methods=['POST'])
# def editRoles():
#     res = request.json
#     id = res.get("id")
#     auth = res.get("auth")
#     try:
#         user = db.session.query(User).filter(User.Id == id).scalar()
#         user.auth = auth
#         db.session.commit()
#     except Exception:
#         return responseError('修改用户权限失败！')
#     return responsePost('成功修改权限', {'data': 'success'})


@api.route("/login", methods=["POST"])
def login():
    mylogger.info("*"*10 + "login start! ts" + "*"*10)
    # testuser = db.session.query(Duser).filter(Duser.eid == '1238034').first()
    # mylogger.debug(testuser.level)
    res_dir = request.json
    if res_dir is None:
        res_dir = request.form
    if not len(res_dir):
        return responseError('请输入用户名密码')
    # 获取前端传过来的参数
    email = res_dir.get("email")
    password = res_dir.get("password")
    r = requests.post(config[env].api_url+"/public/login",
                      {'username': email, 'password': password})
    mylogger.debug(990)
    vdata = r.json()
    mylogger.debug(vdata)
    if vdata['meta']['status'] != 201:
        return responseError(vdata['meta']['msg'])
    else:
        email = vdata['data']['email']
    # try:
    #     user = db.session.query(Role.name, Role.plant, User.auth, User.Id, User.email).outerjoin(Role, User.roleid == Role.Id).filter(
    #         User.isactive == 1).filter(User.email == email).first()
    # except Exception:
    #     traceback.print_exc()
    #     return responseError('查询错误，请联系管理员！')
    # if user is None:
    #     return responseError('本系统中此账户不存在或被禁用，请联系管理员处理！')
    # myAuth = db.session.query(Permission).filter(Permission.Id.in_(user.auth.split(','))).all()
    # authArr = []
    # for m in myAuth:
    #     authArr.append(m.name)

    testuser = db.session.query(Dcal_user).filter(Dcal_user.email == email).first()
    # mylogger.debug(testuser.level)
    tasks = getMytasks(testuser.email)
    # data = {'token': create_token(testuser.id), 'level': testuser.level,
    #         'email': email, 'plant': 'SuZhou', 'dept': testuser.eid, 'tasks': tasks}
    powerlist = testuser.powerlist.split(',')
    powereditlist = testuser.poweredit.split(',')
    def redsin(x):
        return x.strip(' ').strip('\'')
    powerlist = list(map(redsin, powerlist))
    powereditlist = list(map(redsin, powereditlist))
    data = {'token': create_token(testuser.id), 'power': powerlist,'poweredit':powereditlist,
            'email': email, 'plant': 'SuZhou', 'dept': testuser.eid, 'tasks': tasks}
    # testuser = db.session.query(Duser).filter(Duser.email == "<EMAIL>").first()
    mylogger.debug(data)
    return responsePost('登录成功', data)


@api.route("/getTasknumber", methods=["GET"])
def getTasknumber():
    res = request.args
    email = res.get('email')
    tasks = getMytasks(email)
    return responseGet('获取成功', {'tasks': tasks})

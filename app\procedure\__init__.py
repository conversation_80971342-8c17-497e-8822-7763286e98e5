def importPROCEDURERoute(app):
    from app.procedure.route.loginRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/procedure/loginAPI")

    from app.procedure.route.manageRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/procedure/manageAPI")

    from app.procedure.route.planRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/procedure/planAPI")

    from app.procedure.route.ehsRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/procedure/ehsAPI")

    from app.procedure.route.qualityRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/procedure/qualityAPI")

    from app.procedure.route.publicRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/procedure/publicAPI")

    from app.procedure.route.costRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/procedure/costAPI")

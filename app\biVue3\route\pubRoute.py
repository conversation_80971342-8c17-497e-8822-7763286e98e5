from flask import Blueprint,  request, send_from_directory
import datetime
from extensions import db
from sqlalchemy import func, or_
from app.receiving.functions import getParams
from app.public.functions import responseGet, responsePost, responseError
from app.dm.functions import download_img, getServer
from openpyxl import load_workbook
from openpyxl.drawing.image import Image
from app.dm.model.models_dm import Lineinfo, Andon
from app.receiving.model.models_receiving import Ncmr, Inspect
from config import config, env
import requests
import os
import json
import qrcode
from urllib.parse import quote
api = Blueprint('bi3/pubAPI', __name__)


@api.route("/checkFingerPass", methods=["POST"])   # 开启安灯并通知相应的人
def checkFingerPass():
    res = request.json
    password = res.get('pass')
    passtype = res.get('passtype')
    print(passtype, password)
    syspass = {
        't2d': '87312',
        't3q': '031746'
    }
    if syspass[passtype] == password:
        return responsePost('密码正确')
    else:
        return responseError('密码错误，请重新输入')


@api.route('/getAndons', methods=['GET'])  # 主页上获取全部的未关闭安灯信息
def getAndons():
    andons = db.session.query(Andon).filter(Andon.endtime.is_(None)).all()
    outArr = []
    for a in andons:
        outArr.append({
            'linename': a.linename,
            'starttime': datetime.datetime.strftime(a.starttime, '%Y-%m-%d %H:%M:%S'),
            'isactive': a.isactive
        })
    return responseGet('success', {'andons': outArr})


@api.route('/getArealist', methods=['GET'])  # 获得区域的树状结构和非装配线区域的列表，目前只放了B3
def getArealist():
    res = request.args
    plant = res.get('plant')
    lines = db.session.query(Lineinfo.area).filter(
        Lineinfo.isactive == 1).filter(Lineinfo.plant == plant).order_by(Lineinfo.area).group_by(Lineinfo.area).all()
    outArr = []
    for ll in lines:
        outArr.append({
            'value': ll.area,
            'label': ll.area
        })
    return responseGet('获取成功', {'areaList': outArr})


@api.route('/getLinesbyArea', methods=['GET'])  # 通过区域获取产线列表
def getLinesbyArea():
    res = request.args
    plant = res.get('plant')
    area = res.get('area')
    lines = db.session.query(Lineinfo.linegroup).filter(Lineinfo.isactive == 1).filter(Lineinfo.plant == plant).filter(
        Lineinfo.area == area).order_by(Lineinfo.linegroup).group_by(Lineinfo.linegroup).all()
    outArr = []
    for ll in lines:
        outArr.append({
            'value': ll.linegroup,
            'label': ll.linegroup
        })
    return responseGet('获取成功', {'lineList': outArr})


@api.route('/getLinelist', methods=['GET'])  # 获取产线的树状结构
def getLinelist():
    res = request.args
    plant = res.get('plant')
    lines = db.session.query(Lineinfo.linename, Lineinfo.area, Lineinfo.linegroup).filter(
        or_(Lineinfo.isactive == 1, Lineinfo.mditype == 5)).filter(Lineinfo.plant == plant).order_by(Lineinfo.area, Lineinfo.linegroup, Lineinfo.linename).all()
    outDic = {}
    for ll in lines:
        if ll.area in outDic.keys():
            if ll.linegroup in outDic[ll.area]['children'].keys():
                outDic[ll.area]['children'][ll.linegroup]['children'].append({
                    'value': ll.linename, 'label': ll.linename
                })
            else:
                outDic[ll.area]['children'][ll.linegroup] = {
                    'value': ll.linegroup, 'label': ll.linegroup, 'children': [{'value': ll.linename, 'label': ll.linename}]}
        else:
            outDic[ll.area] = {
                'value': ll.area,
                'label': ll.area,
                'children': {
                    ll.linegroup: {'value': ll.linegroup, 'label': ll.linegroup,
                                   'children': [{'value': ll.linename, 'label': ll.linename}]}
                }
            }
    outArr = []
    for k, v in outDic.items():
        outArr.append({
            'value': k,
            'label': k,
            'children': list(v['children'].values())
        })
    return responseGet('获取成功', {'lineList': outArr})


@ api.route('/getFTT', methods=['GET'])  # 从Receiving系统里获取供应商FTT数据，暂时显示，会替代掉
def getFTT():
    res = request.args
    isThermal = res.get('isThermal')
    isVendor = res.get('isVendor')
    isHistory = res.get('isHistory')
    thermalArr = ['B103-FH', 'B103-QC', 'B103-T2B', 'B427-FH', 'B428-CON',
                  ' B429-RTU', 'B430-RAYC', 'B431-T2B', ' B432-Q70', 'B432-Q80']
    dateRange = res.get('dateRange')
    dataX = []
    startyear = int(dateRange.split(',')[0].split('-')[0])
    startmonth = int(dateRange.split(',')[0].split('-')[1])
    startday = dateRange.split(',')[0].split('-')[2]
    endyear = int(dateRange.split(',')[1].split('-')[0])
    endmonth = int(dateRange.split(',')[1].split('-')[1])
    endday = dateRange.split(',')[1].split('-')[2]
    ftargetstart = getParams(startyear)['fttTarget']
    ftargetend = getParams(endyear)['fttTarget']
    useArr = []
    sortArr = []
    pendArr = []
    reworkArr = []
    returnArr = []
    fttArr = []
    acceptArr = []
    fttTargetArr = []
    for i in range(startmonth, endmonth+(endyear-startyear)*12+1):
        if i == startmonth:
            sd = '-'+startday
        else:
            sd = '-01'
        if i == endmonth+(endyear-startyear)*12:
            ed = '-'+endday
            em = str(endmonth)
            xmonth = 0
        else:
            ed = '-01'
            xmonth = 1
            em = str(i-12*int(i/12)+xmonth) if i-12*int(i/12) + \
                xmonth > 9 else '0'+str(i-12*int(i/12)+1)
        startdate = str(startyear+int(i/13))+'-'+(str(i-12*int(i/13))
                                                  if i-12*int(i/13) > 9 else '0'+str(i-12*int(i/13)))+sd
        if i % 12 == 0:
            enddate = str(startyear+int(i/13)+xmonth)+'-'+em+ed
        else:
            enddate = str(startyear+int(i/13))+'-'+em+ed
        dataX.append(startdate[: 7])
        fttdata = getFttdata(startdate, enddate, isThermal, isVendor, thermalArr)
        pendArr.append(fttdata[0])
        useArr.append(fttdata[1])
        reworkArr.append(fttdata[2])
        sortArr.append(fttdata[3])
        returnArr.append(fttdata[4])
        acceptArr.append(fttdata[5])
        fttArr.append(fttdata[6])
        if int(startdate[0:4]) == startyear:
            fttTargetArr.append(ftargetstart)
        else:
            fttTargetArr.append(ftargetend)
    wend = datetime.datetime.strptime(dateRange.split(',')[1], "%Y-%m-%d").date()
    wstart = datetime.datetime(wend.year, wend.month, 1).date()
    currentDay = wstart
    currentEnd = currentDay
    i = 1
    while currentDay <= wend:
        wk = currentEnd.isoweekday()
        if wk == 6 and (currentEnd-currentDay).days > 1:
            fttdata = getFttdata(currentDay, currentEnd,
                                 isThermal, isVendor, thermalArr)
            pendArr.append(fttdata[0])
            useArr.append(fttdata[1])
            reworkArr.append(fttdata[2])
            sortArr.append(fttdata[3])
            returnArr.append(fttdata[4])
            acceptArr.append(fttdata[5])
            fttArr.append(fttdata[6])
            if currentDay.year == startyear:
                fttTargetArr.append(ftargetstart)
            else:
                fttTargetArr.append(ftargetend)
            dataX.append(str(endmonth)+'-WK'+str(i))
            currentDay = currentEnd
            i += 1
        currentEnd = currentEnd+datetime.timedelta(days=1)

    history = {
        'dataX': ['Y2019', 'Y2020', 'Y2021', '2021-01', '2021-02', '2021-03', '2021-04'],
        'sorting': [93, 44, 15, 3, 6, 6, 0],
        'rt': [121, 134, 56, 13, 7, 13, 23],
        'pend': [0, 0, 0, 0, 0, 0, 0],
        'use': [137, 129, 68, 13, 16, 13, 26],
        'rework': [0, 0, 0, 0, 0, 0, 0],
        'accept': [20268, 20403, 8141, 2125, 1287, 2646, 2083],
        'ftt': [98.4, 98.5, 98.2, 98.7, 98.8, 98.8, 97.3],
        'target': [97, 5, 98, 98.5, 98.5, 98.5, 98.5, 98.5]
    }
    print(history['dataX'])
    if isHistory == 'true':
        for i in range(6, -1, -1):
            dataX.insert(0, history['dataX'][i])
            pendArr.insert(0, history['pend'][i])
            useArr.insert(0, history['use'][i])
            reworkArr.insert(0, history['rework'][i])
            sortArr.insert(0, history['sorting'][i])
            returnArr.insert(0, history['rt'][i])
            acceptArr.insert(0, history['accept'][i])
            fttArr.insert(0, history['ftt'][i])
            fttTargetArr.insert(0, history['target'][i])
    date = {'dataX': dataX, 'pendArr': pendArr, 'useArr': useArr, 'reworkArr': reworkArr, 'sortArr': sortArr,
            'returnArr': returnArr, 'acceptArr': acceptArr, 'fttArr': fttArr, 'fttTargetArr': fttTargetArr}
    return responseGet("获取FTT数据成功", date)


def getFttdata(startdate, enddate, isThermal, isVendor, thermalArr):
    query = db.session.query(func.sum(func.if_(Ncmr.dealType.is_(None), 1, 0)),
                             func.sum(func.if_(Ncmr.dealType == 'Use as is', 1, 0)),
                             func.sum(func.if_(Ncmr.dealType == 'Rework', 1, 0)),
                             func.sum(func.if_(Ncmr.dealType.like('Sorting%'), 1, 0)),
                             func.sum(func.if_(Ncmr.dealType == 'return', 1, 0)),
                             ).outerjoin(Inspect, Inspect.Id == Ncmr.inspectid).filter(Ncmr.swtype == 'IQC').filter(
        Inspect.receive_date.between(startdate, enddate))
    nmr = db.session.query(func.count(Inspect.Id)).filter(
        Inspect.receive_date.between(startdate, enddate))

    if isThermal == 'false':
        query = query.filter(Inspect.mgroup.notin_(thermalArr))
        nmr = nmr.filter(Inspect.mgroup.notin_(thermalArr))
    if isVendor == 'false':
        query = query.filter(Ncmr.nogoodType == '供应商问题')
    query = query.all()
    nmr = nmr.scalar()
    pend = 0 if query[0][0] is None else int(query[0][0])
    use = 0 if query[0][1] is None else int(query[0][1])
    rework = 0 if query[0][2] is None else int(query[0][2])
    sort = 0 if query[0][3] is None else int(query[0][3])
    returna = 0 if query[0][4] is None else int(query[0][4])
    accept = 0
    ftt = ''
    if nmr:
        accept = nmr-pend-use-rework-sort-returna
        ftt = round((nmr-pend-use-rework-sort-returna)/nmr*100, 1)
    return [pend, use, rework, sort, returna, accept, ftt]


@api.route('/get5Sstandard', methods=['GET'])  # 获取具体的提案信息
def get5Sstandard():
    r = request.args
    linename = r.get('linename')
    linegroup = r.get('linegroup')
    if not linegroup:
        if linename:
            linegroup = db.session.query(Lineinfo.linegroup).filter(Lineinfo.linename == linename).first()[0]
    if linegroup:
        res = requests.get(
            config[env].cloud_url+"welean/bi/fivesAPI/get5Sstandard", params={
                'linename': linegroup,
            })
        content = res.json()
        if content["meta"]["status"] == 200:
            dts = content['data']['standard']
            dts, picArr = downLoadPics(dts)
            return responseGet('成功', {'standard': dts, 'picArr': picArr})
    return responseError('失败')


def downLoadPics(dts):
    imgPath = config[env].localPath+"BI/img5s/"
    picArr = []
    for d in dts:
        p = d['picurl']
        picurl = p[p.rfind('/')+1:].split('.')
        if len(picurl) == 2:
            picName = picurl[0]
            picAdx = picurl[1]
        else:
            picName = ''
            picAdx = ''
        if p:
            download_img(p, imgPath+picName+'.'+picAdx)
            d['picurl'] = config[env].base_url+'BI/img5s/'+picName+'.'+picAdx
            picArr.append(d['picurl'])
    return dts, picArr


def downLoadPicscheck(dts):
    imgPath = config[env].localPath+"BI/img5s/"
    for d in dts:
        p = d['picurl']
        picurl = p[p.rfind('/')+1:].split('.')
        if len(picurl) == 2:
            picName = picurl[0]
            picAdx = picurl[1]
        else:
            picName = ''
            picAdx = ''
        if p:
            download_img(p, imgPath+picName+'.'+picAdx)
            d['picurl'] = config[env].base_url+'BI/img5s/'+picName+'.'+picAdx
            d['picArr'].append(d['picurl'])
        b = d['benchurl']
        bicurl = b[b.rfind('/')+1:].split('.')
        if len(bicurl) == 2:
            bicName = bicurl[0]
            bicAdx = bicurl[1]
        else:
            bicName = ''
            bicAdx = ''
        if b:
            download_img(b, imgPath+bicName+'.'+bicAdx)
            d['benchurl'] = config[env].base_url+'BI/img5s/'+bicName+'.'+bicAdx
            d['picArr'].append(d['benchurl'])
    return dts


@api.route('/get5Scheck', methods=['GET'])  # 获取具体的提案信息
def get5Scheck():
    res = request.args
    linename = res.get('linename')
    startdate = res.get('startdate')
    enddate = res.get('enddate')
    linegroup = res.get('linegroup')
    if not linegroup:
        if linename:
            linegroup = db.session.query(Lineinfo.linegroup).filter(Lineinfo.linename == linename).first()[0]
    if linegroup:
        res = requests.get(
            config[env].cloud_url+"welean/bi/fivesAPI/get5Scheck", params={
                'linename': linegroup,
                'startdate': startdate,
                'enddate': enddate
            })
        content = res.json()
        if content["meta"]["status"] == 200:
            dts = content['data']['checkDic']
            return responseGet('成功', {'checkDic': dts})
    return responseError('失败')


@api.route('/get5Saudit', methods=['GET'])  # 获取具体的提案信息
def get5Saudit():
    res = request.args
    linename = res.get('linename')
    year = res.get('year')
    linegroup = res.get('linegroup')
    if not linegroup:
        if linename:
            linegroup = db.session.query(Lineinfo.linegroup).filter(Lineinfo.linename == linename).first()[0]
    if linegroup:
        res = requests.get(
            config[env].cloud_url+"welean/bi/fivesAPI/get5Saudit", params={
                'linename': linegroup,
                'year': year
            })
        content = res.json()
        if content["meta"]["status"] == 200:
            dts = content['data']['checkArr']
            print(111, dts)
            return responseGet('成功', {'checkArr': dts})
    return responseError('失败')


@api.route('/get5Scheckbyid', methods=['GET'])  # 获取具体的提案信息
def get5Scheckbyid():
    res = request.args
    linename = res.get('linename')
    checkdate = res.get('checkdate')
    linegroup = res.get('linegroup')
    if not linegroup:
        linegroup = db.session.query(Lineinfo.linegroup).filter(Lineinfo.linename == linename).first()[0]
    res = requests.get(
        config[env].cloud_url+"welean/bi/fivesAPI/get5Scheckbyid", params={
            'linename': linegroup,
            'checkdate': checkdate
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        dts = content['data']['checkArr']
        dts = downLoadPicscheck(dts)
        print(dts)
        return responseGet('成功', {'dailychecks': dts})
    else:
        return responseError('失败')


@api.route('/get5Sauditbyid', methods=['GET'])  # 获取具体的提案信息
def get5Sauditbyid():
    res = request.args
    layeredid = res.get('layeredid')
    sw = res.get('sw')
    left = res.get('left')
    top = res.get('top')
    res = requests.get(
        config[env].cloud_url+"welean/bi/fivesAPI/get5Sauditbyid", params={
            'layeredid': layeredid,
            'sw': sw,
            'left': left,
            'top': top
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        dts = content['data']['checkArr']
        dts = downLoadPicscheck(dts)
        print(dts)
        return responseGet('成功', {'audits': dts})
    else:
        return responseError('失败')


@api.route('/getLinesbygroup', methods=['GET'])  # 获取具体的提案信息
def getLinesbygroup():
    res = request.args
    linegroup = res.get('linegroup')
    lines = db.session.query(Lineinfo.linegroup).filter(Lineinfo.area == linegroup).filter(
        or_(Lineinfo.isactive == 1, Lineinfo.mditype == 5)).group_by(Lineinfo.linegroup).all()
    linearr = []
    for ll in lines:
        linearr.append(ll[0])
    return responseGet('获取成功', {'linenames': linearr})


@api.route('/downloadSW', methods=['GET'])
def downloadSW():
    res = request.args
    path = config[env].localPath+'BI/img5s/'
    query = json.loads(res.get('query'))
    sws = query['standard']
    linegroup = query['linegroup']
    linename = query['linename']
    if not linegroup:
        linegroup = db.session.query(Lineinfo.linegroup).filter(Lineinfo.linename == linename).first()[0]
    qrurl = 'https://welean.pentair.cn/flaskserver/static/direct/audit?atype=5S&linename=' + linegroup
    qrcode = gemQr(qrurl, path+linegroup+'.png')
    filename = gemSW(sws, linegroup, qrcode)
    if os.path.isfile(os.path.join(path, filename)):
        # print(filename)
        response = send_from_directory(
            path, filename, as_attachment=True)
        # qfilename =quote(filename.encode('utf-8'))
        response.headers["Access-Control-Expose-Headers"] = "fname"
        response.headers['fname'] = quote(filename.encode('utf-8'))
        # response.headers['Content-Disposition'] = 'attachment;filename={0};filename*={0}'.format(quote(filename))
        # response.headers['Content-Disposition'] = f'attachment; filename="{qfilename}"; filename*=UTF-8\'\'{qfilename}'
        return response
    return responseError('没有找到下载文件')


def gemSW(sws, linegroup, qrcode):
    path = getServer()['templatePath']   # 输出当前目录
    wb = load_workbook(path+'5Sstandard.xlsx')
    sht = wb['stdwork']
    pos = ['B4',  'D4', 'F4', 'H4', 'J4', 'B10',  'D10', 'F10', 'H10', 'J10']
    qrimg = Image(qrcode)  # 调用图像函数
    qrSize = (115, 115)
    qrimg.width, qrimg.height = qrSize    # 这两个属性分别是对应添加图片的宽高
    sht.add_image(qrimg, 'K2')
    i = 0
    sht.cell(row=2, column=2, value=linegroup+' 5S标准作业')
    for sw in sws:
        if sw['picurl']:
            img_list = sw['picurl'].split(',')
            for r, file in enumerate(img_list):
                filename = file[file.rfind('/')+1:]
                file = config[env].localPath+'BI/img5s/'+filename
                print(file)
                if os.path.exists(file):
                    jpg = os.path.splitext(file)[1]  # 分割文件，并将后缀名提取出来
                    if jpg.lower() == '.jpg' or jpg.lower() == '.png' or jpg.lower() == '.jpeg':
                        img = Image(file)  # 调用图像函数
                        newSize = (230, 180)
                        img.width, img.height = newSize    # 这两个属性分别是对应添加图片的宽高
                        sht.add_image(img, pos[i])  # 向d列中的单元格内指定添加图片
        sht.cell(row=i+16, column=2, value=sw['station'])
        sht.cell(row=i+16, column=4, value=sw['duration'])
        sht.cell(row=i+16, column=5, value=sw['requirement'])
        sht.cell(row=i+16, column=9, value=sw['comment'])
        i += 1
    newname = linegroup + ' standardWork.xlsx'
    wb.save(config[env].localPath+'BI/img5s/' + newname)
    return newname


def gemQr(data, filename):
    # 创建QRCode对象
    qr = qrcode.QRCode(
        version=1,  # 控制二维码的大小，1是21x21
        error_correction=qrcode.constants.ERROR_CORRECT_L,  # 控制二维码的纠错能力
        box_size=10,  # 控制二维码每个方格的像素数
        border=1,  # 控制边框的大小
    )
    # 添加数据
    qr.add_data(data)
    qr.make(fit=True)
    # 生成图片
    img = qr.make_image(fill_color="black", back_color="white")
    # 保存图片
    img.save(filename)
    return filename

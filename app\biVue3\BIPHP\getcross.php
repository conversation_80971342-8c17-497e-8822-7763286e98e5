	<?php
include("coon.php");
$stime=$_GET['stime'];
$etime=$_GET['etime'];
$linename=$_GET['linename'];
$red = '#FF0000';
$blue = '#4E80BA';
$green = '#00B24D';
$yellow = '#FFFF00';
$orange = '#FFBD00';
if ($stime == "") {
    $stime = date('Y-m-01', time());
    $etime = date('Y-m-d',strtotime('+1 day'));
}

$stimestamp = strtotime($stime);
$etimestamp = strtotime($etime);

// 计算日期段内有多少天
$days = ($etimestamp-$stimestamp)/86400+1;

// 保存每天日期
$d = array();

for ($i=0; $i<$days; $i++) {
    $d[] = date('Y-m-d', $stimestamp+(86400*$i));
}

if ($linename) {
    $sql="select id,date(idate) idate,type2,stype from wl_suggest left join wl_userinfo on wl_suggest.eid=wl_userinfo.eid where wl_userinfo.plant='SZ' and idate between '{$stime}' and Date_add('{$etime}',interval 1 day) and stype in ('安全', '安全事件') and linename='{$linename}' and wl_suggest.eid!='9999999'";
} else {
    $sql="select id,date(idate) idate,type2,stype from wl_suggest  where  plant='SZ' and  idate between '{$stime}' and Date_add('{$etime}',interval 1 day) and stype in ('安全', '安全事件') and wl_suggest.eid!='9999999' order by idate desc";
}
$data=[];
$query=mysqli_query($link, $sql);
if (mysqli_num_rows($query) >= 1) {
    while ($rs = mysqli_fetch_array($query)) {
        $type=$rs['stype'];
        $type2=$rs['type2'];
        $idate=$rs['idate'];
        $month=substr($idate, -2);
        $l=$green;
        $r=$green;
        if ($type2 == '可记录事件') {
            $r = $red;
        } elseif ($type2== '财产损失' or $type2 == '急救事件') {
            $r = $orange;
        }


        if ($type2 == '未遂事件') {
            $l = $yellow;
        } elseif ($type == '安全' or $type == '日常检查') {
            $l = $blue;
        }
        if (in_array($idate, array_keys($data))) {
            $ol=$data[$idate][0];
            $or=$data[$idate][1];
            if (($ol==$green and $l!=$green) or ($ol==$blue and $l==$yellow)) {
                $data[$idate][0]=$l;
            }
            if (($or==$green and $r!=$green) or ($or==$orange and $r==$red)) {
                $data[$idate][1]=$r;
            }
        } else {
            $data[$idate]=array($l,$r,$month,$idate,$type);
        }
    }
}
$outArr=[];
  for ($i = 0; $i < count($d); $i++) {
      $month=substr($d[$i], -2);
      if (in_array($d[$i], array_keys($data))) {
          array_push($outArr, $data[$d[$i]]);
      } else {
          array_push($outArr, array($green,$green,$month,$d[$i]));
      }
  }

print_r(json_encode($outArr, JSON_UNESCAPED_UNICODE));

?>
	
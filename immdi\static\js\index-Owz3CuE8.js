import{useColumns as F}from"./column-BvBFjdJ4.js";import{a as L}from"./operation-DCG_Ggeh.js";import{d as U,n as p,G as D,q as G,r as o,o as c,c as h,b as e,h as t,u as a,F as I,t as O,g as x,T as R,f as u,y as C,C as S,_ as W}from"./index-BnxEuBzx.js";import"./moment-C3TZ8gAF.js";const j=U({name:"Mix",__name:"index",setup(H){const{search_condition:n,search:y,loading:g,columns:k,loadingConfig:V,dataList:f,adaptiveConfig:w}=F(),m=p([]),d=p(!1),_=p(!1);D(f,s=>{s.length>0?_.value=!0:s.length==0&&(_.value=!1)});const E=s=>{d.value=!0,s?L({prefix:s}).then(r=>{m.value=r.data,d.value=!1}):(m.value=[],d.value=!1)},M=()=>{f.value=[],n.material="",n.quantity=0};return G(()=>{g.value=!1}),(s,r)=>{const v=o("el-tag"),q=o("el-option"),B=o("el-select"),i=o("el-form-item"),N=o("el-input-number"),b=o("el-button"),T=o("el-form"),z=o("pure-table");return c(),h("div",null,[e(T,{inline:!0,class:"search-form bg-bg_color"},{default:t(()=>[e(i,{label:"成品料号"},{default:t(()=>[e(B,{filterable:"",clearable:"",remote:"","reserve-keyword":"",modelValue:a(n).material,"onUpdate:modelValue":r[0]||(r[0]=l=>a(n).material=l),"default-first-option":!0,"remote-method":E,loading:d.value,"loading-text":"加载料号中...","no-data-text":"未检索到匹配的料号，联系ME添加！",placeholder:"输入料号"},{default:t(()=>[(c(!0),h(I,null,O(m.value,(l,A)=>(c(),x(q,{key:l.material,value:l.material,class:R({"bg-bg_color":A%2===0})},{default:t(()=>[e(v,{style:{"margin-right":"10px"}},{default:t(()=>[u(C(l.material),1)]),_:2},1024),u(C(l.desc),1)]),_:2},1032,["value","class"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),e(i,{label:"成品数量"},{default:t(()=>[e(N,{modelValue:a(n).quantity,"onUpdate:modelValue":r[1]||(r[1]=l=>a(n).quantity=l),min:0,placeholder:"输入数量"},null,8,["modelValue"])]),_:1}),e(i,null,{default:t(()=>[e(b,{type:"primary",onClick:a(y)},{default:t(()=>[u("查询")]),_:1},8,["onClick"])]),_:1}),e(i,null,{default:t(()=>[e(b,{type:"info",onClick:M},{default:t(()=>[u("重置")]),_:1})]),_:1}),e(i,null,{default:t(()=>[_.value?(c(),x(v,{key:0,size:"large",class:"extra_info"},{default:t(()=>[u("实际拌料数量：50EA,比计划多出：20EA")]),_:1})):S("",!0)]),_:1})]),_:1}),e(z,{ref:"tableRef",border:"",adaptiveConfig:a(w),"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:a(g),"loading-config":a(V),data:a(f),columns:a(k),height:"250px"},null,8,["adaptiveConfig","loading","loading-config","data","columns"])])}}}),X=W(j,[["__scopeId","data-v-8dff0375"]]);export{X as default};

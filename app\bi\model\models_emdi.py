from extensions import db


class Issuelog(db.Model):
    __bind_key__ = "emdi"
    __tablename__ = "mdi_issuelog"
    id = db.Column(db.Integer, primary_key=True)
    sqdctype = db.Column(db.String(10))
    problemtype = db.Column(db.String(30))
    linename = db.Column(db.String(10))
    recorder = db.Column(db.String(10))
    desc = db.Column(db.String(200))
    recordtime = db.Column(db.DateTime())
    owner = db.Column(db.String(10))
    rootcause = db.Column(db.String(200))
    ctmeasure = db.Column(db.String(200))
    firstdate = db.Column(db.DateTime())
    duedate = db.Column(db.Date())
    findate = db.Column(db.Date())
    status = db.Column(db.String(10))
    close = db.Column(db.SmallInteger)
    csreason = db.Column(db.String(200))
    inform = db.Column(db.SmallInteger)
    pastdue = db.Column(db.SmallInteger)
    pic = db.Column(db.String(50))
    qty = db.Column(db.Integer)


class Ctqinfo(db.Model):
    __tablename__ = "mdi_ctqinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    skuctqid = db.Column(db.Integer)
    ctqname = db.Column(db.String(20))
    sku = db.Column(db.String(30))
    lsl = db.Column(db.Integer)
    usl = db.Column(db.Integer)
    va1 = db.Column(db.String(20))
    va2 = db.Column(db.String(20))
    va3 = db.Column(db.String(20))
    va4 = db.Column(db.String(20))
    va5 = db.Column(db.String(20))


class Ctqrecord(db.Model):
    __tablename__ = "mdi_ctqrecord"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    sn = db.Column(db.String(50))
    sku = db.Column(db.String(30))
    recordtime = db.Column(db.DateTime())
    ctqid = db.Column(db.Integer)
    lsl = db.Column(db.Integer)
    usl = db.Column(db.Integer)
    actual = db.Column(db.Integer)
    attr1 = db.Column(db.Integer)
    attr2 = db.Column(db.Integer)
    attr3 = db.Column(db.Integer)
    nogo = db.Column(db.Integer)
    go = db.Column(db.SmallInteger)


class Deptinfo(db.Model):
    __tablename__ = "mdi_deptinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    deptname = db.Column(db.String(50))
    auth = db.Column(db.String(15))
    mdidept = db.Column(db.SmallInteger)


class Flexinfo(db.Model):
    __tablename__ = "mdi_flexinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(50))
    linename = db.Column(db.String(15))
    headcount = db.Column(db.Integer)
    hourlyrate = db.Column(db.Float())
    # = db.Column(db.Structure)


class Hourinfo(db.Model):
    __tablename__ = "mdi_hourinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    hourid = db.Column(db.Integer)
    shiftid = db.Column(db.Integer)
    hourstart = db.Column(db.DateTime())
    hourfinish = db.Column(db.DateTime())
    lastrecord = db.Column(db.DateTime())
    restmin = db.Column(db.Integer)
    actualout = db.Column(db.Integer)
    requireout = db.Column(db.Float(8, 4))
    auditname = db.Column(db.String(20))
    hoursku = db.Column(db.String(50))


class Hourlyreport(db.Model):
    __tablename__ = "mdi_hourlyreport"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    hide = db.Column(db.SmallInteger)
    sqdctype = db.Column(db.String(10))
    problemtype = db.Column(db.String(50))
    problemdes = db.Column(db.String(200))
    hourid = db.Column(db.Integer)
    mdi = db.Column(db.String(1))
    abnormalqty = db.Column(db.Integer)
    issueid = db.Column(db.Integer)
    sku = db.Column(db.String(30))


class Implan(db.Model):
    __tablename__ = "mdi_implan"
    __bind_key__ = "emdi"
    Id = db.Column(db.Integer, primary_key=True)
    machine = db.Column(db.String(50))
    pn = db.Column(db.String(50))
    remark = db.Column(db.String(255))
    datenum = db.Column(db.String(10))
    d1 = db.Column(db.String(10))
    d2 = db.Column(db.String(10))
    d3 = db.Column(db.String(10))
    d4 = db.Column(db.String(10))
    d5 = db.Column(db.String(10))
    d6 = db.Column(db.String(10))
    d7 = db.Column(db.String(10))
    d8 = db.Column(db.String(10))
    d9 = db.Column(db.String(10))
    d10 = db.Column(db.String(10))
    d11 = db.Column(db.String(10))
    d12 = db.Column(db.String(10))
    d13 = db.Column(db.String(10))
    type = db.Column(db.String(1))


class Implanpacking(db.Model):
    __tablename__ = "mdi_implanpacking"
    __bind_key__ = "emdi"
    Id = db.Column(db.Integer, primary_key=True)
    mo = db.Column(db.String(10))
    sku = db.Column(db.String(50))
    qty = db.Column(db.String(10))
    due = db.Column(db.String(10))
    category = db.Column(db.String(50))
    lid = db.Column(db.String(50))
    sump = db.Column(db.String(50))
    hh = db.Column(db.String(10))
    pktime = db.Column(db.String(10))
    remark = db.Column(db.String(255))


class Iotinfo(db.Model):
    __tablename__ = "mdi_iotinfo"
    __bind_key__ = "emdi"
    Id = db.Column(db.Integer, primary_key=True)
    linename = db.Column(db.String(20))
    sn = db.Column(db.String(30))
    record = db.Column(db.String(50))
    iot = db.Column(db.SmallInteger)
    productsn = db.Column(db.String(30))
    redate = db.Column(db.DateTime())


class Kpi(db.Model):
    __tablename__ = "mdi_kpi"
    __bind_key__ = "emdi"
    Id = db.Column(db.Integer, primary_key=True)
    lineid = db.Column(db.String(10))
    year = db.Column(db.String(4))
    qtarget = db.Column(db.Float(4, 3))
    dtarget = db.Column(db.Float(4, 3))
    ctarget = db.Column(db.Float(4, 3))
    earnhour = db.Column(db.Integer)


class Lineinfo(db.Model):
    __tablename__ = "mdi_lineinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    active = db.Column(db.SmallInteger)
    linename = db.Column(db.String(50))
    VSM = db.Column(db.String(10))
    currentshift = db.Column(db.Integer)
    cat = db.Column(db.String(10))
    linegroup = db.Column(db.String(50))
    area = db.Column(db.String(10))
    space = db.Column(db.SmallInteger)


class Oplpic(db.Model):
    __tablename__ = "mdi_oplpic"
    __bind_key__ = "emdi"
    Id = db.Column(db.Integer, primary_key=True)
    oplurl = db.Column(db.String(50))
    whereuse = db.Column(db.String(50))


class Planinfo(db.Model):
    __tablename__ = "mdi_planinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    mo = db.Column(db.String(20))
    shifttype = db.Column(db.String(2))
    lineid = db.Column(db.Integer)
    sequence = db.Column(db.Integer)
    headcount = db.Column(db.Integer)
    skuid = db.Column(db.Integer)
    planner = db.Column(db.String(10))
    qty = db.Column(db.Integer)
    plandate = db.Column(db.Date())
    status = db.Column(db.SmallInteger)


class Planmo(db.Model):
    __tablename__ = "mdi_planmo"
    __bind_key__ = "emdi"
    Id = db.Column(db.Integer, primary_key=True)
    mo = db.Column(db.String(10))
    sku = db.Column(db.String(50))
    qty = db.Column(db.Integer)
    stock = db.Column(db.Integer)


class Probleminfo(db.Model):
    __tablename__ = "mdi_probleminfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    sqdctype = db.Column(db.String(10))
    problemtype = db.Column(db.String(50))
    linename = db.Column(db.String(10))
    trigger = db.Column(db.Integer)


class Restinfo(db.Model):
    __tablename__ = "mdi_restinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    shifttype = db.Column(db.String(5))
    hourid = db.Column(db.Integer)
    restmin = db.Column(db.Integer)
    linename = db.Column(db.String(10))


class Scaninfo(db.Model):
    __tablename__ = "mdi_scaninfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    sn = db.Column(db.String(50))
    shiftid = db.Column(db.Integer)
    scantime = db.Column(db.DateTime())
    sku = db.Column(db.String(30))
    sn1 = db.Column(db.String(30))
    sn2 = db.Column(db.String(30))
    sn3 = db.Column(db.String(30))
    sn4 = db.Column(db.String(30))
    sn5 = db.Column(db.String(30))
    scanqty = db.Column(db.Integer)


class Scantemplate(db.Model):
    __tablename__ = "mdi_scantemplate"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    scantemplate = db.Column(db.String(10))
    scanurl = db.Column(db.String(50))


class Shiftinfo(db.Model):
    __tablename__ = "mdi_shiftinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    shifttype = db.Column(db.String(2))
    lineid = db.Column(db.Integer)
    scantemplate = db.Column(db.String(10))
    currentsku = db.Column(db.String(50))
    headcount = db.Column(db.Float())
    lineleader = db.Column(db.String(50))
    team = db.Column(db.String(200))
    starttime = db.Column(db.DateTime())
    finishtime = db.Column(db.DateTime())
    payhour = db.Column(db.Integer)


class Shiftreport(db.Model):
    __tablename__ = "mdi_shiftreport"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    hide = db.Column(db.SmallInteger)
    sqdctype = db.Column(db.String(10))
    problemtype = db.Column(db.String(50))
    problemdes = db.Column(db.String(200))
    planid = db.Column(db.Integer)
    mdi = db.Column(db.String(1))
    abnormalqty = db.Column(db.Integer)
    issueid = db.Column(db.Integer)


class Skuinfo(db.Model):
    __tablename__ = "mdi_skuinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(50))
    routing = db.Column(db.Float())
    snverify = db.Column(db.String(50))
    lenverify = db.Column(db.Integer)
    line = db.Column(db.String(50))
    confirm = db.Column(db.SmallInteger)
    des = db.Column(db.String(255))
    cate = db.Column(db.String(10))
    stat = db.Column(db.String(5))


class Stock(db.Model):
    __tablename__ = "mdi_stock"
    __bind_key__ = "emdi"
    Id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(50))
    countdate = db.Column(db.DateTime())
    countstock = db.Column(db.Integer)
    cyclestock = db.Column(db.Integer)
    varstock = db.Column(db.Integer)
    safetystock = db.Column(db.Integer)
    machine = db.Column(db.String(10))
    av = db.Column(db.SmallInteger)
    mold = db.Column(db.String(50))


class Stockbom(db.Model):
    __tablename__ = "mdi_stockbom"
    __bind_key__ = "emdi"
    Id = db.Column(db.Integer, primary_key=True)
    kanbansku = db.Column(db.String(50))
    whereuse = db.Column(db.String(50))
    usage = db.Column(db.SmallInteger)


class Stockrecord(db.Model):
    __tablename__ = "mdi_stockrecord"
    __bind_key__ = "emdi"
    Id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(50))
    type = db.Column(db.SmallInteger)
    qty = db.Column(db.Integer)
    handledate = db.Column(db.DateTime())
    mo = db.Column(db.String(20))
    modate = db.Column(db.Date())


class Targetinfo(db.Model):
    __tablename__ = "mdi_targetinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    sqdctype = db.Column(db.String(10))
    year = db.Column(db.String(4))
    target = db.Column(db.Integer)


class Userinfo(db.Model):
    __tablename__ = "mdi_userinfo"
    __bind_key__ = "emdi"
    id = db.Column(db.Integer, primary_key=True)
    deptid = db.Column(db.Integer)
    username = db.Column(db.String(50))
    password = db.Column(db.String(50))
    email = db.Column(db.String(50))
    tel = db.Column(db.String(20))
    spv = db.Column(db.String(30))

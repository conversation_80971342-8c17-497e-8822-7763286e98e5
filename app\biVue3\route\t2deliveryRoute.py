from extensions import db
from flask import Blueprint, request
import datetime
from sqlalchemy import func, and_, or_, desc, not_, cast, Time
from app.dm.model.models_dm import Issuelog, Lineinfo as linfo, Planinfo, Restinfo, Shiftinfo, Scaninfo, Skuinfo, Asn, Asnvendor, Epeistock
from app.receiving.model.models_receiving import Inspect
from app.receiving.schemas import inspects_schema
from app.public.functions import responseGet, responseError, responsePost
from app.dm.functions import getkpi, getproblems, getRouting, getReqQtyByShift, getproblemsoee
from app.dm.schemas import shifts_schema
import math
import traceback

api = Blueprint('bi3/t2deliveryAPI', __name__)


@ api.route('/submitEPEIsku', methods=['POST'])
def submitEPEIsku():
    res = request.json
    sku = res.get('sku').upper()
    id = int(res.get('id'))
    countstock = res.get('countstock')
    countdate = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    cyclestock = res.get('cyclestock')
    varstock = res.get('varstock')
    safetystock = res.get('safetystock')
    machine = res.get('machine')
    mold = res.get('mold')
    packqty = res.get('packqty')
    area = res.get('area')
    try:
        if id > 0:
            mysku = db.session.query(Epeistock).filter(Epeistock.Id == id).first()
            if mysku.countstock == countstock:
                mysku.countdate = countdate
            mysku.countstock = countstock
            mysku.cyclestock = cyclestock
            mysku.varstock = varstock
            mysku.safetystock = safetystock
            mysku.machine = machine
            mysku.mold = mold
            mysku.packqty = packqty
            mysku.area = area
            db.session.commit()
            return responsePost('修改成功')
        else:
            mysku = Epeistock(sku=sku, countdate=countdate, countstock=countstock,
                              cyclestock=cyclestock, varstock=varstock, safetystock=safetystock, machine=machine, mold=mold, packqty=packqty, area=area, av=1)
            db.session.add(mysku)
            db.session.commit()
            return responsePost('新增成功')
    except Exception as e:
        traceback.print_exc()
        return responseError('操作失败:'+str(e))


@ api.route('/getEPEIbysku', methods=['GET'])
def getEPEIbysku():
    res = request.args
    sku = res.get('sku')
    mysku = db.session.query(Epeistock).filter(Epeistock.sku == sku).first()
    if mysku:
        form = {
            'id': mysku.Id,
            'sku': mysku.sku,
            'countdate': mysku.countdate,
            'countstock': mysku.countstock,
            'cyclestock': mysku.cyclestock,
            'varstock': mysku.varstock,
            'safetystock': mysku.safetystock,
            'machine': mysku.machine,
            'mold': mysku.mold,
            'packqty': mysku.packqty,
            'area': mysku.area
        }
        return responseGet('获取成功', form)
    return responseError('获取失败')


@ api.route('/getEPEI', methods=['GET'])
def getEPEI():
    res = request.args
    area = res.get('area')
    stype = res.get('stype')
    machine = res.get('machine')
    if stype == 'simple':
        data = db.session.query(Epeistock).filter(Epeistock.area == area).filter(Epeistock.countstock < (Epeistock.safetystock+Epeistock.varstock))
    elif stype == 'trigger':
        subquery = db.session.query(Epeistock.mold).filter(Epeistock.countstock < (Epeistock.safetystock+Epeistock.varstock)).subquery()
        data = db.session.query(Epeistock).filter(Epeistock.area == area).filter(or_(
            Epeistock.countstock < (Epeistock.safetystock+Epeistock.varstock),
            and_(Epeistock.mold.in_(subquery), Epeistock.countstock <
                 (Epeistock.safetystock+Epeistock.varstock+Epeistock.cyclestock)))).distinct(Epeistock.sku)
    else:
        data = db.session.query(Epeistock).filter(Epeistock.area == area)
    if machine:
        data = data.filter(Epeistock.machine == machine)
    print(data)
    data = data.filter(Epeistock.av == 1).all()
    machines = []
    sku = []
    safetystock = []
    safetystockrate = []
    varstock = []
    cyclestock = []
    overflow = []
    varstockrate = []
    cyclestockrate = []
    overflowrate = []
    currentstock = []
    currentstockrate = []
    trigger = []
    triggerrate = []
    fillmax = []
    fillmaxrate = []
    sortrate = []
    alarm = []
    alarmrate = []
    molds = []
    packArr = []
    for i in range(len(data)):
        machines.append(data[i].machine)
        sku.append(data[i].sku)
        if data[i].packqty == 1:
            packArr.append(data[i].countstock)
        else:
            packArr.append(str(data[i].countstock)+'/'+str(math.ceil(data[i].countstock/data[i].packqty))+'箱')
        cs = data[i].cyclestock
        vs = data[i].varstock
        ss = data[i].safetystock
        cts = data[i].countstock
        molds.append(data[i].mold)
        rs = cts
        totalstock = cs + vs + ss

        if rs == 0:
            safetystock.append('')
            safetystockrate.append('')
            varstock.append('')
            cyclestock.append('')
            overflow.append('')
            varstockrate.append('')
            cyclestockrate.append('')
            overflowrate.append('')
        elif rs < ss and rs > 0:
            safetystock.append(rs)
            safetystockrate.append(round(safetystock[i] / totalstock * 100, 0))
            varstock.append('')
            cyclestock.append('')
            overflow.append('')
            varstockrate.append('')
            cyclestockrate.append('')
            overflowrate.append('')
        elif rs >= ss and rs <= vs + ss:
            safetystock.append(ss)
            varstock.append(rs - ss)
            cyclestock.append('')
            overflow.append('')
            safetystockrate.append(round(safetystock[i] / totalstock * 100, 0))
            varstockrate.append(round(varstock[i] / totalstock * 100, 0))
            cyclestockrate.append('')
            overflowrate.append('')
        elif rs >= vs + ss and rs <= vs + ss + cs:
            safetystock.append(ss)
            varstock.append(vs)
            cyclestock.append(rs - ss - vs)
            overflow.append('')
            safetystockrate.append(round(safetystock[i] / totalstock * 100, 0))
            varstockrate.append(round(varstock[i] / totalstock * 100, 0))
            cyclestockrate.append(round(cyclestock[i] / totalstock * 100, 0))
            overflowrate.append('')
        elif rs > vs + ss + cs:
            safetystock.append(ss)
            varstock.append(vs)
            cyclestock.append(cs)
            overflow.append(rs - (vs + ss + cs))
            safetystockrate.append(round(safetystock[i] / totalstock * 100, 0))
            varstockrate.append(round(varstock[i] / totalstock * 100, 0))
            cyclestockrate.append(round(cyclestock[i] / totalstock * 100, 0))
            overflowrate.append(round(overflow[i] / totalstock * 100, 0))
        else:
            pass

        currentstock.append(rs)
        currentstockrate.append(round(currentstock[i] / totalstock * 100, 0))
        trigger.append(ss + vs)
        triggerrate.append(round(trigger[i] / totalstock * 100, 0))

        if rs <= cs + ss + vs:
            fillmax.append(vs + ss + cs - rs)
            fillmaxrate.append(round(fillmax[i] / totalstock * 100, 0))
            if rs == 0:
                sortrate.append(200)
            else:
                sortrate.append(round((vs + ss) / rs * 100, 0))
            if rs <= ss + vs:
                alarm.append(ss)
                alarmrate.append(10)
            else:
                alarm.append('')
                alarmrate.append('')
        else:
            alarm.append('')
            fillmax.append('')
            fillmaxrate.append('')
            alarmrate.append('')
            if overflowrate[i] > 0:
                sortrate.append(-overflowrate[i] * 100)
            else:
                sortrate.append((ss + vs) / (ss + cs + vs) * 100)
    print(sku)
    lists = bubble_sort(stype, sortrate, fillmaxrate, sku, cyclestockrate, varstockrate, safetystockrate, currentstockrate,
                        overflowrate, triggerrate, alarmrate, fillmax, currentstock, molds, machines, packArr)
    print(lists[2])
    return responseGet('success', {'lists': lists, 'machines': list(set(machines))})


def quicksort(arr, listarr, low, high):
    if low < high:
        pi = partition(arr, listarr, low, high)
        quicksort(arr, listarr, low, pi - 1)
        quicksort(arr, listarr, pi + 1, high)


def partition(arr, listarr, low, high):
    pivot = arr[high]
    i = low - 1
    for j in range(low, high):
        if arr[j] > pivot:  # Sort in descending order
            i += 1
            arr[i], arr[j] = arr[j], arr[i]
            for k in range(1, len(listarr)):
                listarr[k][i], listarr[k][j] = listarr[k][j], listarr[k][i]
    arr[i + 1], arr[high] = arr[high], arr[i + 1]
    for k in range(1, len(listarr)):
        listarr[k][i + 1], listarr[k][high] = listarr[k][high], listarr[k][i + 1]
    return i + 1


def bubble_sort(type, arr, fillmaxrate, sku, cyclestockrate, varstockrate, safetystockrate,
                currentstockrate, overflowrate, triggerrate, alarmrate, fillmax, currentstock, molds, machine, packArr):
    listarr = [arr, fillmaxrate, sku, cyclestockrate, varstockrate, safetystockrate, currentstockrate,
               overflowrate, triggerrate, alarmrate, fillmax, currentstock, molds, machine, packArr]
    len_arr = len(listarr[0])

    # 冒泡排序
    for i in range(1, len_arr):
        for k in range(len_arr - i):
            if listarr[0][k] < listarr[0][k + 1]:
                for ll in range(len(listarr)):
                    tmp = listarr[ll][k + 1]
                    listarr[ll][k + 1] = listarr[ll][k]
                    listarr[ll][k] = tmp

    if type != 'all':
        for i in range(len_arr):
            pos = 1
            for j in range(i + 1, len_arr):
                if listarr[12][j] == listarr[12][i]:
                    for ll in range(len(listarr)):
                        tmp = listarr[ll][j]
                        for p in range(j, i + pos, -1):
                            listarr[ll][p] = listarr[ll][p - 1]
                        listarr[ll][i + pos] = tmp
                    pos += 1

    return listarr


@ api.route('/getIQC', methods=['GET'])
def getIQC():
    today = (datetime.datetime.today() + datetime.timedelta(-1)).strftime("%Y-%m-%d %H:%M:%S")
    iqc = db.session.query(Inspect.Id, Inspect.orderno, Inspect.isurge, Inspect.sku, Inspect.issap, Inspect.receive_date,
                           Inspect.receive_qty, Inspect.sku_desc, Inspect.supplier_code, Inspect.location, Inspect.status).filter(
        or_(and_(Inspect.insclose_date >= today, Inspect.issap == 1), Inspect.status.in_(['open', 'inspect']))).filter(Inspect.location.like("11%")).order_by(
        Inspect.issap, desc(Inspect.status), desc(Inspect.isurge), Inspect.receive_date).all()
    total = db.session.query(func.count(Inspect.Id)).filter(
        or_(and_(Inspect.insclose_date >= today, Inspect.issap == 1),  Inspect.status.in_(['open', 'inspect']))).filter(Inspect.location.like("11%")).scalar()
    iqclist = inspects_schema.dump(iqc)
    tvdata = getTV()
    data = {'total': total, 'iqclist': iqclist, 'tvdata': tvdata}
    return responseGet("获取iqc列表成功", data)


def getTV():
    startdate = (datetime.datetime.today() + datetime.timedelta(-1)).strftime("%Y-%m-%d %H:%M:%S")
    enddate = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    options = ['open', 'inspect', 'ncmr', 'closed', 'return']
    oArr = []
    query = db.session.query(func.sum(func.if_(Inspect.status == 'open', 1, 0)),
                             func.sum(func.if_(Inspect.status == 'inspect', 1, 0)),
                             func.sum(func.if_(Inspect.status == 'ncmr', 1, 0)),
                             func.sum(func.if_(Inspect.status == 'closed', 1, 0)),
                             func.sum(func.if_(Inspect.status == 'return', 1, 0))
                             ).filter(Inspect.receive_date.between(startdate, enddate)).all()

    o = 0 if query[0][0] is None else int(query[0][0])
    i = 0 if query[0][1] is None else int(query[0][1])
    n = 0 if query[0][2] is None else int(query[0][2])
    c = 0 if query[0][3] is None else int(query[0][3])
    r = 0 if query[0][4] is None else int(query[0][4])
    oArr.append(o)
    oArr.append(i)
    oArr.append(n)
    oArr.append(c)
    oArr.append(r)
    query2 = db.session.query(Inspect.status, func.count(Inspect.status)).filter(
        Inspect.receive_date.between(startdate, enddate)).group_by(Inspect.status).all()
    pie = []
    finDic = {'name': '已完成', 'value': 0, 'label': {
        'show': 'true', 'fontWeight': 'bold'}}
    insDic = {'name': '未完成', 'value': 0}
    for q in query2:
        if q[0] == 'closed' or q[0] == 'return':
            finDic['value'] = finDic['value'] + q[1]
        else:
            insDic['value'] = insDic['value'] + q[1]
    if insDic['value'] == 0:
        insDic['value'] = '--'
    if finDic['value'] == 0:
        finDic['value'] = '--'
    pie.append(finDic)
    pie.append(insDic)
    tvdata = {'dataX': options, 'oArr': oArr, 'pie': pie}
    return tvdata


def get_dates_between(previous_monday, next_sunday):
    dates_list = []
    current_date = previous_monday
    while current_date <= next_sunday:
        dates_list.append(current_date)
        current_date += datetime.timedelta(days=1)
    return dates_list


def get_previous_monday_and_next_sunday(date_str):
    # 将日期字符串转换为 datetime 对象
    date = datetime.datetime.strptime(date_str, '%Y-%m-%d')
    # 找到给定日期的上周周日
    this_week_monday = date - datetime.timedelta(days=date.weekday()+1)
    # 找到给定日期的上上周周日
    previous_week_monday = this_week_monday - datetime.timedelta(days=7)
    # 找到给定日期的下周周六
    next_week_sunday = this_week_monday + datetime.timedelta(days=13)
    return previous_week_monday, next_week_sunday


@api.route('/getAsn', methods=['GET'])
def getAsn():
    res = request.args
    dt = res.get('dt')
    bsdate, enddate = get_previous_monday_and_next_sunday(dt)
    asns = db.session.query(Asn.asnno, Asnvendor.shortname, Asnvendor.mprc, Asn.cdate, Asnvendor.lt, Asn.adate, Asnvendor.lt).outerjoin(
        Asnvendor, Asn.vendorid == Asnvendor.vendorid).filter(or_(Asn.cdate.between(bsdate, enddate), Asn.adate.between(bsdate, enddate))).all()
    datelist = get_dates_between(bsdate, enddate)
    asnInfo = {}
    for d in datelist:
        asnInfo[datetime.datetime.strftime(d, '%Y-%m-%d')] = []
    for a in asns:
        oadate = ''
        ocdate = ''
        if a.adate and a.adate != "0000-00-00":
            status = 1
            dt = datetime.datetime.strftime(a.adate, '%Y-%m-%d')
            oadate = dt
        elif a.cdate:
            ocdate = datetime.datetime.strftime(a.cdate, '%Y-%m-%d')
            newcdate = a.cdate+datetime.timedelta(days=a.lt)
            if newcdate < datetime.date.today():
                status = -1
                dt = datetime.datetime.strftime(datetime.date.today(), '%Y-%m-%d')
            else:
                status = 0
                dt = datetime.datetime.strftime(newcdate, '%Y-%m-%d')
        else:
            continue
        if dt not in asnInfo.keys():
            asnInfo[dt] = [{
                'status': status,
                'name': a.shortname if a.shortname else '未知',
                'cdate': dt if a.cdate else '',
                'asnno': a.asnno,
                'mprc': a.mprc if a.mprc else '',
                'ocdate': ocdate,
                'adate': oadate,
                'lt': a.lt if a.lt else ''
            }]
        else:
            asnInfo[dt].append({
                'status': status,
                'cdate': dt if a.cdate else '',
                'name': a.shortname if a.shortname else '未知',
                'asnno': a.asnno,
                'mprc': a.mprc,
                'ocdate': ocdate,
                'adate': oadate,
                'lt': a.lt if a.lt else ''
            })
    print(asnInfo)
    return responseGet('成功', {'asnInfo': asnInfo})


@api.route('/getOrder', methods=['GET'])   # 获取当日订单的完成状况，按产线划分
def getOrder():
    res = request.args
    area = res.get('area')
    dt = res.get('dt2')
    # dt = '2022-01-15'
    if area in ['B2Extrusion']:
        plans = db.session.query(Planinfo.sequence, Planinfo.shifttype, Planinfo.linename, linfo.linegroup, Planinfo.qty, Planinfo.sku, Planinfo.planner,
                                 Planinfo.status, Shiftinfo.headcount, Shiftinfo.routing, func.sum(Scaninfo.scanqty).label('finishQty')).outerjoin(
            Shiftinfo, and_(Shiftinfo.linename == Planinfo.linename, Shiftinfo.shifttype == Planinfo.shifttype,
                            Shiftinfo.sku == Planinfo.sku, or_(func.datediff(func.date(Shiftinfo.starttime), Planinfo.plandate) == 0,
                                                               and_(func.datediff(func.date(Shiftinfo.starttime),
                                                                                  Planinfo.plandate) == 1, cast(func.time(Shiftinfo.starttime), Time) <= '08:00:00',
                                                                    Shiftinfo.shifttype == '夜班')))).outerjoin(
            Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(linfo, linfo.linename == Planinfo.linename).filter(
                not_(and_(Shiftinfo.shifttype == '夜班', func.datediff(func.date(Shiftinfo.starttime), dt) == 1, cast(func.time(Shiftinfo.starttime), Time) > '08:00:00'))).filter(
                not_(and_(Shiftinfo.shifttype == '白班', func.datediff(func.date(Shiftinfo.starttime), dt) == 1))).filter(
                not_(and_(cast(func.time(Shiftinfo.starttime), Time) <= '08:00:00', Shiftinfo.shifttype == '夜班', func.datediff(func.date(Shiftinfo.starttime), dt) == 0))).filter(
                Planinfo.plandate == dt, and_(func.datediff(func.date(Shiftinfo.starttime), dt) >= 0, func.datediff(func.date(Shiftinfo.starttime), dt) <= 1)).filter(
                linfo.area == area).group_by(Planinfo.linename, Planinfo.plandate, Planinfo.shifttype, Planinfo.sku).all()
    else:
        plans = db.session.query(Planinfo.sequence, Planinfo.shifttype, Planinfo.linename, linfo.linegroup, Planinfo.qty, Planinfo.sku, Planinfo.planner,
                                 Planinfo.status, Shiftinfo.headcount, Shiftinfo.routing, func.sum(Scaninfo.scanqty).label('finishQty')).outerjoin(
            Shiftinfo, and_(Shiftinfo.linename == Planinfo.linename, Shiftinfo.sku == Planinfo.sku, func.date(Shiftinfo.starttime) == Planinfo.plandate)).outerjoin(
            Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(linfo, linfo.linename == Planinfo.linename).filter(
                Planinfo.plandate == dt).filter(linfo.area == area).group_by(Planinfo.Id).all()
        # print(plans)
    dic = {}
    for p in plans:
        fqty = p.finishQty if p.finishQty else 0
        if p.linename in dic.keys():
            dic[p.linename]['children'].append({
                'shifttype': p.shifttype,
                'linename': p.linename,
                'sku': p.sku,
                'sequence': p.sequence,
                'headcount': p.headcount,
                'routing': p.routing,
                'planner': p.planner,
                'planqty': p.qty,
                'status': p.status,
                'scanqty': fqty,
                'resttime': (p.routing * (p.qty-fqty)/p.headcount*3600) if p.headcount else '-'
            })
        else:
            dic[p.linename] = {
                'linename': p.linename,
                'children': [
                    {
                        'shifttype': p.shifttype,
                        'linename': p.linename,
                        'sku': p.sku,
                        'sequence': p.sequence,
                        'headcount': p.headcount,
                        'routing': p.routing,
                        'planner': p.planner,
                        'planqty': p.qty,
                        'status': p.status,
                        'scanqty': fqty,
                        'resttime': (p.routing * (p.qty-fqty)/p.headcount*3600) if p.headcount else '-'
                    }
                ]
            }
    outArr = list(dic.values())
    return responseGet('成功', outArr)


@api.route('/getDay', methods=['GET'])  # 获取指定日期，某个区域的所有产线的生产状况和问题
def getDay():
    r = request.args
    dt = r.get('dt')
    area = r.get('area')
    restList = db.session.query(Restinfo).all()
    rests = {}
    for r in restList:
        mydt = dt
        if str(r.resttime) < '08:00:00':
            mydt = (datetime.datetime.strptime(dt, '%Y-%m-%d') +
                    datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        if r.linegroup in rests.keys():
            rests[r.linegroup].append({
                'reststart': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S'),
                'restfinish': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S')+datetime.timedelta(minutes=r.restmin)
            })
        else:
            rests[r.linegroup] = [{
                'reststart': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S'),
                'restfinish': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S')+datetime.timedelta(minutes=r.restmin)
            }]
    shifts = db.session.query(func.date(Shiftinfo.starttime).label('shiftdate'), linfo.linegroup, Shiftinfo.linename, Shiftinfo.shifttype,
                              linfo.linegroup, func.sum(Scaninfo.scanqty).label('scanQty'), Shiftinfo.headcount,
                              Shiftinfo.starttime, Shiftinfo.headcount, Shiftinfo.finishtime, Shiftinfo.routing, Shiftinfo.sku).outerjoin(
                                  Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(
        linfo, linfo.linename == Shiftinfo.linename).filter(
            not_(and_(Shiftinfo.shifttype == '夜班', func.datediff(func.date(Shiftinfo.starttime), dt) == 1, cast(func.time(Shiftinfo.starttime), Time) > '08:00:00'))).filter(
            not_(and_(Shiftinfo.shifttype.in_(['白班', '中班']), func.datediff(func.date(Shiftinfo.starttime), dt) == 1))).filter(
            not_(and_(cast(func.time(Shiftinfo.starttime), Time) <= '08:00:00', Shiftinfo.shifttype == '夜班', func.datediff(func.date(Shiftinfo.starttime), dt) == 0))).filter(
            and_(func.datediff(func.date(Shiftinfo.starttime), dt) >= 0, func.datediff(
                func.date(Shiftinfo.starttime), dt) <= 1)).filter(linfo.area == area).group_by(Shiftinfo.Id).all()
    scanDic = {}
    for s in shifts:
        linename = s.linename+'-'+s.sku
        if s.linegroup in rests.keys():
            lg = s.linegroup
        else:
            lg = 'all'
        if s.finishtime:
            ftime = s.finishtime
        else:
            ftime = datetime.datetime.now()
        # shiftdate = datetime.datetime.strftime(s.starttime, '%Y-%m-%d')
        if linename in scanDic.keys():
            scanQty = s.scanQty if s.scanQty else 0
            # requireQty = getReqQty(shiftdate, s.headcount, s.linegroup,
            #                        s.starttime, s.finishtime, restList, s.routing)
            requireQty = getReqQtyByShift(s.headcount, s.starttime,
                                          ftime, rests[lg], s.routing)
            scanDic[linename]['scanQty'] = scanDic[linename]['scanQty']+scanQty
            scanDic[linename]['requireQty'] = scanDic[linename]['requireQty']+requireQty
        else:
            scanDic[linename] = {
                'linegroup': s.linegroup,
                'headcount': s.headcount,
                'routing': round(1/s.routing, 1),
                'sku': s.sku,
                'linename': s.linename,
                'scanQty': s.scanQty if s.scanQty else 0,
                'requireQty': getReqQtyByShift(s.headcount, s.starttime, ftime, rests[lg], s.routing),
                'planQty': 0,
                'finishQty': 0,
                'desc': []
            }
    plans = db.session.query(Planinfo.Id, Planinfo.linename, Planinfo.mo, linfo.linegroup, Planinfo.qty, Planinfo.sku, func.sum(Scaninfo.scanqty).label('finishQty')).outerjoin(
        Shiftinfo, and_(Shiftinfo.shifttype == Planinfo.shifttype, Shiftinfo.linename == Planinfo.linename,
                        Shiftinfo.sku == Planinfo.sku, or_(and_(func.date(Shiftinfo.starttime) == Planinfo.plandate,
                                                                cast(func.time(Shiftinfo.starttime), Time) >= '08:00:00'),
                                                           and_(cast(func.time(Shiftinfo.starttime), Time) < '08:00:00', Shiftinfo.shifttype == '夜班',
                                                                func.datediff(func.date(Shiftinfo.starttime), Planinfo.plandate) == 1)))).outerjoin(
        Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(linfo, linfo.linename == Planinfo.linename).filter(
            Planinfo.plandate == dt).filter(linfo.area == area).group_by(Planinfo.Id).all()
    planDic = {}
    for p in plans:
        linename = p.linename+'-'+p.sku
        if linename in planDic.keys():
            planDic[linename]['finishQty'] = planDic[linename]['finishQty'] + \
                (p.finishQty if p.finishQty else 0)
            planDic[linename]['planQty'] = planDic[linename]['planQty'] + \
                (p.qty if p.qty else 0)
        else:
            planDic[linename] = {
                'mo': p.mo,
                'linegroup': p.linegroup,
                'routing': round(1/getRouting(p.sku, p.linename, 1), 1),
                'sku': p.sku,
                'linename': p.linename,
                'planQty': p.qty if p.qty else 0,
                'finishQty': p.finishQty if p.finishQty else 0,
                'desc': []
            }

    for k in scanDic.keys():
        if k in planDic.keys():
            scanDic[k]['planQty'] = planDic[k]['planQty']
            scanDic[k]['finishQty'] = planDic[k]['finishQty']
            scanDic[k]['mo'] = planDic[k]['mo']
    for k in planDic.keys():
        if k not in scanDic.keys():
            scanDic[k] = planDic[k]
            scanDic[k]['scanQty'] = '-'
            scanDic[k]['requireQty'] = '-'
    problems = db.session.query(Issuelog).join(linfo, linfo.linename == Issuelog.linename).filter(
        Issuelog.shiftdate == dt).filter(linfo.area == area).all()
    pDic = {}
    for p in problems:
        if p.linename in pDic.keys():
            pDic[p.linename].append(p.sqdctype+'-'+p.desc)
        else:
            pDic[p.linename] = [p.sqdctype+'-'+p.desc]
    arr = list(scanDic.values())
    for i in range(len(arr)):
        if arr[i]['linename'] in pDic.keys():
            arr[i]['desc'] = pDic[arr[i]['linename']]
    outArr = sorted(arr, key=lambda item: item['linename'])
    return responseGet('成功', outArr)


@api.route('/getDayoee', methods=['GET'])  # 获取指定日期，某个区域的所有产线的生产状况和问题
def getDayoee():
    r = request.args
    dt = r.get('dt')
    area = r.get('area')
    restDic = {}
    resttime = db.session.query(Restinfo.resttime, Restinfo.restmin, linfo.linename).join(
        linfo, Restinfo.linegroup == linfo.linegroup).all()
    for r in resttime:
        if r.linename not in restDic.keys():
            restDic[r.linename] = {
                r.resttime: r.restmin
            }
        else:
            restDic[r.linename][r.resttime] = r.restmin
    shifts = db.session.query(func.date(Shiftinfo.starttime).label('shiftdate'), linfo.linegroup, Shiftinfo.linename, linfo.linegroup,
                              func.sum(Scaninfo.scanqty).label(
                                  'scanQty'), Shiftinfo.headcount, Skuinfo.typegroup,
                              Shiftinfo.starttime, Shiftinfo.shifttype, Shiftinfo.finishtime, Shiftinfo.routing,
                              Shiftinfo.sku).outerjoin(Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(
        linfo, linfo.linename == Shiftinfo.linename).join(Skuinfo, Shiftinfo.sku == Skuinfo.sku).filter(
        func.date(Shiftinfo.starttime) == dt).filter(linfo.area == area).order_by(desc(Shiftinfo.shifttype)).group_by(Shiftinfo.Id).all()
    scanDic = {}
    for s in shifts:
        linename = s.linename+'-'+s.shifttype+'-'+s.sku
        ttrest = 0
        for k, v in restDic[s.linename].items():
            ss = datetime.datetime.strftime(s.starttime, '%H:%M:%S')
            ff = datetime.datetime.strftime(s.finishtime, '%H:%M:%S')
            kk = datetime.time.strftime(k, '%H:%M:%S')
            if (ss > ff):
                if ss <= kk < '24:00:00' or '00:00:00' <= kk < ff:
                    ttrest += v
            else:
                if ss <= kk < ff:
                    ttrest += v
        if linename in scanDic.keys():
            scanQty = s.scanQty if s.scanQty else 0
            totalmin = (s.finishtime-s.starttime).seconds/60-ttrest
            scanDic[linename]['scanQty'] = scanDic[linename]['scanQty']+scanQty
            scanDic[linename]['totalmin'] = scanDic[linename]['totalmin']+totalmin
        else:
            scanDic[linename] = {
                'starttime': datetime.datetime.strftime(s.starttime, '%Y-%m-%d %H:%M:%S '),
                'finishtime': datetime.datetime.strftime(s.finishtime, '%Y-%m-%d %H:%M:%S'),
                'linegroup': s.linegroup,
                'shifttype': s.shifttype,
                'routing': round(1/s.routing, 1),
                'typegroup': s.typegroup,
                'sku': s.sku,
                'linename': s.linename,
                'scanQty': s.scanQty if s.scanQty else 0,
                'totalmin':  (s.finishtime-s.starttime).seconds/60-ttrest,
                'totalactmin': s.routing*s.scanQty*60 if s.scanQty else 0,
                'defectQty': 0,
                'issuemin': 0,
                'desc': []
            }
    problems = db.session.query(Issuelog).join(linfo, linfo.linename == Issuelog.linename).filter(
        Issuelog.shiftdate == dt).filter(linfo.area == area).all()
    pDic = {}
    issueDic = {}
    for p in problems:
        print(11111, p.linename, p.shifttype, p.qty, p.issuemin)
        if p.linename in pDic.keys():
            pDic[p.linename].append(p.sqdctype+'-'+p.desc)
            if p.shifttype in issueDic[p.linename].keys():
                issueDic[p.linename][p.shifttype]['issuemin'] += p.issuemin
                issueDic[p.linename][p.shifttype]['defectQty'] += p.qty
            else:
                issueDic[p.linename][p.shifttype] = {
                    'issuemin': p.issuemin if p.issuemin else 0,
                    'defectQty': p.qty if p.qty else 0
                }
        else:
            pDic[p.linename] = [p.sqdctype+'-'+p.desc]
            issueDic[p.linename] = {
                p.shifttype: {
                    'issuemin': p.issuemin if p.issuemin else 0,
                    'defectQty': p.qty if p.qty else 0
                },
                '夜班' if p.shifttype == '白班' else '白班': {
                    'issuemin':  0,
                    'defectQty':  0
                }
            }
    print(123123, issueDic)
    arr = list(scanDic.values())
    for i in range(len(arr)):
        if arr[i]['linename'] in pDic.keys():
            arr[i]['desc'] = pDic[arr[i]['linename']]
    for i in range(len(arr)):
        if arr[i]['linename'] in issueDic.keys():
            if arr[i]['shifttype'] in issueDic[arr[i]['linename']].keys():
                arr[i]['issuemin'] = issueDic[arr[i]['linename']
                                              ][arr[i]['shifttype']]['issuemin']
                arr[i]['defectQty'] = issueDic[arr[i]['linename']
                                               ][arr[i]['shifttype']]['defectQty']
            else:
                arr[i]['issuemin'] = 0
                arr[i]['defectQty'] = 0

    outArr = sorted(arr, key=lambda item: item['linename'])
    return responseGet('成功', outArr)


@api.route('/getDmonth', methods=['GET'])  # 获得交货相关的趋势图，柏拉图和问题清单
def getDmonth():
    r = request.args
    stime = r.get('stime')
    etime = r.get('etime')
    days = (datetime.datetime.strptime(etime, '%Y-%m-%d') -
            datetime.datetime.strptime(stime, '%Y-%m-%d')).days
    linegroup = r.get('area')
    area = r.get('group')
    kpi = getkpi(stime.split('-')[0], linegroup)['dtarget']
    plans = db.session.query(Planinfo.plandate, func.sum(Planinfo.qty).label('qty'), func.sum(func.if_(or_(Planinfo.status >= 2, Planinfo.status == 0), 1, 0)).label('fail'),
                             func.sum(func.if_(Planinfo.status == 1, 1, 0)).label('achieve')).join(linfo, Planinfo.linename == linfo.linename).filter(
        and_(Planinfo.plandate >= stime, Planinfo.plandate < etime)).filter(Planinfo.status >= 0).group_by(Planinfo.plandate)
    if linegroup:
        plans = plans.filter(linfo.linegroup == linegroup)
    else:
        plans = plans.filter(linfo.area == area)
    plans = plans.all()
    data = {
        'ftt': [],
        'fttmin': 50,
        'ttplan': [],
        'achieveplan': [],
        'fttx': [],
        'green': [],
        'red': [],
        'yellow': [],
        'listData': [],
        'paretodata': [],
        'paretotarget': [],
        'paretox': []
    }
    for p in plans:
        pdate = datetime.datetime.strftime(p.plandate, '%m-%d')
        data['fttx'].append(pdate)
        ttplan = p.achieve+p.fail
        ftt = round(p.achieve/ttplan*100, 0)
        if ftt < 0:
            ftt = 0
        data['ftt'].append(ftt)
        data['ttplan'].append(ttplan)
        data['achieveplan'].append(p.achieve)
        data['green'].append(100-kpi*100)
        data['red'].append(kpi*95)
        data['yellow'].append(kpi*5)
    problems = getproblems([linegroup], '交货', days)
    for p in problems:
        data['paretox'].append(p['ptype'])
        data['paretotarget'].append(p['trigger'])
        data['paretodata'].append(0)
    data['paretox'].append('未分类')
    data['paretotarget'].append(round(5/7*days))
    data['paretodata'].append(0)
    issues = db.session.query(Issuelog).join(linfo, Issuelog.linename == linfo.linename).filter(
        Issuelog.shiftdate.between(stime, etime)).filter(Issuelog.sqdctype == '交货')
    if linegroup:
        issues = issues.filter(linfo.linegroup == linegroup)
    else:
        issues = issues.filter(linfo.area == area)
    issues = issues.order_by(desc(Issuelog.recordtime)).all()
    for i in issues:
        if i.problemtype in data['paretox']:
            data['paretodata'][data['paretox'].index(i.problemtype)] += 1
        else:
            data['paretodata'][len(data['paretodata'])-1] += 1
        data['listData'].append({
            'recorddate': datetime.datetime.strftime(i.shiftdate, "%Y-%m-%d"),
            'desc': i.desc,
            'linename': i.linename,
            'qty': i.qty,
            'problemtype': i.problemtype if i.problemtype else '其他'
        })
    if len(data['ftt']) > 0:
        data['fttmin'] = min(data['ftt']) if min(data['ftt']) < 100 else 80
        maxqty = max(data['ftt']) if max(data['ftt']) > 100 else 100
    for j in range(len(data['green'])):
        data['green'][j] = int(maxqty)-kpi*100 + \
            5 if (kpi*100 < int(maxqty)) else 5
    return responseGet('获取成功', data)


@api.route('/getDmonthoee', methods=['GET'])  # 获得交货相关的趋势图，柏拉图和问题清单
def getDmonthoee():
    r = request.args
    stime = r.get('stime')
    etime = r.get('etime')
    days = (datetime.datetime.strptime(etime, '%Y-%m-%d') -
            datetime.datetime.strptime(stime, '%Y-%m-%d')).days
    linegroup = r.get('area')
    area = r.get('group')
    typeline = []
    alllines = db.session.query(linfo.linegroup).filter(
        linfo.area == area).all()
    issues = db.session.query(Issuelog).join(linfo, Issuelog.linename == linfo.linename).filter(
        Issuelog.shiftdate.between(stime, etime)).filter(Issuelog.sqdctype == '停机损失')
    if linegroup:
        issues = issues.filter(linfo.linegroup == linegroup)
        typeline.append(linegroup)
    else:
        issues = issues.filter(linfo.area == area)
        for a in alllines:
            typeline.append(a[0])
    issues = issues.order_by(desc(Issuelog.recordtime)).all()
    kpi = getkpi(stime.split('-')[0], linegroup)['utiltarget']
    data = {
        'ftt': [],
        'fttmin': 50,
        'fttx': [],
        'green': [],
        'red': [],
        'yellow': [],
        'listData': [],
        'paretodata': [],
        'paretotarget': [],
        'paretox': []
    }
    dateDic = {}
    problems = getproblemsoee(typeline, '停机损失', days)
    for p in problems:
        data['paretox'].append(p['ptype'])
        data['paretotarget'].append(p['trigger'])
        data['paretodata'].append(0)
    data['paretox'].append('未分类')
    data['paretotarget'].append(round(5/7*days))
    data['paretodata'].append(0)

    for i in issues:
        if i.problemtype in data['paretox']:
            data['paretodata'][data['paretox'].index(
                i.problemtype)] += i.issuemin
        else:
            data['paretodata'][len(data['paretodata'])-1] += i.issuemin
        data['listData'].append({
            'recorddate': datetime.datetime.strftime(i.shiftdate, "%Y-%m-%d"),
            'desc': i.desc,
            'linename': i.linename,
            'qty': i.issuemin,
            'problemtype': i.problemtype if i.problemtype else '其他'
        })
        dickey = datetime.datetime.strftime(i.shiftdate, "%Y-%m-%d")
        if dickey not in dateDic:
            dateDic[dickey] = {
                'totalmin': 0,
                'issuemin': i.issuemin,
                'sdate': dickey
            }
        else:
            dateDic[dickey]['issuemin'] += i.issuemin

    plans = db.session.query(func.min(Shiftinfo.starttime).label('stime'), func.max(Shiftinfo.finishtime).label('ftime'),
                             func.date(Shiftinfo.starttime).label('sdate'), Shiftinfo.linename).join(linfo, Shiftinfo.linename == linfo.linename).filter(
        and_(Shiftinfo.starttime >= stime, Shiftinfo.starttime < etime)).filter(func.not_(Shiftinfo.finishtime.is_(None))).group_by(
        Shiftinfo.linename, func.date(Shiftinfo.starttime))
    if linegroup:
        plans = plans.filter(linfo.linegroup == linegroup)
    else:
        plans = plans.filter(linfo.area == area)
    plans = plans.all()
    for p in plans:
        dickey = datetime.datetime.strftime(p.sdate, "%Y-%m-%d")
        if dickey not in dateDic:
            dateDic[dickey] = {
                'totalmin': (p.ftime-p.stime).total_seconds()/60,
                'issuemin': 0,
                'sdate': dickey
            }
        else:
            dateDic[dickey]['totalmin'] += (p.ftime-p.stime).total_seconds()/60
    for k, v in dateDic.items():
        if v['totalmin'] > 0:
            pdate = datetime.datetime.strftime(
                datetime.datetime.strptime(k, "%Y-%m-%d"), '%m-%d')
            data['fttx'].append(pdate)
            ttplan = v['totalmin']
            ftt = round((ttplan-v['issuemin'])/ttplan*100, 0)
            if ftt < 0:
                ftt = 0
            data['ftt'].append(ftt)
            data['green'].append(100-kpi*100)
            data['red'].append(kpi*95)
        data['yellow'].append(kpi*5)
    if len(data['ftt']) > 0:
        data['fttmin'] = min(data['ftt']) if min(data['ftt']) < 100 else 80
        maxqty = max(data['ftt']) if max(data['ftt']) > 100 else 100
    for j in range(len(data['green'])):
        data['green'][j] = int(maxqty)-kpi*100 + \
            5 if (kpi*100 < int(maxqty)) else 5
    return responseGet('获取成功', data)


@ api.route("/getShiftInfo", methods=["get"])  # 根据班次，日期，产线和搜索获取所有的扫描信息
def getShiftInfo():
    res = request.args
    stime = res.get('stime')
    etime = res.get('etime')
    linename = res.get('linename')
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    plant = res.get('plant')
    # 更具stime返回第二天的00:08:00的时间
    # nextday8am=datetime.datetime.strptime(stime, '%Y-%m-%d')+datetime.timedelta(days=1)+datetime.timedelta(hours=8)
    # print(111111111111, stime, etime)
    shifts = db.session.query(Shiftinfo).order_by(desc(Shiftinfo.starttime), Shiftinfo.linename, Shiftinfo.shifttype).join(
        linfo, linfo.linename == Shiftinfo.linename).filter(linfo.plant == plant).filter(
        Shiftinfo.starttime.between(stime, etime))
    total = db.session.query(func.count(Shiftinfo.Id)).join(
        linfo, linfo.linename == Shiftinfo.linename).filter(linfo.plant == plant).filter(
        Shiftinfo.starttime.between(stime, etime))
    if linename:
        shifts = shifts.filter(Shiftinfo.linename == linename)
        total = total.filter(Shiftinfo.linename == linename)
    shifts = shifts.paginate(pagenum, pagesize, error_out=False).items
    total = total.scalar()
    shiftinfo = shifts_schema.dump(shifts)
    return responseGet('success', {'shifts': shiftinfo, 'total': total})


@ api.route("/editShift", methods=["POST"])  # 修改班次信息
def editShift():
    res = request.json
    Id = res.get('Id')
    shifttype = res.get('shifttype')
    starttime = res.get('starttime')
    finishtime = res.get('finishtime')
    linename = res.get('linename')
    lineleader = res.get('lineleader')
    team = res.get('team')
    headcount = res.get('headcount')
    routing = res.get('routing')
    plant = res.get('plant')
    line = db.session.query(linfo).filter(linfo.linename == linename).filter(
        linfo.plant == plant).filter(linfo.isactive == 1).first()
    if not line:
        return responseError(plant+'工厂没有该产线'+linename+',或该产线状态未激活，请确认或联系管理员')
    try:
        db.session.query(Shiftinfo).filter(Shiftinfo.Id == Id).update({
            'shifttype': shifttype,
            'starttime': starttime,
            'finishtime': finishtime,
            'linename': linename,
            'lineleader': lineleader,
            'team': team,
            'headcount': headcount,
            'routing': routing
        })
        db.session.commit()
        return responsePost('修改成功')
    except Exception:
        db.session.rollback()
        return responseError('修改失败，请联系管理员')

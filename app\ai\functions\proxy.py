from flask import Blueprint, request
import requests


api = Blueprint('/im/proxy', __name__)


@api.route('/', defaults={'path': ''}, methods=['GET', 'POST', 'PUT', 'DELETE'])
@api.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE'])
def proxy(path):
    # target_url = 'http://10.76.4.163:8081'  # 要转发的目标URL
    target_url = 'http://127.0.0.1:5000'

    # 获取请求方法和数据
    headers = request.headers
    method = request.method
    data = request.get_data()
    params = request.args  # 获取查询参数

    # 发送转发请求
    response = requests.request(
        method=method,
        url=target_url + '/' + path,
        headers=headers,
        params=params,  # 将查询参数传递给目标主机
        data=data,
        stream=True
    )

    # 将目标服务器的响应转发给客户端
    return response.raw.read(), response.status_code, response.headers.items()

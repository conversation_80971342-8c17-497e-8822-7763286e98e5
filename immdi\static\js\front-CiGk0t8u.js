import{aM as e,aN as a}from"./index-BnxEuBzx.js";const r=t=>e.request("get",a("front/getbasicinfo"),{params:t}),u=t=>e.request("post",a("hourtable/confirmdata"),{data:t}),o=t=>e.request("post",a("hourtable/updatehourdata"),{data:t}),d=t=>e.request("get",a("quality/getdefectbyshift"),{params:t}),n=()=>e.request("get",a("quality/getdefecttypelist")),c=t=>e.request("post",a("quality/updatedefectdata"),{data:t}),p=t=>e.request("post",a("shift/deletestate"),{data:t});export{c as a,d as b,u as c,p as d,n as e,r as g,o as u};

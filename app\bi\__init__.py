def importBIRoute(app):
    from app.bi.route.pubRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi/pubAPI")

    from app.bi.route.wrehsRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi/wrehsAPI")

    from app.bi.route.wrqualityRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi/wrqualityAPI")

    from app.bi.route.wrdeliveryRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi/wrdeliveryAPI")

    from app.bi.route.wrcostRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi/wrcostAPI")

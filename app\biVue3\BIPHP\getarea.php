	<?php
include("coon.php");

$sql3 ="select dept1 from wl_userinfo where dept1 is not null group by dept1 order by dept1 desc";
$query=mysqli_query($link, $sql3);
$total=0;
if (mysqli_num_rows($query) >= 1) {
    while ($rs = mysqli_fetch_array($query)) {
        $area=$rs['dept1'];
        $output[]=array('value'=>$area,'name'=>$area);
    }
}
print_r(json_encode($output, JSON_UNESCAPED_UNICODE));

?>
	
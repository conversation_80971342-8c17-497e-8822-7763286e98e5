/**
 * 货运列表相关类型定义
 */

// 基础数据类型
export interface RowData {
  release_time: string;
  DN_NO: string;
  status: number;
  business_type: number;
  cs_specialist: string;
  shipping_point: string;
  payer_company: string;
  receiver_province: string;
  receiver_city: string;
  receiver_district: string;
  receiver_address: string;
  weight?: number;
  volume?: number;
  goods_value?: number;
  currency?: string;
  BU_NO?: string;
  freight_fee?: number;
  fee_remark?: string;
  freight_calc_method?: number;
  freight_unit_price?: number;
  freight_adjust?: number;
  freight_adjust_reason?: string;
  logistic_remark?: string;
  logistic_company?: string;
}

// 运费计算相关类型
export interface FreightItem {
  template: string;
  unitprice: number;
  province: string;
  city: string;
  district: string;
  lowerlimit: number;
  upperlimit: number;
}

export interface VehicleItem {
  template: string;
  unitprice: number;
  truck: string;
  lowerlimit: number;
  upperlimit: number;
}

export interface FreightData {
  weight: FreightItem[];
  volume: FreightItem[];
  vehicle: Record<string, VehicleItem[]>;
}

// 运费表单数据
export interface FeeFormData {
  freight_fee: number;
  fee_remark: string;
  freight_calc_method: number; // 1: 重量, 2: 体积, 3: 车型
  freight_unit_price: number;
  freight_adjust: number;
  freight_adjust_reason: string;
  logistic_remark: string;
  logistic_company: string;
  freight_total_fee: number;
}

// 选中卡片状态
export interface SelectedCard {
  type: 'weight' | 'volume' | 'vehicle' | '';
  template: string;
  index: number;
}

// 货物量表单数据
export interface VolumeFormData {
  weight: number;
  volume: number;
  warehouse_remark: string;
}

// 释放表单数据
export interface ReleaseFormData {
  row: RowData;
  warehouse: string;
  customer_email: string;
  comments: string;
}

// 搜索条件
export interface SearchCondition {
  dn_number: string;
  status: string;
  daterange: [string?, string?];
}

// 操作标志
export interface OperationFlag {
  can_release: boolean;
}

// 弹窗配置
export interface DialogConfig {
  title: string;
  width?: string;
  top?: string;
  draggable?: boolean;
  closeOnClickModal?: boolean;
  hideFooter?: boolean;
  fullscreenIcon?: boolean;
}

// API 响应类型
export interface ApiResponse<T = any> {
  meta: {
    status: number;
    message?: string;
  };
  data: T;
}

// 运费计算选项
export interface FreightOption {
  template: string;
  unitprice: number;
  freight_fee: number;
  lowerlimit: number;
  upperlimit: number;
  calc_method: number;
}

// 最优运费方案
export interface BestFreightOption {
  option: FreightOption;
  type: 'weight' | 'volume';
  index: number;
  totalPrice: number;
}

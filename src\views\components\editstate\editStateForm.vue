<script setup lang="ts">
import { StateProps, StateItem } from "./types";
import { onMounted, ref, reactive } from "vue";
import { getstatetype } from "@/api/dashboard";
import cyclechart from "@/views/components/cyclechart/index.vue";

const props = withDefaults(defineProps<StateProps>(), {
  stateData: () => ({
    machine_id: 0,
    shift_date: "",
    shift: "",
    state_id: "0",
    start_time: "",
    end_time: "",
    row_id: 0,
    action: ""
  })
});

const newState = ref(props.stateData);

const state_data: any = reactive({ data: [] });

interface States {
  [key: string]: StateItem[];
}

// flatten状态类型数据
function groupByStateType(data: StateItem[]): Record<number, StateItem[]> {
  const groupedData: Record<number, StateItem[]> = {};
  for (const item of data) {
    const { state_type } = item;
    if (groupedData[state_type]) {
      groupedData[state_type].push(item);
    } else {
      groupedData[state_type] = [item];
    }
  }
  return groupedData;
}

onMounted(() => {
  getstatetype().then((res: { data: any }) => {
    state_data.data = groupByStateType(res.data);
  });
});

const state_text = (id: number) => {
  if (id == 1) return "正常运行状态";
  else if (id == 2) return "小停机状态";
  else if (id == 3) return "长时间故障状态";
  else if (id == 4) return "无计划状态";
};
</script>

<template>
  <el-form>
    <el-form-item label="开始时间" label-width="100px">
      <el-date-picker
        v-model="newState.start_time"
        type="datetime"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        placeholder="填写状态开始时间"
      />
      <div
        style="display: flex; justify-content: space-between; margin-left: 10px"
      ></div>
    </el-form-item>
    <el-form-item
      label="结束时间"
      label-width="100px"
      v-if="props.stateData.action == 'update'"
    >
      <el-date-picker
        v-model="newState.end_time"
        type="datetime"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        placeholder="填写状态开始时间"
      />
    </el-form-item>
    <el-form-item label="当班节拍图" label-width="100px">
      <cyclechart :query_data="props.stateData" />
    </el-form-item>
    <template v-for="(value, key) in state_data.data" :key="key">
      <el-divider>
        {{ state_text(key) }}
      </el-divider>

      <el-radio-group v-model="newState.state_id">
        <el-radio-button
          v-for="item in value"
          :value="item.id"
          size="large"
          :key="item.id"
          >{{ item.state_name }}</el-radio-button
        >
      </el-radio-group>
    </template>
  </el-form>
</template>

<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 10px 0;
}
.el-radio-group {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-self: flex-start;
  .el-radio-button {
    border: solid 1px grey;
    margin: 10px;
  }
}
</style>

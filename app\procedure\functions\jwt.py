
import jwt
from config import config, myEnv
from flask import request
from functools import wraps
from app.public.functions import responseError, responseExpire


class AuthService:
    def create_jwt_token(self, user, expiration) -> str:
        """
        创建包含用户信息和权限的JWT token
        """
        from datetime import datetime
        import time

        # 如果expiration是datetime对象，转换为时间戳
        if isinstance(expiration, datetime):
            expiration = int(expiration.timestamp())
        # 如果expiration是timedelta，计算到期时间戳
        elif not isinstance(expiration, int):
            expiration = int(time.time()) + expiration

        payload = {
            'eid': user.eid,
            'name': user.email,
            'exp': expiration
        }
        print(f"生成Token payload: {payload}")
        token = jwt.encode(payload, config[myEnv].SECRET_KEY, algorithm='HS256')
        print(f"生成的Token: {token}")
        return token

    def verify_token(self, token: str) -> dict:
        """
        验证JWT token
        """
        try:
            # 检查token格式
            if not token or len(token.split('.')) != 3:
                raise Exception('无效的Token格式')
            payload = jwt.decode(
                token,
                config[myEnv].SECRET_KEY,
                algorithms=['HS256'],
                options={'verify_exp': True}  # 确保验证过期时间
            )
            return payload
        except Exception:
            raise Exception('Token无效或过期，请在主页重新登录')


def login_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return responseError('未提供有效的token')

        token = auth_header[7:]  # 去掉"Bearer "前缀
        try:
            auth_service = AuthService()
            payload = auth_service.verify_token(token)
            request.user = payload
        except Exception as e:
            return responseExpire(str(e))
        return f(*args, **kwargs)
    return decorated

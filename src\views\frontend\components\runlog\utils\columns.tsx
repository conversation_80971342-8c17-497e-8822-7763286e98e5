import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";
import { ref, onMounted, reactive } from "vue";
import { watch, toRaw } from "vue";
import { getrunlog } from "@/api/runlog";
import { deleteState } from "@/api/front";
import { useProdStoreHook } from "@/store/modules/prod";
import moment from "moment";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import { changeState } from "@/views/components/editstate/index";
import { formatDuration } from "@/views/functions/shift";
import { start } from "nprogress";

export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);

  watch(
    [
      () => useProdStoreHook().selectedDate,
      () => useProdStoreHook().shift,
      () => useProdStoreHook().machineId
    ],
    (newvalue: any, _) => {
      query_param.selecteddate = newvalue[0];
      query_param.shift = newvalue[1];
      query_param.machine = newvalue[2];
      refreshData();
    }
  );

  const query_param = reactive({
    selecteddate: useProdStoreHook().selectedDate,
    machine: useProdStoreHook().machineId,
    shift: useProdStoreHook().shift
  });

  const refreshData = () => {
    loading.value = true;
    getrunlog(toRaw(query_param)).then((res: { data: any }) => {
      dataList.value = res.data;
      loading.value = false;
    });
  };

  onMounted(() => {
    refreshData();
  });

  function autoCheckState() {
    ElMessageBox.confirm(
      `确认自动检查生产状态吗，有可能调整运行记录表中的状态，请确认?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        console.log("自动检查生产状态");
      })
      .catch(() => {
        console.log("取消操作");
      });
  }

  function onDeleteConfirm(row) {
    ElMessageBox.confirm(
      `确认删除该状态吗，删除时将调整相邻状态时间，请确认?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        deleteState({
          id: row.id
        }).then((res: any) => {
          if (res.meta.status == 201) {
            refreshData();
          }
          message(res.meta.msg, {
            customClass: "el",
            type: res.meta.status == 201 ? "success" : "error",
            duration: 2000
          });
        });
      })
      .catch(() => {
        console.log("取消操作");
      });
  }

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载不良记录数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  const adaptiveConfig: AdaptiveConfig = {
    offsetBottom: 20,
    fixHeader: true
  };
  const columns: TableColumnList = [
    { label: "ID", prop: "id", minWidth: 30 },
    {
      label: "开始时间",
      prop: "start_time",
      minWidth: 50,
      formatter(row) {
        return moment(row.start_time).isValid()
          ? moment(row.start_time).format("YYYY-MM-DD HH:mm:ss")
          : "";
      }
    },
    {
      label: "结束时间",
      prop: "end_time",
      minWidth: 50,
      formatter(row) {
        return moment(row.end_time).isValid()
          ? moment(row.end_time).format("YYYY-MM-DD HH:mm:ss")
          : "";
      }
    },
    {
      label: "状态",
      prop: "machine_state",
      minWidth: 30,
      cellRenderer: ({ row }) => {
        return (
          <el-tag
            type={
              row.machine_state == 1
                ? "success"
                : row.machine_state == 2
                  ? "warning"
                  : row.machine_state == 3
                    ? "danger"
                    : "info"
            }
          >
            {row.machine_state == 1
              ? "正常运行"
              : row.machine_state == 2
                ? "小停机"
                : row.machine_state == 3
                  ? "故障"
                  : row.machine_state == 4
                    ? "无计划"
                    : "未知状态"}
          </el-tag>
        );
      }
    },
    { label: "状态描述", prop: "machine_state_des_str", minWidth: 40 },
    {
      label: "持续时间",
      prop: "unit",
      minWidth: 40,
      formatter(row) {
        let starttime = moment(row.start_time);
        let endtime = moment(row.end_time);
        if (starttime.isValid() && endtime.isValid()) {
          return formatDuration(endtime.diff(starttime, "seconds"));
        } else {
          return "";
        }
      }
    },
    {
      label: "操作",
      prop: "",
      fixed: "right",
      minWidth: 60,
      cellRenderer: ({ row }) => {
        return (
          <div class="ops">
            <el-button
              v-show={row.confirm_state != 1}
              type="danger"
              onClick={() => onDeleteConfirm(row)}
            >
              <el-icon size="20">
                <Delete />
              </el-icon>
            </el-button>
            <el-button
              v-show={row.confirm_state != 1}
              type="warning"
              onClick={() =>
                changeState(
                  useProdStoreHook().machineId,
                  useProdStoreHook().selectedDate,
                  useProdStoreHook().shift,
                  row.machine_state_des,
                  row.start_time,
                  row.end_time,
                  row.id,
                  "update",
                  () => {
                    refreshData();
                  }
                )
              }
            >
              <el-icon size="20">
                <Edit />
              </el-icon>
            </el-button>
          </div>
        );
      }
    }
  ];

  return {
    changeState,
    autoCheckState,
    dataList,
    columns,
    loadingConfig,
    adaptiveConfig,
    loading,
    refreshData
  };
}

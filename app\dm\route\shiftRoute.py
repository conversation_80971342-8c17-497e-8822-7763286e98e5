import random
import requests
from config import config, env
import hashlib
from app.dm.schemas import shifts_schema
import traceback
from flask import Blueprint, request
from app.dm.model.models_dm import Issuelog, Lineinfo, Palletinfo, Planinfo, Probleminfo, Restinfo, Scaninfo, Scanpack, Shiftinfo, Scansub, Routing,Probleminfooee
from extensions import db
from sqlalchemy import desc, func, or_, not_,and_
import datetime
from app.public.functions import responseDelete, responseError, responseGet, responsePost
from app.dm.functions import login_required, getLinelist, getReqQtyByShift, getRouting, dmIssueSubmit, dmdelSuggest, dmIssueChange, \
    getReqQty, getSKUlist, getShiftissues, getFinishtime, getReplenishhour, getRestArr,oeeArr,epeiadjust


api = Blueprint('dm/shiftAPI', __name__)


@ api.route("/editShift", methods=["POST"])  # 修改班次信息
@login_required
def editShift():
    res = request.json
    Id = res.get('Id')
    shifttype = res.get('shifttype')
    starttime = res.get('starttime')
    finishtime = res.get('finishtime')
    linename = res.get('linename')
    lineleader = res.get('lineleader')
    team = res.get('team')
    headcount = res.get('headcount')
    routing = res.get('routing')
    plant = res.get('plant')
    line = db.session.query(Lineinfo).filter(Lineinfo.linename == linename).filter(
        Lineinfo.plant == plant).filter(Lineinfo.isactive == 1).first()
    if not line:
        return responseError(plant+'工厂没有该产线'+linename+',或该产线状态未激活，请确认或联系管理员')
    try:
        db.session.query(Shiftinfo).filter(Shiftinfo.Id == Id).update({
            'shifttype': shifttype,
            'starttime': starttime,
            'finishtime': finishtime,
            'linename': linename,
            'lineleader': lineleader,
            'team': team,
            'headcount': headcount,
            'routing': routing
        })
        db.session.commit()
        return responsePost('修改成功')
    except Exception:
        db.session.rollback()
        return responseError('修改失败，请联系管理员')


@ api.route("/closeShift", methods=["POST"])  # 关闭班次
@login_required
def closeShift():
    res = request.json
    id = res.get('shiftid')  # 班次号
    lineleader = res.get('lineleader')  # 关闭班次的人会记入lineleader一栏
    finishtime = res.get('finishtime')  # 关闭时间
    planList = res.get('planList')  # 计划列表
    team = res.get('team')  # 操作员工
    try:
        sft = db.session.query(Shiftinfo).filter(Shiftinfo.Id == id).first()
        shifttype = sft.shifttype
        linename = sft.linename
        sft.finishtime = finishtime
        shiftdate = datetime.datetime.strftime(sft.starttime, '%Y-%m-%d')
        allshts = db.session.query(Shiftinfo).filter(Shiftinfo.linename == linename).filter(
            Shiftinfo.shifttype == shifttype).filter(func.date(Shiftinfo.starttime) == shiftdate).all()
        for s in allshts:
            s.lineleader = lineleader
            s.team = team
        for p in planList:    # 对于每一个plan，如果达到需求，关闭时status为1，未达到为2，如果没有计划，则新建个和实际数量一样的计划，status为3，2和3均判断为未达成
            pid = p['planid']
            sku = p['scansku']
            planqty = p['planqty']
            scanqty = p['scanqty']
            if pid:
                status = 1
                if planqty > scanqty:
                    status = 2
                db.session.query(Planinfo).filter(
                    Planinfo.Id == pid).update({'status': status})
            else:
                newplan = Planinfo(mo='automo', shifttype=shifttype, status=3,
                                   linename=linename, sku=sku, sequence=99, planner='emdi', qty=scanqty, plandate=finishtime)
                db.session.add(newplan)
        lineinfo = db.session.query(Lineinfo).filter(
            Lineinfo.linename == linename).first()
        if lineinfo.currentshift == id:
            lineinfo.currentshift = 0
        db.session.commit()
        return responsePost('关闭成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('关闭班次失败，请联系管理员')


@ api.route("/closeShiftwithoutplan", methods=["post"])  # 获得当天当班第一个班次的开始时间以及问题列表
def closeShiftwithoutplan():
    res = request.json
    id = res.get('shiftid')  # 班次号
    lineleader = 'system'  # 关闭班次的人会记入lineleader一栏
    finishtime=datetime.datetime.now()
    lastitem=db.session.query(Scaninfo).filter(Scaninfo.shiftid==id).order_by(desc(Scaninfo.scantime)).first()
    if lastitem:
        finishtime=lastitem.scantime
    try:
        sft = db.session.query(Shiftinfo).filter(Shiftinfo.Id == id).first()
        linename = sft.linename
        sft.finishtime = finishtime
        sft.lineleader=lineleader
        lineinfo = db.session.query(Lineinfo).filter(
            Lineinfo.linename == linename).first()
        if lineinfo.currentshift == id:
            lineinfo.currentshift = 0
        db.session.commit()
        return responsePost('关闭成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('关闭班次失败，请联系管理员')
    
@ api.route("/closeShiftmock", methods=["post"])  # 获得当天当班第一个班次的开始时间以及问题列表
@login_required
def closeShiftmock():
    res = request.json
    shifttype = res.get('shifttype')
    shiftdate = res.get('shiftdate')
    linename = res.get('linename')
    # starttime = res.get('starttime')
    lineleader = res.get('lineleader')
    mockList = res.get('mockList')
    planList = res.get('planList')  # 计划列表
    team = res.get('team')
    try:
        for p in mockList:
            routing = getRouting(p['sku'], linename, p['headcount'])
            sft = Shiftinfo(shifttype=shifttype, starttime=p['starttime'], finishtime=p['finishtime'], linename=linename, sku=p['sku'],
                            lineleader=lineleader, team=team, headcount=p['headcount'], routing=routing, scantemplate='简单模板')
            db.session.add(sft)
            db.session.flush()
            sn = p['sku']+hashlib.md5((datetime.datetime.now().strftime(
                '%Y-%m-%d %H:%M:%S')+str(random.randint(1, 10000))).encode()).hexdigest()[9:18]
            sftscan = Scaninfo(
                shiftid=sft.Id, sku=p['sku'], scanqty=p['qty'], scantime=p['starttime'], sn=sn)
            epeiadjust(linename,p['sku'],p['qty'])
            db.session.add(sftscan)
            # starttime = p['finishtime']
        for p in planList:    # 对于每一个plan，如果达到需求，关闭时status为1，未达到为2，如果没有计划，则新建个和实际数量一样的计划，status为3，2和3均判断为未达成
            pid = p['planid']
            sku = p['scansku']
            planqty = p['planqty']
            scanqty = p['scanqty']
            if pid:
                status = 1
                if planqty > scanqty:
                    status = 2
                db.session.query(Planinfo).filter(
                    Planinfo.Id == pid).update({'status': status})
            else:
                newplan = Planinfo(mo='automo', shifttype=shifttype, status=3,
                                   linename=linename, sku=sku, sequence=99, planner='emdi', qty=scanqty, plandate=shiftdate)
                db.session.add(newplan)
        db.session.commit()
        return responsePost('关闭成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('关闭班次失败，请联系管理员')


@ api.route("/submitIssue", methods=["POST"])  # 提交问题
@ login_required
def submitIssue():
    res = request.json
    check = dmIssueSubmit(res)
    if check:
        return responsePost('插入成功')
    else:
        return responseError('插入失败，请联系管理员')


@ api.route("/changeIssue", methods=["POST"])  # 提交问题
@ login_required
def changeIssue():
    res = request.json
    print(222222222, res.get('qty'), res.get('issuemin'))
    check = dmIssueChange(res)
    if check:
        return responsePost('插入成功')
    else:
        return responseError('插入失败，请联系管理员')


@ api.route("/confirmDel", methods=["delete"])  # 根据ID号删除提交的问题
@ login_required
def confirmDel():
    res = request.args
    id = res.get('id')
    try:
        db.session.query(Issuelog).filter(Issuelog.Id == id).delete()
        db.session.commit()
        dmdelSuggest(id)
        return responseDelete('删除成功')
    except Exception:
        db.session.rollback()
        return responseError('删除失败，请联系管理员')


def getCanmes(linename, plandate):
    if linename == 'CANMaker':
        try:
            r = requests.get(
                config[env].api_url+"/public/canmes/getProduction", {'plandate': plandate})
            vdata = r.json()
            if vdata['meta']['status'] == 200:
                return vdata['data']
        except Exception:
            traceback.print_exc()
    return []


@ api.route("/startMockdate", methods=["post"])  # 获得当天当班第一个班次的开始时间以及问题列表
@ login_required
def startMockdate():
    res = request.json
    shifttype = res.get('shifttype')
    shiftdate = res.get('shiftdate')
    linename = res.get('linename')
    starttime = res.get('starttime')
    mockList = res.get('mockList')
    linegroup = db.session.query(Lineinfo.linegroup).filter(
        Lineinfo.linename == linename).first()[0]
    restList = db.session.query(Restinfo).all()
    outArr = []
    planDic = {}
    plans = db.session.query(Planinfo).filter(Planinfo.shifttype == shifttype).filter(
        Planinfo.plandate == shiftdate).filter(Planinfo.linename == linename).group_by(Planinfo.sku).all()
    for pp in plans:
        planDic[pp.sku] = {
            'planid': pp.Id,
            'plansku': pp.sku,
            'scansku': '',
            'planqty': pp.qty,
            'scanqty': 0,
            'reqqty': 0
        }
    for p in mockList:
        routing = getRouting(p['sku'], linename, p['headcount'])
        # st = datetime.datetime.strptime(starttime, '%Y-%m-%d %H:%M:%S')
        st = datetime.datetime.strptime(p['starttime'], '%Y-%m-%d %H:%M:%S')
        ft = datetime.datetime.strptime(p['finishtime'], '%Y-%m-%d %H:%M:%S')
        reqqty = round(
            getReqQty(shiftdate, p['headcount'], linegroup, st, ft, restList, routing), 0)
        outArr.append({
            'sku': p['sku'],
            'qty': p['qty'],
            'headcount': p['headcount'],
            'output': reqqty,
            'hourlyrate': round(float(p['headcount'])/float(routing), 0),
            'routing': routing,
            'starttime': st.strftime('%Y-%m-%d %H:%M:%S'),
            'finishtime': p['finishtime']
        })
        if p['sku'] in planDic.keys():
            planDic[p['sku']]['scansku'] = p['sku']
            planDic[p['sku']]['scanqty'] += p['qty']
            planDic[p['sku']]['reqqty'] += reqqty
        else:
            planDic[p['sku']] = {
                'planid': 0,
                'plansku': '',
                'scansku': p['sku'],
                'planqty': 0,
                'scanqty': p['qty'],
                'reqqty': reqqty
            }
        starttime = p['finishtime']
    return responsePost('获取成功', {'mockList': outArr, 'planList': list(planDic.values())})


@ api.route("/startMock", methods=["post"])  # 获得当天当班第一个班次的开始时间以及问题列表
@ login_required
def startMock():
    res = request.json
    shifttype = res.get('shifttype')
    shiftdate = res.get('shiftdate')
    linename = res.get('linename')
    starttime = res.get('starttime')
    mockList = res.get('mockList')
    linegroup = db.session.query(Lineinfo.linegroup).filter(
        Lineinfo.linename == linename).first()[0]
    restList = db.session.query(Restinfo).all()
    outArr = []
    planDic = {}
    plans = db.session.query(Planinfo).filter(Planinfo.shifttype == shifttype).filter(
        Planinfo.plandate == shiftdate).filter(Planinfo.linename == linename).group_by(Planinfo.sku).all()
    for pp in plans:
        planDic[pp.sku] = {
            'planid': pp.Id,
            'plansku': pp.sku,
            'scansku': '',
            'planqty': pp.qty,
            'scanqty': 0,
            'reqqty': 0
        }
    lastfinish = mockList[len(mockList)-1]['finishtime']
    # print(*************, lastfinish)
    for p in mockList:
        routing = getRouting(p['sku'], linename, p['headcount'])
        # st = datetime.datetime.strptime(starttime, '%Y-%m-%d %H:%M:%S')
        st= datetime.datetime.strptime(p['starttime'], '%Y-%m-%d %H:%M:%S')
        # ft = datetime.datetime.strptime(p['finishtime'], '%Y-%m-%d %H:%M:%S')
        ft = getFinishtime(
            shiftdate, st, p['headcount'], linegroup, restList, routing, p['qty'])
        reqqty = round(
            getReqQty(shiftdate, p['headcount'], linegroup, st, ft, restList, routing), 0)
        outArr.append({
            'sku': p['sku'],
            'qty': p['qty'],
            'headcount': p['headcount'],
            'output': reqqty if reqqty > 0 else 0,
            'hourlyrate': round(float(p['headcount'])/float(routing), 0),
            'routing': routing,
            'starttime': st.strftime('%Y-%m-%d %H:%M:%S'),
            'finishtime': ft.strftime('%Y-%m-%d %H:%M:%S')
        })
        if p['sku'] in planDic.keys():
            planDic[p['sku']]['scansku'] = p['sku']
            planDic[p['sku']]['scanqty'] += p['qty']
            planDic[p['sku']]['reqqty'] += reqqty
        else:
            planDic[p['sku']] = {
                'planid': 0,
                'plansku': '',
                'scansku': p['sku'],
                'planqty': 0,
                'scanqty': p['qty'],
                'reqqty': reqqty
            }
        starttime = ft.strftime('%Y-%m-%d %H:%M:%S')
    outArr[len(outArr)-1]['finishtime'] = lastfinish
    lastoutput = round(getReqQty(shiftdate, outArr[len(outArr)-1]['headcount'], linegroup,
                                 st, datetime.datetime.strptime(lastfinish, '%Y-%m-%d %H:%M:%S'), restList, routing), 0)
    outArr[len(outArr)-1]['output'] = lastoutput
    planDic[outArr[len(outArr)-1]['sku']]['reqqty'] = lastoutput
    return responsePost('获取成功', {'mockList': outArr, 'planList': list(planDic.values())})


@ api.route("/getMockList", methods=["get"])  # 获得当天当班第一个班次的开始时间以及问题列表
@ login_required
def getMockList():
    res = request.args
    shifttype = res.get('shifttype')
    shiftdate = res.get('shiftdate')
    tmday= datetime.datetime.strptime(shiftdate,"%Y-%m-%d")+datetime.timedelta(days=1)
    linename = res.get('linename')
    linegroup = db.session.query(Lineinfo.linegroup).filter(
        Lineinfo.linename == linename).first()[0]
    restList = db.session.query(Restinfo).all()
    if linegroup in ['Meltblown']:
        if shifttype == '白班':
            starttime = shiftdate+' 08:00:00'
            finishtime = shiftdate+' 16:00:00'
        elif shifttype == '夜班':
            starttime = shiftdate+' 00:00:00'
            finishtime = shiftdate+' 08:00:00'
        elif shifttype == '中班':
            starttime = shiftdate+' 16:00:00'
            finishtime = (datetime.datetime.strptime(shiftdate, '%Y-%m-%d') +
                          datetime.timedelta(days=1)).strftime('%Y-%m-%d')+' 00:00:00'
    elif linegroup in ['GAC', 'RFC', 'Wound']:
        if shifttype == '白班':
            starttime = shiftdate+' 08:00:00'
            finishtime = shiftdate+' 18:30:00'
        elif shifttype == '夜班':
            starttime = shiftdate+' 20:00:00'
            finishtime = shiftdate+' 08:00:00'
        elif shifttype == '中班':
            starttime = shiftdate+' 16:00:00'
            finishtime = (datetime.datetime.strptime(shiftdate, '%Y-%m-%d') +
                          datetime.timedelta(days=1)).strftime('%Y-%m-%d')+' 00:00:00'
    else:
        if shifttype == '白班':
            starttime = shiftdate+' 08:00:00'
            finishtime = shiftdate+' 20:00:00'
        elif shifttype == '夜班':
            starttime = shiftdate+' 20:00:00'
            finishtime = (datetime.datetime.strptime(shiftdate, '%Y-%m-%d') +
                          datetime.timedelta(days=1)).strftime('%Y-%m-%d')+' 08:00:00'
        elif shifttype == '中班':
            starttime = shiftdate+' 16:00:00'
            finishtime = (datetime.datetime.strptime(shiftdate, '%Y-%m-%d') +
                          datetime.timedelta(days=1)).strftime('%Y-%m-%d')+' 00:00:00'
    plans = db.session.query(Planinfo).join(Routing, Planinfo.sku == Routing.sku).filter(Planinfo.shifttype == shifttype).filter(
        Planinfo.plandate == shiftdate).filter(Planinfo.linename == linename).order_by(Planinfo.sequence).all()
    finishedsku=db.session.query(Shiftinfo).filter(or_(and_(func.date(Shiftinfo.starttime)==shiftdate,Shiftinfo.shifttype!='夜班'),and_(func.date(Shiftinfo.starttime)==tmday,Shiftinfo.shifttype=='夜班'))).filter(
        Shiftinfo.linename==linename).filter(Shiftinfo.shifttype==shifttype).filter(not_(Shiftinfo.finishtime.is_(None))).all()
    fskuArr=[]
    for ss in finishedsku:
        fskuArr.append(ss.sku)
    outArr = []
    planArr = []
    headDic = getHeadcount(plans, starttime, finishtime,
                           linename, linegroup, shiftdate)
    st = datetime.datetime.strptime(starttime, '%Y-%m-%d %H:%M:%S')
    for p in plans:
        if p.sku not in fskuArr:
            routing = float(headDic[p.sku]['routing'])
            ft = getFinishtime(shiftdate, st, headDic[p.sku]['headcount'], linegroup,
                            restList, routing, p.qty)
            if ft > datetime.datetime.strptime(finishtime, '%Y-%m-%d %H:%M:%S'):
                ft = datetime.datetime.strptime(finishtime, '%Y-%m-%d %H:%M:%S')
            reqqty = round(getReqQty(shiftdate, headDic[p.sku]['headcount'], linegroup,
                                    st, ft, restList, routing), 0)
            outArr.append({
                'sku': p.sku,
                'qty': p.qty,
                'headcount': headDic[p.sku]['headcount'],
                'output': reqqty,
                'hourlyrate': round(headDic[p.sku]['headcount']/routing, 0),
                'routing': routing,
                'starttime': st.strftime('%Y-%m-%d %H:%M:%S'),
                'finishtime': ft.strftime('%Y-%m-%d %H:%M:%S')
            })
            planArr.append({
                'planid': p.Id,
                'plansku': p.sku,
                'scansku': p.sku,
                'planqty': p.qty,
                'scanqty': p.qty,
                'reqqty': reqqty
            })
            st = ft
    print(1111,outArr,planArr)
    if len(outArr)>0:
        outArr[len(outArr)-1]['finishtime'] = finishtime
        outArr[len(outArr)-1]['output'] = round(getReqQty(shiftdate, outArr[len(outArr)-1]['headcount'], linegroup,
                        datetime.datetime.strptime(outArr[len(outArr)-1]['starttime'], '%Y-%m-%d %H:%M:%S'), 
                        datetime.datetime.strptime(outArr[len(outArr)-1]['finishtime'], '%Y-%m-%d %H:%M:%S'), restList, routing), 0)
    for plan in planArr:
        if plan['scansku'] == outArr[len(outArr)-1]['sku']:
            plan['reqqty'] = outArr[len(outArr)-1]['output']
            break
    shiftIssues = getShiftissues(shiftdate, shifttype, linename)
    lt = ''
    lastteam = db.session.query(Shiftinfo).filter(not_(Shiftinfo.team.is_(None))).filter(
        Shiftinfo.linename == linename).order_by(desc(Shiftinfo.starttime)).limit(1).first()
    if lastteam:
        lt = lastteam.team
    can = getCanmes(linename, shiftdate)
    return responseGet('获取成功', {'mockList': outArr, 'planList': planArr, 'starttime': starttime, 'skuList': getSKUlist(), 'shiftIssues': shiftIssues, 'team': lt, 'can': can})


def getHeadcount(plans, starttime, finishtime, linename, linegroup, shiftdate):
    stime = datetime.datetime.strptime(starttime, '%Y-%m-%d %H:%M:%S')
    ftime = datetime.datetime.strptime(
        finishtime, '%Y-%m-%d %H:%M:%S')
    hours = (ftime-stime).seconds/3600
    rests = 1
    restList = db.session.query(Restinfo).all()
    rests = getRestArr(shiftdate, restList, linegroup)
    replenish = getReplenishhour(stime.timestamp(), ftime.timestamp(), rests)
    dic = {}
    tttime = 0
    for p in plans:
        routing = getRouting(p.sku, linename, 1)
        asumetime = routing*p.qty
        dic[p.sku] = {
            'routing': routing,
            'qty': p.qty,
            'asumetime': asumetime,
            'headcount': 1
        }
        tttime += asumetime
    for k, v in dic.items():
        if linegroup in ['GAC', 'RFC', 'Wound']:
            v['headcount'] = round(float(tttime)/(float(hours)-replenish), 1)
    return dic


@ api.route("/getMockissues", methods=["get"])  # 获得当天当班第一个班次的开始时间以及问题列表
@ login_required
def getMockissues():
    res = request.args
    shifttype = res.get('shifttype')
    shiftdate = res.get('shiftdate')
    linename = res.get('linename')
    shiftIssues = getShiftissues(shiftdate, shifttype, linename)
    return responseGet('获取成功', {'shiftIssues': shiftIssues})


@ api.route("/getShiftstart", methods=["get"])  # 获得当天当班第一个班次的开始时间以及问题列表
@ login_required
def getShiftstart():
    res = request.args
    shifttype = res.get('shifttype')
    shiftdate = res.get('shiftdate')
    linename = res.get('linename')
    linegroup=db.session.query(Lineinfo.linegroup).filter(Lineinfo.linename==linename).first()[0]
    shifts = db.session.query(Shiftinfo).filter(
        func.date_format(Shiftinfo.starttime, '%Y-%m-%d') == shiftdate).filter(Shiftinfo.shifttype == shifttype).filter(
        Shiftinfo.linename == linename).all()
    shiftid = 0
    start = datetime.datetime.strptime('9999-12-31', '%Y-%m-%d')
    for s in shifts:
        if start > s.starttime:
            start = s.starttime
        if not s.finishtime:
            shiftid = s.Id
    pdic = {
        '质量': [],
        '效率': [],
        '交货': []
    }
    linetype='labor'
    if linegroup in oeeArr:
        problemtypes = db.session.query(Probleminfooee).filter(Probleminfooee.linename==linegroup).all()
        linetype='machine'
    else:
        problemtypes = db.session.query(Probleminfo).outerjoin(Lineinfo, Probleminfo.linename ==Lineinfo.linegroup).filter(
        or_(Lineinfo.linename == linename, Probleminfo.linename == 'ALL')).all()
    for p in problemtypes:
        sqdctype=p.sqdctype
        if sqdctype=='质量不良':
            sqdctype='质量'
        elif sqdctype=='效率损失':
            sqdctype='效率'
        elif sqdctype=='停机损失':
            sqdctype='交货'
        if sqdctype in pdic.keys():
            pdic[sqdctype].append(p.problemtype)
    return responseGet('获取成功', {'shiftStart': datetime.datetime.strftime(start, '%Y-%m-%d %H:%M:%S'), 'shiftid': shiftid,  'problemList': pdic,'linetype':linetype})


@ api.route("/getActiveShifts", methods=["get"])  # 获取某日某班次某产线的所有班次信息，问题和计划
@ login_required
def getActiveShifts():
    res = request.args
    shifttype = res.get('shifttype')
    shiftdate = res.get('shiftdate')
    linename = res.get('linename')
    closeTime = res.get('closeTime')
    lt = ''
    lastteam = db.session.query(Shiftinfo).filter(not_(Shiftinfo.team.is_(None))).filter(
        Shiftinfo.linename == linename).order_by(desc(Shiftinfo.starttime)).limit(1).first()
    if lastteam:
        lt = lastteam.team
    rests = []
    resttimes = db.session.query(Restinfo).outerjoin(
        Lineinfo, Restinfo.linegroup == Lineinfo.linegroup).filter(Lineinfo.linename == linename).all()
    if len(resttimes) == 0:
        resttimes = db.session.query(Restinfo).filter(
            Restinfo.linegroup == 'all').all()
    for r in resttimes:
        mydt = shiftdate
        if str(r.resttime) < '08:00:00':
            mydt = (datetime.datetime.strptime(shiftdate, '%Y-%m-%d') +
                    datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        rests.append({
            'reststart': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S'),
            'restfinish': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S')+datetime.timedelta(minutes=r.restmin)
        })
    scans = db.session.query(Scaninfo.shiftid, Shiftinfo.headcount, Shiftinfo.sku, func.sum(Scaninfo.scanqty).label('scanqty'), Shiftinfo.starttime, Shiftinfo.finishtime
                             ).outerjoin(Scaninfo, Scaninfo.shiftid == Shiftinfo.Id).filter(
        func.date_format(Shiftinfo.starttime, '%Y-%m-%d') == shiftdate).filter(Shiftinfo.shifttype == shifttype).filter(
        Shiftinfo.linename == linename).group_by(Scaninfo.shiftid).all()
    scanDic = {}
    print('ssssssssssss', scans)
    for s in scans:
        if not s.finishtime:
            myFinish = datetime.datetime.strptime(
                closeTime, '%Y-%m-%d %H:%M:%S')
        else:
            myFinish = s.finishtime
        rt = getRouting(s.sku, linename, s.headcount)
        reqQty = getReqQtyByShift(
            s.headcount, s.starttime, myFinish, rests, rt)
        print('haha', reqQty, s.headcount, s.starttime, myFinish, rests, rt)
        if s.sku not in scanDic.keys():
            scanDic[s.sku] = {
                'sku': s.sku,
                'scanqty': s.scanqty if s.scanqty else 0,
                'reqqty': reqQty
            }
        else:
            scanDic[s.sku]['scanqty'] = scanDic[s.sku]['scanqty']+s.scanqty
            scanDic[s.sku]['reqqty'] = scanDic[s.sku]['reqqty']+reqQty
    plans = db.session.query(Planinfo).filter(Planinfo.shifttype == shifttype).filter(
        Planinfo.plandate == shiftdate).filter(Planinfo.linename == linename).group_by(Planinfo.sku).all()
    planDic = {}
    for p in plans:
        planDic[p.sku] = {
            'planid': p.Id,
            'sku': p.sku,
            'qty': p.qty
        }
    outArr = []
    for item in list(planDic.values()):
        if item['sku'] in scanDic.keys():
            outArr.append({
                'planid': item['planid'],
                'plansku': item['sku'],
                'scansku': item['sku'],
                'planqty': item['qty'],
                'scanqty': scanDic[item['sku']]['scanqty'],
                'reqqty': scanDic[item['sku']]['reqqty']
            })
        else:
            outArr.append({
                'planid': item['planid'],
                'plansku': item['sku'],
                'scansku': '',
                'planqty': item['qty'],
                'scanqty': 0,
                'reqqty': 0
            })
    for item in list(scanDic.values()):
        if item['sku'] not in planDic.keys():
            outArr.append({
                'planid': 0,
                'plansku': '',
                'scansku': item['sku'],
                'planqty': 0,
                'scanqty': item['scanqty'],
                'reqqty': item['reqqty']
            })
    dic = getShiftissues(shiftdate, shifttype, linename)
    return responseGet('成功', {'planList': outArr, 'shiftIssues': dic, 'team': lt})


# 删除某个序列号，并删除关联箱子和托盘的料号，同时箱子和托盘变成打开状态
@ api.route("/delSN", methods=["delete"])
@ login_required
def delSN():
    res = request.args
    sn = res.get('sn')
    # scanqty = int(res.get('scanqty'))
    try:
        db.session.query(Scaninfo).filter(Scaninfo.sn == sn).delete()
        db.session.query(Scansub).filter(Scansub.sn == sn).delete()
        pack = db.session.query(Scanpack).filter(Scanpack.sn == sn).first()
        db.session.query(Scanpack).filter(Scanpack.sn == sn).delete()
        if pack:
            pallet = pack.palletsn
            box = pack.boxsn
            if pallet:
                mypallet = db.session.query(Palletinfo).filter(
                    Palletinfo.palletsn == pallet).first()
                mypallet.endtime = None
                # mypallet.setqty = mypallet.setqty-scanqty
            if box:
                mybox = db.session.query(Palletinfo).filter(
                    Palletinfo.palletsn == box).first()
                mybox.endtime = None
                # mybox.setqty = mybox.setqty-scanqty
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('删除失败，请联系管理员')
    return responseDelete('删除成功')


@ api.route("/getLinelist", methods=["get"])  # 过的产线组树状结构
@ login_required
def getLinelist1():
    res = request.args
    plant = res.get('plant')
    lineList = getLinelist(plant)
    return responseGet('成功', {'lineList': lineList})


@ api.route("/getShiftInfo", methods=["get"])  # 根据班次，日期，产线和搜索获取所有的扫描信息
@ login_required
def getShiftInfo():
    res = request.args
    stime = res.get('stime')
    etime = res.get('etime')
    linename = res.get('linename')
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    plant = res.get('plant')
    shifts = db.session.query(Shiftinfo).order_by(desc(Shiftinfo.starttime), Shiftinfo.linename, Shiftinfo.shifttype).join(
        Lineinfo, Lineinfo.linename == Shiftinfo.linename).filter(Lineinfo.plant == plant).filter(
        Shiftinfo.starttime.between(stime, etime))
    total = db.session.query(func.count(Shiftinfo.Id)).join(
        Lineinfo, Lineinfo.linename == Shiftinfo.linename).filter(Lineinfo.plant == plant).filter(
        Shiftinfo.starttime.between(stime, etime))
    if linename:
        shifts = shifts.filter(Shiftinfo.linename == linename)
        total = total.filter(Shiftinfo.linename == linename)
    shifts = shifts.paginate(pagenum, pagesize, error_out=False).items
    total = total.scalar()
    shiftinfo = shifts_schema.dump(shifts)
    return responseGet('success', {'shifts': shiftinfo, 'total': total})


@ api.route("/getShiftScans", methods=["get"])  # 根据班次，日期，产线和搜索获取所有的扫描信息
@ login_required
def getShiftScans():
    res = request.args
    shifttype = res.get('shifttype')
    shiftdate = res.get('shiftdate')
    keywords = res.get('query')
    linename = res.get('linename')
    shiftids = db.session.query(Shiftinfo).filter(Shiftinfo.shifttype == shifttype).filter(
        func.date_format(Shiftinfo.starttime, '%Y-%m-%d') == shiftdate).all()

    sids = []
    for s in shiftids:
        sids.append(s.Id)
    sns = db.session.query(Scaninfo.sn, Scaninfo.scanqty, Scaninfo.scantime, Shiftinfo.linename, Scaninfo.sku,
                           Scansub.subname, Scansub.subsn).outerjoin(Shiftinfo, Scaninfo.shiftid == Shiftinfo.Id).outerjoin(Scansub, Scaninfo.sn == Scansub.sn).filter(
        Scaninfo.shiftid.in_(sids)).order_by(desc(Scaninfo.sn))
    if keywords:
        sns = sns.filter(or_(Scaninfo.sku.like('%{0}%'.format(
            keywords)), Scaninfo.sn.like('%{0}%'.format(keywords))))
    if linename:
        sns = sns.filter(Shiftinfo.linename == linename)
    sns = sns.all()
    scans = {}
    for ss in sns:
        if not (ss.sn in scans.keys()):
            scans[ss.sn] = {
                'sn': ss.sn,
                'sku': ss.sku,
                'scantime': datetime.datetime.strftime(ss.scantime, '%Y-%m-%d %H:%M:%S'),
                'scanqty': ss.scanqty,
                'children': []
            }
        if ss.subsn:
            scans[ss.sn]['children'].append({
                'subname': ss.subname,
                'subsn': ss.subsn
            })
    snlist = list(scans.values())
    return responseGet('成功', {'sns': snlist, 'total': len(snlist)})


@ api.route("/getLines", methods=["get"])  # 获取所有待关闭的产线清单，按区域检索
@ login_required
def getLines():
    res = request.args
    area = res.get('area')
    lines = db.session.query(Lineinfo.linename, Shiftinfo.Id, Lineinfo.mditype, Shiftinfo.shifttype, Shiftinfo.starttime).join(
        Shiftinfo, Lineinfo.linename == Shiftinfo.linename).filter(Shiftinfo.finishtime.is_(None)).filter(
        Lineinfo.isactive == 1).filter(Lineinfo.mditype == 0).order_by(Shiftinfo.starttime)
    dic = {}
    simplelines = db.session.query(Lineinfo.linename, Lineinfo.mditype).filter(
        Lineinfo.isactive == 1, Lineinfo.mditype == 1)
    if area:
        lines = lines.filter(Lineinfo.area == area)
        simplelines = simplelines.filter(Lineinfo.area == area)
    lines = lines.all()
    simplelines = simplelines.all()
    for ll in lines:
        scan = db.session.query(Scaninfo).filter(
            Scaninfo.shiftid == ll.Id).order_by(desc(Scaninfo.scantime)).first()
        if scan:
            lastscan = datetime.datetime.strftime(
                scan.scantime, '%Y-%m-%d %H:%M:00') if scan.scantime else ''
        else:
            lastscan = ''
        if ll.linename in dic.keys():
            dic[ll.linename]['children'].append(
                {
                    'shiftid': ll.Id,
                    'shiftdate': datetime.datetime.strftime(ll.starttime, "%Y-%m-%d") if ll.starttime else '',
                    'shifttype': ll.shifttype if ll.shifttype else '',
                    'lastscan': lastscan,
                    'mditype': ll.mditype
                }
            )
        else:
            dic[ll.linename] = {
                'linename': ll.linename,
                'mditype': ll.mditype,
                'children': [{
                    'shiftid': ll.Id,
                    'shiftdate': datetime.datetime.strftime(ll.starttime, "%Y-%m-%d") if ll.starttime else '',
                    'shifttype': ll.shifttype if ll.shifttype else '',
                    'lastscan': lastscan,
                    'mditype': ll.mditype
                }]
            }
    for sl in simplelines:
        plans = db.session.query(Planinfo).filter(Planinfo.status == 0).filter(
            Planinfo.linename == sl.linename).group_by(Planinfo.plandate, Planinfo.shifttype).all()
        for plan in plans:
            newshift = Shiftinfo(shifttype=plan.shifttype, sku=plan.sku, scantemplate='简单模板', starttime=plan.plandate,
                                 linename=sl.linename, lineleader='emdi', team='emdi', headcount=1, routing=0.1)
            db.session.add(newshift)
            db.session.flush()
            if sl.linename in dic.keys():
                # isshift = db.session.query(Shiftinfo).filter(Shiftinfo.shifttype == newshift.shifttype, Shiftinfo.linename == newshift.linename,
                #                                              func.date(Shiftinfo.starttime) == func.date(newshift.starttime)).first()
                # if not isshift:
                dic[sl.linename]['children'].append(
                    {
                        'shiftid': newshift.Id,
                        'shiftdate': datetime.datetime.strftime(newshift.starttime, "%Y-%m-%d") if newshift.starttime else '',
                        'shifttype': newshift.shifttype if newshift.shifttype else '',
                        'lastscan': '',
                        'mditype': sl.mditype
                    }
                )
            else:
                dic[sl.linename] = {
                    'linename': sl.linename,
                    'mditype': sl.mditype,
                    'children': [{
                        'shiftid': newshift.Id,
                        'shiftdate': datetime.datetime.strftime(newshift.starttime, "%Y-%m-%d") if newshift.starttime else '',
                        'shifttype': newshift.shifttype if newshift.shifttype else '',
                        'lastscan': '',
                        'mditype': sl.mditype
                    }]
                }
            # 删除dic的children中shiftdate和shifttype同时重复的数据
            for k, v in dic.items():
                trimDic = {}
                newArr = []
                for vv in v['children']:
                    if vv['shiftdate']+vv['shifttype'] not in trimDic.keys():
                        newArr.append(vv)
                    trimDic[vv['shiftdate']+vv['shifttype']] = 1
                v['children'] = newArr
    return responseGet('成功', {'lines': list(dic.values())})

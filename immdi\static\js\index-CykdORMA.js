var r=Object.defineProperty;var a=Object.getOwnPropertySymbols;var n=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable;var l=(s,t,e)=>t in s?r(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,o=(s,t)=>{for(var e in t||(t={}))n.call(t,e)&&l(s,e,t[e]);if(a)for(var e of a(t))d.call(t,e)&&l(s,e,t[e]);return s};import{d as m,aj as u,b9 as p}from"./index-BnxEuBzx.js";const h=m({name:"ReCol",props:{value:{type:Number,default:24}},render(){const s=this.$attrs,t=this.value;return u(p,o({xs:t,sm:t,md:t,lg:t,xl:t},s),{default:()=>this.$slots.default()})}});export{h as R};

import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export const getstdparam = (querydata?: object) => {
  return http.request(
    "get",
    "http://10.76.1.234:8080/documents/paramAPI/getIMparam",
    {
      params: querydata
    }
  );
};

export const getparammonitor = (querydata?: object) => {
  return http.request("get", baseUrlApi("param/getparam"), {
    params: querydata
  });
};

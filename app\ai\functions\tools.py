import datetime
import calendar

# 日期处理


def last_day_of_month(any_day):  # 获取任意一天的月末日期
    next_month = any_day.replace(day=28) + datetime.timedelta(days=4)
    return next_month - datetime.timedelta(days=next_month.day)


def dateDiffInHours(t1, t2):  # 计算两个日期的小时间隔
    td = t2 - t1
    return td.days * 24 + td.seconds / 3600


def get_month_range(start_day, end_day):  # 返回两个日期见的月份数组，如2021-01~2021-10
    months = (end_day.year - start_day.year) * \
        12 + end_day.month - start_day.month
    month_range = ['%s-%s' % (start_day.year + mon//12, mon % 12+1)
                   for mon in range(start_day.month-1, start_day.month + months)]
    return month_range


# def get_week_range(start_day, end_day):  # 返回两个日期见的周组，如2021-01~2021-10
#     weeks = rrule.rrule(rrule.WEEKLY, dtstart=start_day, until=end_day)
#     days = weeks.between(start_day, end_day)
#     arr = []
#     for d in days:
#         wk = str(d.year)+'-'+str(int(datetime.datetime.strftime(d, '%W'))
#                                  ).zfill(2)+'_'+str(d.month)+'/'+str(d.day)
#         arr.append(wk)
#     return arr


def getMonthFirstDayAndLastDay(year=None, month=None):  # 得到某年某月的第一天和最后一天
    """
    :param year: 年份，默认是本年，可传int或str类型
    :param month: 月份，默认是本月，可传int或str类型
    :return: firstDay: 当月的第一天，datetime.date类型
              lastDay: 当月的最后一天，datetime.date类型
    """
    if year:
        year = int(year)
    else:
        year = datetime.date.today().year

    if month:
        month = int(month)
    else:
        month = datetime.date.today().month

    # 获取当月第一天的星期和当月的总天数
    firstDayWeekDay, monthRange = calendar.monthrange(year, month)

    # 获取当月的第一天
    firstDay = datetime.date(year=year, month=month, day=1)
    lastDay = datetime.date(year=year, month=month, day=monthRange)

    return firstDay, lastDay

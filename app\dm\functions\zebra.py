from PIL import Image, ImageDraw, ImageFont
import textwrap
from zebrafy import ZebrafyImage,ZebrafyZPL

def extendImg(original_image,h,rotated):
    if rotated:
        new_width = original_image.width + h
        new_image = Image.new("RGB", (new_width, original_image.height), color="white")
        new_image.paste(original_image, (h, 0))
    else:
        new_height = original_image.height + h
        new_image = Image.new("RGB", (original_image.width, new_height), color="white")
        new_image.paste(original_image, (0, 0))
    # new_image.save('F:\\new_img.png', format="PNG")
    return new_image


def create_text_image(text, font_size=40, image_size=(2000, 2000), text_color=(0,0, 0 ),  font_path='msyh.ttc', output_path="text_image.png",rotated=0,fixedH=0):
    # print(333,text,fixedH)
    # 创建一个带有透明背景的图像
    image = Image.new("RGBA", image_size, (255, 255, 255,0))
    # 加载字体
    font = ImageFont.truetype(font_path, font_size) if font_path else ImageFont.load_default()
    # 创建一个绘图对象
    draw = ImageDraw.Draw(image)
    # 计算文本的行高
    line_height = font.getsize("hg")[1]  # 假设所有字符的高度相同
    # 分割文本为多行
    lines = text.split("\n")
    text_x = 0
    text_y = 0
    for line in lines:
        # line_width, line_height = draw.textsize(line, font=font)
        if '<NA>' not in line:  
            draw.text((text_x, text_y), line, font=font, fill=text_color)
            text_y += line_height
    if rotated:
       image=image.rotate(-90)
    # 裁剪图像，仅保留文字区域
    cropped_image = crop_image_to_text_area(image)
    if fixedH:
        if rotated:
            h=fixedH-cropped_image.width
        else:
            h=fixedH-cropped_image.height
        cropped_image=extendImg(cropped_image,h,rotated)
    # 保存图像
    # cropped_image.save(output_path, format="PNG")
    # print("图像已保存为:", output_path)
    return toZpl(cropped_image,cropped_image.width)

def crop_image_to_text_area(image):
    # 将图像转换为RGBA模式，带有Alpha通道
    image = image.convert("RGBA")
    # 获取图像的边界框
    bbox = image.getbbox()
    # 裁剪图像，仅保留文字区域
    cropped_image = image.crop(bbox)
    # 创建一个纯白背景的图像，与裁剪后的图像尺寸相同
    white_image = Image.new("RGB", cropped_image.size, (255, 255, 255))
    # 将裁剪后的图像放置在纯白背景图像上
    white_image.paste(cropped_image, (0, 0), mask=cropped_image)
    return white_image

def toZpl(img,width=0):

    zpl_string = ZebrafyImage(
        img,
        compression_type="A",
        invert=True,
        dither=False,
        threshold=128,
        # width=720,
        # height=1280,
        # pos_x=100,
        # pos_y=100,
        complete_zpl=False,
    ).to_zpl()

    return zpl_string,width

def toImg():

    with open("output.zpl", "r") as zpl:
        pil_images = ZebrafyZPL(zpl.read()).to_images()
        for count, pil_image in enumerate(pil_images):
            pil_image.save(f"output_{count}.png", "PNG")

# toZpl()
# text = "一本正经"
# font_path = "msyhl.ttc"  # 替换为你的字体文件路径
# create_text_image(text, font_path=font_path,fixedH=800)

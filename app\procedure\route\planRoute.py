from flask import Blueprint, request
from extensions import db
from app.procedure.model.models_procedure import <PERSON>, Moitem,  Skuinfo, Standardprocedure, Moitempath
import datetime
from sqlalchemy import or_
from app.public.functions import responseError,  responsePost, responseGet
from app.procedure.functions.jwt import login_required
from app.procedure.functions import getServer
import traceback
import hashlib
import openpyxl
api = Blueprint('procedure/planAPI', __name__)


@api.route('/uploadMO', methods=['POST'])
@login_required
def uploadMO():
    file_obj = request.files.get('file')
    plant = request.headers.get('plant')
    name = 'moupload' + datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        file_obj.save(getServer()['basePath']+'mo/'+name+appendix)
        issuccess, exempts = handleExcel(getServer()['basePath']+'mo/'+name+appendix, plant)
        if issuccess:
            return responsePost("上传成功", {'exempts': exempts})
    return responseError("上传文件失败，请联系管理员")


def handleExcel(path, plant):
    try:
        wb = openpyxl.load_workbook(path)
        ws = wb['mo']
        activeSkus = db.session.query(Skuinfo).filter(Skuinfo.plant == plant).all()
        arr = []
        for a in activeSkus:
            arr.append(a.sku)
        moarr = []  # 存储所有订单号
        exempts = []  # 存储所有异常订单信息
        skuarr = []  # 存储所有不为空且输入skuarr里的料号集合
        mos = {}  # 存储所有不为空且输入skuarr里的订单信息
        moitems = []  # 存储所有订单sn项
        moitempaths = []  # 存储所有订单sn路径
        procedureDic = {}  # 存储所有订单按sku整合的流程步骤
        createtime = datetime.datetime.now()
        for row in ws.iter_rows(min_row=2, values_only=True):
            createtime = row[0]
            mono = row[1]
            sku = row[2]
            desc = row[3]
            qty = row[4]
            impellerd = row[5]
            duetime = row[6]
            so = row[7]
            project = row[8]
            mcomments = row[9] if row[9] else ''
            tcomments = row[10] if row[10] else ''
            # 如何上面所有都不为空
            if sku and createtime and mono and desc and str(qty).isdigit() and impellerd and duetime and so and project:
                if sku in arr:
                    mos[mono] = [createtime, mono, sku, desc, qty, impellerd, duetime, so, project, mcomments, tcomments]
                    skuarr.append(sku)
                else:
                    exempts.append({'sku': sku, 'mono': mono, 'des': '缺少该料号的基本信息'})
            else:
                exempts.append({'sku': sku, 'mono': mono, 'des': 'Excel除了备注外有空值'})
        vamos = db.session.query(Mo).filter(Mo.mono.in_(mos.keys())).all()
        if len(vamos) > 0:
            for mo in vamos:
                exempts.append({'sku': mo.sku, 'mono': mo.mono, 'des': '该订单号已经存在'})
                mos.pop(mo.mono)
        procedures = db.session.query(Skuinfo.sku, Standardprocedure.station
                                      ).outerjoin(Standardprocedure, Skuinfo.procedure == Standardprocedure.stdprocedurename).filter(
            Skuinfo.sku.in_(skuarr)).all()
        for pro in procedures:
            if pro.sku not in procedureDic:
                procedureDic[pro.sku] = [pro.station]
            else:
                procedureDic[pro.sku].append(pro.station)
        for mo in mos.keys():
            mvalue = mos[mo]
            if (mvalue[2] not in procedureDic) or (len(procedureDic[mvalue[2]]) == 0):
                exempts.append({'sku': mvalue[2], 'mono': mvalue[1], 'des': '缺少工艺流程或工艺流程不完整'})
            else:
                moarr.append(Mo(createtime=mvalue[0], mono=mvalue[1], sku=mvalue[2], desc=mvalue[3], moqty=mvalue[4], impellerd=mvalue[5],
                                duetime=mvalue[6], pono=mvalue[7], project=mvalue[8], mcomments=mvalue[9], tcomments=mvalue[10], status='open', plant=plant))
                for i in range(mvalue[4]):
                    sn = mvalue[1]+'-'+str(i+1)
                    moitems.append(Moitem(mono=mvalue[1], sku=mvalue[2], sn=sn, status='open'))
                    for pstation in procedureDic[mvalue[2]]:
                        moitempaths.append(Moitempath(station=pstation, starttime=createtime,
                                                      sn=sn, status='open'))
        db.session.add_all(moitems)
        db.session.add_all(moitempaths)
        db.session.add_all(moarr)
        db.session.commit()
        wb.close()
        return True, exempts
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return False, []


@api.route('/getDetailbymono', methods=['GET'])
@login_required
def getDetailbymono():
    res = request.args
    mono = res.get('mono')
    mos = db.session.query(Moitem.sn, Moitem.status.label('mostatus'), Moitem.sku, Moitempath.station, Moitempath.status, Moitempath.owner).outerjoin(
        Moitempath, Moitem.sn == Moitempath.sn).filter(Moitem.mono == mono).all()
    outDic = {}
    for mo in mos:
        detail = {
            'sn': mo.sn,
            'station': mo.station,
            'status': mo.status,
            'owner': mo.owner
        }
        if mo.sn not in outDic:
            outDic[mo.sn] = {
                'mostatus': mo.mostatus,
                'sku': mo.sku,
                'sn': mo.sn,
                'details': [detail]
            }
        else:
            outDic[mo.sn]['details'].append(detail)
    return responseGet('获取成功', outDic)


@api.route('/createPO', methods=['POST'])
@login_required
def createPO():
    res = request.json
    Id = res.get('Id')
    wready = False
    try:
        if Id:
            # 更新现有记录
            update_data = {}
            for key, value in res.items():
                if hasattr(Mo, key) and key != 'Id':  # 只更新模型存在的字段
                    update_data[getattr(Mo, key)] = value
                if key == 'wfinishtime':
                    wready = True
            if update_data:
                record = db.session.query(Mo).filter(Mo.Id == Id).first()
                if record.status == 'open' and wready:
                    update_data[getattr(Mo, 'status')] = 'ongoing'
                db.session.query(Mo).filter(Mo.Id == Id).update(update_data)
        else:
            # 创建新记录
            newmo = Mo()
            for key, value in res.items():
                if hasattr(Mo, key):  # 只设置模型存在的字段
                    setattr(newmo, key, value)
            createtime = datetime.datetime.now()
            setattr(newmo, 'createtime', createtime)
            setattr(newmo, 'status', 'open')
            db.session.add(newmo)
            db.session.flush()
            moitems = []
            moitempaths = []
            procedures = db.session.query(Skuinfo.sku, Standardprocedure.station
                                          ).outerjoin(Standardprocedure, Skuinfo.procedure == Standardprocedure.stdprocedurename).filter(
                Skuinfo.sku == newmo.sku).all()
            if (len(procedures) == 0):
                db.session.rollback()
                return responseError(f"操作失败，未找料号{newmo.sku}的工序，请先建立改料号的基本信息后在试")
            for i in range(newmo.moqty):
                sn = newmo.mono+'-'+str(i+1)
                moitems.append(Moitem(mono=newmo.mono, sku=newmo.sku, sn=sn, status='open'))
                for pro in procedures:
                    moitempaths.append(Moitempath(station=pro.station, starttime=createtime,
                                       sn=sn, status='open'))
            db.session.add_all(moitems)
            db.session.add_all(moitempaths)
        db.session.commit()
        return responsePost("操作成功", {})
    except Exception:
        db.session.rollback()
        print(traceback.format_exc())
        return responseError("操作失败: 无法插入相同的工单号，工单号必须唯一")


@api.route('/getOrders', methods=['POST'])
@login_required
def getOrders():
    # print(111111)
    res = request.json
    keywords = res.get('keywords')
    status = res.get('status')
    page = res.get('page')
    size = res.get('size')
    plant = res.get('plant')
    printed = res.get('printed')
    poinfos = db.session.query(Mo).filter(Mo.plant == plant)
    if keywords:
        poinfos = poinfos.filter(or_(Mo.pono.like(f'%{keywords}%'), Mo.mono.like(f'%{keywords}%'), Mo.project.like(f'%{keywords}%')))
    if len(status) > 0:
        poinfos = poinfos.filter(Mo.status.in_(status))
    else:
        return responsePost("获取成功", {'polists': [], 'total': 0})
    if printed:
        poinfos = poinfos.filter(or_(Mo.printed == 0, Mo.printed.is_(None)))
    total = poinfos.count()
    moinfos = poinfos.order_by(Mo.priority.asc(), Mo.duetime.asc()).offset((page-1)*size).limit(size).all()
    data = []
    for mo in moinfos:
        data.append({
            'Id': mo.Id,
            'mono': mo.mono,
            'moqty': mo.moqty,
            'sku': mo.sku,
            'desc': mo.desc,
            'impellerd': mo.impellerd,
            'duetime': datetime.datetime.strftime(mo.duetime, '%Y-%m-%d %H:%M:%S') if mo.duetime else '',
            'requiretime': datetime.datetime.strftime(mo.requiretime, '%Y-%m-%d %H:%M:%S') if mo.requiretime else '',
            'createtime': datetime.datetime.strftime(mo.createtime, '%Y-%m-%d %H:%M:%S') if mo.createtime else '',
            'finishtime': datetime.datetime.strftime(mo.finishtime, '%Y-%m-%d %H:%M:%S') if mo.finishtime else '',
            'plant': mo.plant,
            'status': mo.status,
            'priority': mo.priority,
            'pono': mo.pono,
            'project': mo.project,
            'mcomments': mo.mcomments,
            'wduetime': datetime.datetime.strftime(mo.wduetime, '%Y-%m-%d %H:%M:%S') if mo.wduetime else '',
            'wfinishtime': datetime.datetime.strftime(mo.wfinishtime, '%Y-%m-%d %H:%M:%S') if mo.wfinishtime else '',
            'wcomments': mo.wcomments,
            'wlackmaterials': mo.wlackmaterials,
            'packfinishtime': datetime.datetime.strftime(mo.packfinishtime, '%Y-%m-%d %H:%M:%S') if mo.packfinishtime else '',
            'pcomments': mo.pcomments,
            'tcomments': mo.tcomments,
            'printed': mo.printed
        })
    # print(data, total)
    return responsePost("获取成功", {'polists': data, 'total': total})


@api.route('/changePrinted', methods=['POST'])
@login_required
def changePrinted():
    res = request.json
    Id = res.get('Id')
    try:
        mo = db.session.query(Mo).filter(Mo.Id == Id).first()
        if not mo.printed:
            mo.printed = 1
        else:
            mo.printed = 0
        db.session.commit()
        return responsePost("修改成功")
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError("修改失败")


@api.route('/cancelMO', methods=['POST'])
@login_required
def cancel_mo():
    res = request.json
    Id = res.get('Id')
    try:
        mo = db.session.query(Mo).filter(Mo.Id == Id).first()
        if mo:
            update_data = {}
            if mo.status == 'open':
                update_data['status'] = 'cancel'
            else:
                return responseError("操作失败", {})
            db.session.query(Mo).filter(Mo.Id == Id).update(update_data)
        else:
            return responseError("操作失败", {})
        db.session.commit()
        return responsePost("操作成功", {})
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError("操作失败", {})

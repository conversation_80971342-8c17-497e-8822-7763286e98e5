import moment from "moment";

//根据传过来的日期与班次判断是否为当前班次
export const isCurrentShift = (selectedDate: string, selectedshift: string) => {
  const hour_now = new Date().getHours();
  const shift =
    hour_now >= 0 && hour_now < 8
      ? "C"
      : hour_now >= 8 && hour_now < 16
        ? "A"
        : hour_now >= 16 && hour_now < 24
          ? "B"
          : "";
  const currentDate = moment().format("YYYY-MM-DD");
  const previousDate = moment().subtract(1, "days").format("YYYY-MM-DD");

  if (selectedshift === shift) {
    return (
      (shift === "C" && selectedDate === previousDate) ||
      ((shift === "A" || shift === "B") && selectedDate === currentDate)
    );
  } else {
    return false;
  }
};

export const getShiftByHour = (): string => {
  const hour = new Date().getHours();
  if (hour >= 0 && hour <= 7) {
    return "C";
  } else if (hour >= 8 && hour <= 15) {
    return "A";
  } else if (hour >= 16 && hour <= 23) {
    return "B";
  } else {
    return "";
  }
};

// 通过班次代码取得班次名称
export const getShiftName = (shift: string) => {
  switch (shift) {
    case "C":
      return "夜班(次日0:00~8:00)";
    case "A":
      return "早班(8:00~16:00)";
    case "B":
      return "中班(16:00~24:00)";
    default:
      return "";
  }
};

export const formatDuration = (seconds: number): string => {
  console.log(seconds);
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    // 超过一小时
    return `${hours}小时${minutes.toString().padStart(2, "0")}分${remainingSeconds.toString().padStart(2, "0")}秒`;
  } else if (minutes > 0) {
    // 超过一分钟
    return `${minutes}分${remainingSeconds.toString().padStart(2, "0")}秒`;
  } else {
    // 不超过一分钟
    return `${remainingSeconds}秒`;
  }
};

from extensions import db
from werkzeug.security import generate_password_hash, check_password_hash


class Edi(db.Model):
    __bind_key__ = "cs"
    __tablename__ = "cs_edi"
    Id = db.Column(db.Integer, primary_key=True)
    customer = db.Column(db.String(10))
    planner = db.Column(db.String(64))


class Options(db.Model):
    __bind_key__ = "cs"
    __tablename__ = "cs_options"
    Id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(10))
    listitem = db.Column(db.String(50))
    editable = db.Column(db.SmallInteger)


class Orderlist(db.Model):
    __bind_key__ = "cs"
    __tablename__ = "cs_orderlist"
    Id = db.Column(db.Integer, primary_key=True)
    salesdoc = db.Column(db.Integer)
    line = db.Column(db.SmallInteger)
    customer = db.Column(db.String(10))
    shipto = db.Column(db.String(10))
    mgroup = db.Column(db.String(20))
    mgroupdesc = db.Column(db.String(100))
    sku = db.Column(db.String(64))
    skudesc = db.Column(db.String(200))
    orderqty = db.Column(db.Integer)
    unit = db.Column(db.String(10))
    requestdate = db.Column(db.Date())
    orderdate = db.Column(db.Date())
    mprc = db.Column(db.String(10))
    ordertype = db.Column(db.String(10))
    updatedate = db.Column(db.Date())
    updatereason = db.Column(db.String(50))
    updateremark = db.Column(db.String(255))
    planner = db.Column(db.String(64))
    ponumber = db.Column(db.String(30))
    confirmer = db.Column(db.String(64))
    confirmdate = db.Column(db.Date())
    recorddate = db.Column(db.Date())


class Permission(db.Model):
    __bind_key__ = "cs"
    __tablename__ = "cs_permission"
    Id = db.Column(db.Integer, primary_key=True)
    ismenu = db.Column(db.SmallInteger)
    pid = db.Column(db.SmallInteger)
    name = db.Column(db.String(10))
    order = db.Column(db.SmallInteger)
    path = db.Column(db.String(20))


class Planner(db.Model):
    __bind_key__ = "cs"
    __tablename__ = "cs_planner"
    Id = db.Column(db.Integer, primary_key=True)
    mprc = db.Column(db.String(10))
    planner = db.Column(db.String(64))
    plannerbackup = db.Column(db.String(64))
    supervisor = db.Column(db.String(64))


class Role(db.Model):
    __bind_key__ = "cs"
    __tablename__ = "cs_role"
    Id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(10))
    default_auth = db.Column(db.String(100))


class User(db.Model):
    __bind_key__ = "cs"
    __tablename__ = "cs_user"
    Id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(64))
    password_hash = db.Column(db.String(255))
    roleid = db.Column(db.Integer)
    auth = db.Column(db.String(255))
    isactive = db.Column(db.SmallInteger)

    def __repr__(self):
        return '<User %r>' % self.Id

    @property
    def password(self):
        raise AttributeError("密码不允许读取")

    # 转换密码为hash存入数据库
    @password.setter
    def password(self, password):
        self.password_hash = generate_password_hash(password)

    # 检查密码
    def check_password_hash(self, password):
        return check_password_hash(self.password_hash, password)

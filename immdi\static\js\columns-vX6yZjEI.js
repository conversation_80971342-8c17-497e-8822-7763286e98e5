import{n as d,G as x,a2 as u,S as y,q as C,aW as f,b as a,r as n,w as h,D as p,aE as M}from"./index-BnxEuBzx.js";import{g as v}from"./runlog-CNDP2hwV.js";import{d as H}from"./front-CiGk0t8u.js";import{u as e}from"./prod-CmDsiAIL.js";import{h as s}from"./moment-C3TZ8gAF.js";import{c as g}from"./index-CA30dg9C.js";import{f as S}from"./shift-DH35BNzV.js";import"./editStateForm-BQH9QYuy.js";import"./dashboard-dtTxmf4X.js";import"./index.vue_vue_type_script_setup_true_lang-DEM1uiK0.js";function A(){const l=d([]),m=d(!0);x([()=>e().selectedDate,()=>e().shift,()=>e().machineId],(t,i)=>{o.selecteddate=t[0],o.shift=t[1],o.machine=t[2],r()});const o=u({selecteddate:e().selectedDate,machine:e().machineId,shift:e().shift}),r=()=>{m.value=!0,v(y(o)).then(t=>{l.value=t.data,m.value=!1})};C(()=>{r()});function _(){f.confirm("确认自动检查生产状态吗，有可能调整运行记录表中的状态，请确认?","系统提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0,draggable:!0}).then(()=>{}).catch(()=>{})}function D(t){f.confirm("确认删除该状态吗，删除时将调整相邻状态时间，请确认?","系统提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0,draggable:!0}).then(()=>{H({id:t.id}).then(i=>{i.meta.status==201&&r(),M(i.meta.msg,{customClass:"el",type:i.meta.status==201?"success":"error",duration:2e3})})}).catch(()=>{})}const b=u({text:"正在加载不良记录数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `});return{changeState:g,autoCheckState:_,dataList:l,columns:[{label:"ID",prop:"id",minWidth:30},{label:"开始时间",prop:"start_time",minWidth:50,formatter(t){return s(t.start_time).isValid()?s(t.start_time).format("YYYY-MM-DD HH:mm:ss"):""}},{label:"结束时间",prop:"end_time",minWidth:50,formatter(t){return s(t.end_time).isValid()?s(t.end_time).format("YYYY-MM-DD HH:mm:ss"):""}},{label:"状态",prop:"machine_state",minWidth:30,cellRenderer:({row:t})=>a(n("el-tag"),{type:t.machine_state==1?"success":t.machine_state==2?"warning":t.machine_state==3?"danger":"info"},{default:()=>[t.machine_state==1?"正常运行":t.machine_state==2?"小停机":t.machine_state==3?"故障":t.machine_state==4?"无计划":"未知状态"]})},{label:"状态描述",prop:"machine_state_des_str",minWidth:40},{label:"持续时间",prop:"unit",minWidth:40,formatter(t){let i=s(t.start_time),c=s(t.end_time);return i.isValid()&&c.isValid()?S(c.diff(i,"seconds")):""}},{label:"操作",prop:"",fixed:"right",minWidth:60,cellRenderer:({row:t})=>a("div",{class:"ops"},[h(a(n("el-button"),{type:"danger",onClick:()=>D(t)},{default:()=>[a(n("el-icon"),{size:"20"},{default:()=>[a(n("Delete"),null,null)]})]}),[[p,t.confirm_state!=1]]),h(a(n("el-button"),{type:"warning",onClick:()=>g(e().machineId,e().selectedDate,e().shift,t.machine_state_des,t.start_time,t.end_time,t.id,"update",()=>{r()})},{default:()=>[a(n("el-icon"),{size:"20"},{default:()=>[a(n("Edit"),null,null)]})]}),[[p,t.confirm_state!=1]])])}],loadingConfig:b,adaptiveConfig:{offsetBottom:20,fixHeader:!0},loading:m,refreshData:r}}export{A as useColumns};

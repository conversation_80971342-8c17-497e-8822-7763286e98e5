from flask import Blueprint, request
from app.welean.model.models_welean import Newspaper, Questiontask, Settings, Questionresult, Questions, Relationquestions, Userinfo
from extensions import db
from datetime import datetime, date
from openpyxl import load_workbook
from sqlalchemy import func,  or_, and_, desc, not_
from app.public.functions import responseError, responsePost, responseGet,  login_required
from app.welean.functions import getServer, getScore, deletePicthread
from app.welean.schemas import questions_schema
import hashlib
api = Blueprint('welean/V304/trainingAPI', __name__)

plantList = [
    {'name': 'SZ'},
    {'name': 'WX'},
    {'name': 'NB'},
    {'name': 'ALL'}
]


@ api.route('/getTasksetting', methods=['GET'])
@ login_required
def getTasksetting():
    tasks = db.session.query(func.year(Questiontask.startdate).label('year'),
                             Questiontask.tasktitle).group_by(Questiontask.tasktitle).all()
    dic = {}
    for t in tasks:
        if t.year in dic.keys():
            dic[t.year]['children'].append(
                {
                    'value': t.tasktitle,
                    'label': t.tasktitle
                }
            )
        else:
            dic[t.year] = {
                'value': t.year,
                'label': t.year,
                'children': [
                    {
                        'value': t.tasktitle,
                        'label': t.tasktitle
                    }
                ]
            }
    outArr = list(dic.values())
    return responseGet("获取成功", {'taskTypes': outArr, 'plants': plantList})


@ api.route('/taskBind', methods=['POST'])
@ login_required
def taskBind():
    res = request.json
    tqarr = res.get('tqarr').split(',')
    taskid = int(res.get('taskid'))
    try:
        db.session.query(Relationquestions).filter(Relationquestions.taskid == taskid).delete()
        for tq in tqarr:
            print('aa', taskid, tq)
            Rq = Relationquestions(taskid=taskid, questionid=tq)
            db.session.add(Rq)
        db.session.commit()
        return responsePost("修改成功")
    except Exception:
        db.session.rollback()
    return responseError("绑定问题失败")


@ api.route('/uploadTaskpic', methods=['POST'])
@ login_required
def uploadTaskpic():
    file_obj = request.files.get('file')
    qid = request.headers["qid"]
    mystr = ('questiontask' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        try:
            db.session.query(Questiontask).filter(
                Questiontask.Id == qid).update({'piccover': name+appendix})
            db.session.commit()
            file_obj.save(getServer()['questionsPath']+name+appendix)
            return responsePost("更新成功", {'upload_url': getServer()['questionsUrl']+name+appendix})
        except Exception:
            db.session.rollback()
    return responseError("上传文件失败，请联系管理员")


@ api.route('/initTask', methods=['POST'])
@ login_required
def initTask():
    res = request.json
    currenttask = int(res.get('currenttask')) if res.get('currenttask') else 0
    duedate = res.get('duedate')
    tasktitle = res.get('tasktitle')
    startdate = res.get('testdate')
    passscore = res.get('passscore')
    newsid = int(res.get('id')) if res.get('id') else 0
    plant = res.get('plant')
    randnb = int(res.get('randnb')) if res.get('randnb') else 0
    randcate = res.get('randcate')
    if currenttask == 0:
        try:
            task = Questiontask(tasktitle=tasktitle, duedate=duedate, startdate=startdate,
                                passscore=passscore, newsid=newsid, plant=plant, randnb=randnb, randcate=randcate)
            db.session.add(task)
            db.session.flush()
            qid = task.Id
            db.session.commit()
            return responsePost("成功", {'qid': qid})
        except Exception as e:
            db.session.rollback()
            print(e)
            return responseError('失败，请联系精益部门反馈该问题')
    else:
        print('aaaaaaaa', currenttask, passscore)
        db.session.query(Questiontask).filter(
            Questiontask.Id == currenttask).update({
                'tasktitle': tasktitle,
                'duedate': duedate,
                'startdate': startdate,
                'passscore': passscore,
                'newsid': newsid,
                'plant': plant,
                'randnb': randnb,
                'randcate': randcate
            })
        db.session.commit()
        return responsePost("修改成功", {'qid': currenttask})


@ api.route('/getTrainingTypes', methods=['GET'])
@ login_required
def getTrainingTypes():
    sets = db.session.query(Settings).filter(Settings.cate == 'trainingtypes').all()
    outArr = []
    for dp in sets:
        dic = {
            'value': dp.name1,
            'label': dp.name1
        }
        outArr.append(dic)
    newsList = [
        {
            'value': '系统通知',
            'label': '系统通知'
        },
        {
            'value': '精益分享',
            'label': '精益分享'
        },
        {
            'value': '改善信息',
            'label': '改善信息'
        }
    ]
    return responseGet("获取列表成功", {'trainingTypes': outArr, 'plantList': plantList, 'newsList': newsList})


@ api.route('/uploadQuestionpic', methods=['POST'])
@ login_required
def uploadQuestionpic():
    file_obj = request.files.get('file')
    qid = request.headers["qid"]
    mystr = ('question' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        try:
            db.session.query(Questions).filter(
                Questions.Id == qid).update({'picurl': name+appendix})
            db.session.commit()
            file_obj.save(getServer()['questionsPath']+name+appendix)
            return responsePost("更新成功", {'upload_url': getServer()['questionsUrl']+name+appendix})
        except Exception:
            db.session.rollback()
    return responseError("上传文件失败，请联系管理员")


@ api.route('/initQuestion', methods=['POST'])
@ login_required
def initQuestion():
    res = request.json
    question = res.get('question')
    trainingtypes = res.get('trainingtypes')
    clearpic = res.get('clearpic')
    answer = res.get('answer')
    qtype = int(res.get('qtype'))
    qid = int(res.get('qid'))
    checkorder = int(res.get('checkorder')) if res.get('checkorder') else 0
    optionA = res.get('optionA')
    optionB = res.get('optionB')
    optionC = res.get('optionC')
    optionD = res.get('optionD')
    optionE = res.get('optionE')
    picurl = res.get('picurl')
    if qtype == 0 or qtype == 1:
        b = []
        for i in range(len(answer)):
            b.append(answer[i])
        answer = ','.join(b)
    if qid == 0:
        if qtype == 0 or qtype == 1:
            question = Questions(trainingtypes=trainingtypes, question=question, answer=answer, qtype=qtype, checkorder=checkorder,
                                 optionA=optionA, optionB=optionB, optionC=optionC, optionD=optionD, optionE=optionE)
        else:
            question = Questions(trainingtypes=trainingtypes, question=question,
                                 answer=answer, qtype=qtype, checkorder=checkorder)
        try:
            db.session.add(question)
            db.session.flush()
            qid = question.Id
            db.session.commit()
            return responsePost("出题成功", {'qid': qid})
        except Exception as e:
            db.session.rollback()
            print(e)
            return responseError('出题失败，请联系精益部门反馈该问题')
    else:
        if clearpic == 1:
            pth = getServer()['questionsPath']+picurl
            print('pthpth1', pth)
            deletePicthread(pth)
            db.session.query(Questions).filter(
                Questions.Id == qid).update({
                    'qtype': qtype,
                    'checkorder': checkorder,
                    'question': question,
                    'optionA': optionA,
                    'optionB': optionB,
                    'optionC': optionC,
                    'optionD': optionD,
                    'optionE': optionE,
                    'answer': answer,
                    'trainingtypes': trainingtypes,
                    'picurl': ''
                })
        else:
            db.session.query(Questions).filter(
                Questions.Id == qid).update({
                    'qtype': qtype,
                    'checkorder': checkorder,
                    'question': question,
                    'optionA': optionA,
                    'optionB': optionB,
                    'optionC': optionC,
                    'optionD': optionD,
                    'optionE': optionE,
                    'answer': answer,
                    'trainingtypes': trainingtypes
                })
        db.session.commit()
        return responsePost("修改成功", {'qid': qid})


@api.route('/getQuestions', methods=['GET'])
@login_required
def getQuestions():
    res = request.args
    keywords = res.get('keywords')
    ttype = res.get('ttype')
    nb = res.get('nb')
    questions = db.session.query(Questions)
    if ttype:
        questions = questions.filter(Questions.trainingtypes == ttype)
    if keywords:
        questions = questions.filter(or_(Questions.trainingtypes.like(
            '%{0}%'.format(keywords)), Questions.question.like('%{0}%'.format(keywords))))
    questions = questions.order_by(desc(Questions.Id)).limit(nb).all()
    questionList = questions_schema.dump(questions)
    for i in range(len(questionList)):
        questionList[i]['showurl'] = getServer()['questionsUrl'] + \
            questionList[i]['picurl'] if questionList[i]['picurl'] else ''
        questionList[i]['show'] = False
    return responseGet("获取问题列表成功", {'questionlist': questionList})


@api.route('/getTaskQuestions', methods=['GET'])
@login_required
def getTaskQuestions():
    res = request.args
    taskid = res.get('taskid')
    questions = db.session.query(Questions).join(
        Relationquestions, Questions.Id == Relationquestions.questionid).filter(Relationquestions.taskid == taskid).all()
    questionList = questions_schema.dump(questions)
    for i in range(len(questionList)):
        questionList[i]['showurl'] = getServer()['questionsUrl'] + \
            questionList[i]['picurl'] if questionList[i]['picurl'] else ''
        questionList[i]['show'] = False
    return responseGet("获取问题列表成功", {'tqlist': questionList})


@ api.route('/submitTask', methods=['POST'])
@ login_required
def submitTask():
    res = request.json
    eid = res.get('eid')
    testdate = date.today()
    taskid = res.get('taskid')
    score = res.get('score')
    passscore = res.get('passscore')
    myresult = db.session.query(Questionresult).filter(
        Questionresult.eid == eid).filter(Questionresult.taskid == taskid).scalar()
    if myresult:
        if myresult.score < passscore and score >= passscore:
            myresult.score = score
            myresult.testdate = testdate
            db.session.commit()
            getScore(eid, '问答', taskid)
        else:
            if score > myresult.score:
                myresult.score = score
                myresult.testdate = testdate
                db.session.commit()
        return responsePost('修改成功')
    else:
        try:
            rst = Questionresult(taskid=taskid, testdate=testdate,
                                 eid=eid, score=score, firstscore=score)
            db.session.add(rst)
            if score >= passscore:
                getScore(eid, '问答', taskid)
            db.session.commit()
        except Exception:
            db.session.rollback()
            return responseError('新增失败')
        return responsePost('新增成功')


@api.route('/getExams', methods=['GET'])
@login_required
def getExams():
    res = request.args
    taskid = int(res.get('id'))
    QuestionList = []
    questions = db.session.query(Questions).outerjoin(
        Relationquestions, Questions.Id == Relationquestions.questionid).filter(Relationquestions.taskid == taskid).all()
    qt = db.session.query(Questiontask.passscore, Questiontask.randcate, Questiontask.randnb).filter(
        Questiontask.Id == taskid).first()
    passsore = qt.passscore
    if qt.randnb > 0:
        questions2 = db.session.query(Questions).filter(Questions.trainingtypes ==
                                                        qt.randcate).order_by(func.rand()).limit(qt.randnb)
        questions = list(set(list(questions)+list(questions2)))
    for q in questions:
        if q.qtype == 2:
            QuestionOptionList = ''
        else:
            QuestionOptionList = []
            if q.optionA:
                QuestionOptionList.append(
                    {
                        "optionID": "A",
                        "fldOptionText": q.optionA,
                        "checked": False,
                        "fldOptionIndex": 1
                    }
                )
            if q.optionB:
                QuestionOptionList.append(
                    {
                        "optionID": "B",
                        "fldOptionText": q.optionB,
                        "checked": False,
                        "fldOptionIndex": 2
                    }
                )
            if q.optionC:
                QuestionOptionList.append(
                    {
                        "optionID": "C",
                        "fldOptionText": q.optionC,
                        "checked": False,
                        "fldOptionIndex": 3
                    }
                )
            if q.optionD:
                QuestionOptionList.append(
                    {
                        "optionID": "D",
                        "fldOptionText": q.optionD,
                        "checked": False,
                        "fldOptionIndex": 4
                    }
                )
            if q.optionE:
                QuestionOptionList.append(
                    {
                        "optionID": "E",
                        "fldOptionText": q.optionE,
                        "checked": False,
                        "fldOptionIndex": 5
                    }
                )
        dic = {
            'picUrl': getServer()['questionsUrl']+q.picurl if q.picurl else '',
            'checkorder': q.checkorder,
            'answer': q.answer,
            'questionID': str(q.Id),
            'fldName': q.question,
            'fldAnswer': '',
            'questionType': q.qtype,
            'QuestionOptionList': QuestionOptionList
        }
        QuestionList.append(dic)
    return responseGet("获取列表成功", {'qList': QuestionList, 'passscore': passsore})


@api.route('/getMyTasks', methods=['GET'])
@login_required
def getMyTasks():
    res = request.args
    eid = res.get('eid')
    newsid = res.get('newsid')
    tasks = db.session.query(Questiontask.Id, Questiontask.passscore, Questiontask.startdate, Questionresult.testdate, Questionresult.firstscore, Questionresult.score).outerjoin(
        Questionresult, Questionresult.taskid == Questiontask.Id).filter(Questiontask.newsid == newsid).filter(or_(Questionresult.eid == eid, Questionresult.eid.is_(None))).all()
    myTasks = []
    for t in tasks:
        dic = {
            'taskid': str(t.Id),
            'testdate': datetime.strftime(t.startdate, "%Y-%m-%d"),
            'firstscore': int(t.firstscore) if t.firstscore else '未参加',
            'score': int(t.score) if t.score else '未参加',
            'passscore': int(t.passscore)
        }
        myTasks.append(dic)
    return responseGet("获取列表成功", {'myTasks': myTasks})


@api.route('/getAllTasks', methods=['GET'])
@login_required
def getAllTasks():
    res = request.args
    eid = res.get('eid')
    nb = int(res.get('nb'))
    keywords = res.get('keywords')
    myTasks = []
    if eid:
        tasks = db.session.query(Questiontask.Id, Questiontask.tasktitle, Questiontask.newsid, Questiontask.piccover, Questiontask.passscore, Questiontask.startdate, Questionresult.testdate,
                                 Questionresult.firstscore, Questiontask.duedate, Questiontask.plant, Questionresult.score, Questiontask.randcate, Questiontask.randnb).outerjoin(
            Questionresult, Questionresult.taskid == Questiontask.Id).filter(Questionresult.score >= Questiontask.passscore).filter(Questionresult.eid == eid)
    else:
        tasks = db.session.query(Questiontask)
    if keywords:
        tasks = tasks.filter(Questiontask.tasktitle.like('%{0}%'.format(keywords)))
    tasks = tasks.order_by(desc(Questiontask.startdate)).limit(nb).all()
    for t in tasks:
        dic = {
            'tasktitle': t.tasktitle,
            'id': t.newsid,
            'currenttask': str(t.Id) if t.Id else 0,
            'piccover': getServer()['questionsUrl'] + t.piccover,
            'testdate': datetime.strftime(t.startdate, "%Y-%m-%d"),
            'duedate': datetime.strftime(t.duedate, "%Y-%m-%d"),
            'firstscore': t.firstscore if eid else 0,
            'score': t.score if eid else 0,
            'passscore': t.passscore,
            'plant': t.plant,
            'randnb': t.randnb if t.randnb else 0,
            'randcate': t.randcate
        }
        myTasks.append(dic)
    return responseGet("获取列表成功", {'contents': myTasks})


@api.route('/getCourses', methods=['GET'])
@login_required
def getCourses():
    res = request.args
    plant = res.get('plant')
    eid = res.get('eid')
    subcate = res.get('subcate')
    current = int(res.get('current'))  # 0未完成，1已完成，2全部
    coursesArr = []
    mycourses = db.session.query(Newspaper.Id, Newspaper.currenttask, Newspaper.title, Newspaper.subcate, Newspaper.postdate, Newspaper.reservedate, Newspaper.piccover, Questionresult.eid
                                 ).outerjoin(Questiontask, Newspaper.currenttask == Questiontask.Id).outerjoin(
        Questionresult, Questionresult.taskid == Questiontask.Id).filter(or_(Newspaper.plant == plant, Newspaper.plant == 'ALL')).filter(Newspaper.currenttask > 0).filter(
            Newspaper.cate == 'training').filter(Newspaper.subcate == subcate).filter(Questionresult.eid == eid).filter(Questionresult.score >= Questiontask.passscore).all()
    blankcourses = db.session.query(Newspaper).outerjoin(Questiontask, Newspaper.currenttask == Questiontask.Id).outerjoin(
        Questionresult, Questionresult.taskid == Questiontask.Id).filter(or_(Newspaper.plant == plant, Newspaper.plant == 'ALL')).filter(Newspaper.currenttask > 0).filter(
            Newspaper.cate == 'training').filter(Newspaper.subcate == subcate).group_by(Newspaper.Id).all()
    fcoursesDic = {}
    coursesDic = {}
    for mc in mycourses:
        fcoursesDic[mc.Id] = mc
    if current == 1:
        coursesDic = fcoursesDic
    elif current == 0:
        for bc in blankcourses:
            if bc.Id not in fcoursesDic.keys():
                coursesDic[bc.Id] = bc
    elif current == 2:
        for bc in blankcourses:
            coursesDic[bc.Id] = bc
    courses = coursesDic.values()

    for c in courses:
        dic = {
            'id': c.Id,
            'currenttask': c.currenttask,
            'name': c.title,
            'subcate': c.subcate,
            'postdate': datetime.strftime(c.postdate, "%Y-%m-%d"),
            'updatedate': datetime.strftime(c.reservedate, "%Y-%m-%d"),
            'src': getServer()['newsUrl']+str(c.Id)+'/'+c.piccover,
        }
        coursesArr.append(dic)
    item = {
        'qty': len(blankcourses),
        'nfqty': len(mycourses),
        'rate': round(len(mycourses)/len(blankcourses)*100, 0)
    }
    return responseGet("获取列表成功", {'courses': coursesArr, 'item': item})


@api.route('/getTrainingMenu', methods=['GET'])
@login_required
def getTrainingMenu():
    res = request.args
    plant = res.get('plant')
    eid = res.get('eid')
    today = date.today()
    tasks = db.session.query(Questiontask.Id, Questiontask.passscore, Questiontask.piccover, Questiontask.tasktitle, Questiontask.duedate, Questiontask.newsid
                             ).filter(or_(Questiontask.plant == plant, Questiontask.plant == 'ALL')).filter(and_(
                                 today >= Questiontask.startdate, today <= Questiontask.duedate)).all()
    tasksArr = []
    for t in tasks:
        mytask = db.session.query(Questionresult).filter(
            Questionresult.taskid == t.Id).filter(Questionresult.eid == eid).filter(Questionresult.score >= t.passscore).scalar()
        if not mytask:
            dic = {
                'id': t.Id,
                'newsid': t.newsid,
                'image': getServer()['questionsUrl']+t.piccover,
                'title': t.tasktitle+' 截止：'+datetime.strftime(t.duedate, "%Y-%m-%d")
            }
            tasksArr.append(dic)
    courses = db.session.query(Settings.Id, Settings.name2, Settings.name1, func.count(Newspaper.subcate).label('qty')).outerjoin(
        Newspaper, Settings.name1 == Newspaper.subcate).group_by(Settings.name1).order_by(Settings.name3).filter(
            Settings.cate == 'trainingtypes').filter(Newspaper.cate == 'training').filter(Newspaper.currenttask > 0).filter(or_(Newspaper.plant == plant, Newspaper.plant == 'ALL'))
    coursesArr = []
    myDic = {}
    myCourses = db.session.query(Newspaper.subcate, func.count(Questionresult.taskid).label('fqty')).join(Questiontask, Newspaper.currenttask == Questiontask.Id).join(
        Questionresult, Questionresult.taskid == Questiontask.Id).filter(Questionresult.eid == eid).filter(Questionresult.score >= Questiontask.passscore).group_by(Newspaper.subcate).all()
    for mc in myCourses:
        myDic[mc.subcate] = mc.fqty
    for c in courses:
        if c.qty > 0:
            if c.name1 in myDic.keys():
                fqty = myDic[c.name1]
            else:
                fqty = 0
            dic = {
                'name': c.name1,
                'src': getServer()['coursesUrl']+c.name2,
                'id': c.Id,
                'qty': c.qty,
                'nfqty': c.qty-fqty
            }
            coursesArr.append(dic)
    notice = [
        '新的限时问答已经发布',
        '请点击下方图片进行培训和答题,获取精益积分参与抽奖',
        '如果一直能看到此滚动条和下方的大图',
        '说明没有完成或答题未达到及格要求',
        '请继续答题直到此滚动条消失即可获得积分'
    ],
    return responseGet("获取列表成功", {'tasksArr': tasksArr, 'coursesArr': coursesArr, 'notice': notice})


@ api.route('/getTaskCharts', methods=['GET'])
@ login_required
def getTaskCharts():
    res = request.args
    plant = res.get('plant')
    taskname = res.get('taskname')
    charts = getCharts(plant, taskname)
    return responseGet("获取列表成功", {'charts': charts})


def getCharts(plant, taskname):
    charts = []
    coursenum = db.session.query(func.count(Questiontask.Id)).filter(
        Questiontask.tasktitle == taskname).scalar()
    results = db.session.query(Userinfo.plant, Userinfo.dept1, Userinfo.cname, func.count(Questionresult.eid).label('qty')).outerjoin(
        Questionresult, Userinfo.eid == Questionresult.eid).outerjoin(Questiontask, Questionresult.taskid == Questiontask.Id).filter(
        Questiontask.tasktitle == taskname).filter(Userinfo.active == 1).order_by(desc(func.count(Questionresult.Id)))
    users = db.session.query(Userinfo.plant, Userinfo.dept1, func.count(Userinfo.eid).label('qty')
                             ).filter(Userinfo.active == 1).filter(not_(Userinfo.dept1.is_(None))).group_by(Userinfo.plant, Userinfo.dept1)
    if plant != 'ALL':
        results = results.filter(Userinfo.plant == plant)
        users = users.filter(Userinfo.plant == plant)
    results = results.group_by(Userinfo.plant, Userinfo.dept1).all()
    users = users.all()
    attendees = 0
    totalUsers = 0
    deptDic = {}
    for uu in users:
        totalUsers += uu.qty
        deptDic[uu.plant+'-'+uu.dept1] = uu.qty

    pie = []
    bar = []
    for rr in results:
        attendees += rr.qty
        dic = {
            'name': rr.plant+'-'+rr.dept1,
            'value': rr.qty,
            "labelText": rr.dept1+':'+str(rr.qty)+'人',
        }
        ddic = {
            'name': rr.plant+'-'+rr.dept1,
            'value': round(rr.qty/deptDic[rr.plant+'-'+rr.dept1]*100, 1),
        }
        pie.append(dic)
        bar.append(ddic)
    print('aaaaaaaaaa', pie)
    charts.append({
        'title': '培训参与率',
        'type': 'ring',
        'opts': {
            'rotate': False,
            'rotateLock': False,
            'color': ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
            'padding': [5, 50, 5, 50],
            'dataLabel': True,
            'legend': {
                'show': False,
                'position': "bottom",
                'lineHeight': 25
            },
            'title': {
                'name': "总体参与率",
                'fontSize': 15,
                'color': "#666666"
            },
            'subtitle': {
                'name': str(round(attendees/totalUsers/coursenum*100, 1))+'%',
                'fontSize': 25,
                'color': "#7cb5ec" if attendees/totalUsers/coursenum > 0.3 else "#EE6666"
            },
            'extra': {
                'ring': {
                    'ringWidth': 30,
                    'activeOpacity': 0.5,
                    'activeRadius': 10,
                    'offsetAngle': 0,
                    'labelWidth': 15,
                    'border': True,
                    'borderWidth': 3,
                    'borderColor': "#FFFFFF",
                    'linearType': "custom"
                }
            }
        },
        'chartData': {
            'categories': [],
            'series': [
                {
                    'data': pie
                }
            ]
        }
    })
    # 获取部门完成率
    charts.append({
        'title': '部门参与率',
        'type': 'mount',
        'opts': {
            'xAxis': {
                'disableGrid': True,
                'rotateLabel': True
            },
            'legend': {
                'show': False,
                'position': "bottom",
                'lineHeight': 25
            },
            'yAxis': {
                'data': [
                    {
                        'min': 0
                    }
                ]
            },
            'extra': {
                'mount': {
                    'type': "mount",
                    'widthRatio': 1.5
                }
            }
        },
        'chartData': {
            'series': [
                     {
                         'data': bar
                     }
            ]
        }
    })
    return charts


@ api.route('/downloadTask', methods=['GET'])
@ login_required
def downloadTask():
    res = request.args
    plant = res.get('plant')
    taskname = res.get('taskname')
    results = db.session.query(Userinfo.plant, Userinfo.dept1, Userinfo.eid, Userinfo.cname, Questiontask.tasktitle, func.sum(Questionresult.score).label('score'),
                               func.sum(Questionresult.firstscore).label('firstscore')).outerjoin(
        Questionresult, Userinfo.eid == Questionresult.eid).outerjoin(Questiontask, Questionresult.taskid == Questiontask.Id).filter(
        Questiontask.tasktitle == taskname).filter(Userinfo.active == 1).group_by(Userinfo.plant, Userinfo.dept1, Userinfo.eid, Questiontask.tasktitle)
    if plant != 'ALL':
        results = results.filter(Userinfo.plant == plant)
    results = results.all()
    if results:
        path = getServer()['downloadsPath']
        excelFile = 'tasks' + datetime.now().strftime("%Y-%m-%d %H%M%S") + '.xlsx'
        createExcel(results, path, excelFile)
        return responseGet("获取列表成功", {'url': getServer()['downloadsUrl']+excelFile})
    else:
        return responseError("没有任何数据可以下载")


def createExcel(query, path, excelFile):
    wb = load_workbook(path+'templates/taskTemplate.xlsx')
    sht = wb.worksheets[0]
    i = 2
    for q in query:
        sht.cell(row=i, column=1).value = q.plant
        sht.cell(row=i, column=2).value = q.dept1
        sht.cell(row=i, column=3).value = q.eid
        sht.cell(row=i, column=4).value = q.cname
        sht.cell(row=i, column=5).value = q.tasktitle
        sht.cell(row=i, column=6).value = q.score
        sht.cell(row=i, column=7).value = q.firstscore
        i += 1
    wb.save(path + excelFile)

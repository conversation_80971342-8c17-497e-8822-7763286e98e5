import { defineStore } from "pinia";
import { store } from "@/store";
import { prodType } from "./types";
import { storageSession } from "@pureadmin/utils";
import moment from "moment";

export const useProdStore = defineStore({
  id: "prod-info",
  state: (): prodType => ({
    selectedDate:
      storageSession().getItem<prodType>("prod-info")?.selectedDate ?? "",
    shift: storageSession().getItem<prodType>("prod-info")?.shift ?? "",
    machineId: storageSession().getItem<prodType>("prod-info")?.machineId ?? ""
  }),
  getters: {},
  actions: {
    setData(data: prodType) {
      this.selectedDate = moment(data.selectedDate).format("YYYY-MM-DD");
      this.shift = data.shift;
      this.machineId = data.machineId;
      storageSession().setItem("prod-info", {
        selectedDate: this.selectedDate,
        shift: this.shift,
        machineId: this.machineId
      });
    }
  }
});

export function useProdStoreHook() {
  return useProdStore(store);
}

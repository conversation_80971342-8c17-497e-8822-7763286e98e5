# OrderSchema调整总结

## 📋 调整概述

根据Master模型文件，已成功调整orderSchema，使其与实际的数据库模型字段完全对齐。

## 🔄 调整前后对比

### 调整前的orderSchema字段
```python
fields = ('id',
          'SP_NO',
          'BU_NO', 
          'DN_NO',
          'DN_DATE',
          'PO_NO',
          'PO_DATE',
          'CUSTOMER_NO',      # ❌ 不存在于Master模型
          'sale_order',       # ❌ 不存在于Master模型
          'cs_specialist',
          'payer_name',       # ❌ 不存在于Master模型
          'payer_company',
          'payer_address',    # ❌ 不存在于Master模型
          'payer_phone',      # ❌ 不存在于Master模型
          'receiver_name',
          'receiver_address',
          'delivery_terms',
          'dn_attachment',
          'goods_value',
          'volume',
          'weight',
          'freight_fee',
          'fee_remark',
          'status',
          'business_type',
          'warehouse')
```

### 调整后的orderSchema字段
```python
fields = ('id',
          'SP_NO',
          'BU_NO',
          'DN_NO',
          'DN_DATE',
          'PO_NO',
          'PO_DATE',
          'cs_specialist',
          'payer_company',
          'receiver_name',
          'receiver_address',
          'delivery_terms',
          'dn_attachment',
          'goods_value',
          'volume',
          'weight',
          'freight_fee',
          'fee_remark',
          'status',
          'create_time',           # ✅ 新增
          'business_type',
          'warehouse',
          'shipping_point',        # ✅ 新增
          'release_time',          # ✅ 新增
          'freight_calc_method',   # ✅ 新增
          'freight_unit_price',    # ✅ 新增
          'freight_adjust',        # ✅ 新增
          'freight_adjust_reason', # ✅ 新增
          'is_valid')              # ✅ 新增
```

## 📊 Master模型完整字段列表

根据`app/tms/model/models.py`中的Master模型定义：

```python
class Master(db.Model):
    __bind_key__ = "tms"
    __tablename__ = "tms_master"
    
    id = db.Column(db.Integer, primary_key=True)
    SP_NO = db.Column(db.String(32))
    BU_NO = db.Column(db.String(4))
    DN_NO = db.Column(db.String(255))
    DN_DATE = db.Column(db.Date)
    PO_NO = db.Column(db.String(32))
    PO_DATE = db.Column(db.Date)
    cs_specialist = db.Column(db.String(32))
    payer_company = db.Column(db.String(32))
    receiver_name = db.Column(db.String(32))
    receiver_address = db.Column(db.String(80))
    delivery_terms = db.Column(db.String(3))
    dn_attachment = db.Column(db.String(255))
    goods_value = db.Column(db.Float(53))
    volume = db.Column(db.Float(53))
    weight = db.Column(db.Float(53))
    freight_fee = db.Column(db.Float(53))
    fee_remark = db.Column(db.String(50))
    status = db.Column(db.Integer)
    create_time = db.Column(db.DateTime())
    business_type = db.Column(db.Integer)
    warehouse = db.Column(db.Integer)
    shipping_point = db.Column(db.String(4))
    release_time = db.Column(db.DateTime())
    freight_calc_method = db.Column(db.Integer)
    freight_unit_price = db.Column(db.Float(53))
    freight_adjust = db.Column(db.Float(53))
    freight_adjust_reason = db.Column(db.String(255))
    is_valid = db.Column(db.SmallInteger)
```

## ✅ 调整结果

### 移除的字段（不存在于Master模型）
- `CUSTOMER_NO` - 客户编号
- `sale_order` - 销售订单
- `payer_name` - 付款人姓名
- `payer_address` - 付款人地址
- `payer_phone` - 付款人电话

### 新增的字段（之前遗漏的）
- `create_time` - 创建时间
- `shipping_point` - 发货点
- `release_time` - 释放时间
- `freight_calc_method` - 运费计算方法
- `freight_unit_price` - 运费单价
- `freight_adjust` - 运费调整
- `freight_adjust_reason` - 运费调整原因
- `is_valid` - 是否有效

## 🎯 影响的API接口

### 1. `/tms/order/getdnlist` - 获取订单列表
- ✅ 现在返回完整的Master模型字段
- ✅ 移除了不存在的字段，避免序列化错误
- ✅ 新增了重要的业务字段如运费相关信息

### 2. `/tms/order/generatepdf/<dn_no>` - 生成PDF
- ✅ 使用调整后的orderSchema序列化Master数据
- ✅ 确保PDF生成时能获取到所有必要字段

### 3. `/tms/order/release_dn` - 释放订单
- ✅ 工作流程不受影响
- ✅ 可以正确处理Master模型的所有字段

## 📝 API响应示例

调整后的API响应将包含完整的字段：

```json
{
  "meta": {
    "status": 200,
    "msg": "获取订单列表成功"
  },
  "data": [
    {
      "id": 1,
      "SP_NO": "SP00001",
      "BU_NO": "1200",
      "DN_NO": "80405823",
      "DN_DATE": "2022-01-04",
      "PO_NO": "SHTH210906-1",
      "PO_DATE": "2021-09-06",
      "cs_specialist": "CHERRY.YIN",
      "payer_company": "上海添浩环保科技发展有限公司",
      "receiver_name": "方忠",
      "receiver_address": "上海市金山区亭林镇松隐小康路34号119室",
      "delivery_terms": "DAP",
      "dn_attachment": null,
      "goods_value": 15000.00,
      "volume": 2.5,
      "weight": 150.0,
      "freight_fee": 500.00,
      "fee_remark": "标准运费",
      "status": 1,
      "create_time": "2022-01-04T10:30:00",
      "business_type": 1,
      "warehouse": 1,
      "shipping_point": "SH01",
      "release_time": null,
      "freight_calc_method": 1,
      "freight_unit_price": 10.00,
      "freight_adjust": 0.00,
      "freight_adjust_reason": null,
      "is_valid": 1
    }
  ]
}
```

## 🔧 技术细节

### 序列化器位置
- 文件：`app/tms/route/order.py`
- 类名：`orderSchema`
- 基类：`ma.Schema`

### 相关模型
- 主模型：`Master` (tms_master表)
- 关联模型：`Products` (tms_products表)
- 工作流：`WorkFlow` (tms_workflow表)

## ✅ 验证方法

1. **字段完整性检查**
   ```python
   # 确保所有Master模型字段都在Schema中
   master_fields = [field.name for field in Master.__table__.columns]
   schema_fields = orderSchema().fields.keys()
   assert set(master_fields) == set(schema_fields)
   ```

2. **API测试**
   ```bash
   # 测试获取订单列表
   curl -X GET "http://localhost:5000/tms/order/getdnlist" \
        -H "Authorization: Bearer <token>"
   ```

3. **序列化测试**
   ```python
   # 测试序列化是否正常
   master = Master.query.first()
   data = orderSchema().dump(master)
   # 应该不会出现序列化错误
   ```

## 🎉 总结

✅ **完成的工作：**
- 移除了5个不存在的字段
- 新增了8个遗漏的字段
- orderSchema现在与Master模型完全对齐
- 所有相关API接口将正常工作

✅ **预期效果：**
- 消除序列化错误
- 提供完整的订单数据
- 支持所有业务功能
- 便于前端开发和数据展示

现在orderSchema已经完全根据Master模型文件进行了调整，确保了数据的完整性和一致性！

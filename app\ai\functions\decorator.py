from app.im.functions.response import responseError, responseExpire
from flask import request
import traceback
from config import config, env
from functools import wraps
from flask_jwt_extended import decode_token, JWTManager
import jwt


def login_required(view_func):  # 通过一个装饰函数来实现登录验证
    @wraps(view_func)
    def verify_token(*args, **kwargs):
        try:
            token = request.headers["Authorization"]
        except Exception:
            traceback.print_exc()
            return responseError('登录失败！')
        try:
            decoded_token = decode_token(token)
        except jwt.exceptions.ExpiredSignatureError:
            return responseExpire('登录已过期！请重新登录！')
        return view_func(*args, **kwargs)
    return verify_token

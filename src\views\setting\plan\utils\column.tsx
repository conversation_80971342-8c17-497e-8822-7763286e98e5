import type { AdaptiveConfig, LoadingConfig } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { getexchangematerial } from "@/api/operation";
import { ref, onMounted, reactive, h } from "vue";
import { addDialog } from "@/components/ReDialog";
import addForm from "../component/uploadplan.vue";
import moment from "moment";
import { toRaw } from "vue";
import { message } from "@/utils/message";

export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);
  const search_condition = reactive({
    selected_date: moment().format("YYYY-MM-DD")
  });

  onMounted(() => {});

  const formRef = ref();
  // 上传刷新生产计划
  const openUpload = (action: string, row?: any) => {
    addDialog({
      title: "上传生产计划",
      width: "30%",
      draggable: true,
      fullscreenIcon: false,
      closeOnClickModal: false,
      contentRenderer: () => h(addForm, { ref: formRef }),
      beforeSure: async (done, { options }) => {
        const FormRef = formRef.value.getRef();
        function chores() {
          message(`${action == "change" ? "修改" : "新增"}数据成功！`, {
            type: "success"
          });
          done(); // 关闭弹框
          // getRoutingList(); // 刷新表格数据
        }
        // const formdata = options.props.formInline as FormItemProps;
        FormRef.validate(valid => {
          if (valid) {
            console.log("准备upload");
          }
        });
      }
    });
  };

  const search = () => {
    loading.value = true;
    console.log(toRaw(search_condition));
    getexchangematerial(toRaw(search_condition))
      .then((res: { data: any; meta: any }) => {
        if (res.meta.status != 200) {
          message(res.meta.msg, { type: "error" });
          loading.value = false;
          return;
        }
        dataList.value = res.data;
        loading.value = false;
      })
      .catch(() => {
        message("系统请求数据错误！", { type: "error" });
        loading.value = false;
      });
  };

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载换料单数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  /** 撑满内容区自适应高度相关配置 */
  const adaptiveConfig: AdaptiveConfig = {
    /** 表格距离页面底部的偏移量，默认值为 `96` */
    offsetBottom: 30
    /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
    // fixHeader: true
    /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
    // timeout: 60
    /** 表头的 `z-index`，默认值为 `100` */
    // zIndex: 100
  };

  const columns: TableColumnList = [
    {
      label: "粒子料号",
      prop: "component",
      minWidth: "100"
    },
    {
      label: "粒子描述",
      prop: "component_des",
      minWidth: "160"
    },
    {
      label: "类型",
      prop: "component_type",
      width: "90",
      cellRenderer: ({ row }) => {
        if (row.component_type == 1) {
          return <el-tag size="large">树脂</el-tag>;
        } else if (row.component_type == 2) {
          return (
            <el-tag size="large" type="success">
              色母
            </el-tag>
          );
        } else if (row.component_type == 3) {
          return (
            <el-tag size="large" type="warning">
              发泡剂
            </el-tag>
          );
        }
      }
    },
    {
      label: "重量",
      prop: "total_weight",
      formatter(row, column, cellValue) {
        if (Number.isInteger(cellValue)) {
          return cellValue.toLocaleString();
        } else {
          return cellValue.toFixed(3);
        }
      }
    },
    {
      label: "单位",
      prop: "unit",
      width: "80",
      formatter() {
        return "KG";
      }
    },
    {
      label: "不良描述",
      prop: "defect_des"
    }
  ];

  return {
    search_condition,
    search,
    openUpload,
    loading,
    columns,
    dataList,
    loadingConfig,
    adaptiveConfig
  };
}

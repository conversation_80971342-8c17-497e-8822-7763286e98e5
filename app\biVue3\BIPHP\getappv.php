	<?php
include("coon.php");

$sql3 ="select round(avg(datediff(current_date(),idate)),0) as df,dept1,count(dept1) as qty from wl_suggest left join wl_userinfo on wl_userinfo.eid=wl_suggest.eid 
where status='open' group by dept1 order by df desc";
$query=mysqli_query($link, $sql3);
$total=0;
if (mysqli_num_rows($query) >= 1) {
    while ($rs = mysqli_fetch_array($query)) {
        $output['yaxis'][]=$rs['dept1'];
        $df=$rs['df'];
        $output['data'][]=$df;
        if ($df>$total) {
            $total=$df;
        }
        $output['pie'][]=$rs['qty'];
    }
    $output['total']=$total;
}
print_r(json_encode($output, JSON_UNESCAPED_UNICODE));

?>
	
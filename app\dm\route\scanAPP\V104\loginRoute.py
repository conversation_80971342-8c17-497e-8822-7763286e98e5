from flask import Blueprint, request
from app.dm.model.models_dm import User, Permission, Appversion, Appdevices
from extensions import db
import requests
from config import config, env
import traceback
import datetime
from app.public.functions import create_token, responseError, responsePost, responseGet
from app.dm.functions import login_required
api = Blueprint('dm/app/V104/loginAPI', __name__)


@api.route('/getVersion', methods=['GET'])  # 获取app版本
def getVersion():
    res = request.args
    appname = res.get('appname')
    deviceId = res.get('deviceId')
    version = res.get('version') if res.get('version') else ''
    lastline = res.get('lastline') if res.get('lastline') else ''
    ar = db.session.query(Appdevices).filter(
        Appdevices.deviceId == deviceId).first()
    if not ar:
        area = {
            'area': '',
            'plant': ''
        }
        device = Appdevices(deviceId=deviceId)
        db.session.add(device)
        db.session.commit()
    else:
        area = {
            'area': ar.area,
            'plant': ar.plant
        }
        ar.lastdate = datetime.datetime.now()
        ar.lastcount = ar.lastcount+1 if ar.lastcount else 1
        ar.lastversion = version
        ar.lastline = lastline
        db.session.commit()
    v = db.session.query(Appversion).filter(Appversion.appname ==
                                            appname).filter(Appversion.active == 1).scalar()
    if v:
        return responseGet('成功获取信息', {'version': v.version, 'releasedate': v.releasedate.strftime('%Y-%m-%d'), 'desc': v.desc, 'area': area['area'], 'plant': area['plant']})
    else:
        return responseError('未找到此app信息')


@api.route('/getDeviceinfo', methods=['GET'])  # 获取app信息
def getDeviceinfo():
    res = request.args
    deviceId = res.get('deviceId')
    ar = db.session.query(Appdevices.area, Appdevices.plant).filter(
        Appdevices.deviceId == deviceId).first()
    if not ar:
        area = {
            'area': '',
            'plant': ''
        }
        device = Appdevices(deviceId=deviceId)
        db.session.add(device)
        db.session.commit()
    else:
        area = {
            'area': ar.area,
            'plant': ar.plant
        }
    return responseGet('成功获取信息', {'area': area['area'], 'plant': area['plant']})


@api.route("/login", methods=["POST"])  # 登录
def login():
    res_dir = request.json
    if res_dir is None:
        res_dir = request.form
    if not len(res_dir):
        return responseError('请输入用户名密码')
    # 获取前端传过来的参数
    email = res_dir.get("email")
    password = res_dir.get("password")
    r = requests.post(config[env].api_url+"/public/login",
                      {'username': email, 'password': password})
    vdata = r.json()
    if vdata['meta']['status'] != 201:
        return responseError(vdata['meta']['msg'])
    else:
        email = vdata['data']['email']
        plant = vdata['data']['plant']
    try:
        user = db.session.query(User.auth, User.Id, User.email).filter(
            User.isactive == 1).filter(User.email == email).first()
    except Exception:
        traceback.print_exc()
        return responseError('查询错误，请联系管理员！')
    if user is None:
        return responseError('本系统中此账户不存在或被禁用，请联系管理员处理！')
    print(user.auth.split(','))
    if '5' in user.auth.split(','):
        data = {'token': create_token(user.Id), 'email': email, 'plant': plant}
        return responsePost('登录成功', data)
    else:
        return responseError('无法登录，该账号没有开通权限，请通知管理员开通')


@ api.route("/getMenu", methods=["get"])  # 获取产线树状列表
@login_required
def getMenu():
    auth = request.headers["auth"]
    # print('authaaa', auth)
    ms = db.session.query(Permission).filter(Permission.path.in_(auth.split(','))).filter(
        Permission.ismenu == 1).order_by(Permission.pid, Permission.order).all()
    dic = {}
    outArray = []

    for m in ms:
        if m.pid == 0:
            dic[m.Id] = {
                'name': m.name,
                'children': []
            }
        if m.pid > 0:
            if m.pid in dic.keys():
                dic[m.pid]['children'].append(
                    {
                        'name': m.name,
                        'path': '/'+m.path
                    }
                )
    for value in dic.values():
        outArray.append(value)
    return responseGet('获取成功', outArray)

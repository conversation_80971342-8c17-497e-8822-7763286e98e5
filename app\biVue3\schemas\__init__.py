from extensions import ma
from marshmallow import fields


class IssueSchema(ma.Schema):
    id = fields.Integer()
    sqdctype = fields.String()
    problemtype = fields.String()
    linename = fields.String()
    recorder = fields.String()
    desc = fields.String()
    recordtime = fields.String()
    owner = fields.String()
    rootcause = fields.String()
    ctmeasure = fields.String()
    firstdate = fields.String()
    duedate = fields.String()
    findate = fields.String()
    status = fields.String()
    close = fields.Integer()
    csreason = fields.String()
    inform = fields.Integer()
    pastdue = fields.String()
    deptname = fields.String()
    qty = fields.Integer()


Issues_schema = IssueSchema(many=True)
Issue_schema = IssueSchema()

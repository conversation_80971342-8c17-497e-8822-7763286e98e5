import pandas as pd
import chardet
import re
import os


def convert_to_utf8_csv(input_file, output_file, tag):
    # 自动检测文件编码
    print('sff', os.getcwd())
    with open(input_file, 'rb') as f:
        result = chardet.detect(f.read())
    encoding = result['encoding']

    # 根据文件扩展名确定文件类型
    file_extension = input_file.split('.')[-1].lower()

    # 读取Excel文件
    if file_extension == 'xlsx' or file_extension == 'xlsm':
        try:
            df = pd.read_excel(input_file, engine='openpyxl')
            print(111, df.columns)
        except Exception:
            arr = dealFakeExcel(input_file)
            if len(arr) > 0:
                df = pd.DataFrame(arr[1:], columns=arr[0])
            else:
                return -1
    elif file_extension == 'xls':
        try:
            df = pd.read_excel(input_file, engine='xlrd')
        except Exception:
            arr = dealFakeExcel(input_file)
            # print(arr)
            # return
            if len(arr) > 0:
                df = pd.DataFrame(arr[1:], columns=arr[0])
            else:
                return -1
    # 读取CSV文件
    elif file_extension == 'csv' or file_extension == 'txt':
        df = pd.read_csv(input_file, encoding=encoding)
    else:
        return -1
    # 将 'Unnamed' 列名替换为空字符串
    df.columns = ["" if isinstance(col, str) and "Unnamed" in col else str(col) for col in df.columns]
    # 处理第三列的数字格式
    if tag == 'ASN' and len(df.columns) >= 3:
        df.iloc[:, 2] = df.iloc[:, 2].apply(lambda x: re.sub(r'[^\d.]', '', str(x)) if pd.notnull(x) else x)
        df.iloc[:, 2] = pd.to_numeric(df.iloc[:, 2], errors='coerce')
    # 尝试保存为不带 BOM 的 UTF-8 编码
    if tag == 'SAP1':
        df.to_csv(output_file, index=False, encoding='utf-8')
    else:
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
    return len(df)


def dealFakeExcel(fn):
    f = open(fn, encoding='utf-16', errors='ignore')
    line = f.readline()
    b = []
    while line:
        a = line.split('\t')
        b.append(a)
        line = f.readline()
    f.close()
    arr = []
    for i in range(len(b)):
        row = []
        if len(b[i]) > 1:
            for j in range(len(b[i])):
                if b[i][j] != '':
                    row.append(b[i][j].replace('\x00', '').replace('\n', ''))
            # print(row)
            arr.append(row)
    firstrow = arr[0]
    for row in arr:
        if len(row) < len(firstrow):
            for j in range(len(firstrow)-len(row)):
                row.append('')
    return arr


if __name__ == '__main__':
    frompath = os.getcwd()+'/mails/222.xlsx'
    topath = os.getcwd()+'/mails/222done.csv'
    a = convert_to_utf8_csv(frompath, topath, 'ASN')
    print(a)

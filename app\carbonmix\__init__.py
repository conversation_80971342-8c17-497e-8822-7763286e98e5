def importCarbonmixRoute(app):
    from app.carbonmix.route.loginRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/carbonmix/loginAPI")

    from app.carbonmix.route.templateRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/carbonmix/templateAPI")

    from app.carbonmix.route.settingRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/carbonmix/settingAPI")

    from app.carbonmix.route.calRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/carbonmix/calAPI")

from flask import Blueprint, request
from app.welean.model.models_welean import Account, Setting<PERSON>, Suggest, <PERSON>rinfo, Scan, Layeredresult, Layeredlist
from extensions import db
from sqlalchemy import func, desc, or_, and_
from sqlalchemy.orm import aliased
from app.public.functions import responseGet, responsePost, responseError, responsePut
from datetime import datetime, timedelta
import traceback
from app.welean.functions import TASKthread, getServer
api = Blueprint('welean/bi/issueAPI', __name__)
Userinfo1 = aliased(Userinfo)
Userinfo2 = aliased(Userinfo)

boardDept = ['EHS', 'F&M', 'Lean', 'M&L', 'Quality',
             'R&D', 'ME', 'Production', 'Planning']
vsmsub = {
    'VSM 1': 'Production',
    'VSM 2': 'Production',
    'Production': 'Production',
    'ME': 'ME',
    'Production Planning': 'Planning',
    'Process Quality': 'Quality'
}

wxboardDept = ['EHS',
               'Engineering',
               'Material',
               'ME&NPI',
               'Operations',
               'Production',
               'Quality',
               'Warehouse']


@api.route('/checkscan', methods=['GET'])
def checkscan():
    res = request.args
    scankey = res.get('scankey')
    stype = res.get('stype')
    isscan = db.session.query(Scan).filter(Scan.scankey == scankey).filter(
        Scan.status == 1).filter(Scan.stype == stype).first()
    if isscan:
        return responseGet('success')
    else:
        return responseError('fail')


@api.route('/initscan', methods=['POST'])
def initscan():
    res = request.args
    scankey = res.get('scankey')
    stype = res.get('stype')
    try:
        scan = Scan(scankey=scankey, stype=stype, scantime=datetime.now(), status=0)
        print(scan)
        db.session.add(scan)
        db.session.commit()
    except Exception:
        traceback.print_exc()
        db.session.rollback()
        return responseError('fail')
    return responsePost('success')


@api.route('/checkAndon', methods=['GET'])
def checkAndon():
    res = request.args
    andontime = res.get('andontime')
    linename = res.get('linename')
    sug = db.session.query(Suggest.Id, Userinfo.cname).join(Userinfo, Userinfo.eid == Suggest.eid).filter(Suggest.audittype == 4).filter(
        Suggest.appvdate == andontime).filter(Suggest.linename == linename).first()
    if sug:
        return responseGet('success', {'cname': sug.cname, 'id': sug.Id})
    else:
        return responseError('fail')


@api.route('/escSuggest', methods=['PUT'])
def escSuggest():
    res = request.json
    id = res.get('id')
    comments = res.get('comments')
    content = res.get('content')
    exeid = res.get('exeid')
    sug = db.session.query(Suggest).filter(Suggest.Id == id).first()
    try:
        if sug:
            sug.comments = comments
            sug.content = content
            sug.exeid = exeid
            sug.mdi = 'T3'
            db.session.commit()
        return responsePut('success')
    except Exception:
        db.session.rollback()
        return responseError('fail')


@api.route('/acceptSuggest', methods=['PUT'])
def acceptSuggest():
    res = request.json
    id = res.get('id')
    comments = res.get('comments')
    content = res.get('content')
    exeid = res.get('exeid')
    sug = db.session.query(Suggest).filter(Suggest.Id == id).first()
    try:
        if sug:
            sug.status = 'closed'
            if not sug.acdate:
                sug.acdate = datetime.now()
            sug.comments = comments
            sug.content = content
            sug.exeid = exeid
            sug.mdi = '结'
            db.session.commit()
        return responsePut('success')
    except Exception:
        db.session.rollback()
        return responseError('fail')


@api.route('/newSuggest', methods=['POST'])
def newSuggest():
    res = request.json
    idate = res.get('idate')
    if not idate:
        idate = datetime.now()
    aid = res.get('auditid')
    if not aid:
        aid = 0
    cfdate = res.get('cfdate')
    comments = res.get('comments')
    content = res.get('content')
    exeid = res.get('exeid')
    linename = res.get('linename')
    stype = res.get('stype')
    type2 = res.get('type2')
    plant = res.get('plant')
    mdi = res.get('mdi')
    machine = res.get('machine')
    missqty = res.get('missqty')
    missunit = res.get('missunit')
    if not cfdate or cfdate == '0000-00-00':
        cfdate = datetime.now()
    try:
        sug = Suggest(idate=idate, content=content, auditid=aid, machine=machine, missqty=missqty, missunit=missunit,
                      eid='9999999', exeid=exeid, status='ongoing', stype=stype, type2=type2, comments=comments, duedate=cfdate,
                      cfdate=cfdate, linename=linename, audittype=0, fifi='否', plant=plant, appvdate=cfdate, mdi=mdi, pastdue=0)
        db.session.add(sug)
        print(111, sug.exeid, exeid)
        if str(exeid) != '9999999':
            dealer = db.session.query(Account.wxid, Userinfo.cname).join(
                Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == exeid).first()
            taskBody = {
                'idate': datetime.strftime(datetime.now(), '%Y-%m-%d %H:%M:%S'),
                'linename': linename[:20],
                'suggester': 'MDI',
                'jobtype': 'MDI提案',
                'content': content[:20]
            }
            dwxid = dealer.wxid
            href = 'pages/login/login'
            TASKthread(taskBody, dwxid, href)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('出错了')
    return responsePost('成功')


@api.route('/delSuggest', methods=['POST'])
def delSuggest():
    res = request.json
    auditid = res.get('auditid')
    db.session.query(Suggest).filter(
        Suggest.auditid == auditid, Suggest.audittype == 0).delete()
    db.session.commit()
    return responsePost('成功')


@api.route('/addSuggest', methods=['POST'])
def addSuggest():
    res = request.json
    Id = res.get('Id')
    aid = res.get('auditid')
    cfdate = res.get('cfdate')
    comments = res.get('comments')
    content = res.get('content')
    exeid = res.get('exeid')
    linename = res.get('linename')
    stype = res.get('stype')
    type2 = res.get('type2')
    machine = res.get('machine')
    missqty = res.get('missqty')
    missunit = res.get('missunit')
    try:
        if Id:
            sug = db.session.query(Suggest).filter(Suggest.Id == Id).first()
        else:
            sug = db.session.query(Suggest).filter(
                Suggest.audittype == 0, Suggest.auditid == aid).first()
        pasdue = sug.pastdue if sug.pastdue else 0
        if cfdate:
            if sug.cfdate:
                if datetime.strftime(sug.cfdate, '%Y-%m-%d') < cfdate:
                    sug.pastdue = pasdue+1
            sug.cfdate = cfdate
            if not sug.duedate:
                sug.duedate = cfdate
        sug.comments = comments
        sug.content = content
        print(111, content)
        if sug and sug.exeid != exeid and str(sug.exeid) != '9999999':
            dealer = db.session.query(Account.wxid, Userinfo.cname).join(
                Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == exeid).first()
            taskBody = {
                'idate': datetime.strftime(datetime.now(), '%Y-%m-%d %H:%M:%S'),
                'linename': linename[:20],
                'suggester': 'MDI',
                'jobtype': '转移提案',
                'content': content[:20]
            }
            dwxid = dealer.wxid
            href = 'pages/login/login'
            TASKthread(taskBody, dwxid, href)
        sug.exeid = exeid
        sug.linename = linename
        sug.stype = stype
        sug.type2 = type2
        sug.machine = machine
        sug.missqty = missqty
        sug.missunit = missunit
        if pasdue > 3:
            sug.mdi = 'T3'
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('出错了')
    return responsePost('成功')


@api.route('/getLinesSZ', methods=['GET'])
def getLinesSZ():
    res = request.args
    plant = 'SZ'
    area = res.get('area')
    lines = db.session.query(Settings).filter(Settings.cate == 'areas').filter(
        Settings.plant == plant).filter(Settings.isactive == 1)
    if area:
        if area == 'B4WR':
            lines = lines.filter(Settings.name2.in_(['B4WR', 'B4WR2']))
        else:
            lines = lines.filter(Settings.name2 == area)
    lines = lines.all()
    lineArr = []
    for ll in lines:
        lineArr.append(ll.name3)
    types = db.session.query(Settings).filter(Settings.cate == 'sugtypes').filter(
        Settings.plant == plant).all()
    tpDic = {}
    for ll in types:
        if ll.name1 in tpDic.keys():
            tpDic[ll.name1]['children'].append({
                'label': ll.name2,
                'value': ll.name2
            })
        else:
            tpDic[ll.name1] = {
                'label': ll.name1,
                'value': ll.name1,
                'children': [{
                    'label': ll.name2,
                    'value': ll.name2
                }]
            }
    tpArr = list(tpDic.values())
    owners = db.session.query(Userinfo).join(Account, Userinfo.eid ==
                                             Account.eid).filter(Userinfo.plant == plant).filter(Userinfo.cate == 'Staff').filter(
        Userinfo.active == 1).order_by(desc(Userinfo.dept1), Userinfo.dept2).all()
    ownerDic = {}
    for o in owners:
        if o.dept1 in ownerDic.keys():
            ownerDic[o.dept1]['children'].append(
                {'value': o.eid,
                 'label': o.cname}
            )
        else:
            ownerDic[o.dept1] = {
                'label': o.dept1,
                'value': o.dept1,
                'children': [{'value': o.eid,
                              'label': o.cname}]
            }
    ownerArr = list(ownerDic.values())
    return responseGet("获取列表成功", {'lines': lineArr, 'types': tpArr, 'owners': ownerArr})


@api.route('/getAccountsSZ', methods=['GET'])
def getAccountsSZ():
    res = request.args
    query = res.get('query')
    line = res.get('line')
    area = res.get('area')
    # plant = 'SZ'
    print(111, query, line, area)
    suggests = db.session.query(Suggest.idate, Suggest.beforepic, Suggest.afterpic, Suggest.mdi, Suggest.exeid, Suggest.duedate,
                                Suggest.stype, Suggest.Id, Suggest.cfdate, Suggest.content, Userinfo2.dept1, Userinfo2.dept2,
                                Suggest.pastdue, Suggest.comments, Suggest.type2, Userinfo2.cname.label(
                                    'execname'), Suggest.linename, Suggest.machine, Suggest.missqty, Suggest.missunit,
                                Userinfo.cname, Suggest.audittype, Suggest.auditid, Suggest.acdate, Suggest.status).outerjoin(
        Userinfo2, Suggest.exeid == Userinfo2.eid).outerjoin(Settings, Suggest.linename == Settings.name3).join(
        Userinfo, Suggest.eid == Userinfo.eid)
    if area:
        if area == 'B4WR':
            suggests = suggests.filter(or_(Settings.name2.in_(['B4WR', 'B4WR2']), Suggest.linename.in_(['B4WR', 'B4WR2'])))
        else:
            suggests = suggests.filter(or_(Settings.name2 == area, Suggest.linename == area))
    else:
        suggests = suggests.filter(Suggest.mdi == 'T3')
    if line:
        suggests = suggests.filter(Suggest.linename == line)
    if query:
        suggests = suggests.filter(Suggest.content.like('%{0}%'.format(query)))
    else:
        suggests = suggests.filter(or_(Suggest.mdi == '是', Suggest.mdi == 'T3'))
    suggests = suggests.order_by(Suggest.cfdate).all()
    outArr = []
    for s in suggests:
        dept = s.dept1
        if dept == 'VSM 1' or dept == 'VSM 2':
            dept = vsmsub[s.dept2] if s.dept2 else ''
        if dept not in boardDept:
            dept = '其他'
        # content = s.content
        # if s.missqty:
        #     content = content+'\n'+'<共损失：%s%s>' % (s.missqty, s.missunit)
        # if s.stype == '停机损失' and s.linename == 'CAN':
        #     content = content+'\n'+'<问题设备：%s>' % (s.machine)
        outArr.append({
            'idate': datetime.strftime(s.idate, "%Y-%m-%d") if s.idate else '',
            'duedate': datetime.strftime(s.duedate, "%Y-%m-%d") if s.duedate else '',
            'stype': [s.stype, s.type2],
            'Id': s.Id,
            'cfdate': datetime.strftime(s.cfdate, "%Y-%m-%d") if s.cfdate else '',
            'content': s.content,
            'missqty': s.missqty,
            'missunit': s.missunit,
            'machine': s.machine,
            'dept1': dept,
            'audittype': s.audittype,
            'auditid': s.auditid,
            'exeid': [s.dept1, s.exeid],
            'comments': s.comments,
            'cname': s.cname,
            'linename': s.linename,
            'execname': s.execname,
            'acdate': datetime.strftime(s.acdate, "%Y-%m-%d") if s.acdate else '',
            'status': s.status,
            'pastdue': s.pastdue,
            'mdi': s.mdi,
            'beforepic': getServer()['suggestionUrl']+s.beforepic.split(',')[0] if s.beforepic else '',
            'afterpic': getServer()['suggestionUrl']+s.afterpic.split(',')[0] if s.afterpic else '',
            'picsArr': []
        })
    return responseGet("获取列表成功", {'suggests': outArr})


@api.route('/getDailyIssues', methods=['GET'])
def getDailyIssues():
    res = request.args
    linename = res.get('linename')
    shiftdate = res.get('shiftdate')
    suggests = db.session.query(Suggest.Id, Suggest.exeid, Suggest.idate, Suggest.auditid, Suggest.stype, Userinfo.cname, Suggest.content).join(
        Userinfo, Suggest.eid == Userinfo.eid).filter(Suggest.linename == linename).filter(
        func.date(Suggest.idate) == shiftdate).all()
    outArr = []
    for s in suggests:
        outArr.append({
            'Id': s.Id,
            'exeid': s.exeid,
            'auditid': s.auditid,
            'stype': s.stype,
            'idate': datetime.strftime(s.idate, '%H:%M'),
            'cname': s.cname,
            'content': s.content
        })
    print(suggests)
    return responseGet('aa', {'suggests': outArr})


@api.route('/getPastdueIssues', methods=['GET'])
def getPastdueIssues():
    td = datetime.now()
    suggests = db.session.query(Suggest).filter(Suggest.status == 'ongoing').filter(
        or_(Suggest.cfdate < td, Suggest.cfdate.is_(None))).all()
    outDic = {}
    for s in suggests:
        if s.exeid in outDic.keys():
            outDic[s.exeid]['content'] = outDic[s.exeid]['content']+'\n'+s.content
        else:
            outDic[s.exeid] = {
                'eid': s.exeid,
                'content': s.content
            }
    return responseGet('aa', {'suggests': outDic})


@api.route('/getLSWbyeid', methods=['GET'])
def getLSWbyeid():
    res = request.args
    eid = res.get('eid')
    suggests = db.session.query(Suggest).filter(Suggest.exeid == eid).filter(Suggest.status == 'ongoing').all()
    outDic = {
        '1.当前TP项目': [],
        '2.稽核任务': [],
        '3.提案任务': []
    }
    for s in suggests:
        outDic['3.提案任务'].append({
            'Id': s.Id,
            'name': s.content,
            'cfdate': datetime.strftime(s.duedate, "%Y-%m-%d") if s.duedate else '',
            'tasktype': s.type2,
            'subitem': s.mdi
        })
    audits = db.session.query(Layeredresult.Id, Layeredlist.name, Layeredlist.tasktype, Layeredlist.subitem, Layeredlist.lastfinish, Layeredlist.freq).join(
        Layeredlist, Layeredresult.listid == Layeredlist.Id).filter(
        Layeredresult.eid == eid, Layeredresult.status == 0).limit(5).all()
    for a in audits:
        outDic['2.稽核任务'].append({
            'Id': a.Id,
            'name': a.name,
            'cfdate': datetime.strftime(a.lastfinish+timedelta(days=a.freq), '%Y-%m-%d') if a.lastfinish else datetime.strftime(datetime.now()+timedelta(days=a.freq), '%Y-%m-%d'),
            'tasktype': a.tasktype,
            'subitem': a.subitem
        })
    return responseGet('lsw', outDic)


@api.route('/getMDIIssues', methods=['GET'])
def getMDIIssues():
    res = request.args
    area = res.get('area')
    startdate = res.get('startdate')
    enddate = res.get('enddate')
    plant = res.get('plant')
    suggests = db.session.query(Settings.name1, Settings.name3, Settings.name2, Suggest.Id, Suggest.fifi,
                                Suggest.idate, Suggest.cfdate, Suggest.acdate, Suggest.afterpic, Suggest.content,
                                Userinfo1.cname.label('scname'), Userinfo1.dept1.label(
                                    'sdept'), Suggest.status, Suggest.stype, Suggest.type2, Userinfo2.cname.label('dcname'), Suggest.mdi, Suggest.auditid,
                                Userinfo2.dept1.label('ddept'), Suggest.comments, Suggest.duedate).outerjoin(Userinfo1, Suggest.eid == Userinfo1.eid).outerjoin(
        Userinfo2, Suggest.exeid == Userinfo2.eid).outerjoin(Settings, Suggest.linename == Settings.name3).filter(Suggest.plant == plant).filter(
        and_(Suggest.idate >= startdate, Suggest.idate <= enddate)).filter(Settings.name2 == area).filter(
            Suggest.mdi.in_(['是', '结', 'T3'])).order_by(desc(Userinfo2.cname), desc(Suggest.acdate)).all()
    outArr = []
    for q in suggests:
        outArr.append(
            {
                'Id': q.Id,
                'fifi': q.fifi,
                'idate': datetime.strftime(q.idate, "%Y-%m-%d") if q.idate else '',
                'cfdate': datetime.strftime(q.cfdate, "%Y-%m-%d") if q.cfdate else '',
                'acdate': datetime.strftime(q.acdate, "%Y-%m-%d") if q.acdate else '',
                'afterpic': getServer()['suggestionUrl']+q.afterpic.split(',')[0] if q.afterpic else '',
                'content': q.content,
                'scname': q.scname,
                'sdept': q.sdept,
                'status': q.status,
                'stype': q.stype,
                'type2': q.type2,
                'dcname': q.dcname,
                'ddept': q.ddept,
                'comments': q.comments,
                'duedate': datetime.strftime(q.duedate, "%Y-%m-%d") if q.duedate else '',
                'linename': q.name3,
                'area': q.name2,
                'plant': q.name1,
                'mdi': q.mdi,
                'auditid': q.auditid,
                'sqdctype': '',
                'problemtype': '',
                'qty': '',
                'issuemin': ''
            }
        )

    print(suggests)
    return responseGet('aa', {'suggests': outArr})


@api.route('/getLinesWX', methods=['GET'])
def getLinesWX():
    plant = 'WX'
    lines = db.session.query(Settings).filter(Settings.cate == 'areas').filter(
        Settings.plant == plant).filter(Settings.isactive == 1)
    lines = lines.all()
    lineArr = []
    for ll in lines:
        lineArr.append(ll.name3)
    types = db.session.query(Settings).filter(Settings.cate == 'sugtypes').filter(
        Settings.plant == plant).all()
    tpDic = {}
    for ll in types:
        if ll.name1 in tpDic.keys():
            tpDic[ll.name1]['children'].append({
                'label': ll.name2,
                'value': ll.name2
            })
        else:
            tpDic[ll.name1] = {
                'label': ll.name1,
                'value': ll.name1,
                'children': [{
                    'label': ll.name2,
                    'value': ll.name2
                }]
            }
    tpArr = list(tpDic.values())
    owners = db.session.query(Userinfo).join(Account, Userinfo.eid ==
                                             Account.eid).filter(Userinfo.plant == plant).filter(
        Userinfo.active == 1).order_by(desc(Userinfo.dept1), Userinfo.dept2).all()
    ownerDic = {}
    for o in owners:
        if o.dept1 in ownerDic.keys():
            ownerDic[o.dept1]['children'].append(
                {'value': o.eid,
                 'label': o.cname}
            )
        else:
            ownerDic[o.dept1] = {
                'label': o.dept1,
                'value': o.dept1,
                'children': [{'value': o.eid,
                              'label': o.cname}]
            }
    ownerArr = list(ownerDic.values())
    return responseGet("获取列表成功", {'lines': lineArr, 'types': tpArr, 'owners': ownerArr})


@api.route('/getAccountsWX', methods=['GET'])
def getAccountsWX():
    res = request.args
    query = res.get('query')
    line = res.get('line')
    plant = 'WX'
    suggests = db.session.query(Suggest.idate, Suggest.beforepic, Suggest.afterpic, Suggest.mdi, Suggest.exeid, Suggest.duedate,
                                Suggest.stype, Suggest.Id, Suggest.cfdate, Suggest.content, Userinfo2.dept1, Userinfo2.dept2,
                                Suggest.pastdue, Suggest.comments, Suggest.type2, Userinfo2.cname.label(
                                    'execname'), Suggest.linename, Suggest.machine, Suggest.missqty, Suggest.missunit,
                                Userinfo.cname, Suggest.audittype, Suggest.auditid, Suggest.acdate, Suggest.status).outerjoin(
        Userinfo2, Suggest.exeid == Userinfo2.eid).join(
        Userinfo, Suggest.eid == Userinfo.eid).filter(Suggest.plant == plant).filter(func.date(Suggest.idate) >= '2025-01-01')
    if line:
        suggests = suggests.join(Settings, Suggest.linename == Settings.name3).filter(Suggest.linename == line)
    if query:
        suggests = suggests.filter(Suggest.content.like('%{0}%'.format(query)))
    else:
        suggests = suggests.filter(Suggest.mdi == '是')
    suggests = suggests.order_by(Suggest.cfdate).all()
    outArr = []
    for s in suggests:
        dept = s.dept1
        if dept not in wxboardDept:
            dept = '其他'
        outArr.append({
            'idate': datetime.strftime(s.idate, "%Y-%m-%d") if s.idate else '',
            'duedate': datetime.strftime(s.duedate, "%Y-%m-%d") if s.duedate else '',
            'stype': [s.stype, s.type2],
            'Id': s.Id,
            'cfdate': datetime.strftime(s.cfdate, "%Y-%m-%d") if s.cfdate else '',
            'content': s.content,
            'missqty': s.missqty,
            'missunit': s.missunit,
            'machine': s.machine,
            'dept1': dept,
            'audittype': s.audittype,
            'auditid': s.auditid,
            'exeid': [s.dept1, s.exeid],
            'comments': s.comments,
            'cname': s.cname,
            'linename': s.linename,
            'execname': s.execname,
            'acdate': datetime.strftime(s.acdate, "%Y-%m-%d") if s.acdate else '',
            'status': s.status,
            'pastdue': s.pastdue,
            'mdi': s.mdi,
            'beforepic': getServer()['suggestionUrl']+s.beforepic.split(',')[0] if s.beforepic else '',
            'afterpic': getServer()['suggestionUrl']+s.afterpic.split(',')[0] if s.afterpic else '',
            'picsArr': []
        })
    return responseGet("获取列表成功", {'suggests': outArr})

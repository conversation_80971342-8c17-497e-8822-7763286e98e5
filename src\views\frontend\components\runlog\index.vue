<script setup lang="ts">
import { useProdStoreHook } from "@/store/modules/prod";
import { watch, onMounted, onUnmounted } from "vue";
import { message } from "@/utils/message";
import { useColumns } from "./utils/columns";
import { ref, Ref } from "vue";
const {
  changeState,
  autoCheckState,
  dataList,
  columns,
  loadingConfig,
  adaptiveConfig,
  loading,
  refreshData
} = useColumns();
</script>

<template>
  <div class="main">
    <div class="header">
      <span style="margin-right: 10px" class="lightfont"
        >设备运行及停机记录表</span
      >
      <el-button
        type="primary"
        @click="
          changeState(
            useProdStoreHook().machineId,
            useProdStoreHook().selectedDate,
            useProdStoreHook().shift,
            undefined,
            undefined,
            undefined,
            undefined,
            'add',
            () => {
              refreshData();
            }
          )
        "
        ><el-icon :size="18"><CirclePlus /></el-icon>&nbsp; 插入状态
      </el-button>
    </div>
    <div class="content">
      <pure-table
        ref="tableRef"
        border
        stripe
        adaptive
        :adaptiveConfig="adaptiveConfig"
        row-key="id"
        alignWhole="center"
        showOverflowTooltip
        :loading="loading"
        :loading-config="loadingConfig"
        :data="dataList"
        :columns="columns"
        height="100%"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 100%;
  flex: 1;
  .header {
    .lightfont {
      font-size: 18px;
      text-shadow:
        0 0 10px red,
        0 0 20px red,
        0 0 30px red,
        0 0 40px red;
    }

    .el-button {
      margin: 0 10px 5px 0;
    }
  }
  .content {
    // border: 1px solid red;
    display: flex;
    justify-content: flex-start;
    align-items: start;
    color: grey;
    height: 100%;
  }
}

:deep(.el-table__row) {
  background-color: transparent;
}

.pure-table {
  height: 100%;
  .el-table--border {
    height: 100%;
  }
}
</style>

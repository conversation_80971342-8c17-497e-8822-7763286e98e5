from flask import Blueprint,  request
import requests
from sqlalchemy import func, not_, and_, or_, desc
from config import config, env
from app.public.functions import responseGet, responsePut, responseError, responsePost
from app.dm.model.models_dm import Scaninfo, Shiftinfo, Issuelog,  Lineinfo as linfo, Restinfo, Routing, Flexinfo
from app.dm.functions import getkpi, getproblems, getReqQty, getproblemsoee, dmIssueChange, dmIssueSubmit
from app.biVue3.route.t2ehsRoute import downLoadPics
from extensions import db
import datetime
import os
import traceback
api = Blueprint('bi3/t2costAPI', __name__)


@ api.route('/changeRouting', methods=['PUT'])  # BI界面快速修改routing
def changeRouting():
    res = request.json
    sku = res.get('sku')
    routing = res.get('routing')
    headcount = res.get('headcount') if res.get('headcount') else 0
    linename = res.get('linename') if res.get('linename') else ''
    dt2 = res.get('dt2')
    rt = round(1/routing, 6)
    try:
        db.session.query(Shiftinfo).filter(Shiftinfo.sku == sku).filter(Shiftinfo.starttime >= dt2).filter(Shiftinfo.linename==linename).update({
            'routing': rt
        })
        flex = db.session.query(Flexinfo).filter(Flexinfo.sku == sku).order_by(desc(Flexinfo.revision)).first()
        if flex:
            if flex.revision == datetime.date.today() and flex.linename == linename and flex.headcount == headcount:
                flex.hourlyrate = routing
            else:
                newFlex = Flexinfo(headcount=headcount, linename=linename, sku=sku, hourlyrate=routing*headcount, revision=datetime.date.today())
                db.session.add(newFlex)
        else:
            rmod = db.session.query(Routing).filter(
                Routing.sku == sku).filter(Routing.revision == dt2).first()
            if rmod:
                rmod.routing = rt
            else:
                rmd = Routing(sku=sku, revision=datetime.date.today(), routing=rt)
                db.session.add(rmd)
        db.session.commit()
        return responsePut('修改成功')
    except Exception:
        db.session.rollback()
        return responseError('修改失败，请联系管理员')


@ api.route('/escBoard', methods=['PUT'])  # 提升到Tie3，把MDI标记为T3
def escBoard():
    res = request.json
    id = res.get('Id')
    exeid = res.get('exeid')[1]
    comments = res.get('comments')
    content = res.get('content')
    res1 = requests.put(
        config[env].cloud_url+"welean/bi/issueAPI/escSuggest", json={
            'id': id,
            'comments': comments,
            'content': content,
            'exeid': exeid
        })
    content1 = res1.json()
    if content1["meta"]["status"] == 202:
        return responsePut("提升到TIE3成功", {'info': 'success'})
    else:
        return responseError('获取问题列表失败，请尝试刷新或联系管理员')


@ api.route('/acceptBoard', methods=['PUT'])  # 发送云端结束一个提案，状态会标记为closed，结束人时工号为9999999的EMDI用户
def acceptBoard():
    res = request.json
    id = res.get('Id')
    exeid = res.get('exeid')[1]
    comments = res.get('comments')
    content = res.get('content')
    res1 = requests.put(
        config[env].cloud_url+"welean/bi/issueAPI/acceptSuggest", json={
            'id': id,
            'comments': comments,
            'content': content,
            'exeid': exeid
        })
    content1 = res1.json()
    if content1["meta"]["status"] == 202:
        return responsePut("更新成功", {'info': 'success'})
    else:
        return responseError('获取问题列表失败，请尝试刷新或联系管理员')


@ api.route('/newBoard', methods=['POST'])  # 新建一个提案给云端
def newBoard():
    res = request.json
    Id = res.get('Id')
    try:
        if Id:
            check = dmIssueChange(res)
            if check:
                return responsePost('更新成功')
            else:
                return responseError('更新失败，请联系管理员')
        else:
            check = dmIssueSubmit(res)
            if check:
                return responsePost('插入成功')
            else:
                return responseError('插入失败，请联系管理员')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('更新失败,请联系管理员')


@ api.route('/getBoards', methods=['GET'])   # 获取accountbility board的列表数据，产线分类
def getBoards():
    res = request.args
    query = res.get('query')
    line = res.get('line')
    area = res.get('area')
    res = requests.get(
        config[env].cloud_url+"welean/bi/issueAPI/getAccountsSZ", params={
            'query': query,
            'line': line,
            'area': area
        })
    content = res.json()
    issues = []
    if content["meta"]["status"] == 200:
        issues = content['data']['suggests']
        issues = downLoadPics(issues)
    else:
        return responseError('获取问题列表失败，请尝试刷新或联系管理员')
    dt = getLines(area)
    lines = dt[0]
    stypes = dt[1]
    owners = dt[2]
    total = len(issues)
    return responseGet('成功', {'data': issues, 'lines': lines, 'total': total, 'stypes': stypes, 'owners': owners})


def find_file(dept, path, url):
    i = 0
    result = []
    for root, lists, files in os.walk(path):
        for file in files:
            if dept in file:
                i = i + 1
                write = url+file
                result.append(write)
    return result


@ api.route('/getAccounts', methods=['GET'])  # 获得accountbility board的部门分类数据，产线分类
def getAccounts():
    res = request.args
    query = res.get('query')
    line = res.get('line')
    area = res.get('area')
    res = requests.get(
        config[env].cloud_url+"welean/bi/issueAPI/getAccountsSZ", params={
            'query': query,
            'line': line,
            'area': area
        })
    content = res.json()
    issues = []
    if content["meta"]["status"] == 200:
        issues = content['data']['suggests']
        issues = downLoadPics(issues)
    else:
        return responseError('获取问题列表失败，请尝试刷新或联系管理员')
    wkArr = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
    url = config[env].base_url+'BI/boardimg/'+area+'/'
    path = config[env].localPath+'BI/boardimg/'+area+'/'
    boardDept = {
        'EHS': 'EHS',
        'Production': '生产',
        'Lean': 'Lean',
        'ME': '工程',
        'R&D': '研发',
        'F&M': '维修',
        'Planning': '计划',
        'Quality': '质量',
        'M&L': '仓库',
        '其他': '采购'
    }
    rowDic = {}
    for b, v in boardDept.items():
        result = find_file(v, path, url)
        rowDic[b] = {
            'dept': v,
            'deptimg': result
        }
    today = datetime.date.today()
    thisyear = str(today.year)
    dateArr = []
    weekArr = []
    for i in range(14):
        dd = today+datetime.timedelta(days=i)
        wd = dd.weekday()
        if wd in [0, 1, 2, 3, 4]:
            dateArr.append(dd.strftime('%m-%d'))
            weekArr.append(wkArr[wd])
    for o in issues:
        if o['cfdate'] == '0000-00-00':
            o['cfdate'] = ''
        if o['dept1'] in rowDic.keys():
            if o['cfdate'] < thisyear+'-'+dateArr[0]:
                if '过期未处理' in rowDic[o['dept1']].keys():
                    rowDic[o['dept1']]['过期未处理'].append(o)
                else:
                    rowDic[o['dept1']]['过期未处理'] = [o]
            elif o['cfdate'] > thisyear+'-'+dateArr[len(dateArr)-1]:
                if '两周后' in rowDic[o['dept1']].keys():
                    rowDic[o['dept1']]['两周后'].append(o)
                else:
                    rowDic[o['dept1']]['两周后'] = [o]
            else:
                if o['cfdate'][5:] in rowDic[o['dept1']].keys():
                    rowDic[o['dept1']][o['cfdate'][5:]].append(o)
                else:
                    rowDic[o['dept1']][o['cfdate'][5:]] = [o]

    dateArr.append('两周后')
    dateArr.insert(0, '过期未处理')
    weekArr.append(' ')
    weekArr.insert(0, ' ')
    dt = getLines(area)
    lines = dt[0]
    stypes = dt[1]
    owners = dt[2]
    rowArr = list(rowDic.values())
    return responseGet('成功', {'data': rowArr, 'lines': lines, 'pp': dateArr, 'ww': weekArr, 'stypes': stypes, 'owners': owners})


def getLines(area):   # 获取产线列表，会根据用户信息的Dept2进行切分，未激活和未注册的用户无法出现在选单
    res = requests.get(
        config[env].cloud_url+"welean/bi/issueAPI/getLinesSZ", params={
            'area': area
        })
    content = res.json()
    lines = []
    if content["meta"]["status"] == 200:
        lines = content['data']['lines']
        owners = content['data']['owners']
        types = content['data']['types']
    lineData = []
    for r in lines:
        lineData.append({'name': r, 'value': r})
    return [lineData, types, owners]


@api.route('/getCmonth', methods=['GET'])  # 获得效率相关的趋势图，柏拉图和问题清单
def getCmonth():
    r = request.args
    stime = r.get('stime')
    etime = r.get('etime')
    ttime = datetime.datetime.strptime(etime, '%Y-%m-%d')+datetime.timedelta(days=1)
    days = (datetime.datetime.strptime(etime, '%Y-%m-%d') -
            datetime.datetime.strptime(stime, '%Y-%m-%d')).days
    linegroup = r.get('area')
    area = r.get('group')
    kpi = getkpi(stime.split('-')[0], linegroup)['ctarget']
    restList = db.session.query(Restinfo).all()
    shifts = db.session.query(func.date(Shiftinfo.starttime).label('shiftdate'), linfo.linegroup, func.sum(Scaninfo.scanqty).label('pqty'), Shiftinfo.headcount,
                              Shiftinfo.starttime, Shiftinfo.finishtime, Shiftinfo.routing).outerjoin(
        Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(linfo, Shiftinfo.linename == linfo.linename).filter(and_(
            Shiftinfo.starttime >= stime, Shiftinfo.finishtime <= ttime)).filter(not_(Shiftinfo.finishtime.is_(None))).group_by(Shiftinfo.Id)
    if linegroup:
        shifts = shifts.filter(linfo.linegroup == linegroup)
    else:
        shifts = shifts.filter(linfo.area == area)
    shifts = shifts.all()
    data = {
        'ftt': [],
        'pd': [],
        'fttmin': 50,
        'fttx': [],
        'green': [],
        'red': [],
        'yellow': [],
        'listData': [],
        'paretodata': [],
        'paretotarget': [],
        'paretox': []
    }
    scanDic = {}
    for s in shifts:
        shiftdate = datetime.datetime.strftime(s.starttime, '%Y-%m-%d')
        yesterday = s.starttime+datetime.timedelta(days=-1)
        td8 = datetime.datetime.strftime(s.starttime, '%Y-%m-%d 08:00:00')
        strfinish = datetime.datetime.strftime(s.finishtime, '%Y-%m-%d %H:%M:%S')
        if strfinish <= td8:
            shiftdate = datetime.datetime.strftime(yesterday, '%Y-%m-%d')
        rqty = getReqQty(shiftdate, s.headcount, s.linegroup,
                             s.starttime, s.finishtime, restList, s.routing)
        print(s.pqty,rqty,s.starttime,s.finishtime)
        if shiftdate in scanDic.keys():
            pqty = s.pqty if s.pqty else 0
            scanDic[shiftdate]['pqty'] = scanDic[shiftdate]['pqty']+pqty
            scanDic[shiftdate]['rqty'] = scanDic[shiftdate]['rqty']+rqty
        else:
            scanDic[shiftdate] = {
                'shiftdate': shiftdate,
                'pqty': s.pqty if s.pqty else 0,
                'rqty': rqty
            }
    print(scanDic)
    for p in scanDic.values():
        sdate = p['shiftdate'].split('-')
        data['fttx'].append(sdate[1]+'-'+sdate[2])
        data['pd'].append(p['pqty'])
        ftt = round(int(p['pqty'])/p['rqty']*100, 0) if p['rqty'] else 0
        if ftt < 0:
            ftt = 0
        data['ftt'].append(ftt)
        data['green'].append(100-kpi*100)
        data['red'].append(kpi*95)
        data['yellow'].append(kpi*5)
    problems = getproblems([linegroup], '效率', days)
    for p in problems:
        data['paretox'].append(p['ptype'])
        data['paretotarget'].append(p['trigger'])
        data['paretodata'].append(0)
    data['paretox'].append('未分类')
    data['paretotarget'].append(round(5/7*days))
    data['paretodata'].append(0)
    issues = db.session.query(Issuelog).join(linfo, Issuelog.linename == linfo.linename).filter(
        Issuelog.shiftdate.between(stime, etime)).filter(Issuelog.sqdctype == '效率')
    if linegroup:
        issues = issues.filter(linfo.linegroup == linegroup)
    else:
        issues = issues.filter(linfo.area == area)
    issues = issues.order_by(desc(Issuelog.recordtime)).all()
    for i in issues:
        if i.problemtype in data['paretox']:
            data['paretodata'][data['paretox'].index(i.problemtype)] += i.qty
        else:
            data['paretodata'][len(data['paretodata'])-1] += i.qty
        data['listData'].append({
            'recorddate': datetime.datetime.strftime(i.shiftdate, "%Y-%m-%d"),
            'desc': i.desc,
            'linename': i.linename,
            'qty': i.qty,
            'problemtype': i.problemtype if i.problemtype else '其他'
        })
    maxqty = 100
    if len(data['ftt']) > 0:
        data['fttmin'] = min(data['ftt']) if min(data['ftt']) < 100 else 80
        maxqty = max(data['ftt']) if max(data['ftt']) > 100 else 100
    for j in range(len(data['green'])):
        data['green'][j] = int(maxqty)-kpi*100+5 if (kpi*100 < int(maxqty)) else 5
    return responseGet('获取成功', data)


@api.route('/getCmonthoee', methods=['GET'])  # 获得效率相关的趋势图，柏拉图和问题清单
def getCmonthoee():
    r = request.args
    stime = r.get('stime')
    etime = r.get('etime')
    ttime = datetime.datetime.strptime(etime, '%Y-%m-%d')+datetime.timedelta(days=1)
    days = (datetime.datetime.strptime(etime, '%Y-%m-%d') -
            datetime.datetime.strptime(stime, '%Y-%m-%d')).days
    linegroup = r.get('area')
    area = r.get('group')
    kpi = getkpi(stime.split('-')[0], linegroup)['ctarget']
    typeline = []
    alllines = db.session.query(linfo.linegroup).filter(linfo.area == area).all()
    restDic = {}
    resttime = db.session.query(Restinfo.resttime, Restinfo.restmin, linfo.linename).join(
        linfo, Restinfo.linegroup == linfo.linegroup).all()
    for r in resttime:
        if r.linename not in restDic.keys():
            restDic[r.linename] = {
                r.resttime: r.restmin
            }
        else:
            restDic[r.linename][r.resttime] = r.restmin
    shifts = db.session.query(func.date(Shiftinfo.starttime).label('shiftdate'), Shiftinfo.Id, linfo.linegroup, func.sum(Scaninfo.scanqty).label('scanQty'), Shiftinfo.headcount,
                              Shiftinfo.starttime, Shiftinfo.shifttype, Shiftinfo.finishtime, Shiftinfo.routing, Shiftinfo.linename).outerjoin(
        Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(linfo, Shiftinfo.linename == linfo.linename).filter(and_(
            Shiftinfo.starttime >= stime, Shiftinfo.finishtime <= ttime)).filter(not_(Shiftinfo.finishtime.is_(None))).filter(linfo.linegroup != 'Tank包装').group_by(Shiftinfo.Id)

    if linegroup:
        shifts = shifts.filter(linfo.linegroup == linegroup)
        typeline.append(linegroup)
    else:
        shifts = shifts.filter(linfo.area == area)
        for a in alllines:
            typeline.append(a.linegroup)
    shifts = shifts.all()
    data = {
        'ftt': [],
        'pd': [],
        'fttmin': 50,
        'fttx': [],
        'green': [],
        'red': [],
        'yellow': [],
        'listData': [],
        'paretodata': [],
        'paretotarget': [],
        'paretox': []
    }
    scanDic = {}
    checkDic = {}
    for s in shifts:
        shiftdate = datetime.datetime.strftime(s.starttime, '%Y-%m-%d')
        yesterday = s.starttime+datetime.timedelta(days=-1)
        td8 = datetime.datetime.strftime(s.starttime, '%Y-%m-%d 08:00:00')
        strfinish = datetime.datetime.strftime(s.finishtime, '%Y-%m-%d %H:%M:%S')
        if strfinish <= td8:
            shiftdate = datetime.datetime.strftime(yesterday, '%Y-%m-%d')
        ttrest = 0
        for k, v in restDic[s.linename].items():
            ss = datetime.datetime.strftime(s.starttime, '%H:%M:%S')
            ff = datetime.datetime.strftime(s.finishtime, '%H:%M:%S')
            kk = datetime.time.strftime(k, '%H:%M:%S')
            if (ss > ff):
                if ss <= kk < '24:00:00' or '00:00:00' <= kk < ff:
                    ttrest += v
            else:
                if ss <= kk < ff:
                    ttrest += v
        if shiftdate in scanDic.keys():
            scanDic[shiftdate]['pqty'] += s.routing*s.scanQty*60 if s.scanQty else 0
            if s.linename+'-'+shiftdate+'-'+s.shifttype not in checkDic.keys():
                scanDic[shiftdate]['rqty'] += (s.finishtime-s.starttime).seconds/60-ttrest
            checkDic[s.linename+'-'+shiftdate+'-'+s.shifttype] = 1
        else:
            scanDic[shiftdate] = {
                'shiftdate': shiftdate,
                'issuemin': 0,
                'pqty':  s.routing*s.scanQty*60 if s.scanQty else 0,
                'rqty': (s.finishtime-s.starttime).seconds/60-ttrest
            }
            checkDic[s.linename+'-'+shiftdate+'-'+s.shifttype] = 1
    issues = db.session.query(Issuelog).join(linfo, Issuelog.linename == linfo.linename).filter(
        Issuelog.shiftdate.between(stime, etime)).filter(or_(Issuelog.sqdctype == '效率损失', Issuelog.sqdctype == '停机损失'))
    if linegroup:
        issues = issues.filter(linfo.linegroup == linegroup)
    else:
        issues = issues.filter(linfo.area == area)
    issues = issues.order_by(desc(Issuelog.recordtime)).all()
    problems = getproblemsoee(typeline, '效率损失', days)
    for p in problems:
        data['paretox'].append(p['ptype'])
        data['paretotarget'].append(p['trigger'])
        data['paretodata'].append(0)
    data['paretox'].append('未分类')
    data['paretotarget'].append(round(5/7*days))
    data['paretodata'].append(0)
    for i in issues:
        if i.sqdctype == '效率损失':
            if i.problemtype in data['paretox']:
                data['paretodata'][data['paretox'].index(i.problemtype)] += 1
            else:
                data['paretodata'][len(data['paretodata'])-1] += 1
            data['listData'].append({
                'recorddate': datetime.datetime.strftime(i.shiftdate, "%Y-%m-%d"),
                'desc': i.desc,
                'linename': i.linename,
                'qty': i.qty,
                'problemtype': i.problemtype if i.problemtype else '其他'
            })
        else:
            shiftdate = datetime.datetime.strftime(i.shiftdate, '%Y-%m-%d')
            print(i.issuemin, shiftdate, scanDic)
            if shiftdate in scanDic.keys():
                scanDic[shiftdate]['issuemin'] += i.issuemin
    for p in scanDic.values():
        print(111, p)
        sdate = p['shiftdate'].split('-')
        data['fttx'].append(sdate[1]+'-'+sdate[2])
        data['pd'].append(p['pqty'])
        ftt = round(int(p['pqty'])/(p['rqty']-p['issuemin'])*100, 0)
        if ftt < 0:
            ftt = 0
        data['ftt'].append(ftt)
        data['green'].append(100-kpi*100)
        data['red'].append(kpi*95)
        data['yellow'].append(kpi*5)
    maxqty = 100
    if len(data['ftt']) > 0:
        data['fttmin'] = min(data['ftt']) if min(data['ftt']) < 100 else 80
        maxqty = max(data['ftt']) if max(data['ftt']) > 100 else 100
    for j in range(len(data['green'])):
        data['green'][j] = int(maxqty)-kpi*100+5 if (kpi*100 < int(maxqty)) else 5
    return responseGet('获取成功', data)

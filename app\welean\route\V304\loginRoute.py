from flask import Blueprint, request
from app.welean.model.models_welean import Account, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Logs
from extensions import db
import requests
import datetime
from sqlalchemy import desc, not_
from app.public.functions import responseError, responsePost, responseGet,  create_token, responsePut, login_required
from app.welean.functions import getServer, defaultAuth
from app.welean.schemas import account_schema

api = Blueprint('welean/V304/loginAPI', __name__)


@ api.route('/getUsers', methods=['GET'])
@ login_required
def getUsers():
    res = request.args
    plant = res.get('plant')
    users = db.session.query(Userinfo.dept1, Userinfo.cname, Userinfo.eid).filter(Userinfo.plant == plant).filter(
        not_(Userinfo.dept1.is_(None))).filter(Userinfo.active == 1).order_by(desc(Userinfo.dept1)).all()
    deptDic = {}
    for u in users:
        if u.dept1 in deptDic.keys():
            deptDic[u.dept1]['children'].append({
                'value': u.eid,
                'label': u.cname
            })
        else:
            deptDic[u.dept1] = {
                'value': u.dept1,
                'label': u.dept1,
                'children': [{
                    'value': u.eid,
                    'label': u.cname
                }]
            }
    userArr = list(deptDic.values())
    return responseGet("获取列表成功", {'users': userArr})


@api.route('/checkLogs', methods=['GET'])
def checkLogs():
    lgs = db.session.query(Logs).order_by(desc(Logs.updatedate)).limit(5).all()
    logs = []
    for ll in lgs:
        dic = {
            'updatedate': datetime.datetime.strftime(ll.updatedate, '%Y-%m-%d'),
            'version': ll.version,
            'logs': ll.versionlogs
        }
        logs.append(dic)
    return responseGet("获取用户信息成功", {'logs': logs})


@api.route('/getOpenid', methods=['GET'])
def getOpenid():
    res = request.args
    code = res.get('code')
    appid = "wx120b5bd72ac43ab9"
    secret = "31ef616d928d24184583d8f80790704f"
    print(code)
    url = 'https://api.weixin.qq.com/sns/jscode2session?appid=' + appid + \
        '&secret=' + secret + '&js_code=' + code + '&grant_type=authorization_code'
    response = requests.get(url).json()
    print(response)
    # session_key = response['session_key']
    openid = response['openid']
    account = db.session.query(Userinfo.position, Account.eid, Account.avatar, Userinfo.cname, Userinfo.plant, Userinfo.dept1, Userinfo.dept2, Userinfo.dept3).outerjoin(
        Userinfo, Userinfo.eid == Account.eid).filter(
        Account.wxid == openid).filter(Account.isactive == 1).first()
    print(response)
    if account:
        userInfo = account_schema.dump(account)
        color = 'red' if account.position == 'restrict' else 'green'
        auth = db.session.query(Auth).filter(Auth.eid == account.eid).scalar()
        if auth:
            myAuth = defaultAuth+','+auth.auth
        else:
            myAuth = defaultAuth
        userInfo['auth'] = myAuth
        userInfo['color'] = color
        # userInfo['avatar'] = getServer()['baseUrl']+'avatars/'+userInfo['avatar']
        print(userInfo)
        return responseGet("获取用户信息成功", {'data': userInfo, 'token': create_token(
            account.eid)})
    else:
        print('dd')
        return responseError("获取用户信息失败,请先注册", {'data': response})


@api.route('/register', methods=['POST'])
def register():
    res = request.json
    openid = res.get('openid')
    avatar = res.get('avatar')
    eid = res.get('eid')
    cname = res.get('cname')
    print(cname, eid)
    user = db.session.query(Userinfo).filter(
        Userinfo.eid == eid).filter(Userinfo.cname == cname).filter(Userinfo.active == 1).scalar()
    if not user:
        return responseError("不存在该员工或姓名与工号不匹配")
    else:
        plant = user.plant
        dept1 = user.dept1
        dept2 = user.dept2
        dept3 = user.dept3
        color = 'red' if user.position == 'restrict' else 'green'

    try:
        myItem = Account(wxid=openid, eid=eid,
                         avatar=avatar, loginauth='b', isactive=1)
        db.session.add(myItem)
        db.session.commit()
        auth = db.session.query(Auth).filter(Auth.eid == eid).scalar()
        if auth:
            myAuth = defaultAuth+','+auth.auth
        else:
            myAuth = defaultAuth
        info = {'token': create_token(
            eid), 'userInfo': {'eid': eid, 'color': color, 'avatar': avatar, 'auth': myAuth, 'cname': cname, 'plant': plant, 'dept1': dept1, 'dept2': dept2, 'dept3': dept3}}
        # downloadAvatar(eid, avatar)
        return responsePost("注册成功，登录系统", {'info': info})
    except Exception as e:
        db.session.rollback()
        print(e)
    return responseError('该工号已被绑定或此账号已被禁用，如有疑义，请联系主管反馈该问题')


@api.route('/updateAvatar', methods=['PUT'])
def updateAvatar():
    res = request.json
    eid = res.get('eid')
    avatar = res.get('avatar')
    print('wxid avatar', eid, avatar)
    db.session.query(Account).filter(
        Account.eid == eid).update({'avatar': avatar})
    db.session.commit()
    return responsePut('updated')


def downloadAvatar(eid, url):
    fname = getServer()['basePath']+'avatars/'+eid+'.jpg'
    print(fname)
    r = requests.get(url)
    with open(fname, 'wb') as f:
        f.write(r.content)


@api.route('/loginH5', methods=['POST'])
def loginH5():
    res = request.json
    eid = res.get('eid')
    account = db.session.query(Userinfo.position, Account.eid, Account.avatar, Userinfo.cname, Userinfo.plant, Userinfo.dept1,
                               Userinfo.dept2, Userinfo.dept3).outerjoin(Userinfo, Userinfo.eid == Account.eid).filter(
        Account.eid == eid).first()
    if account:
        userInfo = account_schema.dump(account)
        color = 'red' if account.position == 'restrict' else 'green'
        auth = db.session.query(Auth).filter(Auth.eid == eid).scalar()
        if auth:
            myAuth = defaultAuth+','+auth.auth
        else:
            myAuth = defaultAuth
        userInfo['auth'] = myAuth
        userInfo['color'] = color
        return responsePost("获取用户信息成功", {'data': userInfo, 'token': create_token(
            eid)})
    else:
        return responseError("获取用户信息失败,请先通过微信小程序和任意Lean内部程序注册")

import asyncio
import datetime
import time
import traceback
import os
import io

import requests
from app.public.functions import responseError, responsePost, responseGet
from flask import Blueprint, json, request, jsonify, send_file, make_response, current_app,send_from_directory
from itsdangerous import TimedJSONWebSignatureSerializer as Serializer
from config import config, env
from app.tms.model.models import Master, Products,Customer, WorkFlow
from extensions import ma,db
from sqlalchemy import func
from concurrent.futures import ThreadPoolExecutor

# ReportLab imports
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

api = Blueprint('tms/order', __name__)
async_executor = ThreadPoolExecutor(max_workers=5)  # 全局线程池

# Master表的序列化器
class orderSchema(ma.Schema):
    class Meta:
        fields = ('id',
                  'SP_NO',
                  'BU_NO',
                  'DN_NO',
                  'DN_DATE',
                  'PO_NO',
                  'PO_DATE',
                  'cs_specialist',
                  'payer_company',
                  'receiver_name',
                  'receiver_address',
                  'delivery_terms',
                  'dn_attachment',
                  'goods_value',
                  'volume',
                  'weight',
                  'freight_fee',
                  'fee_remark',
                  'status',
                  'import_time',
                  'business_type',
                  'warehouse',
                  'shipping_point',
                  'release_time',
                  'freight_calc_method',
                  'freight_unit_price',
                  'freight_adjust',
                  'freight_adjust_reason',
                  'is_valid',
                  'receiver_province',
                  'receiver_city',
                  'receiver_district',
                  'customer_mail')

# Products表的序列化器
class ProductsSchema(ma.Schema):
    class Meta:
        fields = ('id',
                  'DN_NO',
                  'item_number',
                  'material_number',
                  'description',
                  'quatity',
                  'estimated_shipdate')

@api.route('/getdnlist', methods=['GET'])
def getdnlist():
    # s = Serializer(config[env].SECRET_KEY)
    # auth = request.headers['Authorization'].replace("Bearer ", "")
    # print(s.loads(auth))
    try:
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        start_date = request.args.get('start_date', None)
        end_date = request.args.get('end_date', None)
        if start_date and end_date:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d')
            start_date = start_date.date()
            end_date = end_date.date()
            dn_list = Master.query.filter(Master.DN_DATE.between(start_date, end_date)).paginate(page=page, per_page=size, error_out=False)
        else:
            dn_list = Master.query.paginate(page=page, per_page=size, error_out=False)
        data = orderSchema(many=True).dump(dn_list.items)
        return responseGet('获取订单列表成功', data)
    except Exception as e:
        traceback.print_exc()
        return responseError('获取订单列表失败')


@api.route('/getproductsbydn', methods=['GET'])
def getproductsbydn():
    """通过GET方式根据DN号码获取products数据（DN号码用逗号分隔）"""
    dn_numbers = request.args.get('dn_numbers', None)  # 获取URL参数中的dn_numbers字符串
    try:
        # 解析DN号码字符串为数组
        dn_list = [dn.strip() for dn in dn_numbers.split(',') if dn.strip()]

        if not dn_list:
            return responseError('请提供有效的DN号码')

        # 查询products表中匹配的数据
        products = Products.query.filter(Products.DN_NO.in_(dn_list)).all()

        # 序列化数据
        products_data = ProductsSchema(many=True).dump(products)

        return responseGet('获取产品数据成功', products_data)

    except Exception as e:
        traceback.print_exc()
        return responseError('获取产品数据失败')


def create_delivery_note_pdf(master_data, products_data):
    """创建交货单PDF"""
    # 创建内存中的PDF文件
    buffer = io.BytesIO()

    # 注册中文字体
    chinese_font_name = 'ChineseFont'
    font_path=os.path.dirname(os.path.abspath(__file__))
    img_path=os.path.join(font_path,'assets','img')
    font_path=os.path.join(font_path,'assets','font')
    try:
        # Windows系统字体路径   
        font_paths = [
            os.path.join(font_path,"simsun.ttc"),  # 宋体
            os.path.join(font_path,"simhei.ttf")   # 黑体
        ]

        # 尝试注册第一个可用的字体
        chinese_font_registered = False
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont(chinese_font_name, font_path))
                    chinese_font_registered = True
                    break
                except:
                    continue

        # 如果没有找到系统字体，使用ReportLab内置字体
        if not chinese_font_registered:
            try:
                # 使用ReportLab内置的支持中文的字体
                from reportlab.pdfbase.cidfonts import UnicodeCIDFont
                pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
                chinese_font_name = 'STSong-Light'
            except:
                # 最后备用方案：使用Helvetica
                chinese_font_name = 'Helvetica'

    except Exception as e:
        # 如果字体注册失败，使用Helvetica作为备用
        print(f"字体注册失败: {e}")
        chinese_font_name = 'Helvetica'

    # 创建PDF文档
    doc = SimpleDocTemplate(buffer, pagesize=A4,
                          rightMargin=2*cm, leftMargin=2*cm,
                          topMargin=1*cm, bottomMargin=2*cm)

    # 获取样式
    styles = getSampleStyleSheet()

    # 创建自定义样式（使用中文字体）
    title_style = ParagraphStyle(
        'CustomTitle',
        fontName=chinese_font_name,
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=30,
        alignment=TA_CENTER
    )

    header_style = ParagraphStyle(
        'CustomHeader',
        fontName=chinese_font_name,
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=12,
        alignment=TA_LEFT
    )

    normal_style = ParagraphStyle(
        'CustomNormal',
        fontName=chinese_font_name,
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6,
        alignment=TA_LEFT
    )

    # 构建PDF内容
    story = []

    # 添加Logo到右上角（使用自定义Canvas）
    def add_logo_to_page(canvas, doc):
        """在页面右上角添加Logo"""
        try:
            logo_path = os.path.join(img_path, "logo.jpg")
            if os.path.exists(logo_path):
                # 获取页面尺寸
                page_width, page_height = A4

                # Logo尺寸设置（可以根据需要调整）
                logo_width = 4*cm  # Logo宽度
                logo_height = 1*cm  # Logo高度

                # 计算Logo位置：右边距1cm，上边距1cm
                logo_x = page_width - logo_width - 1*cm  # 右边距1cm
                logo_y = page_height - logo_height - 1*cm  # 上边距1cm

                # 绘制Logo
                canvas.drawImage(logo_path, logo_x, logo_y,
                               width=logo_width, height=logo_height,
                               preserveAspectRatio=True, mask='auto')
        except Exception as e:
            print(f"Logo添加失败: {e}")

    # 标题
    title = Paragraph("Delivery Note (交货单)", title_style)
    story.append(title)
    story.append(Spacer(1, 20))

    # 公司信息头部
    company_info = [
        ["Pentair Water (Suzhou) Co., Ltd", ""],
        ["苏州滨特尔水处理有限公司", ""]
    ]

    company_table = Table(company_info, colWidths=[10*cm, 8*cm])
    company_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), chinese_font_name),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))
    story.append(company_table)
    story.append(Spacer(1, 20))

    # 订单信息表格
    order_info_data = [
        ["Number/Date(订单号/日期)", master_data.get('DN_NO', ''), "Date:", master_data.get('DN_DATE', '')],
        ["Cust. PO/Date(客户采购单号/日期)", master_data.get('PO_NO', ''), "Date:", master_data.get('PO_DATE', '')],
        ["Cust. no./Our order (客户编号/我司订单号)", master_data.get('CUSTOMER_NO', ''), "", ""],
        ["CS specialist(客户专员代表)", master_data.get('cs_specialist', ''), "", ""],
        ["货运 YTS", "", "", ""],
        ["Ship Via(运输方式)", "", "", ""],
        ["FOB Truck load 装运", "", "", ""]
    ]

    order_table = Table(order_info_data, colWidths=[4*cm, 4*cm, 2*cm, 4*cm])
    order_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), chinese_font_name),
        ('FONTSIZE', (0, 0), (-1, -1), 9),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
    ]))
    story.append(order_table)
    story.append(Spacer(1, 20))

    # 地址信息
    address_data = [
        ["Terms of delivery", "DAP", "L.port/Shanghai"],
        ["", "", ""]
    ]

    address_table = Table(address_data, colWidths=[4*cm, 4*cm, 6*cm])
    address_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), chinese_font_name),
        ('FONTSIZE', (0, 0), (-1, -1), 9),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))
    story.append(address_table)
    story.append(Spacer(1, 20))

    # 产品明细表头
    product_headers = ["Line(序号)", "Material(产品编号)", "Description(产品描述)", "Qty(数量)", "Estimated Shipdate(预计发货日期)"]

    # 产品数据
    product_rows = [product_headers]
    for i, product in enumerate(products_data, 1):
        row = [
            str(i).zfill(6),
            product.get('material_number', ''),
            product.get('description', ''),
            str(product.get('quatity', '')),
            str(product.get('estimated_shipdate', ''))
        ]
        product_rows.append(row)

    # 如果没有产品数据，添加示例行
    if len(product_rows) == 1:
        product_rows.append(["000001", "CHF135-1", "示例产品 Tune-Up Kit; 4T18;Tripod;100psi;SV", "10.00", "2022.01.15"])
        product_rows.append(["000020", "CHF135-1", "示例产品 Tune-Up Kit; 4T18;Tripod;100psi;SV", "10.00", "2022.01.15"])

    product_table = Table(product_rows, colWidths=[2*cm, 3*cm, 7*cm, 2*cm, 3*cm])
    product_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), chinese_font_name),
        ('FONTSIZE', (0, 0), (-1, -1), 8),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),  # 表头背景
        ('FONTSIZE', (0, 0), (-1, 0), 9),  # 表头字体大小
    ]))
    story.append(product_table)
    story.append(Spacer(1, 30))

    # 特殊说明
    special_instructions = [
        "Special instructions(客户备注):",
        "",
        "Transaction terms: Incoterms 2020 Rules",
        "Duties: The invoice shall be the sole of Cargo pickup from Seller's facility.",
        "On domestic sales: Transfer of both title and risk of loss shall occur upon pickup from seller's site.",
        "On international sales: Transfer of both title and risk of loss shall occur upon entry into international waters. International airspace,",
        "or the crossing of an international border.",
        "Marine insurance: The party responsible for paying the main transportation shall provide full cargo insurance coverage - defined as",
        "door-to-door. 'A' cover, all risk, marine, war, strike and riot - regardless of the shipping terms, with the exception of CFR/CPT Incoterms, in",
        "which insurance is the buyer's responsibility. Therefore, for F and D terms, plus CIF and CIP, the buyer shall provide full cargo insurance",
        "coverage, and on D terms, plus CIF and CIP, the seller shall provide full cargo insurance coverage.",
        "THIS IS A COMPUTER GENERATED, NO SIGNATURE REQUIRED"
    ]

    for instruction in special_instructions:
        para = Paragraph(instruction, normal_style)
        story.append(para)

    # 生成PDF（使用自定义页面模板添加Logo）
    doc.build(story, onFirstPage=add_logo_to_page, onLaterPages=add_logo_to_page)
    buffer.seek(0)
    return buffer


@api.route('/generatepdf/<dn_no>', methods=['GET'])
def generatepdf(dn_no):
    """根据DN号码生成PDF交货单"""
    try:
        # 查询Master表数据
        master = Master.query.filter_by(DN_NO=dn_no).first()
        if not master:
            return responseError(f'未找到DN号码为 {dn_no} 的订单')

        # 序列化Master数据
        master_data = orderSchema().dump(master)

        # 查询Products表数据
        products = Products.query.filter_by(DN_NO=dn_no).all()
        products_data = ProductsSchema(many=True).dump(products)

        # 生成PDF
        print('master_data:', master_data)
        print('products_data:', products_data)
        pdf_buffer = create_delivery_note_pdf(master_data, products_data)

        # 返回PDF文件
        response = make_response(pdf_buffer.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=delivery_note_{dn_no}.pdf'

        return response
        # return os.path.dirname(os.path.abspath(__file__)).join('assets/font/simsun.ttc')

    except Exception as e:
        traceback.print_exc()
        return responseError(f'生成PDF失败: {str(e)}')

@api.route('/release_dn',methods=['POST'])
def release_dn():
    """释放订单"""
    try:
        s = Serializer(config[env].SECRET_KEY)
        auth = request.headers['Authorization'].replace("Bearer ", "")
        user_info=s.loads(auth)
    except Exception as e:
        return responseError('系统错误，登录信息误！')       
    try:
        # 获取参数
        data = request.get_json()
        if (len(data['row'])==0):
            return responseError('未发现有效的工单数据')
        # 1-内仓，2-外仓
        if (data['warehouse'] not in ['1','2']):
            return responseError('仓库选择错误')
        customer_email=data['customer_email']
        comments=data['comments']
        # 找出当前已分配的最大SP_NO
        max_sp_no = db.session.query(func.max(Master.SP_NO)).scalar()
        if (max_sp_no is None):
            max_sp_no=1
        # row里面的信息为：'row': [{'BU_NO': '1200', 'CUSTOMER_NO': 'CH1557-002', 'DN_DATE': '2022-01-04', 'DN_NO': '80405823', 'PO_DATE': '2021-09-06', 'PO_NO': 'SHTH210906-1', 'SP_NO': 'SP00001', 'business_type': 1, 'create_time': None, 'cs_specialist': 'CHERRY.YIN', 'delivery_terms': None, 'dn_attachment': None, 'fee_remark': None, 'freight_fee': None, 'goods_value': None, 'id': 1, 
        # 'payer_address': '上海市金山区亭林镇松隐小康路34号119室', 'payer_company': '上海添浩环保科技发展有限公司', 'payer_name': '方忠', 'payer_phone': '***********', 'receiver_address': None, 'receiver_name': None, 'sale_order': '0000332472', 'status': 1, 'volume': None, 'weight': None}, {'BU_NO': '1100', 'CUSTOMER_NO': 'AH1554-001', 'DN_DATE': '2025-04-25', 'DN_NO': '80405121', 'PO_DATE': '2025-04-25', 'PO_NO': 'SHTH210285', 'SP_NO': '', 'business_type': 2, 'create_time': None, 'cs_specialist': 'TEST.NAME', 'delivery_terms': None, 'dn_attachment': None, 'fee_remark': None, 'freight_fee': None, 'goods_value': None, 'id': 2, 'payer_address': '上海市金山区亭林镇松隐小康路34号119室', 'payer_company': '测试公司', 'payer_name': '测试用名', 'payer_phone': '***********', 'receiver_address': None, 'receiver_name': None, 'sale_order': '0000332445', 'status': 1, 'volume': None, 'weight': None}]
        # 筛出row中DN_NO集合
        dn_nos = [row['DN_NO'] for row in data['row']]
        # 从Master表中找出这些DN_NO对应的条目，将找出的这些条目的status改为2（已释放）
        masters = Master.query.filter(Master.DN_NO.in_(dn_nos)).all()
        for master in masters:
            master.SP_NO=max_sp_no+1
            master.status = 2
            db.session.add(master)
        # 更新信息
        db.session.commit()
        # 将流程写入Workflow表
        work_item=WorkFlow(SP_NO=max_sp_no+1,action="工单释放",owner=user_info['user_id'],comment=comments,update_time=datetime.datetime.now())
        db.session.add(work_item)
        db.session.commit()
        

        # 异步处理收货人地址（传递应用上下文）
        app_context = current_app.app_context()
        async_executor.submit(async_task, masters[0].receiver_address, customer_email, app_context)
        return responsePost('释放成功')
    except Exception as e:
        traceback.print_exc()
        return responseError(f'释放失败: {str(e)}')

# 将收件人地址入库，并将省市区地址解析
def async_task(data, customer_email, app_context):
    with app_context:
        # 将地址写入
        try:
            customer = Customer.query.filter_by(address=data).first()
            if customer is None:
                customer = Customer(address=data, email=customer_email)
                db.session.add(customer)
            else:
                if customer_email != '':
                    customer.email = customer_email
            db.session.commit()
        except Exception as e:
            pass
        # 将地址中的省市区解析，写入
        api_url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
        authorization_token = config[env].ZHIPU_API_KEY       
        headers = {
            'Authorization': f'Bearer {authorization_token}',
            'Content-Type': 'application/json'
        }    
        request_data = {
        'model': 'glm-4-flash',
        'messages':[
                {"role": "user", "content": data+"，将这个地址的省，市，区解析出来，放在key为province，city，district的字典中，并将字典序列化成json字符串，发送给我。"}
            ],
        'response_format':{
            'type': 'json_object'
            }
        }
        response = requests.post(api_url, json=request_data,headers=headers)
        if response.status_code == 200:
            # 处理成功响应的数据
            res=response.json()['choices'][0]['message']['content']
            customer = Customer.query.filter_by(address=data).first()
            if customer is not None:
                res=json.loads(res)
                customer.province = res['province']
                customer.city = res['city']
                customer.district = res['district']
                print(res['province'],res['city'],res['district'])
            db.session.commit()         
        else:
            pass      

@api.route('get_dn_file', methods=['GET'])
def get_dn_file():
    time_str = request.args.get('time_str')
    dt = datetime.datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%S")
    filename = dt.strftime("%Y-%m-%d%H%M%S")
    file_path = config[env].localPath + "tms/dn/" + filename + ".pdf"
    
    # 判断文件是否存在
    if os.path.exists(file_path):
        # 使用 send_file 并设置 as_attachment=False 以允许浏览器预览
        return send_file(
            file_path,
            mimetype='application/pdf',  # 确保 Content-Type 正确
            as_attachment=False
        )
    else:
        return responseError('文件不存在')

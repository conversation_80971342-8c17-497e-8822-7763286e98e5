import{aM as s,aN as t}from"./index-BnxEuBzx.js";const o=()=>s.request("get",t("routes/getFullMenu")),u=e=>s.request("post",t("/sys/menus/edit"),{data:e}),n=e=>s.request("post",t("/sys/menus/del"),{data:e}),l=()=>s.request("get",t("routes/getRoles")),a=e=>s.request("post",t("/sys/roles/edit"),{data:e}),i=e=>s.request("post",t("/sys/roles/del"),{data:e}),p=e=>s.request("get",t("routes/getAuthbyRoleid"),{params:e}),g=e=>s.request("post",t("/sys/roles/assign"),{data:e}),c=()=>s.request("get","/list-all-role"),d=e=>s.request("post","/dept",{data:e}),q=e=>s.request("post","/list-role-ids",{data:e}),R=e=>s.request("post","/user",{data:e});export{g as a,i as b,l as c,n as d,u as e,a as f,d as g,c as h,R as i,q as j,o as l,p};

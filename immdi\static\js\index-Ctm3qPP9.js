var n=(r,t,a)=>new Promise((o,s)=>{var i=e=>{try{c(a.next(e))}catch(l){s(l)}},m=e=>{try{c(a.throw(e))}catch(l){s(l)}},c=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,m);c((a=a.apply(r,t)).next())});import{aX as f,aE as p}from"./index-BnxEuBzx.js";import d from"./editPnForm-mTeSfSrW.js";import{d as u}from"./dashboard-dtTxmf4X.js";function N(r,t,a){f({title:r+"# 注塑机生产料号编辑",props:{PNData:{machine_id:r,pn:t}},width:"50%",draggable:!0,fullscreenIcon:!1,closeOnClickModal:!1,contentRenderer:()=>d,beforeSure:(i,m)=>n(this,[i,m],function*(o,{options:s}){s.props.PNData.pn!=t?(yield u({machine_id:s.props.PNData.machine_id,pn:s.props.PNData.pn})).meta.status!=201?p("变更状态失败",{customClass:"el",type:"error"}):(o(),p("变更状态成功",{customClass:"el",type:"success"}),a()):o()})})}export{N as c};

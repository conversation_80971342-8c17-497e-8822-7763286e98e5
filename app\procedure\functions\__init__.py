from config import config, myEnv
import os
import requests
import traceback
import datetime


def getServer():  # 获取上传文件的访问路径
    urls = {
        'basePath': config[myEnv].pentair_path+"static/procedure/",
        'baseUrl': config[myEnv].pentair_url+'procedure/',
    }
    return urls


# def storeFile(id, filename):
#     tempFile = getServer()['basePath']+'uploads/'+filename
#     storePath = getServer()['basePath']+'archives/'+str(id)+'/'
#     if os.path.exists(storePath+filename):
#         os.remove(storePath+filename)
#     if not os.path.exists(storePath):
#         os.mkdir(storePath)
#     shutil.move(tempFile, storePath)


def download_img(url, path):
    # print(url, path, r.status_code, os.path.exists(path))  # 返回状态码
    if os.path.exists(path):
        pass
    else:
        r = requests.get(url)
        if r.status_code == 200:
            img = r.content
            with open(path, 'wb') as f:
                f.write(img)


def dmIssueSubmit(res):
    sqdctype = res.get('sqdctype')  # 问题分类，目前只有质量，效率和交货3个
    problemtype = res.get('problemtype')  # 问题子分类
    shifttype = res.get('shifttype')  # 白中夜班
    shiftdate = res.get('shiftdate')  # 班次日期
    linename = res.get('linename')  # 产线
    linegroup = res.get('linegroup')  # 产线组
    machine = res.get('machine') if res.get('machine') else ''   # 机器
    issuemin = res.get('issuemin') if res.get('issuemin') else 0
    desc = res.get('desc') if res.get('desc') else problemtype  # 描述
    sku = res.get('sku') if res.get('sku') else '待完善'  # 料号
    qty = res.get('qty') if res.get('qty') else 1  # 数量
    recordtime = datetime.datetime.strptime(res.get('recordtime'), '%Y-%m-%d %H:%M:%S') if res.get(
        'recordtime') else datetime.datetime.now()  # 记录日期
    recorder = res.get('recorder') if res.get('recorder') else linegroup  # 记录人
    cfdate = res.get('cfdate') if res.get(
        'cfdate') else datetime.datetime.strftime(recordtime, '%Y-%m-%d')
    comments = res.get('comments') if res.get('comments') else ''
    mdi = res.get('mdi') if res.get('mdi') else '是'
    content = res.get('content') if res.get('content') else  \
        sqdctype+'/'+problemtype+'\n'+desc
    exeid = res.get('exeid') if res.get('exeid') else '9999999'
    try:
        aid = 0
        # if linename:
        #     myissue = Issuelog(sqdctype=sqdctype, problemtype=problemtype, shifttype=shifttype, shiftdate=shiftdate, linename=linename,
        #                        recorder=recorder, desc=desc, sku=sku, qty=qty, recordtime=recordtime, issuemin=issuemin, machine=machine)
        #     db.session.add(myissue)
        #     db.session.flush()
        #     aid = myissue.Id
        missqty = 0
        missunit = 0
        # if sqdctype == '停机损失':
        #     missunit = '分钟'
        # else:
        #     missunit = '件'
        # if sqdctype == '质量' or sqdctype == '质量不良':
        #     missqty = qty
        requests.post(
            config[myEnv].cloud_url+"welean/bi/issueAPI/newSuggest", json={
                'auditid': aid,
                'cfdate': cfdate,
                'idate': datetime.datetime.strftime(recordtime, '%Y-%m-%d %H:%M:%S'),
                'comments': comments,
                'content': content,
                'exeid': exeid,
                'linename': linegroup,
                'stype': sqdctype,
                'type2': problemtype,
                'missunit': missunit,
                'missqty': missqty,
                'machine': machine,
                'plant': 'WX',
                'mdi': mdi
            })
        # db.session.commit()
        return True
    except Exception:
        # db.session.rollback()
        traceback.print_exc()
        return False


def dmIssueChange(res):
    Id = res.get('Id')
    aid = res.get('auditid')
    sqdctype = res.get('sqdctype')  # 问题分类，目前只有质量，效率和交货3个
    problemtype = res.get('problemtype')  # 问题子分类
    # shiftdate = res.get('shiftdate')  # 班次日期
    linename = res.get('linename')  # 产线
    linegroup = res.get('linegroup')  # 产线组
    issuemin = res.get('issuemin') if res.get('issuemin') else 0
    machine = res.get('machine') if res.get('machine') else ''
    desc = res.get('desc') if res.get('desc') else problemtype  # 描述
    sku = res.get('sku') if res.get('sku') else '待完善'  # 料号
    qty = res.get('qty') if res.get('qty') else 1  # 数量
    recordtime = datetime.datetime.strptime(res.get('recordtime'), '%Y-%m-%d %H:%M:%S') if res.get(
        'recordtime') else datetime.datetime.now()  # 记录日期
    exeid = res.get('exeid') if res.get('exeid') else '9999999'
    cfdate = res.get('cfdate') if res.get(
        'cfdate') else datetime.datetime.strftime(recordtime, '%Y-%m-%d')
    comments = res.get('comments') if res.get('comments') else ''
    content = res.get('content') if res.get('content') else desc
    recorder = res.get('recorder') if res.get('recorder') else linegroup  # 记录人
    # print(11111111111111111, aid)
    # return True
    try:
        # if aid:
        #     db.session.query(Issuelog).filter(Issuelog.Id == aid).update({
        #         'sqdctype': sqdctype,
        #         'problemtype': problemtype,
        #         'recorder': recorder,
        #         'desc': content if content else desc,
        #         'sku': sku,
        #         'qty': qty,
        #         'issuemin': issuemin
        #     })
        missqty = 0
        missunit = 0
        # if sqdctype == '停机损失':
        #     missunit = '分钟'
        # else:
        #     missunit = '件'
        # if sqdctype == '质量' or sqdctype == '质量不良':
        #     missqty = qty
        requests.post(
            config[myEnv].cloud_url+"welean/bi/issueAPI/addSuggest", json={
                'Id': Id,
                'auditid': aid,
                'cfdate': cfdate,
                'idate': datetime.datetime.strftime(recordtime, '%Y-%m-%d %H:%M:%S'),
                'comments': comments,
                'content': content if content else desc,
                'exeid': exeid,
                'linename': linegroup,
                'missunit': missunit,
                'missqty': missqty,
                'machine': machine,
                'stype': sqdctype,
                'type2': problemtype
            })
        # db.session.commit()
        return True
    except Exception:
        # db.session.rollback()
        traceback.print_exc()
        return False


def dmdelSuggest(aid):
    requests.post(
        config[myEnv].cloud_url+"welean/bi/issueAPI/delSuggest", json={
            'auditid': aid,
        })


def downLoadPics(dts):
    imgPath = config[myEnv].pentair_path+"static/procedure/img/"
    for d in dts:
        p = d['beforepic']
        pa = d['afterpic']
        picurl = p[p.rfind('/')+1:].split('.')
        if len(picurl) == 2:
            picName = picurl[0]
            picAdx = picurl[1]
        else:
            picName = ''
            picAdx = ''
        if p:
            download_img(p, imgPath+picName+'.'+picAdx)
            d['beforepic'] = config[myEnv].pentair_url+'procedure/img/'+picName+'.'+picAdx
            d['picsArr'].append(d['beforepic'])
        if pa:
            download_img(pa, imgPath+picName+'after.'+picAdx)
            d['afterpic'] = config[myEnv].pentair_url+'procedure/img/'+picName+'after.'+picAdx
            d['picsArr'].append(d['afterpic'])
    return dts

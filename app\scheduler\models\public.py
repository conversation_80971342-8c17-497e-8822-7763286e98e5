from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, DateTime, SmallInteger
import os
import sys
parentdir = os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
# print(parentdir)
sys.path.append(parentdir)
if True:
    from config import config, myEnv
engine = create_engine(config[myEnv].SQLALCHEMY_BINDS['pentair_public'], echo=True)
# engine = create_engine("mysql+pymysql://root:1@127.0.0.1/tomdb",encoding='utf-8', echo=True)
# 1 上面的create_engine就是用来连接数据库的引擎：
# 2 mysql+pymysql指定使用pymysql来执行原生SQL语句
# 3 //root:1@127.0.0.1/tomdb   <<==>> //用户名：密码@ip/数据库名
# 4 encoding='utf-8' 指定创建的表用‘utf-8'编码（可以存中文）
# 5 echo=True 将执行SQL原生语句的执行细节打印出来

Base = declarative_base()  # 生成orm基类，执行SQL语句的类就继承Base


class Scriptlog(Base):
    __bind_key__ = "pentair_public"
    __tablename__ = "scriptlog"
    Id = Column(Integer, primary_key=True)
    starttime = Column(DateTime())
    finishtime = Column(DateTime())
    script = Column(String(30))
    status = Column(String(10))  # success, failed
    logs = Column(String(255))
    due = Column(SmallInteger)


# 创建与数据库的会话session class ,注意,这里返回给session的是个class类,不是实例
Session_class = sessionmaker(bind=engine)  # 创建用于数据库session的类
session = Session_class()

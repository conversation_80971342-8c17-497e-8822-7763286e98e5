from calendar import month
import email
from tkinter.messagebox import NO
from turtle import title
from flask import Blueprint, request
from sqlalchemy import or_, func, desc, true, case, distinct, and_, text
from app.voc.model.models_voc import Dfmonth, Dfweek, Dmgroup, Dship, Dsort,  Duser, Dvoc, Dsku, Dsku2, Drccm, Dopl, Rrccm, Ropl, Dquality, Dline, Dtraining, Dname, Dtype, Dskudate
from app.cal.model.models_cal import Dcal, Dcal_instep, Dcal_standcaleq, Dcal_user, Dcal_cal_version
from extensions import db
from datetime import datetime, date, timedelta
from app.public.functions import responseGet, responseError, get_week_range, dateDiffInHours, responsePost, responsePut
# from app.voc.functions import login_required, getServer, storeFile, getOptionsVoc, getDsortVoc, getvendorcarVoc
from app.cal.functions import login_required, getServer
from app.public.functions.ews import sendMailTread
from app.tp.functions import getUserlist
from app.voc.schemas import tasks_schema, task_schema, dvoc_schema, duser_schema, dsku_schema, dsku2_schema, drccm_schema, dopl_schema, dtraining_schema, dsort_schema, dquality_schema, dtype_schema, dline_schema, dmgroup_schema, dfmonth_schema, dfweek_schema, dship_schema
import traceback
import hashlib
import os
from ultils.log_helper import ProjectLogger
import shutil
import time
from openpyxl import load_workbook
import requests
from config import config, env
from app.public.model.models_public import Publicuser
import traceback
# import datetime

mylogger = ProjectLogger()

api = Blueprint('cal/calAPI', __name__)

NAVURL = 'http://***********/pnr-cal/#/'


@ api.route('/getCAL', methods=['GET'])
@ login_required
def getCAL():
    mylogger.debug('*'*10+'getCAL'+'*'*10)
    res = request.args
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    mylogger.debug(res)
    option = res.get('oploption')
    calvalue = res.get('vocvalue')
    calstatus = res.get('calstatus')
    measurestatus = res.get('measurestatus')
    filter1 = res.get('filter1')
    vaildvalue1 = res.get('vaildvalue1')
    vaildvalue2 = res.get('vaildvalue2')
    checkmode = res.get('checkmode')
    sortformat = res.get('sortformat')

    cal = db.session.query(Dcal.id.label('id'), Dcal.instrumentno.label('instrumentno'), Dcal.instrumentname.label('instrumentname'), Dcal.factorysign.label('factorysign'), Dcal.addreason.label('addreason'), Dcal.type.label('type'), Dcal.factoryno.label('factoryno'), Dcal_cal_version.calperiod.label('calperiod'), Dcal_cal_version.caldata.label('caldata'), Dcal_cal_version.checkstatus.label('checkstatus'), Dcal_cal_version.checkresultall.label('checkresultall'), Dcal.locno.label('locno'), Dcal.location.label('location'), Dcal_cal_version.duedate.label('duedate'), Dcal.refdoc.label('refdoc'), Dcal.calunit.label('calunit'), Dcal.checkmode.label('checkmode'), Dcal_cal_version.failpic.label('failpic'), Dcal_cal_version.measurestatus.label('measurestatus'), Dcal_cal_version.calcost.label('calcost'), Dcal.precal.label('precal'), Dcal.caltype.label('caltype'), Dcal.keeper.label('keeper'), Dcal.deptment.label('deptment'), Dcal.comment.label('comment'), Dcal.measureparameter.label('measureparameter'), Dcal.offset.label('offset'), Dcal.showradio.label('showradio'), Dcal.grade.label('grade'),  Dcal_cal_version.appearancefailpic.label('appearancefailpic'), Dcal_cal_version.appearance.label(
        'appearance'), Dcal_cal_version.ngreason.label('ngreason'), Dcal_cal_version.ngconfirm.label('ngconfirm'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.opendate.label('opendate'), Dcal_cal_version.checkbox1.label('checkbox1'), Dcal_cal_version.checkbox2.label('checkbox2'), Dcal_cal_version.abstatus.label('abstatus'), Dcal_cal_version.checkbox3.label('checkbox3'), Dcal_cal_version.checkbox4.label('checkbox4'), Dcal_cal_version.deptA.label('deptA'), Dcal_cal_version.deptB.label('deptB'), Dcal_cal_version.deptC.label('deptC'), Dcal_cal_version.deptD.label('deptD'), Dcal_cal_version.austatus.label('austatus'), Dcal_cal_version.recallev.label('recallev'), Dcal_cal_version.rootcase.label('rootcase'), Dcal_cal_version.processre.label('processre'), Dcal_cal_version.repaircost.label('repaircost'), Dcal_cal_version.newbuycost.label('newbuycost'), Dcal_cal_version.classify.label('classify'), Dcal_cal_version.verpersion.label('verpersion'), Dcal_cal_version.verdate.label('verdate'), Dcal_cal_version.damagedes.label('damagedes'), Dcal_cal_version.outconfirm.label('outconfirm'), Dcal_cal_version.temperature.label('temperature'), Dcal_cal_version.humidity.label('humidity'), Dcal_cal_version.version.label('version')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.version < 9000000)
    total = db.session.query(Dcal.id.label('id'), Dcal.instrumentno.label('instrumentno'), Dcal.instrumentname.label('instrumentname'), Dcal.factorysign.label('factorysign'), Dcal.addreason.label('addreason'), Dcal.type.label('type'), Dcal.factoryno.label('factoryno'), Dcal_cal_version.calperiod.label('calperiod'), Dcal_cal_version.caldata.label('caldata'), Dcal_cal_version.checkstatus.label('checkstatus'), Dcal_cal_version.checkresultall.label('checkresultall'), Dcal.locno.label('locno'), Dcal.location.label('location'), Dcal_cal_version.duedate.label('duedate'), Dcal.refdoc.label('refdoc'), Dcal.calunit.label('calunit'), Dcal.checkmode.label('checkmode'), Dcal_cal_version.failpic.label('failpic'), Dcal_cal_version.measurestatus.label('measurestatus'), Dcal_cal_version.calcost.label('calcost'), Dcal.precal.label('precal'), Dcal.caltype.label('caltype'), Dcal.keeper.label('keeper'), Dcal.deptment.label('deptment'), Dcal.comment.label('comment'), Dcal.measureparameter.label('measureparameter'), Dcal.offset.label('offset'), Dcal.showradio.label('showradio'), Dcal.grade.label('grade'),  Dcal_cal_version.appearancefailpic.label('appearancefailpic'), Dcal_cal_version.appearance.label(
        'appearance'), Dcal_cal_version.ngreason.label('ngreason'), Dcal_cal_version.ngconfirm.label('ngconfirm'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.opendate.label('opendate'), Dcal_cal_version.checkbox1.label('checkbox1'), Dcal_cal_version.checkbox2.label('checkbox2'), Dcal_cal_version.abstatus.label('abstatus'), Dcal_cal_version.checkbox3.label('checkbox3'), Dcal_cal_version.checkbox4.label('checkbox4'), Dcal_cal_version.deptA.label('deptA'), Dcal_cal_version.deptB.label('deptB'), Dcal_cal_version.deptC.label('deptC'), Dcal_cal_version.deptD.label('deptD'), Dcal_cal_version.austatus.label('austatus'), Dcal_cal_version.recallev.label('recallev'), Dcal_cal_version.rootcase.label('rootcase'), Dcal_cal_version.processre.label('processre'), Dcal_cal_version.repaircost.label('repaircost'), Dcal_cal_version.newbuycost.label('newbuycost'), Dcal_cal_version.classify.label('classify'), Dcal_cal_version.verpersion.label('verpersion'), Dcal_cal_version.verdate.label('verdate'), Dcal_cal_version.damagedes.label('damagedes'), Dcal_cal_version.outconfirm.label('outconfirm'), Dcal_cal_version.temperature.label('temperature'), Dcal_cal_version.humidity.label('humidity'), Dcal_cal_version.version.label('version')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.version < 9000000)
    if calvalue != '':
        if option == '仪器编号':
            tag = Dcal.instrumentno
        elif option == '仪器名称':
            tag = Dcal.instrumentname
        elif option == 'id':
            tag = Dcal.id
        elif option == '厂牌':
            tag = Dcal.factorysign
        elif option == '型号':
            tag = Dcal.type
        # elif option == '校验类别':
        #     tag = Dcal.checkmode
        # cal = db.session.query(Dcal).filter(tag.like('%{0}%'.format(calvalue)))
        # total = db.session.query(Dcal).filter(tag.like('%{0}%'.format(calvalue)))
        cal = cal.filter(tag.like('%{0}%'.format(calvalue)))
        total = total.filter(tag.like('%{0}%'.format(calvalue)))
    # else:
    #     cal = db.session.query(Dcal)
    #     total = db.session.query(Dcal)
    if calstatus != '':
        if calstatus == '尚未校验':
            cal = cal.filter(Dcal_cal_version.checkstatus == 1)
            total = total.filter(Dcal_cal_version.checkstatus == 1)
        elif calstatus == '校验中':
            cal = cal.filter(Dcal_cal_version.checkstatus == 2)
            total = total.filter(Dcal_cal_version.checkstatus == 2)
        else:
            cal = cal.filter(Dcal_cal_version.checkstatus == 3)
            total = total.filter(Dcal_cal_version.checkstatus == 3)
    if measurestatus != '':
        cal = cal.filter(Dcal_cal_version.measurestatus == measurestatus)
        total = total.filter(Dcal_cal_version.measurestatus == measurestatus)
    if checkmode != '':
        cal = cal.filter(Dcal_cal_version.checkmode == checkmode)
        total = total.filter(Dcal_cal_version.checkmode == checkmode)
    mylogger.debug(filter1)
    mylogger.debug(1111)
    if filter1 == 'true':
        mylogger.debug(222)
        times = time.localtime()
    # .filter(func.datediff(text('day'), func.current_date(), Dcal_cal_version.duedate) >= 0)
        cal = cal.filter(func.datediff(text('day'), func.current_date(),
                         Dcal_cal_version.duedate) <= 37).filter(Dcal_cal_version.checkstatus != 3)
        total = total.filter(func.datediff(text('day'), func.current_date(
        ), Dcal_cal_version.duedate) <= 37).filter(Dcal_cal_version.checkstatus != 3)
    if vaildvalue1 != '' and vaildvalue1 != None:
        cal = cal.filter(Dcal_cal_version.duedate.between(vaildvalue1, vaildvalue2))
        total = total.filter(Dcal_cal_version.duedate.between(vaildvalue1, vaildvalue2))
    if sortformat == '1':
        cal = cal.order_by(Dcal_cal_version.duedate).paginate(
            pagenum, pagesize, error_out=False).items
    else:
        cal = cal.order_by(Dcal.id.desc()).paginate(pagenum, pagesize, error_out=False).items
    total = total.count()
    calList = []
    idList = []
    for i in cal:
        if i.duedate:
            timeArray = time.strptime(i.duedate.strftime("%Y-%m-%d"), "%Y-%m-%d")
            timeStamp = int(time.mktime(timeArray))
        else:
            timeArray = ''
            timeStamp = 0
        times = time.localtime()
        timeStampnow = int(time.mktime(times))
        due = (timeStamp - timeStampnow)/3600/24
        caldict = {}
        if due > 30:
            caldict['urgents'] = "primary"
        else:
            if i.checkresultall == None:
                caldict['urgents'] = "danger"
            else:
                caldict['urgents'] = "primary"
        caldict['id'] = i.id
        caldict['instrumentno'] = i.instrumentno
        caldict['instrumentname'] = i.instrumentname
        caldict['factorysign'] = i.factorysign
        caldict['type'] = i.type
        caldict['factoryno'] = i.factoryno
        caldict['calperiod'] = i.calperiod
        if i.caldata:
            caldict['caldata'] = i.caldata.strftime("%Y-%m-%d")
        else:
            caldict['caldata'] = ''
        caldict['checkstatus'] = i.checkstatus
        caldict['locno'] = i.locno
        caldict['location'] = i.location
        caldict['duedate'] = i.duedate
        if i.duedate:
            caldict['duedate'] = i.duedate.strftime("%Y-%m-%d")
        caldict['refdoc'] = i.refdoc
        caldict['calunit'] = i.calunit
        caldict['checkmode'] = i.checkmode
        caldict['checkresultall'] = i.checkresultall
        caldict['measurestatus'] = i.measurestatus
        caldict['calcost'] = i.calcost
        caldict['precal'] = i.precal
        caldict['caltype'] = i.caltype
        caldict['keeper'] = i.keeper
        caldict['deptment'] = i.deptment
        caldict['comment'] = i.comment
        caldict['measureparameter'] = i.measureparameter
        caldict['offset'] = i.offset
        caldict['showradio'] = i.showradio
        caldict['grade'] = i.grade
        caldict['opensingnl'] = i.opensingnl
        caldict['opendate'] = i.opendate
        caldict['abstatus'] = i.abstatus
        caldict['ngreason'] = i.ngreason
        caldict['ngconfirm'] = i.ngconfirm
        caldict['temperature'] = i.temperature
        caldict['humidity'] = i.humidity
        caldict['addreason'] = i.addreason
        # if i.appearancefailpic:
        #     caldict['appearancefailpic'] = config[env].base_url+'cal/workfolder/uploads/appearfail/' + \
        #         i.appearancefailpic.split('/')[-1].split('\\')[-1]
        caldict['appearancefailpic'] = []
        if i.appearancefailpic:
            for j in i.appearancefailpic.split(','):
                if j != '':
                    caldict['appearancefailpic'].append(
                        config[env].base_url+'cal/workfolder/uploads/appearfail/' + j.split('/')[-1].split('\\')[-1])
        caldict['failpic'] = []
        if i.failpic:
            for j in i.failpic.split(','):
                if j != '':
                    caldict['failpic'].append(
                        config[env].base_url+'cal/workfolder/uploads/outcal/' + j.split('/')[-1].split('\\')[-1])
        caldict['checkbox1'] = i.checkbox1
        caldict['checkbox2'] = i.checkbox2
        caldict['checkbox3'] = i.checkbox3
        caldict['checkbox4'] = i.checkbox4
        caldict['deptA'] = i.deptA
        caldict['deptB'] = i.deptB
        caldict['deptC'] = i.deptC
        caldict['deptD'] = i.deptD
        caldict['austatus'] = i.austatus
        caldict['recallev'] = i.recallev
        caldict['rootcase'] = i.rootcase
        caldict['processre'] = i.processre
        caldict['classify'] = i.classify
        caldict['repaircost'] = i.repaircost
        caldict['newbuycost'] = i.newbuycost
        caldict['verpersion'] = i.verpersion
        if i.verdate:
            caldict['verdate'] = i.verdate.strftime("%Y-%m-%d")
        else:
            caldict['verdate'] = ''
        # caldict['verdate'] = i.verdate
        caldict['damagedes'] = i.damagedes
        caldict['outconfirm'] = i.outconfirm
        # mylogger.debug(8877)
        # mylogger.debug(i.version)
        if i.version != None:
            caldict['version'] = int(i.version)
        else:
            caldict['version'] = -1
        # mylogger.debug(caldict['version'])
        caldict['checktask'] = []
        caldict['checkinstrum'] = []
        calList.append(caldict)
        idList.append(i.id)
    calstep = db.session.query(Dcal_instep).filter(Dcal_instep.calid.in_(idList)).all()
    mylogger.debug(calList)
    if sortformat == '1':
        calList = sorted(calList, key=lambda x: x.get('urgents'), reverse=False)
    # calstand = db.session.query(Dcal_standcaleq).filter(Dcal_standcaleq.calid.in_(idList)).all()
    calstand = db.session.query(func.max(Dcal_cal_version.duedate).label('duedate'), Dcal_standcaleq.calid.label('calid'), Dcal_standcaleq.id.label('id'),
                                Dcal.instrumentname.label('sname'), Dcal_standcaleq.insno.label('insno'), Dcal.factorysign.label(
                                'mfg_model'), Dcal.factoryno.label('serno')).outerjoin(Dcal, Dcal.instrumentno == Dcal_standcaleq.insno).outerjoin(Dcal_cal_version,
                                                                                                                                                   Dcal.id == Dcal_cal_version.calid).filter(Dcal_standcaleq.calid.in_(idList)).group_by(
        Dcal.id, Dcal_standcaleq.calid, Dcal_standcaleq.id, Dcal.instrumentname,
        Dcal_standcaleq.insno, Dcal.factorysign, Dcal.factoryno).all()
    # .having(Dcal_cal_version.version == func.max(Dcal_cal_version.version))
    # for i in calstep:
    mylogger.debug(calstep)
    mylogger.debug(331)
    for i in range(len(calList)):
        for j in calstep:
            # mylogger.debug(calList[i].get('version'))
            # mylogger.debug(j.version)
            # mylogger.debug(8899)
            if int(calList[i].get('id')) == j.calid and calList[i].get('version') == j.version:
                checkdict = {}
                checkdict['checkid'] = j.id
                checkdict['checkname'] = j.checkname
                checkdict['checkresult'] = j.checkresult
                checkdict['spec'] = j.spec
                checkdict['standard'] = j.standard
                checkdict['reading'] = j.reading
                checkdict['upline'] = j.upline
                checkdict['downline'] = j.downline
                checkdict['version'] = j.version
                calList[i].get('checktask').append(checkdict)
        for j in calstand:
            if int(calList[i].get('id')) == j.calid:
                checkdict = {}
                checkdict['checkinsid'] = j.id
                checkdict['sname'] = j.sname
                checkdict['insno'] = j.insno
                checkdict['description'] = 'aa'
                checkdict['mfg_model'] = j.mfg_model
                checkdict['serno'] = j.serno
                # checkdict['duedate'] = j.duedate
                if j.duedate:
                    checkdict['duedate'] = j.duedate.strftime("%Y-%m-%d")
                # checkdict['downline'] = j.downline
                calList[i].get('checkinstrum').append(checkdict)
    # sorted(calList,key=lambda x:x[2])
    mylogger.debug(calList)
    return responseGet("maxvocId", {'calList': calList, 'total': total})


@api.route('/posticcal', methods=['POST'])
@ login_required
def posticcal():
    mylogger.debug('*'*10+'posticcal'+'*'*10)
    try:
        res = request.json
        mylogger.debug(res)
        checklist = res.get('ruleForm').get('checklist')
        ruleFormold = res.get('ruleFormold')
        ruleFormchange = res.get('ruleFormchange')
        if res.get('val') == 0:
            times = time.localtime()
            # times = time.strptime(caldata, "%Y-%m-%d")
            # timeStampnow = int(time.mktime(times))
            nowtimes = time.strftime("%Y/%m/%d %H:%M:%S", times)
            mylogger.debug('0000000')
            mylogger.debug(times)
            calperiod = res.get('ruleForm').get('calperiod', 12)
            if calperiod == "" or calperiod == None:
                calperiod = 12
            comment = res.get('ruleForm').get('comment')
            ruleFormnew = res.get('ruleForm')
            formname = {'checkmode': '校验方式', 'deptment': '部门', 'keeper': '保管员',
                        'calperiod': '校准周期', 'measurestatus': '量具状态', 'location': '位置', 'locno': '位置编码'}
            for i in ruleFormchange:
                if ruleFormchange[i] > 0:
                    if comment == '':
                        comment = nowtimes+'  更改 ' + \
                            str(formname[i])+' 由 ' + str(ruleFormold[i]) + \
                            ' 变为 ' + str(ruleFormnew[i])
                    else:
                        comment = str(comment) + '\n'+nowtimes+'  更改 '+str(formname[i])+' 由 ' + \
                            str(ruleFormold[i]) + ' 变为 ' + str(ruleFormnew[i])
            mylogger.debug(comment)
            db.session.query(Dcal).filter(Dcal.id == res.get('ruleForm').get('id')).update({'instrumentno': res.get('ruleForm').get('instrumentno'), 'instrumentname': res.get('ruleForm').get('instrumentname'),  'factorysign': res.get('ruleForm').get('factorysign'), 'type': res.get('ruleForm').get('type'), 'addreason': res.get('ruleForm').get('addreason'), 'calperiod': res.get('ruleForm').get('calperiod'),  'locno': res.get('ruleForm').get('locno'), 'location': res.get('ruleForm').get('location'), 'refdoc': res.get('ruleForm').get('refdoc'), 'calunit': res.get(
                'ruleForm').get('calunit'), 'precal': res.get('ruleForm').get('precal'), 'caltype': res.get('ruleForm').get('caltype'), 'keeper': res.get('ruleForm').get('keeper'), 'deptment': res.get('ruleForm').get('deptment'), 'comment': comment, 'measureparameter': res.get('ruleForm').get('measureparameter'), 'offset': res.get('ruleForm').get('offset'), 'showradio': res.get('ruleForm').get('showradio'), 'grade': res.get('ruleForm').get('grade'), 'checkmode': res.get('ruleForm').get('checkmode')})
            db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'measurestatus': res.get(
                'ruleForm').get('measurestatus'), 'calcost': res.get('ruleForm').get('calcost'),   'temperature': res.get('ruleForm').get('temperature'), 'calperiod': calperiod, 'humidity': res.get('ruleForm').get('humidity'), 'caldata': res.get('ruleForm').get('caldata'), 'checkmode': res.get('ruleForm').get('checkmode')})
            db.session.query(Dcal_instep).filter(Dcal_instep.calid == res.get('ruleForm').get(
                'id')).filter(Dcal_instep.version == res.get('ruleForm').get('version')).delete()
            db.session.query(Dcal_standcaleq).filter(
                Dcal_standcaleq.calid == res.get('ruleForm').get('id')).delete()
            checkins = res.get('ruleForm').get('checkins')
            addins = []
            for i in checkins:
                if i.get('insno', '').get('content') != '':
                    addins.append(Dcal_standcaleq(calid=res.get('ruleForm').get('id'), sname=i.get('sname', '').get('content'), insno=i.get('insno', '').get('content').strip(), mfg_model=i.get(
                        'mfg_model', '').get('content'), serno=i.get('serno', '').get('content'), duedate=i.get('duedate', '').get('content')))
            checklist = res.get('ruleForm').get('checklist')
            db.session.add_all(addins)
            addcheobj = []
            for i in checklist:
                if i.get('checkname', '').get('content') != '':
                    mylogger.debug(i.get('reading', '').get('content', ''))
                    mylogger.debug(222)
                    if i.get('reading', '').get('content', '') == '' or i.get('reading', '').get('content', '') == None:
                        reading = 0
                        mylogger.debug(333)
                    else:
                        mylogger.debug(444)
                        reading = float(i.get('reading', '').get('content'))
                    addcheobj.append(Dcal_instep(calid=res.get('ruleForm').get('id'), checkname=i.get('checkname', '').get('content'),
                                                 downline=float(i.get('downline').get('content')), reading=reading, spec=i.get(
                        'spec').get('content'), standard=float(i.get('standard').get('content')), upline=i.get('upline', '').get('content'), checkresult='', version=res.get('ruleForm').get('version')))
            db.session.add_all(addcheobj)
            db.session.commit()
        elif res.get('val') == 1:
            ngconfirm = res.get('ruleForm').get('ngconfirm', '')
            caldata = res.get('ruleForm').get('caldata')
            if caldata == '' or caldata == None:
                return responsePost('成功修改权限', {'msg': '校验日期不能为空', 'data': 'fail'})
            times = time.strptime(caldata, "%Y-%m-%d")
            timeStampnow = int(time.mktime(times))
            # caldata = time.strftime("%Y-%m-%d", times)

            db.session.query(Dcal).filter(Dcal.id == res.get('ruleForm').get('id')).update({'instrumentno': res.get('ruleForm').get('instrumentno'), 'instrumentname': res.get('ruleForm').get('instrumentname'),  'factorysign': res.get('ruleForm').get('factorysign'), 'type': res.get('ruleForm').get('type'),  'addreason': res.get('ruleForm').get('addreason'), 'calperiod': res.get('ruleForm').get('calperiod'),  'locno': res.get('ruleForm').get('locno'), 'location': res.get('ruleForm').get('location'), 'refdoc': res.get('ruleForm').get('refdoc'), 'calunit': res.get(
                'ruleForm').get('calunit'), 'precal': res.get('ruleForm').get('precal'), 'caltype': res.get('ruleForm').get('caltype'), 'keeper': res.get('ruleForm').get('keeper'), 'deptment': res.get('ruleForm').get('deptment'), 'comment': res.get('ruleForm').get('comment'), 'measureparameter': res.get('ruleForm').get('measureparameter'), 'offset': res.get('ruleForm').get('offset'), 'showradio': res.get('ruleForm').get('showradio'), 'grade': res.get('ruleForm').get('grade')})
            db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkstatus': 3, 'measurestatus': res.get(
                'ruleForm').get('measurestatus'), 'calcost': res.get('ruleForm').get('calcost'), 'caldata': caldata,   'temperature': res.get('ruleForm').get('temperature'), 'calperiod': res.get('ruleForm').get('calperiod'), 'humidity': res.get('ruleForm').get('humidity'), 'checkmode': res.get('ruleForm').get('checkmode')})
            db.session.query(Dcal_instep).filter(Dcal_instep.calid == res.get('ruleForm').get(
                'id')).filter(Dcal_instep.version == res.get('ruleForm').get('version')).delete()
            db.session.query(Dcal_instep).filter(Dcal_instep.calid == res.get('ruleForm').get(
                'id')).filter(Dcal_instep.version == res.get('ruleForm').get('version')+1).delete()
            db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get(
                'id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')+1).delete()
            db.session.query(Dcal_standcaleq).filter(
                Dcal_standcaleq.calid == res.get('ruleForm').get('id')).delete()
            checkins = res.get('ruleForm').get('checkins')
            addins = []
            for i in checkins:
                if i.get('insno', '').get('content') != '':
                    addins.append(Dcal_standcaleq(calid=res.get('ruleForm').get('id'), sname=i.get('sname', '').get('content'), insno=i.get('insno', '').get('content').strip(), mfg_model=i.get(
                        'mfg_model', '').get('content'), serno=i.get('serno', '').get('content'), duedate=i.get('duedate', '').get('content')))
            db.session.add_all(addins)
            # times = time.localtime()
            # timeStampnow = int(time.mktime(times))
            calperiod = int(res.get('ruleForm').get('calperiod', ''))
            if calperiod == "":
                calperiod = 12
            duedate = timeStampnow+calperiod*30*24*3600
            duedate = time.localtime(duedate)
            duedate = time.strftime("%Y-%m-%d", duedate)
            # caldata = time.strftime("%Y-%m-%d", timeStampnow)
            version = res.get('ruleForm').get('version')
            db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get(
                'id')).filter(Dcal_cal_version.version == version).update({'duedate': duedate})
            dcalversionadd = Dcal_cal_version(calid=res.get('ruleForm').get('id'), measurestatus=res.get('ruleForm').get(
                'measurestatus'), calcost=res.get('ruleForm').get('calcost'), calperiod=calperiod, checkmode=res.get('ruleForm').get('checkmode'), duedate=duedate, checkstatus=1, version=version+1)
            db.session.add(dcalversionadd)
            checklist = res.get('ruleForm').get('checklist')
            addcheobj = []
            failcount = 0
            failmsg = ''
            if res.get('ruleForm').get('appearance') == 1:
                mylogger.debug('oopppp1')
                failcount += 1
                bp = ''
                for i in res.get('ruleForm').get('upurl'):
                    beforefilename = i.split('/')[-1]
                    oldpath = getServer()['uploadPath']+'temp/'
                    newpath = getServer()['uploadPath']+'appearfail/'
                    bp = bp+newpath+beforefilename+','
                    if beforefilename and not os.path.isfile(bp):
                        shutil.move(oldpath+beforefilename, newpath)
                failmsg = failmsg + res.get('ruleForm').get('ngreason', '')
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == version).update(
                    {'appearance': res.get('ruleForm').get('appearance'), 'ngreason': res.get('ruleForm').get('ngreason', ''), 'appearancefailpic': bp})
            if res.get('ruleForm').get('checkmode') != '内校':
                bp = ''
                for i in res.get('ruleForm').get('outurlArr'):
                    beforefilename = i.split('/')[-1]
                    oldpath = getServer()['uploadPath']+'temp/'
                    newpath = getServer()['uploadPath']+'outcal/'
                    bp = bp+newpath+beforefilename+','
                    if beforefilename and not os.path.isfile(bp):
                        shutil.move(oldpath+beforefilename, newpath)
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get(
                    'id')).filter(Dcal_cal_version.version == version).update({'outconfirm': res.get('ruleForm').get('outconfirm'),  'failpic': bp})
                if int(res.get('ruleForm').get('outconfirm')) == 2:
                    failcount += 1
                    mylogger.debug('oopppp2')
            for i in checklist:
                if res.get('ruleForm').get('checkmode') == '内校' and i.get('checkname', '').get('content') != '':
                    addcheobj.append(Dcal_instep(version=version+1, calid=res.get('ruleForm').get('id'), checkname=i.get('checkname', '').get('content'), downline=float(i.get('downline', '').get('content')), spec=i.get(
                        'spec', '').get('content'), standard=float(i.get('standard', '').get('content')), upline=i.get('upline', '').get('content')))
                    if ngconfirm == '' or ngconfirm == None:
                        if i.get('upline', '').get('content') == '±' and (round(float(i.get('reading', '').get('content')) - float(i.get('standard', '').get('content')), 5) <= float(i.get('downline', '').get('content')) and round(float(i.get('reading', '').get('content')) - float(i.get('standard', '').get('content')), 5) >= 0-float(i.get('downline', '').get('content'))):
                            addcheobj.append(Dcal_instep(version=version, calid=res.get('ruleForm').get('id'), checkname=i.get('checkname', '').get('content'), downline=float(i.get('downline', '').get('content')), reading=float(i.get('reading', '').get('content')), spec=i.get(
                                'spec', '').get('content'), standard=float(i.get('standard', '').get('content')), upline=i.get('upline', '').get('content'), checkresult='pass'))
                        elif i.get('upline', '').get('content') == '+' and (round(float(i.get('reading', '').get('content')) - float(i.get('standard', '').get('content')), 5) <= float(i.get('downline', '').get('content')) and round(float(i.get('reading', '').get('content')) - float(i.get('standard', '').get('content')), 5) >= 0):
                            addcheobj.append(Dcal_instep(version=version, calid=res.get('ruleForm').get('id'), checkname=i.get('checkname', '').get('content'), downline=float(i.get('downline', '').get('content')), reading=float(i.get('reading', '').get('content')), spec=i.get(
                                'spec', '').get('content'), standard=float(i.get('standard', '').get('content')), upline=i.get('upline', '').get('content'), checkresult='pass'))
                        elif i.get('upline', '').get('content') == '-' and (round(float(i.get('reading', '').get('content')) - float(i.get('standard', '').get('content')), 5) >= 0-float(i.get('downline', '').get('content')) and round(float(i.get('reading', '').get('content')) - float(i.get('standard', '').get('content')), 5) <= 0):
                            addcheobj.append(Dcal_instep(version=version, calid=res.get('ruleForm').get('id'), checkname=i.get('checkname', '').get('content'), downline=float(i.get('downline', '').get('content')), reading=float(i.get('reading', '').get('content')), spec=i.get(
                                'spec', '').get('content'), standard=float(i.get('standard', '').get('content')), upline=i.get('upline', '').get('content'), checkresult='pass'))
                        else:
                            addcheobj.append(Dcal_instep(version=version, calid=res.get('ruleForm').get('id'), checkname=i.get('checkname', '').get('content'), downline=float(i.get('downline', '').get('content')), reading=float(i.get('reading', '').get('content')), spec=i.get(
                                'spec', '').get('content'), standard=float(i.get('standard', '').get('content')), upline=i.get('upline', '').get('content'), checkresult='fail'))
                            failcount += 1
                            mylogger.debug('oopppp3')
                            if failcount < 5:
                                failmsg = failmsg + '\n 校正项目' + \
                                    i.get('checkname', '').get('content') + '校验失败,其参数为，标准值=' + str(i.get('standard', '').get('content')) + \
                                    ' 读值=' + str(i.get('reading', '').get('content')) + \
                                    ' 规格=' + str(i.get('spec', '').get('content'))
                            elif failcount == 5:
                                failmsg = failmsg + '\n 其余异常请查看详细信息'
                            else:
                                pass
            if ngconfirm != '' and ngconfirm != None:
                failcount += 1
                failmsg = failmsg + '\n '+ngconfirm
            mylogger.debug(failcount)
            db.session.add_all(addcheobj)
            curtime = time.strftime('%Y-%m-%d', time.localtime())
            mylogger.debug(333)
            mylogger.debug(version)
            if failcount == 0:
                mylogger.debug(334)
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get(
                    'ruleForm').get('id')).filter(Dcal_cal_version.version == version).update({'checkstatus': 3, 'checkresultall': 'pass', 'opensingnl': res.get('ruleForm').get('opensingnl'), 'opendate': curtime})
                db.session.commit()
                return responsePost('成功修改权限', {'data': 'success', 'msg': ''})
            else:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get(
                    'ruleForm').get('id')).filter(Dcal_cal_version.version == version).update({'checkstatus': 3, 'checkresultall': 'fail', 'opensingnl': res.get('ruleForm').get('opensingnl'), 'opendate': curtime, 'abstatus': 1, 'damagedes': failmsg, 'ngconfirm': ngconfirm})
                db.session.commit()
                return responsePost('成功修改权限', {'data': 'fail', 'msg': ''})
        else:
            mylogger.debug(123)
            url = config[env].api_url+'#/cal'
            url = NAVURL
            sendMailTread(['<EMAIL>'],  'test',
                          f'请登录<a href="{url}">量具管理</a>系统进行操作')
            # sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
            mylogger.debug(321)
    except:
        mylogger.info(traceback.format_exc())
        return responsePost('成功修改权限', {'data': traceback.format_exc(), 'msg': ''})
    return responsePost('成功修改权限', {'data': 'success', 'msg': ''})


@api.route('/addcal', methods=['POST'])
@ login_required
def addcal():
    mylogger.debug('*'*10+'addcal'+'*'*10)
    res = request.json
    mylogger.debug(res)
    addcheobj = []
    addins = []
    caldate = res.get('ruleForm').get('caldata')
    duedate = res.get('ruleForm').get('duedate')
    measurestatus = res.get('ruleForm').get('measurestatus')
    if measurestatus == 'MX':
        calperiod = 0
        times = time.localtime()
        timeStampnow = int(time.mktime(times))
        caldate = time.localtime(timeStampnow)
        caldate = time.strftime("%Y-%m-%d", caldate)
        duedate = caldate
    else:
        if duedate != '':
            pass
        elif caldate != '':
            timeArray = time.strptime(caldate, "%Y-%m-%d")
            timeStamp = int(time.mktime(timeArray))
            calperiod = int(res.get('ruleForm').get('calperiod'))
            duedate = timeStamp+calperiod*30*24*3600
            duedate = time.localtime(duedate)
            duedate = time.strftime("%Y-%m-%d", duedate)
        else:
            times = time.localtime()
            timeStampnow = int(time.mktime(times))
            calperiod = int(res.get('ruleForm').get('calperiod'))
            duedate = timeStampnow+calperiod*30*24*3600
            duedate = time.localtime(duedate)
            duedate = time.strftime("%Y-%m-%d", duedate)

    dcaladd = Dcal(instrumentno=res.get('ruleForm').get('instrumentno'), instrumentname=res.get('ruleForm').get('instrumentname'),  factorysign=res.get('ruleForm').get('factorysign'), type=res.get('ruleForm').get('type'), addreason=res.get('ruleForm').get('addreason'), calperiod=res.get('ruleForm').get('calperiod'),  locno=res.get('ruleForm').get('locno'), location=res.get('ruleForm').get('location'), refdoc=res.get('ruleForm').get('refdoc'), calunit=res.get('ruleForm').get('calunit'),  precal=res.get('ruleForm').get(
        'precal'), caltype=res.get('ruleForm').get('caltype'), keeper=res.get('ruleForm').get('keeper'), deptment=res.get('ruleForm').get('deptment'), comment=res.get('ruleForm').get('comment'), measureparameter=res.get('ruleForm').get('measureparameter'), offset=res.get('ruleForm').get('offset'), showradio=res.get('ruleForm').get('showradio'), grade=res.get('ruleForm').get('grade'), checkmode=res.get('ruleForm').get('checkmode'))
    db.session.add(dcaladd)
    db.session.flush()
    checklist = res.get('ruleForm').get('checklist')
    checkins = res.get('ruleForm').get('checkins')
    dcalid = dcaladd.id
    dcalversionadd = Dcal_cal_version(calid=dcalid, caldata=caldate, calperiod=res.get('ruleForm').get('calperiod'),  measurestatus=res.get('ruleForm').get(
        'measurestatus'), calcost=res.get('ruleForm').get('calcost'), checkmode=res.get('ruleForm').get('checkmode'), duedate=duedate, checkstatus=1, version=0)
    db.session.add(dcalversionadd)
    for i in checklist:
        if i.get('checkname', '').get('content') != '':
            if i.get('downline', '').get('content', 0) == "":
                downline = float(0)
            else:
                downline = float(i.get('downline', '').get('content', 0))
            if i.get('standard', '').get('content', 0) == "":
                standard = float(0)
            else:
                standard = float(i.get('standard', '').get('content', 0))
            if i.get('reading', '').get('content', 0) == "":
                reading = float(0)
            else:
                reading = float(i.get('reading', '').get('content', 0))
            addcheobj.append(Dcal_instep(calid=dcalid, checkname=i.get('checkname', '').get('content'), downline=downline, reading=reading, spec=i.get(
                'spec', '').get('content'), standard=standard, upline=i.get('upline', '').get('content'), checkresult='', version=0))
    db.session.add_all(addcheobj)
    for i in checkins:
        if i.get('insno', '').get('content') != '':
            addins.append(Dcal_standcaleq(calid=dcalid, sname=i.get('sname', '').get('content'), insno=i.get('insno', '').get('content').strip(), mfg_model=i.get(
                'mfg_model', '').get('content'), serno=i.get('serno', '').get('content'), duedate=i.get('duedate', '').get('content')))
    db.session.add_all(addins)
    db.session.commit()
    if res.get('val') == 0:
        mylogger.debug('0000000')
        # db.session.query(Dcal).filter(Dcal.id == res.get(
        #     'ruleForm').get('id')).update({'checkstatus': 2})
        # for i in checklist:
        #     db.session.query(Dcal_instep).filter(Dcal_instep.id == i.get(
        #         'checkid')).update({'spec': i.get('spec')})
        db.session.commit()

    return responsePost('成功修改权限', {'data': 'success'})


@ api.route('/getCALstand', methods=['GET'])
@ login_required
def getCALstand():
    mylogger.debug('*'*10+'getCALstand'+'*'*10)
    res = request.args
    mylogger.debug(res)
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    vaildvalue1 = res.get('vaildvalue1')
    vaildvalue2 = res.get('vaildvalue2')
    filter1 = res.get('filter1')
    calperson = res.get('calperson')
    if filter1 == 'true':
        cal = db.session.query(Dcal.instrumentno.label('instrumentno'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.caldata.label(
            'caldata'), Dcal_cal_version.duedate.label('duedate'), Dcal_cal_version.checkmode.label('checkmode'), Dcal_cal_version.calperiod.label('calperiod')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.checkresultall == None)
        if vaildvalue1 != '':
            cal = cal.filter(Dcal_cal_version.duedate.between(vaildvalue1, vaildvalue2))
        if calperson and calperson != "":
            if calperson != '苏州计量':
                cal = cal.filter(Dcal_cal_version.checkmode == '内校')
            else:
                cal = cal.filter(Dcal_cal_version.checkmode != '内校')
        total = cal.count()
        cal = cal.order_by(Dcal_cal_version.duedate).paginate(
            pagenum, pagesize, error_out=False).items
        calList = []
        for i in cal:
            caldict = {}
            caldict['instrumentno'] = i.instrumentno
            if i.checkmode == '内校':
                caldict['opensingnl'] = i.opensingnl
            else:
                caldict['opensingnl'] = '苏州计量'
            if i.duedate and i.calperiod:
                timeArray = time.strptime(i.duedate.strftime("%Y-%m-%d"), "%Y-%m-%d")
                timeStamp = int(time.mktime(timeArray))
                duedate = timeStamp+i.calperiod*30*24*3600
                duedate = time.localtime(duedate)
                caldict['duedate'] = time.strftime("%Y-%m-%d", duedate)
            else:
                caldict['duedate'] = ''
            if i.duedate:
                caldict['caldata'] = i.duedate.strftime("%Y-%m-%d")
            else:
                caldict['caldata'] = ''
            calList.append(caldict)
        mylogger.debug(total)
        return responseGet("maxvocId", {'calList': calList, 'total': total})
    # cal = db.session.query(Dcal.instrumentno.label('instrumentno'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.caldata.label(
    #     'caldata'), Dcal_cal_version.duedate.label('duedate'),Dcal_cal_version.checkmode.label('checkmode')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.checkresultall == 'pass')
    # if vaildvalue1 != '':
    #     cal = cal.filter(Dcal_cal_version.caldata.between(vaildvalue1, vaildvalue2))
    # maxvocId = db.session.query(func.max(Dvoc.Id)).first()
    # vfromArrVoc = getDsortVoc('VOC来源')
    # vsmArrVoc = getOptionsVoc('vsm')
    calList = []
    # for i in cal:
    #     caldict = {}
    #     caldict['instrumentno'] = i.instrumentno
    #     if i.checkmode == '内校':
    #         caldict['opensingnl'] = i.opensingnl
    #     else:
    #         caldict['opensingnl'] = '苏州计量'
    #     # caldict['duedate'] = i.duedate
    #     if i.duedate:
    #         caldict['duedate'] = i.duedate.strftime("%Y-%m-%d")
    #     else:
    #         caldict['duedate'] = ''
    #     if i.caldata:
    #         caldict['caldata'] = i.caldata.strftime("%Y-%m-%d")
    #     else:
    #         caldict['caldata'] = ''
    #     calList.append(caldict)
    cal = db.session.query(Dcal_cal_version.caldata.label('caldata'), Dcal_cal_version.duedate.label('duedate'), Dcal.instrumentno.label('instrumentno'),
                           Dcal_cal_version.checkmode.label('checkmode'), Dcal_cal_version.opensingnl.label('opensingnl')).outerjoin(Dcal,
                                                                                                                                     Dcal.id == Dcal_cal_version.calid).filter(or_(Dcal_cal_version.checkresultall == 'pass', and_(Dcal_cal_version.checkresultall == 'fail', or_(Dcal_cal_version.processre == '特采', Dcal_cal_version.processre == '延期'))))
    if vaildvalue1 != '':
        cal = cal.filter(Dcal_cal_version.caldata.between(vaildvalue1, vaildvalue2))
    if calperson and calperson != "":
        if calperson != '苏州计量':
            cal = cal.filter(Dcal_cal_version.opensingnl.like(
                '%{0}%'.format(calperson))).filter(Dcal_cal_version.checkmode == '内校')
        else:
            cal = cal.filter(Dcal_cal_version.checkmode != '内校')
    total = cal.count()
    cal = cal.order_by(Dcal_cal_version.caldata).paginate(pagenum, pagesize, error_out=False).items
    for i in cal:
        caldict = {}
        caldict['instrumentno'] = i.instrumentno
        if i.checkmode == '内校':
            caldict['opensingnl'] = i.opensingnl
        else:
            caldict['opensingnl'] = '苏州计量'
        # caldict['duedate'] = i.duedate
        if i.duedate:
            caldict['duedate'] = i.duedate.strftime("%Y-%m-%d")
        else:
            caldict['duedate'] = ''
        if i.caldata:
            caldict['caldata'] = i.caldata.strftime("%Y-%m-%d")
        else:
            caldict['caldata'] = ''
        calList.append(caldict)
    # if len(calList) < 35:
    #     for i in range(len(calList), 35):
    #         calList.append({"instrumentno": "", "caldata": ""})
    mylogger.debug(total)
    return responseGet("maxvocId", {'calList': calList, 'total': total})


@ api.route('/getNoCAL', methods=['GET'])
@ login_required
def getNoCAL():
    mylogger.debug('*'*10+'getNoCAL'+'*'*10)
    res = request.args
    mylogger.debug(res)
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    vaildvalue1 = res.get('vaildvalue1')
    vaildvalue2 = res.get('vaildvalue2')

    cal = db.session.query(Dcal.instrumentno.label('instrumentno'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.caldata.label(
        'caldata'), Dcal_cal_version.duedate.label('duedate'), Dcal_cal_version.checkmode.label('checkmode'), Dcal_cal_version.calperiod.label('calperiod')).outerjoin(
        Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.measurestatus == 'MX')
    if vaildvalue1 != '':
        cal = cal.filter(Dcal_cal_version.duedate.between(vaildvalue1, vaildvalue2))
    total = cal.count()
    cal = cal.order_by(Dcal_cal_version.duedate).paginate(
        pagenum, pagesize, error_out=False).items
    calList = []
    for i in cal:
        caldict = {}
        caldict['instrumentno'] = i.instrumentno
        if i.checkmode == '内校':
            caldict['opensingnl'] = i.opensingnl
        else:
            caldict['opensingnl'] = '苏州计量'
        if i.duedate and i.calperiod:
            timeArray = time.strptime(i.duedate.strftime("%Y-%m-%d"), "%Y-%m-%d")
            timeStamp = int(time.mktime(timeArray))
            duedate = timeStamp+i.calperiod*30*24*3600
            duedate = time.localtime(duedate)
            caldict['duedate'] = time.strftime("%Y-%m-%d", duedate)
        else:
            caldict['duedate'] = ''
        if i.duedate:
            caldict['caldata'] = i.duedate.strftime("%Y-%m-%d")
        else:
            caldict['caldata'] = ''
        calList.append(caldict)
    mylogger.debug(total)
    return responseGet("maxvocId", {'calList': calList, 'total': total})


@ api.route('/caluploadimgAFile', methods=['POST'])
def caluploadimgAFile():
    mylogger.debug("caluploadimgAFile")
    file_obj = request.files.get('file')
    mylogger.debug(file_obj)
    mystr = ('cal' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    # file_obj.save('RCCM.xlsx')
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        file_obj.save(getServer()['uploadPath']+'temp/'+'fail_'+name+appendix)
        mylogger.debug(getServer()['uploadPath']+'temp/'+'fail_'+name+appendix)
        # file_obj.save('temp/'+uploadType+'_'+name+appendix)
    return responsePost("更新成功", {'upload_url': getServer()['uploadUrl']+'temp/'+'fail_'+name+appendix})


@api.route('/postcalfailimg', methods=['POST'])
def postcalfailimg():
    mylogger.info('postcalfailimg start=====')
    res = request.json
    mylogger.debug(res)
    beforefilename = res.get('url').split('/')[-1]
    oldpath = getServer()['uploadPath']+'temp/'
    newpath = getServer()['uploadPath']+'calfail/'
    bp = newpath+beforefilename
    if beforefilename and not os.path.isfile(bp):
        shutil.move(oldpath+beforefilename, newpath)
    else:
        bp = ''
    db.session.query(Dcal).filter(Dcal.id == res.get('val').get(
        'id')).update({'failpic': bp, 'checkresultall': 'fail'})
    db.session.commit()
    return responsePost('成功')


@ api.route('/getablist', methods=['GET'])
@ login_required
def getablist():
    mylogger.debug('*'*10+'getablist'+'*'*10)
    res = request.args
    mylogger.debug(res.get('filter1'))
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    owneremail = res.get('owner')+'@pentair.com'
    filter1 = res.get('filter1')
    option = res.get('oploption')
    calvalue = res.get('vocvalue')
    abstatus1 = res.get('abstatus1')
    if filter1 == 'true':
        dept = db.session.query(Dcal_user.dept).filter(Dcal_user.email == owneremail,).first()[0]
        mylogger.debug(dept)
        # if dept == 'A':
        #     cal = db.session.query(Dcal).filter(Dcal.checkresultall == 'fail').filter(or_(and_(Dcal.checkbox2 == 2, Dcal.deptA == res.get('owner'), Dcal.austatus == None, Dcal.abstatus == 2), and_(
        #         Dcal.checkbox2 == 1, Dcal.austatus == 2, Dcal.abstatus == 2, Dcal.deptA == res.get('owner')), and_(Dcal.abstatus == 1, Dcal.opensingnl == res.get('owner')), and_(Dcal.abstatus == 3, Dcal.opensingnl == res.get('owner'))))
        # elif dept == 'B':
        #     cal = db.session.query(Dcal).filter(Dcal.checkresultall == 'fail').filter(or_(and_(Dcal.deptB == res.get('owner'), Dcal.austatus ==
        #                                                                                        None, Dcal.checkbox2 != 2, Dcal.checkbox2 != None, Dcal.abstatus == 2), and_(Dcal.abstatus == 1, Dcal.opensingnl == res.get('owner')), and_(Dcal.abstatus == 3, Dcal.opensingnl == res.get('owner'))))
        # elif dept == 'C':
        #     cal = db.session.query(Dcal).filter(Dcal.checkresultall == 'fail').filter(or_(and_(Dcal.deptC == res.get('owner'), Dcal.austatus == 1, Dcal.checkbox2 != 2, Dcal.checkbox2 !=
        #                                                                                        None, Dcal.abstatus == 2), and_(Dcal.abstatus == 1, Dcal.opensingnl == res.get('owner')), and_(Dcal.abstatus == 3, Dcal.opensingnl == res.get('owner'))))
        # else:
        #     cal = db.session.query(Dcal).filter(Dcal.checkresultall == 'fail').filter(or_(and_(
        #         Dcal.abstatus == 1, Dcal.opensingnl == res.get('owner')), and_(Dcal.abstatus == 3, Dcal.opensingnl == res.get('owner'))))

        if dept == 'A':
            cal = db.session.query(Dcal.id.label('id'), Dcal.instrumentno.label('instrumentno'), Dcal.instrumentname.label('instrumentname'), Dcal.factorysign.label('factorysign'), Dcal.type.label('type'), Dcal.factoryno.label('factoryno'), Dcal_cal_version.calperiod.label('calperiod'), Dcal_cal_version.caldata.label('caldata'), Dcal_cal_version.checkstatus.label('checkstatus'), Dcal_cal_version.checkresultall.label('checkresultall'), Dcal.locno.label('locno'), Dcal.location.label('location'), Dcal_cal_version.duedate.label('duedate'), Dcal.refdoc.label('refdoc'), Dcal.calunit.label('calunit'), Dcal.checkmode.label('checkmode'), Dcal_cal_version.failpic.label('failpic'), Dcal_cal_version.measurestatus.label('measurestatus'), Dcal_cal_version.calcost.label('calcost'), Dcal.precal.label('precal'), Dcal.caltype.label('caltype'), Dcal.keeper.label('keeper'), Dcal.deptment.label('deptment'), Dcal.comment.label('comment'), Dcal.measureparameter.label('measureparameter'), Dcal.offset.label('offset'), Dcal.showradio.label('showradio'), Dcal.grade.label('grade'),  Dcal_cal_version.appearancefailpic.label('appearancefailpic'), Dcal_cal_version.appearance.label('appearance'), Dcal_cal_version.ngreason.label('ngreason'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.opendate.label('opendate'), Dcal_cal_version.checkbox1.label('checkbox1'), Dcal_cal_version.checkbox2.label('checkbox2'), Dcal_cal_version.abstatus.label('abstatus'), Dcal_cal_version.checkbox3.label('checkbox3'), Dcal_cal_version.checkbox4.label('checkbox4'), Dcal_cal_version.deptA.label('deptA'), Dcal_cal_version.deptB.label('deptB'), Dcal_cal_version.deptC.label('deptC'), Dcal_cal_version.deptD.label('deptD'), Dcal_cal_version.austatus.label('austatus'), Dcal_cal_version.recallev.label('recallev'), Dcal_cal_version.rootcase.label('rootcase'), Dcal_cal_version.processre.label('processre'), Dcal_cal_version.repaircost.label('repaircost'), Dcal_cal_version.newbuycost.label('newbuycost'), Dcal_cal_version.classify.label('classify'), Dcal_cal_version.verpersion.label('verpersion'), Dcal_cal_version.verdate.label('verdate'), Dcal_cal_version.damagedes.label('damagedes'), Dcal_cal_version.outconfirm.label('outconfirm'), Dcal_cal_version.temperature.label('temperature'), Dcal_cal_version.humidity.label('humidity'), Dcal_cal_version.version.label('version')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.checkresultall == 'fail').filter(or_(and_(Dcal_cal_version.checkbox2 == 2, Dcal_cal_version.deptA == res.get('owner'), Dcal_cal_version.austatus == 2, Dcal_cal_version.abstatus == 2), and_(
                Dcal_cal_version.checkbox2 == 1, Dcal_cal_version.austatus == 2, Dcal_cal_version.abstatus == 2, Dcal_cal_version.deptA == res.get('owner')), and_(Dcal_cal_version.abstatus == 1, Dcal_cal_version.opensingnl == res.get('owner')), and_(Dcal_cal_version.abstatus == 3, Dcal_cal_version.opensingnl == res.get('owner'))))
        elif dept == 'B':
            cal = db.session.query(Dcal.id.label('id'), Dcal.instrumentno.label('instrumentno'), Dcal.instrumentname.label('instrumentname'), Dcal.factorysign.label('factorysign'), Dcal.type.label('type'), Dcal.factoryno.label('factoryno'), Dcal_cal_version.calperiod.label('calperiod'), Dcal_cal_version.caldata.label('caldata'), Dcal_cal_version.checkstatus.label('checkstatus'), Dcal_cal_version.checkresultall.label('checkresultall'), Dcal.locno.label('locno'), Dcal.location.label('location'), Dcal_cal_version.duedate.label('duedate'), Dcal.refdoc.label('refdoc'), Dcal.calunit.label('calunit'), Dcal.checkmode.label('checkmode'), Dcal_cal_version.failpic.label('failpic'), Dcal_cal_version.measurestatus.label('measurestatus'), Dcal_cal_version.calcost.label('calcost'), Dcal.precal.label('precal'), Dcal.caltype.label('caltype'), Dcal.keeper.label('keeper'), Dcal.deptment.label('deptment'), Dcal.comment.label('comment'), Dcal.measureparameter.label('measureparameter'), Dcal.offset.label('offset'), Dcal.showradio.label('showradio'), Dcal.grade.label('grade'),  Dcal_cal_version.appearancefailpic.label('appearancefailpic'), Dcal_cal_version.appearance.label('appearance'), Dcal_cal_version.ngreason.label('ngreason'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.opendate.label('opendate'), Dcal_cal_version.checkbox1.label('checkbox1'), Dcal_cal_version.checkbox2.label('checkbox2'), Dcal_cal_version.abstatus.label('abstatus'), Dcal_cal_version.checkbox3.label('checkbox3'), Dcal_cal_version.checkbox4.label('checkbox4'), Dcal_cal_version.deptA.label('deptA'), Dcal_cal_version.deptB.label('deptB'), Dcal_cal_version.deptC.label('deptC'), Dcal_cal_version.deptD.label('deptD'), Dcal_cal_version.austatus.label('austatus'), Dcal_cal_version.recallev.label('recallev'), Dcal_cal_version.rootcase.label('rootcase'), Dcal_cal_version.processre.label('processre'), Dcal_cal_version.repaircost.label('repaircost'), Dcal_cal_version.newbuycost.label('newbuycost'), Dcal_cal_version.classify.label('classify'), Dcal_cal_version.verpersion.label('verpersion'), Dcal_cal_version.verdate.label('verdate'), Dcal_cal_version.damagedes.label('damagedes'), Dcal_cal_version.outconfirm.label('outconfirm'), Dcal_cal_version.temperature.label('temperature'), Dcal_cal_version.humidity.label('humidity'), Dcal_cal_version.version.label('version')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.checkresultall == 'fail').filter(or_(and_(Dcal_cal_version.deptB == res.get('owner'), Dcal_cal_version.austatus ==
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             None,  Dcal_cal_version.checkbox2 != None, Dcal_cal_version.abstatus == 2), and_(Dcal_cal_version.abstatus == 1, Dcal_cal_version.opensingnl == res.get('owner')), and_(Dcal_cal_version.abstatus == 3, Dcal_cal_version.opensingnl == res.get('owner'))))
        elif dept == 'C':
            cal = db.session.query(Dcal.id.label('id'), Dcal.instrumentno.label('instrumentno'), Dcal.instrumentname.label('instrumentname'), Dcal.factorysign.label('factorysign'), Dcal.type.label('type'), Dcal.factoryno.label('factoryno'), Dcal_cal_version.calperiod.label('calperiod'), Dcal_cal_version.caldata.label('caldata'), Dcal_cal_version.checkstatus.label('checkstatus'), Dcal_cal_version.checkresultall.label('checkresultall'), Dcal.locno.label('locno'), Dcal.location.label('location'), Dcal_cal_version.duedate.label('duedate'), Dcal.refdoc.label('refdoc'), Dcal.calunit.label('calunit'), Dcal.checkmode.label('checkmode'), Dcal_cal_version.failpic.label('failpic'), Dcal_cal_version.measurestatus.label('measurestatus'), Dcal_cal_version.calcost.label('calcost'), Dcal.precal.label('precal'), Dcal.caltype.label('caltype'), Dcal.keeper.label('keeper'), Dcal.deptment.label('deptment'), Dcal.comment.label('comment'), Dcal.measureparameter.label('measureparameter'), Dcal.offset.label('offset'), Dcal.showradio.label('showradio'), Dcal.grade.label('grade'),  Dcal_cal_version.appearancefailpic.label('appearancefailpic'), Dcal_cal_version.appearance.label('appearance'), Dcal_cal_version.ngreason.label('ngreason'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.opendate.label('opendate'), Dcal_cal_version.checkbox1.label('checkbox1'), Dcal_cal_version.checkbox2.label('checkbox2'), Dcal_cal_version.abstatus.label('abstatus'), Dcal_cal_version.checkbox3.label('checkbox3'), Dcal_cal_version.checkbox4.label('checkbox4'), Dcal_cal_version.deptA.label('deptA'), Dcal_cal_version.deptB.label('deptB'), Dcal_cal_version.deptC.label('deptC'), Dcal_cal_version.deptD.label('deptD'), Dcal_cal_version.austatus.label('austatus'), Dcal_cal_version.recallev.label('recallev'), Dcal_cal_version.rootcase.label('rootcase'), Dcal_cal_version.processre.label('processre'), Dcal_cal_version.repaircost.label('repaircost'), Dcal_cal_version.newbuycost.label('newbuycost'), Dcal_cal_version.classify.label('classify'), Dcal_cal_version.verpersion.label('verpersion'), Dcal_cal_version.verdate.label('verdate'), Dcal_cal_version.damagedes.label('damagedes'), Dcal_cal_version.outconfirm.label('outconfirm'), Dcal_cal_version.temperature.label('temperature'), Dcal_cal_version.humidity.label('humidity'), Dcal_cal_version.version.label('version')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.checkresultall == 'fail').filter(or_(and_(
                Dcal_cal_version.deptC == res.get('owner'), Dcal_cal_version.austatus == 1,  Dcal_cal_version.checkbox2 != None, Dcal_cal_version.abstatus == 2), and_(Dcal_cal_version.abstatus == 1, Dcal_cal_version.opensingnl == res.get('owner')), and_(Dcal_cal_version.abstatus == 3, Dcal_cal_version.opensingnl == res.get('owner'))))
        else:
            cal = db.session.query(Dcal.id.label('id'), Dcal.instrumentno.label('instrumentno'), Dcal.instrumentname.label('instrumentname'), Dcal.factorysign.label('factorysign'), Dcal.type.label('type'), Dcal.factoryno.label('factoryno'), Dcal_cal_version.calperiod.label('calperiod'), Dcal_cal_version.caldata.label('caldata'), Dcal_cal_version.checkstatus.label('checkstatus'), Dcal_cal_version.checkresultall.label('checkresultall'), Dcal.locno.label('locno'), Dcal.location.label('location'), Dcal_cal_version.duedate.label('duedate'), Dcal.refdoc.label('refdoc'), Dcal.calunit.label('calunit'), Dcal.checkmode.label('checkmode'), Dcal_cal_version.failpic.label('failpic'), Dcal_cal_version.measurestatus.label('measurestatus'), Dcal_cal_version.calcost.label('calcost'), Dcal.precal.label('precal'), Dcal.caltype.label('caltype'), Dcal.keeper.label('keeper'), Dcal.deptment.label('deptment'), Dcal.comment.label('comment'), Dcal.measureparameter.label('measureparameter'), Dcal.offset.label('offset'), Dcal.showradio.label('showradio'), Dcal.grade.label('grade'),  Dcal_cal_version.appearancefailpic.label('appearancefailpic'), Dcal_cal_version.appearance.label('appearance'), Dcal_cal_version.ngreason.label('ngreason'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.opendate.label('opendate'), Dcal_cal_version.checkbox1.label('checkbox1'), Dcal_cal_version.checkbox2.label('checkbox2'), Dcal_cal_version.abstatus.label('abstatus'), Dcal_cal_version.checkbox3.label('checkbox3'), Dcal_cal_version.checkbox4.label('checkbox4'), Dcal_cal_version.deptA.label('deptA'), Dcal_cal_version.deptB.label('deptB'), Dcal_cal_version.deptC.label('deptC'), Dcal_cal_version.deptD.label('deptD'), Dcal_cal_version.austatus.label('austatus'), Dcal_cal_version.recallev.label('recallev'), Dcal_cal_version.rootcase.label('rootcase'), Dcal_cal_version.processre.label('processre'), Dcal_cal_version.repaircost.label('repaircost'), Dcal_cal_version.newbuycost.label('newbuycost'), Dcal_cal_version.classify.label('classify'), Dcal_cal_version.verpersion.label('verpersion'), Dcal_cal_version.verdate.label('verdate'), Dcal_cal_version.damagedes.label('damagedes'), Dcal_cal_version.outconfirm.label('outconfirm'), Dcal_cal_version.temperature.label('temperature'), Dcal_cal_version.humidity.label('humidity'), Dcal_cal_version.version.label('version')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.checkresultall == 'fail').filter(or_(and_(
                Dcal_cal_version.abstatus == 1, Dcal_cal_version.opensingnl == res.get('owner')), and_(Dcal_cal_version.abstatus == 3, Dcal_cal_version.opensingnl == res.get('owner'))))
    else:
        mylogger.debug(666)
        # cal = db.session.query(Dcal).filter(Dcal.checkresultall == 'fail',)
        cal = db.session.query(Dcal.id.label('id'), Dcal.instrumentno.label('instrumentno'), Dcal.instrumentname.label('instrumentname'), Dcal.factorysign.label('factorysign'), Dcal.type.label('type'), Dcal.factoryno.label('factoryno'), Dcal_cal_version.calperiod.label('calperiod'), Dcal_cal_version.caldata.label('caldata'), Dcal_cal_version.checkstatus.label('checkstatus'), Dcal_cal_version.checkresultall.label('checkresultall'), Dcal.locno.label('locno'), Dcal.location.label('location'), Dcal_cal_version.duedate.label('duedate'), Dcal.refdoc.label('refdoc'), Dcal.calunit.label('calunit'), Dcal.checkmode.label('checkmode'), Dcal_cal_version.failpic.label('failpic'), Dcal_cal_version.measurestatus.label('measurestatus'), Dcal_cal_version.calcost.label('calcost'), Dcal.precal.label('precal'), Dcal.caltype.label('caltype'), Dcal.keeper.label('keeper'), Dcal.deptment.label('deptment'), Dcal.comment.label('comment'), Dcal.measureparameter.label('measureparameter'), Dcal.offset.label('offset'), Dcal.showradio.label('showradio'), Dcal.grade.label('grade'),  Dcal_cal_version.appearancefailpic.label('appearancefailpic'), Dcal_cal_version.appearance.label(
            'appearance'), Dcal_cal_version.ngreason.label('ngreason'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.opendate.label('opendate'), Dcal_cal_version.checkbox1.label('checkbox1'), Dcal_cal_version.checkbox2.label('checkbox2'), Dcal_cal_version.abstatus.label('abstatus'), Dcal_cal_version.checkbox3.label('checkbox3'), Dcal_cal_version.checkbox4.label('checkbox4'), Dcal_cal_version.deptA.label('deptA'), Dcal_cal_version.deptB.label('deptB'), Dcal_cal_version.deptC.label('deptC'), Dcal_cal_version.deptD.label('deptD'), Dcal_cal_version.austatus.label('austatus'), Dcal_cal_version.recallev.label('recallev'), Dcal_cal_version.rootcase.label('rootcase'), Dcal_cal_version.processre.label('processre'), Dcal_cal_version.repaircost.label('repaircost'), Dcal_cal_version.newbuycost.label('newbuycost'), Dcal_cal_version.classify.label('classify'), Dcal_cal_version.verpersion.label('verpersion'), Dcal_cal_version.verdate.label('verdate'), Dcal_cal_version.damagedes.label('damagedes'), Dcal_cal_version.outconfirm.label('outconfirm'), Dcal_cal_version.temperature.label('temperature'), Dcal_cal_version.humidity.label('humidity'), Dcal_cal_version.version.label('version')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.checkresultall == 'fail')

    if calvalue != '':
        if option == '仪器编号':
            tag = Dcal.instrumentno
        elif option == '仪器名称':
            tag = Dcal.instrumentname
        elif option == 'id':
            tag = Dcal.id
        elif option == '厂牌':
            tag = Dcal.factorysign
        elif option == '型号':
            tag = Dcal.type
        elif option == '校验类别':
            tag = Dcal.checkmode
        # cal = db.session.query(Dcal).filter(tag.like('%{0}%'.format(calvalue)))
        # total = db.session.query(Dcal).filter(tag.like('%{0}%'.format(calvalue)))
        cal = cal.filter(tag.like('%{0}%'.format(calvalue)))
    mylogger.debug(abstatus1)
    mylogger.debug(77777)
    if abstatus1 != '':
        if abstatus1 == '尚未核签':
            mylogger.debug(88888)
            cal = cal.filter(Dcal_cal_version.abstatus == 1)
        elif abstatus1 == '待相关人员核签':
            cal = cal.filter(Dcal_cal_version.abstatus == 2)
        elif abstatus1 == '待验证人最终处置':
            cal = cal.filter(Dcal_cal_version.abstatus == 3)
        elif abstatus1 == '核签完成':
            cal = cal.filter(Dcal_cal_version.abstatus == 4)
    cal = cal.order_by(Dcal.id)
    total = cal.count()
    cal = cal.paginate(pagenum, pagesize, error_out=False).items

    calList = []

    for i in cal:
        caldict = {}
        caldict['id'] = i.id
        caldict['instrumentno'] = i.instrumentno
        caldict['instrumentname'] = i.instrumentname
        caldict['factorysign'] = i.factorysign
        caldict['type'] = i.type
        caldict['factoryno'] = i.factoryno
        caldict['calperiod'] = i.calperiod
        caldict['checkstatus'] = i.checkstatus
        caldict['locno'] = i.locno
        caldict['location'] = i.location
        caldict['duedate'] = i.duedate
        caldict['refdoc'] = i.refdoc
        caldict['calunit'] = i.calunit
        caldict['checkmode'] = i.checkmode
        # caldict['failpic'] = config[env].base_url+'cal/workfolder/uploads/calfail/' + \
        #     i.failpic.split('/')[-1].split('\\')[-1]
        caldict['failpic'] = ''
        caldict['appearancefailpic'] = []
        if i.appearancefailpic:
            for j in i.appearancefailpic.split(','):
                if j != '':
                    caldict['appearancefailpic'].append(
                        config[env].base_url+'cal/workfolder/uploads/appearfail/' + j.split('/')[-1].split('\\')[-1])
        caldict['keeper'] = i.keeper
        caldict['failpic'] = []
        if i.failpic:
            for j in i.failpic.split(','):
                if j != '':
                    caldict['failpic'].append(
                        config[env].base_url+'cal/workfolder/uploads/outcal/' + j.split('/')[-1].split('\\')[-1])
        caldict['deptment'] = i.deptment
        caldict['opensingnl'] = i.opensingnl
        caldict['opendate'] = i.opendate
        caldict['checkbox1'] = i.checkbox1
        caldict['checkbox2'] = i.checkbox2
        caldict['checkbox3'] = i.checkbox3
        caldict['checkbox4'] = i.checkbox4
        caldict['deptA'] = i.deptA
        caldict['deptB'] = i.deptB
        caldict['deptC'] = i.deptC
        caldict['deptD'] = i.deptD
        caldict['abstatus'] = i.abstatus
        caldict['austatus'] = i.austatus
        caldict['recallev'] = i.recallev
        caldict['rootcase'] = i.rootcase
        caldict['processre'] = i.processre
        caldict['classify'] = i.classify
        caldict['repaircost'] = i.repaircost
        caldict['newbuycost'] = i.newbuycost
        caldict['verpersion'] = i.verpersion
        caldict['ngreason'] = i.ngreason
        caldict['temperature'] = i.temperature
        caldict['humidity'] = i.humidity
        if i.verdate:
            caldict['verdate'] = i.verdate.strftime("%Y-%m-%d")
        else:
            caldict['verdate'] = ''
        # caldict['verdate'] = i.verdate
        caldict['damagedes'] = i.damagedes
        caldict['outconfirm'] = i.outconfirm
        caldict['version'] = i.version
        calList.append(caldict)
    # 获取下拉框数据，用于搜加检索需要处理的异常单 db.session.query(Dsku.sku).distinct(Dsku.sku).all()
    dropdowndata = db.session.query(Dcal.instrumentno).distinct(Dcal.instrumentno)
    droplist = []
    count = 0
    for i in dropdowndata:
        if i.instrumentno:
            dic = {}
            # dic['value'] = 'Option ' + str(count)
            dic['value'] = i.instrumentno + ' ' + str(count)
            dic['label'] = i.instrumentno
            droplist.append(dic)
            count += 1

    return responseGet("maxvocId", {'calList': calList, 'total': total, 'droplist': droplist})


@ api.route('/getabinfo', methods=['GET'])
@ login_required
def getabinfo():
    mylogger.debug('*'*10+'getabinfo'+'*'*10)
    res = request.args
    insno = res.get('insno').split(' ')[0]
    response = db.session.query(Dcal.instrumentname, Dcal.keeper,
                                Dcal.deptment).filter(Dcal.instrumentno == insno).first()
    mylogger.debug(response)
    return responseGet("maxvocId", {'response': response})


@api.route('/postablist', methods=['POST'])
def postablist():
    mylogger.info('*'*10+'postablist'+'*'*10)
    res = request.json.get('addArr')
    mylogger.debug(res)
    insno = res.get('insno').split(' ')[0]
    calinsno = db.session.query(func.max(Dcal_cal_version.version)).outerjoin(
        Dcal, Dcal.id == Dcal_cal_version.calid).filter(Dcal.instrumentno == insno).first()
    calid = db.session.query(Dcal.id).filter(Dcal.instrumentno == insno).first()
    calid = calid[0]
    mylogger.debug(calinsno)
    if calinsno[0] < 9000000:
        version = 9000000
    else:
        version = calinsno[0] + 1
    url = config[env].api_url+'#/cal'
    url = NAVURL
    curtime = time.strftime('%Y-%m-%d', time.localtime())
    addab = Dcal_cal_version(calid=calid, version=version, checkbox1=res.get('checkbox1'), checkbox2=res.get('checkbox2'), opensingnl=res.get('onwer'), opendate=curtime, damagedes=res.get('damagedes'),
                             deptA=res.get('Adept'), deptB=res.get('Bdept'), deptC=res.get('Cdept'), deptD=res.get('Ddept'), abstatus=2, checkresultall='fail', checkstatus=3)
    db.session.add(addab)
    if res.get('appearance') == 1:
        mylogger.debug('oopppp1')
        bp = ''
        for i in res.get('upurl'):
            beforefilename = i.split('/')[-1]
            oldpath = getServer()['uploadPath']+'temp/'
            newpath = getServer()['uploadPath']+'appearfail/'
            bp = bp+newpath+beforefilename+','
            if beforefilename and not os.path.isfile(bp):
                shutil.move(oldpath+beforefilename, newpath)
        # failmsg = failmsg + res.get('ngreason')
        db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == calid).filter(Dcal_cal_version.version == version).update(
            {'appearance': res.get('appearance'), 'damagedes': res.get('damagedes'), 'appearancefailpic': bp})
    db.session.commit()
    try:
        emailarr = []
        email = res.get('ruleForm').get('Bdept')+'@pentair.com'
        emailarr.append(email)
        sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
    except:
        pass
    return responsePost('成功', {'re': 'success'})


@ api.route('/getUserArr', methods=['GET'])
@ login_required
def getUserArr():
    mylogger.info('getUserArr start=====')
    res = request.args
    users = db.session.query(Dcal_user).all()
    userlist = []

    def redsin(x):
        return x.strip(' ').strip('\'')
    for i in users:
        userdic = {}
        userdic['email'] = i.email
        userdic['eid'] = i.eid
        userdic['id'] = i.id
        userdic['dept'] = i.dept
        if i.powerlist:
            powerlist = i.powerlist.split(',')
            powerlist = list(map(redsin, powerlist))
            userdic['power'] = powerlist
        if i.poweredit:
            powereditlist = i.poweredit.split(',')
            powereditlist = list(map(redsin, powereditlist))
            userdic['poweredit'] = powereditlist
        # if i.deptarr:
        #     powereditlist = i.deptarr.split(',')
        #     powereditlist = list(map(redsin, powereditlist))
        #     userdic['deptarr'] = powereditlist
        userlist.append(userdic)
    return responseGet('haha', {'users': userlist})


@api.route('/postuser', methods=['POST'])
def postuser():
    mylogger.info('postuser start=====')
    res = request.json.get('ulist')
    power = request.json.get('power')
    # mylogger.debug(res.get('deptarr'))
    # mylogger.debug(str(res.get('deptarr')).strip('[').strip(']'))
    if request.json.get('val') == 0:
        r = db.session.query(Dcal_user).filter(Dcal_user.eid == res.get('eid')).first()
        if r != None:
            return responsePost('失败', {'re': 'chongfu'})
        mylogger.debug(r)
        useradd = Dcal_user(email=res.get('email'), eid=res.get('eid'), poweredit=str(res.get('poweredit')).strip('[').strip(']'), dept=res.get('dept'),
                            powerlist=str(power).strip('[').strip(']'))
        db.session.add(useradd)
        db.session.commit()
        r = requests.post(config[env].api_url+"/public/addUser",
                          {'email': res.get('email').lower(), 'eid': res.get('eid')})
        vdata = r.json()
        print(vdata)
        if vdata['meta']['status'] != 201:
            return responseError(vdata['meta']['msg'])
        else:
            db.session.commit()
            return responsePost('成功', {'re': 'success'})
        return responsePost('成功', {'re': 'success'})
    elif request.json.get('val') == 1:
        db.session.query(Dcal_user).filter(Dcal_user.eid == res.get('eid')).update(
            {"poweredit": str(res.get('poweredit')).strip('[').strip(']'), "powerlist":
             str(power).strip('[').strip(']'), "email": res.get('email'), "dept": res.get('dept')})
        db.session.query(Publicuser).filter(Publicuser.eid == res.get('eid')
                                            ).update({"email": res.get('email')})
        db.session.commit()
        return responsePost('成功', {'re': 'success'})
    elif request.json.get('val') == 2:
        db.session.query(Dcal_user).filter(Dcal_user.eid == res.get('eid')).delete()
        db.session.query(Publicuser).filter(Publicuser.eid == res.get('eid')).delete()
        db.session.commit()
        return responsePost('成功', {'re': 'success'})
    elif request.json.get('val') == 3:
        r = requests.post(config[env].api_url+"public/resetPassword", {'username': res.get('eid')})
        return responsePost('成功', {'re': 'success'})


@ api.route('/batuploadFile', methods=['POST'])
@ login_required
def batuploadFile():
    mylogger.debug("batuploadFile")
    file_obj = request.files.get('file')
    uploadType = request.form.get('type')
    mylogger.debug(file_obj)
    mylogger.debug(uploadType)
    if uploadType == 'skubatch':
        mylogger.debug(998)
        mylogger.debug(uploadType)
        mystr = ('tp' + str(datetime.now())).encode('UTF-8')
        name = hashlib.md5(mystr).hexdigest()[8:-8]
        # file_obj.save('RCCM.xlsx')
        if file_obj:
            appendix = file_obj.filename[file_obj.filename.rfind('.'):]
            file_obj.save(getServer()['uploadPath']+'temp/'+uploadType+'_'+name+appendix)
            # file_obj.save('temp/'+uploadType+'_'+name+appendix)
        url = getServer()['uploadPath']+'temp/'+uploadType+'_'+name+appendix
        mylogger.debug(url)
        wb = load_workbook(url, data_only=True)
        sheets = wb.sheetnames  # 获取全部sheet
        mySheet = wb['Sheet1']
        maxr = mySheet.max_row
        mylogger.debug(mySheet.cell(1, 1).value)
        calArr = []
        calversionArr = []
        for i in range(2, maxr+1):
            if mySheet.cell(row=i, column=1).value:
                col = 1
                arr = []
                while True:
                    col = col+1
                    if mySheet.cell(row=i, column=col).data_type == 'd':
                        arr.append(mySheet.cell(row=i, column=col).value.strftime("%Y-%m-%d"))
                    elif not mySheet.cell(row=i, column=col).value and (col <= 32 or (col > 32 and (col-33) % 5 != 0)):
                        arr.append('')
                    elif not mySheet.cell(row=i, column=col).value and (col > 32 and (col-33) % 5 == 0):
                        break
                    else:
                        arr.append(str(mySheet.cell(row=i, column=col).value))
                # mylogger.debug(arr)

                caldate = arr[21]
                duedate = arr[22]
                # timeArray = time.strptime(caldate, "%Y-%m-%d")
                # timeStamp = int(time.mktime(timeArray))
                # calperiod = int(arr[19])
                # duedate = timeStamp+calperiod*30*24*3600
                # duedate = time.localtime(duedate)
                # duedate = time.strftime("%Y-%m-%d", duedate)
                if arr[0] == 'MX':
                    calperiod = 0
                    times = time.localtime()
                    timeStampnow = int(time.mktime(times))
                    caldate = time.localtime(timeStampnow)
                    caldate = time.strftime("%Y-%m-%d", caldate)
                    duedate = caldate
                else:
                    if duedate != '':
                        pass
                    elif caldate != '':
                        timeArray = time.strptime(caldate, "%Y-%m-%d")
                        timeStamp = int(time.mktime(timeArray))
                        calperiod = int(arr[19])
                        duedate = timeStamp+calperiod*30*24*3600
                        duedate = time.localtime(duedate)
                        duedate = time.strftime("%Y-%m-%d", duedate)
                    else:
                        times = time.localtime()
                        timeStampnow = int(time.mktime(times))
                        calperiod = int(arr[19])
                        duedate = timeStampnow+calperiod*30*24*3600
                        duedate = time.localtime(duedate)
                        duedate = time.strftime("%Y-%m-%d", duedate)
                calArr = Dcal(instrumentno=arr[7], instrumentname=arr[8], factorysign=arr[9], type=arr[10], factoryno=arr[11], measureparameter=arr[12], offset=arr[13], showradio=arr[14], grade=arr[15], locno=arr[16], location=arr[17],
                              checkmode=arr[18], calperiod=arr[19],  refdoc=arr[23], calunit=arr[29], addreason=arr[30],  precal=arr[2], caltype=arr[3], keeper=arr[4], deptment=arr[5], comment=arr[6])
                db.session.add(calArr)
                db.session.flush()
                calid = calArr.id
                calversionArr = Dcal_cal_version(
                    calid=calid, version=0, checkmode=arr[18], calperiod=arr[19], caldata=caldate, duedate=duedate, measurestatus=arr[0], calcost=arr[1], checkstatus=1)
                db.session.add(calversionArr)
                for j in arr[24:29]:
                    if j != '':
                        dcalinsArr = Dcal_standcaleq(insno=j.strip(), calid=calid)
                        db.session.add(dcalinsArr)
                    else:
                        break
                stepArr = arr[31:]
                mylogger.debug(stepArr)
                for j in range(0, len(stepArr), 5):
                    if stepArr[j] == '':
                        break
                    mylogger.debug(stepArr[j])
                    dcalstepArr = Dcal_instep(
                        checkname=stepArr[j], standard=stepArr[j+1], spec=stepArr[j+2], upline=stepArr[j+3], downline=stepArr[j+4], calid=calid, version=0)
                    db.session.add(dcalstepArr)

        db.session.commit()
    return responsePost("更新成功", {'upload_url': 'sss'})
    # if uploadType == 'mgroupfile':


@api.route('/postabn', methods=['POST'])
@ login_required
def postabn():
    mylogger.debug('*'*10+'postabn'+'*'*10)
    res = request.json
    mylogger.debug(res)
    mylogger.debug(res.get('val'))
    valtag = res.get('val')
    # url = 'http://localhost:3000/#/'
    url = config[env].api_url+'#/cal'
    url = NAVURL
    if valtag == 1:
        db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox1': res.get(
            'ruleForm').get('checkbox1'), 'checkbox2': res.get('ruleForm').get('checkbox2'), 'abstatus': 2, 'deptA': res.get('ruleForm').get('Adept'), 'deptB': res.get('ruleForm').get('Bdept'), 'deptC': res.get('ruleForm').get('Cdept'), 'deptD': res.get('ruleForm').get('Ddept')})
        db.session.commit()
        emailarr = []
        # if res.get('ruleForm').get('checkbox2') == 2:
        #     mylogger.debug(6666)
        #     email = res.get('ruleForm').get('Adept')+'@pentair.com'
        #     mylogger.debug(email)
        #     emailarr.append(email)
        #     sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')

        # else:
        email = res.get('ruleForm').get('Bdept')+'@pentair.com'
        emailarr.append(email)
        sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
    if valtag == 2:
        mylogger.debug(res.get('ruleForm').get('checkbox2'))
        emailarr = []
        # if res.get('ruleForm').get('checkbox2') == 2:
        #     db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
        #         'ruleForm').get('checkbox4'), 'abstatus': 3, 'austatus': 1, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
        #     db.session.commit()
        #     email = res.get('ruleForm').get('opensingnl')+'@pentair.com'
        #     emailarr.append(email)
        #     sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
        if res.get('ruleForm').get('checkbox2') == 2:
            if res.get('ruleForm').get('austatus') == None:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
                    'ruleForm').get('checkbox4'), 'abstatus': 2, 'austatus': 1, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
                db.session.commit()
                email = res.get('ruleForm').get('Cdept')+'@pentair.com'
                emailarr.append(email)
                sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
            elif res.get('ruleForm').get('austatus') == 1:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
                    'ruleForm').get('checkbox4'), 'abstatus': 2, 'austatus': 2, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
                db.session.commit()
                email = res.get('ruleForm').get('Adept')+'@pentair.com'
                emailarr.append(email)
                sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
            elif res.get('ruleForm').get('austatus') == 2:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
                    'ruleForm').get('checkbox4'), 'abstatus': 3, 'austatus': 3, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
                db.session.commit()
                email = res.get('ruleForm').get('opensingnl')+'@pentair.com'
                emailarr.append(email)
                sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')

        elif res.get('ruleForm').get('checkbox2') == 3:
            if res.get('ruleForm').get('austatus') == None:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
                    'ruleForm').get('checkbox4'), 'abstatus': 2, 'austatus': 1, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
                db.session.commit()
                email = res.get('ruleForm').get('Cdept')+'@pentair.com'
                emailarr.append(email)
                sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
            elif res.get('ruleForm').get('austatus') == 1:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
                    'ruleForm').get('checkbox4'), 'abstatus': 3, 'austatus': 2, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
                db.session.commit()
                email = res.get('ruleForm').get('opensingnl')+'@pentair.com'
                emailarr.append(email)
                sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
        elif res.get('ruleForm').get('checkbox2') == 1:
            if res.get('ruleForm').get('austatus') == None:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
                    'ruleForm').get('checkbox4'), 'abstatus': 2, 'austatus': 1, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
                db.session.commit()
                email = res.get('ruleForm').get('Cdept')+'@pentair.com'
                emailarr.append(email)
                sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
            elif res.get('ruleForm').get('austatus') == 1:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
                    'ruleForm').get('checkbox4'), 'abstatus': 2, 'austatus': 2, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
                db.session.commit()
                email = res.get('ruleForm').get('Adept')+'@pentair.com'
                emailarr.append(email)
                sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
            elif res.get('ruleForm').get('austatus') == 2:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
                    'ruleForm').get('checkbox4'), 'abstatus': 3, 'austatus': 3, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
                db.session.commit()
                email = res.get('ruleForm').get('opensingnl')+'@pentair.com'
                emailarr.append(email)
                sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
        elif res.get('ruleForm').get('checkbox2') == 4 or res.get('ruleForm').get('checkbox2') == 5 or res.get('ruleForm').get('checkbox2') == 6:
            if res.get('ruleForm').get('austatus') == None:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
                    'ruleForm').get('checkbox4'), 'abstatus': 2, 'austatus': 1, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
                db.session.commit()
                email = res.get('ruleForm').get('Cdept')+'@pentair.com'
                emailarr.append(email)
                sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
            elif res.get('ruleForm').get('austatus') == 1:
                db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'checkbox3': res.get('ruleForm').get('checkbox3'), 'checkbox4': res.get(
                    'ruleForm').get('checkbox4'), 'abstatus': 3, 'austatus': 2, 'recallev': res.get('ruleForm').get('recallev'), 'rootcase': res.get('ruleForm').get('rootcase')})
                db.session.commit()
                email = res.get('ruleForm').get('opensingnl')+'@pentair.com'
                emailarr.append(email)
                sendMailTread(emailarr,  '量具管理异常流程', f'请登录<a href="{url}">量具管理</a>系统进行操作')
    if valtag == 3:
        curtime = time.strftime('%Y-%m-%d', time.localtime())
        db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'verpersion': res.get('ruleForm').get(
            'applicant'), 'verdate': curtime, 'abstatus': 4,  'processre': res.get('ruleForm').get('processre'), 'repaircost': res.get('ruleForm').get('repaircost'), 'newbuycost': res.get('ruleForm').get('newbuycost'), 'classify': res.get('ruleForm').get('classify')})
        db.session.commit()
        if res.get('ruleForm').get('classify') == '非正常损坏':
            emailarr = []
            db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(
                Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'carstatus': 1})
            db.session.commit()
            email = res.get('ruleForm').get('Adept')+'@pentair.com'
            emailarr.append(email)
            sendMailTread(emailarr,  'CAR改善流程', f'请登录<a href="{url}">量具管理</a>系统 CAR流程 进行操作')
    if valtag == 4:
        if res.get('ruleForm').get('austatus') != None and res.get('ruleForm').get('austatus') > 1:
            db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update(
                {'abstatus': 2, 'austatus': res.get('ruleForm').get('austatus')-1})
            db.session.commit()
        elif res.get('ruleForm').get('austatus') != None and res.get('ruleForm').get('austatus') == 1:
            db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(
                Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'abstatus': 2, 'austatus': None})
            db.session.commit()
        else:
            db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get(
                'id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'abstatus': 1})
            db.session.commit()
    if valtag == 5:
        db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(Dcal_cal_version.version == res.get('ruleForm').get('version')).update(
            {'checkstatus': 1, 'checkresultall': None, 'abstatus': None, 'appearancefailpic': None})
        db.session.commit()
    return responsePost("更新成功", {'upload_url': 'sss'})


@ api.route('/getDeptlist', methods=['GET'])
@ login_required
def getDeptlist():
    mylogger.info('getDeptlist start=====')
    res = request.args
    users = db.session.query(Dcal_user).all()
    deptdic = {'A': [], 'B': [], 'C': [], 'D': []}
    for o in users:
        if o.dept:
            dic = {
                'label': o.email.split('@')[0],
                'value': o.email.split('@')[0]
            }
            deptdic.get(o.dept).append(dic)

    return responseGet('haha', {'users': deptdic})


@ api.route('/getCARFlwo', methods=['GET'])
@ login_required
def getCARFlwo():
    mylogger.debug('*'*10+'getCARFlwo'+'*'*10)
    res = request.args
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    mylogger.debug(res)
    option = res.get('oploption')
    calvalue = res.get('vocvalue')
    calstatus = res.get('calstatus')
    filter1 = res.get('filter1')
    vaildvalue1 = res.get('vaildvalue1')
    vaildvalue2 = res.get('vaildvalue2')

    cal = db.session.query(Dcal.id.label('id'), Dcal.deptment.label('deptment'), Dcal.keeper.label('keeper'), Dcal.instrumentno.label('instrumentno'), Dcal.instrumentname.label('instrumentname'), Dcal_cal_version.processre.label('processre'), Dcal_cal_version.verdate.label('verdate'), Dcal_cal_version.checkbox2.label('checkbox2'), Dcal_cal_version.damagedes.label('damagedes'), Dcal_cal_version.deptA.label('deptA'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.version.label('version'), Dcal_cal_version.carstatus.label(
        'carstatus'), Dcal_cal_version.carrootcase.label('carrootcase'), Dcal_cal_version.carcorrective.label('carcorrective'), Dcal_cal_version.carstandardization.label('carstandardization'), Dcal_cal_version.carimprovetag.label('carimprovetag'), Dcal_cal_version.carevidence.label('carevidence'), Dcal_cal_version.caraccepter.label('caraccepter'), Dcal_cal_version.carclosedate.label('carclosedate'), Dcal_cal_version.appearancefailpic.label('appearancefailpic'), Dcal_cal_version.appearance.label('appearance')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.classify == '非正常损坏')
    total = db.session.query(Dcal.id.label('id'), Dcal.deptment.label('deptment'), Dcal.keeper.label('keeper'), Dcal.instrumentno.label('instrumentno'), Dcal.instrumentname.label('instrumentname'), Dcal_cal_version.processre.label('processre'), Dcal_cal_version.verdate.label('verdate'), Dcal_cal_version.checkbox2.label('checkbox2'), Dcal_cal_version.damagedes.label('damagedes'), Dcal_cal_version.deptA.label('deptA'), Dcal_cal_version.opensingnl.label('opensingnl'), Dcal_cal_version.version.label('version'), Dcal_cal_version.carstatus.label(
        'carstatus'), Dcal_cal_version.carrootcase.label('carrootcase'), Dcal_cal_version.carcorrective.label('carcorrective'), Dcal_cal_version.carstandardization.label('carstandardization'), Dcal_cal_version.carimprovetag.label('carimprovetag'), Dcal_cal_version.carevidence.label('carevidence'), Dcal_cal_version.caraccepter.label('caraccepter'), Dcal_cal_version.carclosedate.label('carclosedate'), Dcal_cal_version.appearancefailpic.label('appearancefailpic'), Dcal_cal_version.appearance.label('appearance')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid).filter(Dcal_cal_version.classify == '非正常损坏')
    if calvalue != '':
        if option == '仪器编号':
            tag = Dcal.instrumentno
        elif option == '仪器名称':
            tag = Dcal.instrumentname
        elif option == 'id':
            tag = Dcal.id
        cal = cal.filter(tag.like('%{0}%'.format(calvalue)))
        total = total.filter(tag.like('%{0}%'.format(calvalue)))
    # else:
    #     cal = db.session.query(Dcal)
    #     total = db.session.query(Dcal)
    if calstatus != '':
        if calstatus == '待责任部门确认':
            cal = cal.filter(Dcal_cal_version.carstatus == 1)
            total = total.filter(Dcal_cal_version.carstatus == 1)
        elif calstatus == '待验收人确认':
            cal = cal.filter(Dcal_cal_version.carstatus == 2)
            total = total.filter(Dcal_cal_version.carstatus == 2)
        else:
            cal = cal.filter(Dcal_cal_version.carstatus == 3)
            total = total.filter(Dcal_cal_version.carstatus == 3)
    if filter1 == 'true':
        cal = cal.filter(or_(and_(Dcal_cal_version.carstatus == 1, Dcal_cal_version.deptA == res.get('owner')), and_(
            Dcal_cal_version.carstatus == 2, Dcal_cal_version.opensingnl == res.get('owner'))))
        total = total.filter(or_(and_(Dcal_cal_version.carstatus == 1, Dcal_cal_version.deptA == res.get('owner')), and_(
            Dcal_cal_version.carstatus == 2, Dcal_cal_version.opensingnl == res.get('owner'))))

    if vaildvalue1 != '':
        cal = cal.filter(Dcal_cal_version.verdate.between(vaildvalue1, vaildvalue2))
        total = total.filter(Dcal_cal_version.verdate.between(vaildvalue1, vaildvalue2))
    cal = cal.order_by(Dcal_cal_version.duedate).paginate(pagenum, pagesize, error_out=False).items
    total = total.order_by(Dcal_cal_version.duedate).count()
    calList = []
    idList = []
    for i in cal:
        caldict = {}
        caldict['id'] = i.id
        caldict['instrumentno'] = i.instrumentno
        caldict['instrumentname'] = i.instrumentname
        if i.verdate:
            caldict['verdate'] = i.verdate.strftime("%Y-%m-%d")
        else:
            caldict['verdate'] = ''
        caldict['deptment'] = i.deptment
        caldict['keeper'] = i.keeper
        caldict['processre'] = i.processre

        caldict['checkbox2'] = i.checkbox2
        caldict['damagedes'] = i.damagedes
        caldict['deptA'] = i.deptA
        caldict['opensingnl'] = i.opensingnl
        caldict['carstatus'] = i.carstatus

        caldict['carrootcase'] = i.carrootcase
        caldict['carcorrective'] = i.carcorrective
        caldict['carstandardization'] = i.carstandardization
        caldict['carimprovetag'] = i.carimprovetag
        caldict['carevidence'] = i.carevidence
        caldict['caraccepter'] = i.caraccepter

        caldict['appearance'] = i.appearance
        # caldict['appearancefailpic'] = i.appearancefailpic

        caldict['appearancefailpic'] = []
        if i.appearancefailpic:
            for j in i.appearancefailpic.split(','):
                if j != '':
                    caldict['appearancefailpic'].append(
                        config[env].base_url+'cal/workfolder/uploads/appearfail/' + j.split('/')[-1].split('\\')[-1])
        # caldict['carclosedate'] = i.carclosedate
        if i.carclosedate:
            caldict['carclosedate'] = i.carclosedate.strftime("%Y-%m-%d")
        else:
            caldict['carclosedate'] = ''

        if i.version != None:
            caldict['version'] = int(i.version)
        else:
            caldict['version'] = -1
        calList.append(caldict)

    return responseGet("maxvocId", {'calList': calList, 'total': total})


@api.route('/postcar', methods=['POST'])
def postcar():
    mylogger.info('postcar start=====')
    res = request.json
    mylogger.debug(res)
    if res.get('val') == 1:
        db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(
            Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'carstatus': 2, 'carcorrective': res.get('ruleForm').get('carcorrective'), 'carimprovetag': res.get('ruleForm').get('carimprovetag'), 'carrootcase': res.get('ruleForm').get('carrootcase'), 'carstandardization': res.get('ruleForm').get('carstandardization')})
        db.session.commit()
        url = config[env].api_url+'#/cal'
        url = NAVURL
        emailarr = []
        email = res.get('ruleForm').get('opensingnl')+'@pentair.com'
        emailarr.append(email)
        sendMailTread(emailarr,  'CAR改善流程', f'请登录<a href="{url}">量具管理</a>系统中的 CAR流程 进行操作')
    elif res.get('val') == 2:
        times = time.localtime()
        carclosedate = time.strftime("%Y-%m-%d", times)
        db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(
            Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'carstatus': 3, 'carevidence': res.get('ruleForm').get('carevidence'), 'caraccepter': res.get('ruleForm').get('opensingnl'), 'carclosedate': carclosedate})
        db.session.commit()

    return responsePost('成功', {'re': 'success'})


@api.route('/postdelcal', methods=['POST'])
def postdelcal():
    mylogger.info('postdelcal start=====')
    res = request.json
    res = res.get('ruleForm')
    id = res.get('id')
    mylogger.debug(res)
    db.session.query(Dcal_instep).filter(Dcal_instep.calid == id).delete()
    db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == id).delete()
    db.session.query(Dcal_standcaleq).filter(Dcal_standcaleq.calid == id).delete()
    db.session.query(Dcal).filter(Dcal.id == id).delete()
    db.session.commit()
    # if res.get('val') == 1:
    #     db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(
    #         Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'carstatus': 2, 'carcorrective': res.get('ruleForm').get('carcorrective'), 'carimprovetag': res.get('ruleForm').get('carimprovetag'), 'carrootcase': res.get('ruleForm').get('carrootcase'), 'carstandardization': res.get('ruleForm').get('carstandardization')})
    #     db.session.commit()
    #     url = config[env].api_url+'#/cal'
    #     url = NAVURL
    #     emailarr = []
    #     email = res.get('ruleForm').get('opensingnl')+'@pentair.com'
    #     emailarr.append(email)
    #     sendMailTread(emailarr,  'CAR改善流程', f'请登录<a href="{url}">量具管理</a>系统中的 CAR流程 进行操作')
    # elif res.get('val') == 2:
    #     times = time.localtime()
    #     carclosedate = time.strftime("%Y-%m-%d", times)
    #     db.session.query(Dcal_cal_version).filter(Dcal_cal_version.calid == res.get('ruleForm').get('id')).filter(
    #         Dcal_cal_version.version == res.get('ruleForm').get('version')).update({'carstatus': 3, 'carevidence': res.get('ruleForm').get('carevidence'), 'caraccepter': res.get('ruleForm').get('opensingnl'), 'carclosedate': carclosedate})
    #     db.session.commit()

    return responsePost('成功', {'re': 'success'})

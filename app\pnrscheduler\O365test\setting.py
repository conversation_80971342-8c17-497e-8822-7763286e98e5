import logging


class Config:  # 公共的配置参数
    gm1 = 'micha<PERSON>.z<PERSON>@pentair.com'
    gm2 = '<EMAIL>'
    suggestiondb = {
        'host': "***************",
        'port': 23306,
        'user': "pentairlean",
        'password': "<PERSON>.jiayun_0217",
        'database': "suggestion",
        'charset': "utf8"
    }
    o365 = {
        '<EMAIL>': {
            # the default protocol will be Microsoft Graph
            "client_id": "43fe3ed7-cee7-4aeb-9458-eb26ce5aef4a",
            "client_secret": "****************************************",
            "tenant_id": "8237194f-de9d-4e96-b705-d713a214c4ea"
        },
        '<EMAIL>': {
            # the default protocol will be Microsoft Graph
            "client_id": "09d12452-d2f2-4821-9363-ca27f81509e9",
            "client_secret": "****************************************",
            "tenant_id": "c3781931-c278-48ba-ba53-56a2fd048b03"
        }
    }


class LocalConfig(Config):  # 开发环境的配置参数
    loglevel = logging.DEBUG
    redis = {
        'host': "127.0.0.1",
        'port': 6379,
        'db': 10,
        'password': "pims"
    }
    emdidb = {
        'host': "127.0.0.1",
        'port': 3306,
        'user': "root",
        'password': "Zhao.jiayun_0217",
        'charset': "utf8"
    }
    flaskurl = "http://127.0.0.1:5000/"
    flaskPath = '/Users/<USER>/Documents/Develop/Backend/flask307/'
    backupFolder = '/Users/<USER>/Documents/Develop/Backend/dbbackups/'
    vocdb = {
        'host': "127.0.0.1",
        'user': "sa",
        'password': "Start12345",
        'database': "voc"
    }
    rccmdb = {
        'host': "127.0.0.1",
        'user': "sa",
        'password': "Start12345",
        'database': "EMDI_RCCM"
    }


# cfg = ProductionConfig
# cfg = DevelopmentConfig
cfg = LocalConfig

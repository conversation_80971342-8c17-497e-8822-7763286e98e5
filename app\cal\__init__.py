def importCALRoute(app):
    from app.cal.route.loginRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/cal/loginAPI")

    from app.cal.route.calRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/cal/calAPI")

    from app.cal.route.settingRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/cal/settingAPI")

    from app.cal.route.chartsRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/cal/chartsAPI")


# def importTPRoute(app):
#     from app.tp.route.loginRoute import api as api_blueprint
#     app.register_blueprint(api_blueprint, url_prefix="/tp/loginAPI")

#     from app.tp.route.tpRoute import api as api_blueprint
#     app.register_blueprint(api_blueprint, url_prefix="/tp/tpAPI")

#     from app.tp.route.settingRoute import api as api_blueprint
#     app.register_blueprint(api_blueprint, url_prefix="/tp/settingAPI")

#     from app.tp.route.chartsRoute import api as api_blueprint
#     app.register_blueprint(api_blueprint, url_prefix="/tp/chartsAPI")


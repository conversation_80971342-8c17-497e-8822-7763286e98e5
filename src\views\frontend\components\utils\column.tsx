import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";
import { ref, onMounted, reactive } from "vue";
import { watch, toRaw } from "vue";
import { gethourlydata } from "@/api/dashboard";
import { confirmhourdata } from "@/api/front";
import { changehourdata } from "@/views/components/edithourdata/index";
import { useProdStoreHook } from "@/store/modules/prod";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";

export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);

  watch(
    [
      () => useProdStoreHook().selectedDate,
      () => useProdStoreHook().shift,
      () => useProdStoreHook().machineId
    ],
    (newvalue: any, _) => {
      query_param.selecteddate = newvalue[0];
      query_param.shift = newvalue[1];
      query_param.machine = newvalue[2];
      refreshData();
    }
  );

  const query_param = reactive({
    selecteddate: useProdStoreHook().selectedDate,
    machine: useProdStoreHook().machineId,
    shift: useProdStoreHook().shift
  });

  const refreshData = () => {
    loading.value = true;
    gethourlydata(toRaw(query_param)).then((res: { data: any }) => {
      dataList.value = res.data;
      loading.value = false;
    });
  };

  function onConfirm(row) {
    ElMessageBox.confirm(`确认该小时产量记录吗?`, "系统提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    })
      .then(() => {
        confirmhourdata({
          selecteddate: query_param.selecteddate,
          machine: query_param.machine,
          hourid: row.hourid
        }).then((res: any) => {
          if (res.meta.status == 201) {
            refreshData();
            message("确认小时数据成功！", {
              customClass: "el",
              type: "success"
            });
          }
        });
      })
      .catch(() => {
        console.log("取消操作");
      });
  }

  onMounted(() => {
    refreshData();
  });

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载小时记录表数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  const adaptiveConfig: AdaptiveConfig = {
    offsetBottom: 15,
    fixHeader: true
  };
  const columns: TableColumnList = [
    {
      label: "小时号",
      prop: "hourid",
      minWidth: 52,
      fixed: true,
      formatter(row) {
        return row.hourid + "~" + (row.hourid + 1);
      }
    },
    { label: "料号", prop: "pn", minWidth: 100, fixed: true },
    { label: "产出", prop: "output", minWidth: 60 },
    {
      label: "调整",
      prop: "adjustion",
      minWidth: 60,
      cellRenderer({ row }) {
        if (row.adjustion == 0) {
          return (
            <el-tag
              type="info"
              onClick={() => {
                changehourdata("edit", query_param.machine, row, refreshData);
              }}
            >
              +
            </el-tag>
          );
        } else {
          return (
            <el-tag
              size="large"
              type="warning"
              style="width:100%;"
              onClick={() => {
                changehourdata("edit", query_param.machine, row, refreshData);
              }}
            >
              {row.adjustion}
            </el-tag>
          );
        }
      }
    },
    {
      label: "实际",
      prop: "",
      minWidth: 60,
      cellRenderer({ row }) {
        if (row.output == 0 && row.adjustion == 0) {
          return <div></div>;
        } else if (row.output + row.adjustion >= row.computed_output) {
          return (
            <el-tag size="large" type="success" style="width:100%">
              {row.output + row.adjustion}
            </el-tag>
          );
        } else if (row.output + row.adjustion < row.computed_output) {
          return (
            <el-tag size="large" type="danger" style="width:100%">
              {row.output + row.adjustion}
            </el-tag>
          );
        }
      }
    },
    {
      label: "标准",
      prop: "computed_output",
      minWidth: 60,
      formatter(row) {
        return row.computed_output.toFixed(0);
      }
    },
    {
      label: "实际累积",
      prop: "acumulated_output",
      minWidth: 100,
      cellRenderer({ row }) {
        if (row.acumulated_output == 0) {
          return <div></div>;
        } else if (row.acumulated_output >= row.acumulated_computedvalue) {
          return (
            <el-tag size="large" type="success" style="width:60%">
              {row.acumulated_output}
            </el-tag>
          );
        } else if (row.acumulated_output < row.acumulated_computedvalue) {
          return (
            <el-tag size="large" type="danger" style="width:60%">
              {row.acumulated_output}
            </el-tag>
          );
        }
      }
    },
    {
      label: "标准累积",
      prop: "acumulated_computedvalue",
      minWidth: 100,
      formatter(row) {
        return row.acumulated_computedvalue.toFixed(0);
      }
    },
    {
      label: "不良",
      prop: "defect_count",
      minWidth: 60,
      cellRenderer: ({ row }) => {
        if (row.defect_count > 0) {
          return (
            <el-tag type="danger" size="large" style="width:100%">
              {row.defect_count}
            </el-tag>
          );
        } else {
          return <el-tag type="info">+</el-tag>;
        }
      }
    },
    {
      label: "操作",
      prop: "",
      fixed: "right",
      minWidth: 60,
      cellRenderer: ({ row }) => {
        return (
          <div class="ops">
            <el-button
              v-show={row.confirm_state != 1}
              type={row.confirm_state == 1 ? "default" : "warning"}
              onClick={() => onConfirm(row)}
            >
              <el-icon size="20">
                <CircleCheck />
              </el-icon>
            </el-button>
          </div>
        );
      }
    }
  ];

  return {
    dataList,
    columns,
    loadingConfig,
    adaptiveConfig,
    loading,
    refreshData
  };
}

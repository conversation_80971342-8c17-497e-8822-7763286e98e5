interface StateProps {
  stateData: {
    machine_id: number;
    shift_date: string;
    shift: string;
    state_id: string;
    start_time: string;
    end_time: string;
    row_id: number;
    action: string;
  };
}

interface StateItem {
  is_active: number;
  is_output: number;
  is_scheduled_stop: number | null;
  state_name: string;
  state_type: number;
}

export type { StateProps, StateItem };

from extensions import rs, db
import json
from app.im.model.models_im import shift_type


def refreshState():
    state_arry = []
    for row in db.session.query(shift_type).all():
        data = {
            "id": row.id,
            "state_name": row.state_name,
            "state_type": row.state_type,
            "is_scheduled_stop": row.is_scheduled_stop,
            "is_active": row.is_active,
            "is_output": row.is_output
        }
        # rs.hset('STATEDES', "ID" + str(row.id),
        #         json.dumps(data, ensure_ascii=False))
        state_arry.append(data)
    return state_arry

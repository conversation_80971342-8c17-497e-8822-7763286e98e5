from script_config import my_config
import redis
import socket
import threading
import time
import binascii
import re
import json

DTU_IP, DTU_PORT = my_config['DTU_URL'].split("//")[1].split(":")

try:
    pool = redis.ConnectionPool.from_url(my_config['REDIS_URL'])
    r = redis.Redis(connection_pool=pool)
except Exception as message:
    print('连接服务器报错%s' % message)
else:
    print('redis服务连接成功')


def is_hex_string_and_in_range(hex_string):
    if len(hex_string) != 2:
        return False
    for char in hex_string:
        if char.isdigit() or char.upper() in 'ABCDEF':
            continue
        else:
            return False
    decimal_value = int(hex_string, 16)
    if 0 < decimal_value < 26:
        return True
    return False


class SocketClient:

    def __init__(self, ip, port):
        self.IP = ip
        self.PORT = port

    """ socket 建立链接 """

    def connect(self):
        while True:
            server2 = (self.IP, self.PORT)
            global socket_client2
            socket_client2 = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            try:
                socket_client2.connect(server2)
            except Exception as message:
                print('连接DTU报错,%s' % message)
                time.sleep(1)
                continue
            else:
                print('连接DTU成功')
                threading.Thread(target=self._recv).start()
                break

    """ socket发送数据 """

    def _send(self, msg):
        try:
            hex_data = bytes.fromhex(msg)
            socket_client2.send(hex_data)
        except Exception as message:
            print('连接服务器报错%s' % message)
            self.connect()

    """ socket接收数据 """

    def _recv(self):
        try:
            while (True):
                res = binascii.b2a_hex(
                    socket_client2.recv(1024)).decode('utf-8')
                if not res:
                    break
                if len(res) == 4 and res[-2:].upper() == 'DD':
                    if is_hex_string_and_in_range(res[:2]):
                        state_des = r.hget('STATE', f'IM{int(res[:2],16)}')
                        state_des = str(int(state_des))
                        state_type = r.hget('STATEDES', f'ID{state_des}')
                        state_type = json.loads(state_type)['state_type']
                        self._send(
                            f'00{res[:2]}{res[:2]}{str(state_type).zfill(2)}FF')
                        continue
                # 如果数据包的长度不是4或6，则将异常数据包记录
                if len(res) not in [4, 6]:
                    r.lpush(
                        'EXCEPTION', f"{{'data': '{res}', 'ts': '{int(round(time.time() * 1000))}'}}")
                # 此处用字符006401来分割接收的数据包，防止粘包数据无法正确解析
                res_arry = re.split('ff|ef|006401', res)
                res_arry = [x for x in res_arry if x != '']
                i = 0
                for item in res_arry:
                    # 将接收的数据包缓存入REDIS服务器,KEY:MESSAGEQUEUE,如果存在相同时间的数据包，时间戳加1，防止重复
                    r.lpush(
                        'MESSAGEQUEUE', f"{{'data': '{item}', 'ts': '{int(round(time.time() * 1000))+i}'}}")
                    i = i+1
        except Exception as message:
            print('收到服务器的消息报错%s' % message)
        self.connect()


if __name__ == '__main__':
    client = SocketClient(DTU_IP, int(DTU_PORT))
    client.connect()

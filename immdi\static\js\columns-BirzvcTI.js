import{h as a}from"./moment-C3TZ8gAF.js";import{a as d}from"./prod-CfeywgVC.js";import{n as o,a2 as f,b as n}from"./index-BnxEuBzx.js";function h(){const e=o([]),r=o(!0),s=[{label:"合模时间",prop:"start",formatter:t=>a(t.start).format("MM-DD HH:mm:ss")},{label:"开模时间",prop:"end",formatter:t=>a(t.end).format("MM-DD HH:mm:ss"),minWidth:"150px"},{label:"注塑时间",prop:"duration",cellRenderer:({row:t})=>t.flag==1?n("div",{style:"color: #f50"},[t.duration.toFixed(1)]):n("div",null,[t.duration.toFixed(1)])},{label:"取料时间",prop:"interval",formatter:t=>t.interval.toFixed(1)}],i=f({text:"正在加载小时记录表数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `});return{refreshData:t=>{d(t).then(l=>{e.value=l,r.value=!1})},loading:r,columns:s,dataList:e,loadingConfig:i,adaptiveConfig:{fixHeader:!0}}}export{h as useColumns};

import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";
import { formatToken } from "@/utils/auth";

export type UserResult = {
  meta: {
    msg: string;
    status: number;
  };
  data: {
    /** 用户名 */
    username: string;
    /** 当前登陆用户的角色 */
    roles: Array<string>;
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type RefreshTokenResult = {
  meta: {
    msg: string;
    status: number;
  };
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

/** 登录 */
export const getLogin = (data?: object) => {
  return http.request<UserResult>("post", baseUrlApi("auth/login"), { data });
};

/** 刷新token */
export const refreshTokenApi = (data?: string) => {
  return http.request<RefreshTokenResult>(
    "post",
    baseUrlApi("auth/refreshToken"),
    {
      headers: { Authorization: formatToken(data) }
    }
  );
};

/** 获取用户列表 */
export const getUserList = (data?: object) => {
  return http.request("get", baseUrlApi("auth/getuserlist"));
};

// /** 用户管理-获取所有角色列表 */
// export const getAllRoleList = () => {
//   return http.request("get", "/list-all-role");
// };

// /** 用户管理-根据userId，获取对应角色id列表（userId：用户id） */
// export const getRoleIds = (data?: object) => {
//   return http.request("post", "/list-role-ids", { data });
// };

// /** 获取角色管理列表 */
// export const getRoleList = (data?: object) => {
//   return http.request("post", "/role", { data });
// };

// /** 获取部门管理列表 */
// export const getDeptList = (data?: object) => {
//   return http.request("post", "/dept", { data });
// };

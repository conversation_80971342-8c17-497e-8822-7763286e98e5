import logging
import os
from utils.config import cfg


class MyLogger:
    def __init__(self, title='log', log_file='mailserver.log', max_lines=20000, clear_lines=10000):
        # 设置日志等级
        self.logger = logging.getLogger('MyLogger')
        self.logger.setLevel(cfg.loglevel)

        # 创建控制台处理器，输出到控制台
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)

        # 创建文件处理器，写入到文件
        logPath = os.path.join(os.path.dirname(os.path.abspath(__file__)), log_file)
        file_handler = logging.FileHandler(logPath)
        file_handler.setLevel(logging.CRITICAL)

        # 设置日志格式
        formatter = logging.Formatter(f'{title} - %(asctime)s - %(levelname)s - %(process)d: \n %(message)s')
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)

        # 添加处理器到logger
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)

        # 记录文件行数
        self.log_file = log_file
        self.max_lines = max_lines
        self.clear_lines = clear_lines

    def clear_old_logs(self):
        if os.path.exists(self.log_file):
            with open(self.log_file, 'r') as f:
                lines = f.readlines()

            # 如果行数超过最大行数，则清除最早的行
            if len(lines) > self.max_lines:
                with open(self.log_file, 'w') as f:
                    f.writelines(lines[self.clear_lines:])

    def debug(self, message):
        self.logger.debug(message)
        self.clear_old_logs()

    def info(self, message):
        self.logger.info(message)
        self.clear_old_logs()

    def warning(self, message):
        self.logger.warning(message)
        self.clear_old_logs()

    def error(self, message):
        self.logger.error(message)
        self.clear_old_logs()

    def critical(self, message):
        self.logger.critical(message)
        self.clear_old_logs()


logger = MyLogger()
# 使用示例
# if __name__ == "__main__":
#     logger = MyLogger()
#     logger.debug("This is a debug message.")
#     logger.info("This is an info message.")
#     logger.warning("This is a warning message.")
#     logger.error("This is an error message.")
#     logger.critical("This is a critical message.")

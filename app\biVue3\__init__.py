def importBI3Route(app):
    from app.biVue3.route.t2ehsRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi3/t2ehsAPI")

    from app.biVue3.route.t2qualityRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi3/t2qualityAPI")

    from app.biVue3.route.t2deliveryRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi3/t2deliveryAPI")

    from app.biVue3.route.t2costRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi3/t2costAPI")

    from app.biVue3.route.pubRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi3/pubAPI")

    from app.biVue3.route.t1Route import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi3/t1API")

    from app.biVue3.route.t3productionRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi3/t3productionAPI")

    from app.biVue3.route.t3accountRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi3/t3accountAPI")

    from app.biVue3.route.t2mbRoute import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix="/bi3/t2mbAPI")

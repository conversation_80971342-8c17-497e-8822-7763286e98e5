import os
import shutil
from utils.mylog import logger


def backupStatic():
    '''
    Backup the contents of a folder to a destination folder
    '''
    source_folder = '/Users/<USER>/Documents/Develop/Backend/flask310/app/pnrscheduler/mailserver/mails'
    destination_folder = '/Users/<USER>/Documents/Develop/Backend/flask310/app/pnrscheduler/mailserver/mailsto'
    # source_folder = '/welean/publicfolder/oldserver'
    # destination_folder = '/welean/apiserver/flask307/static'

    # Create the destination folder if it doesn't exist
    if not os.path.exists(destination_folder):
        os.makedirs(destination_folder)

    # Iterate through each file in the source folder and its subdirectories
    for root, dirs, files in os.walk(source_folder):
        # Create the corresponding subdirectories in the destination folder
        relative_path = os.path.relpath(root, source_folder)
        dest_dir = os.path.join(destination_folder, relative_path)

        if not os.path.exists(dest_dir):
            os.makedirs(dest_dir)
            logger.info("Created directory: %s" % dest_dir)

        # Copy each file to the destination folder if it's newer than the existing file
        for file_name in files:
            source_file = os.path.join(root, file_name)
            dest_file = os.path.join(dest_dir, file_name)

            try:
                # Check if the destination file needs to be copied
                if not os.path.exists(dest_file) or \
                        int(os.stat(source_file).st_mtime) > int(os.stat(dest_file).st_mtime):  # 允许1秒的差异
                    shutil.copy2(source_file, dest_file)
                    logger.info("Copied file: %s to %s" % (source_file, dest_file))

            except OSError as e:
                logger.error("Error copying file %s to %s: %s" % (source_file, dest_file, e))

# Remember to configure your logger if you want to see the output


backupStatic()

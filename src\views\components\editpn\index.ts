import { addDialog } from "@/components/ReDialog";
import editPnForm from "./editPnForm.vue";
import { changepn } from "@/api/dashboard";
import { message } from "@/utils/message";

type CallbackFunction = () => void;
export function changePn(
  machine_id: any,
  pn: any,
  fun_callback?: CallbackFunction
) {
  addDialog({
    title: machine_id + "# 注塑机生产料号编辑",
    props: {
      PNData: {
        machine_id: machine_id,
        pn: pn
      }
    },
    width: "50%",
    draggable: true,
    fullscreenIcon: false,
    closeOnClickModal: false,
    contentRenderer: () => editPnForm,
    beforeSure: async (done, { options }) => {
      if (options.props.PNData.pn != pn) {
        const res = (await changepn({
          machine_id: options.props.PNData.machine_id,
          pn: options.props.PNData.pn
        })) as { meta: any };
        if (res.meta.status != 201) {
          message("变更状态失败", { customClass: "el", type: "error" });
        } else {
          done();
          message("变更状态成功", { customClass: "el", type: "success" });
          fun_callback();
        }
      } else {
        done();
      }
    }
  });
}

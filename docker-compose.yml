version: "1.0"

services:
  api:
    container_name: apiserver
    restart: always
    image: im_api:1.0
    privileged: true
    networks:
      front-tier:
        ipv4_address: **********
    volumes:
      - .:/apiserver
      - ./flask/server_config/nginx_flask.conf:/etc/nginx/sites-available/nginx.conf
      - ./flask/server_config/supervisord.conf:/etc/supervisor/supervisord.conf
    ports:
      - "80:80"
      - "4840-4870:4840-4870"
      - "5000:5000"
      - "8080-8081:8080-8081"
      - "8886:8886"
    command: "/usr/bin/supervisord -c /etc/supervisor/supervisord.conf -n"

networks:
  front-tier:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/24

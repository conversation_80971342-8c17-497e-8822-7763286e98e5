# 注塑车间的消费者程序
import threading
import redis
import json
import ast
from db_model import Connection
from db_model import hourly_output, routing, uptime_log
from datetime import datetime, timedelta
from script_config import my_config


def refreshUptime(rds, machine, ts):
    with Connection() as connection:
        session = connection.get_session()
        # 将设备通电记录刷新
        rds.hset('LASTBEAT', f'IM{machine}', ts)
        current_time = datetime.fromtimestamp(int(ts)/1000)
        # 取相应机台最后一条运行记录
        latest = session.query(uptime_log).filter_by(
            machine=machine).order_by(uptime_log.start_time.desc()).first()
        # 如果没有最后一条记录，则进行初始化
        if latest is None:
            item = uptime_log(
                machine=machine, start_time=current_time)
            session.add(item)
            session.commit()
        else:
            # 如果有最后一条记录，并且记录的结束时间为空，说明该记录是第一次修改
            if latest.end_time is None:
                duration = (current_time - latest.start_time).total_seconds()
                # 如果接收的时间距记录开始时间超过310秒，则结束该条记录，以接收的时间戳新建一条记录
                if duration > 310:
                    latest.end_time = latest.start_time + timedelta(seconds=300)
                    item = uptime_log(machine=machine, start_time=current_time)
                    session.add(item)
                    session.commit()
                else:
                    # 如果未超过310秒，将结束时间刷新为接收时间
                    latest.end_time = current_time
                    session.commit()
            else:
                # 如果有最后一条记录，并且存在记录的结束时间，说明该记录已经过第一次修改
                duration = (current_time-latest.end_time).total_seconds()
                # 如果接收的时间距记录结束时间超过310秒，则以接收的时间戳新建一条记录
                if duration > 310:
                    item = uptime_log(machine=machine, start_time=current_time)
                    session.add(item)
                    session.commit()
                # 如果接收的时间距记录结束时间不超过130秒，则刷新接收时间
                else:
                    latest.end_time = current_time
                    session.commit()


# 节拍处理

def handleCycle(rds, data, ts):
    # 前两位为设备编号,第3、4位是动作类型
    machine = int(data[0:2], 16)
    action = int(data[2:4], 16)
    ts = int(ts)

    # 取当前是否记录产量
    state = rds.hget('STATE', f'IM{machine}')
    state_des = json.loads(rds.hget('STATEDES', f'ID{state}'))

    # 取出当前生产料号
    material = rds.hget('PN', f'IM{machine}')

    # 取当前标准routing信息
    routing_item = rds.hget('ROUTING', f'{material}')

    with Connection() as connection:
        session = connection.get_session()
        # 从redis中取标准routing,如果不存在，到Mysql中查询
        if routing_item is None:
            row = session.query(routing).filter(
                routing.pn == material).first()
            if row:
                routing_item = {
                    "pn_des": row.pn_des,
                    "cavity": row.cavity,
                    "unload": row.unload if row.unload is not None else '',
                    "moldingcycle": row.moldingcycle if row.moldingcycle is not None else '',
                    "manualcycle": row.manualcycle if row.manualcycle is not None else '',
                    "sap_value": row.sap_value if row.sap_value is not None else ''
                }
                # 将查询到的标准routing缓存
                rds.hset('ROUTING', material, json.dumps(
                    routing_item, ensure_ascii=False))
        else:
            routing_item = json.loads(routing_item)

        # 判断当前班次，并生成当前班次开始时间戳shift_start_ts
        hour_now = datetime.now().hour
        if hour_now in range(8, 16):
            shift = 'A'
            shift_start_ts = int(datetime.now().replace(
                hour=8, minute=0, second=0, microsecond=0).timestamp() * 1000)
        elif hour_now in range(16, 24):
            shift = 'B'
            shift_start_ts = int(datetime.now().replace(
                hour=16, minute=0, second=0, microsecond=0).timestamp() * 1000)
        elif hour_now in range(0, 8):
            shift = 'C'
            shift_start_ts = int(datetime.now().replace(
                hour=0, minute=0, second=0, microsecond=0).timestamp() * 1000)

        # 取当前班次信息
        shift_info = rds.hget('SHIFTINFO', F'IM{machine}')
        shift_info = json.loads(shift_info) if shift_info else {
            # 如果是C班，则将初始化班次的日期设置为昨天
            'prodate': str((datetime.now() - timedelta(days=1) if shift == 'C' else datetime.now()).date()),
            'shift': shift,
            'planned_stop': 0,
            'uptime': 0,
            'stdtime': 0,
            'total_output': 0,
            'std_output': 0,
            'defect': 0
        }

        # 记录有效稼动时间
        last_action = int(rds.hget('LASTRUN', f'IM{machine}'))
        # 如果上一个模具动作在当前班次起点之前，为换班，认为有效运行时间起点为上班整点时间
        if last_action < shift_start_ts:
            # 本次节拍时间距上班时间超过当前设定的2.2倍注塑周期，认为无效
            duration = ts-shift_start_ts if ts - \
                shift_start_ts < int(routing_item['moldingcycle'])*2.2*1000 else 0
        # 如果上一个模具动作在当前班次起点之后
        else:
            duration = ts-last_action if ts - \
                last_action < int(routing_item['moldingcycle'])*2.2*1000 else 0
        # 如果合模距上一次开模的的时间差，即取料时间在60s内，认为是有效间隔时间，,将取料时间累加到稼动时间里
        if action == 2 and duration < 60000:
            shift_info['uptime'] = int(shift_info['uptime'])+duration

        if action == 1:
            # 如果开模的时间距上次模具动作在注塑cycle的2.2倍以内，认为是有效的，将标准routing加到理论时间内
            if duration < int(routing_item['moldingcycle'])*2.2*1000:
                # 开模时记录有效稼动时间
                shift_info['std_output'] = float(shift_info['std_output']) + duration / \
                    1000/float(routing_item['moldingcycle']
                               )*routing_item['cavity']
                shift_info['uptime'] = int(shift_info['uptime'])+duration
                # 如果状态信息中的需要记录产量时，则将当前产量写入到小时记录表中
                if state_des['is_output']:
                    # 记录标准理论生产时间
                    shift_info['stdtime'] = int(
                        shift_info['stdtime'])+int(routing_item['moldingcycle'])*1000
                    # 记录总产量
                    shift_info['total_output'] = int(
                        shift_info['total_output'])+len(material.split('/'))*int(routing_item['cavity'])

                    # 遍历料号，大部分情况下material中只有一个元素，共模生产时，会有多个料号
                    for item in material.split('/'):
                        # 查询对应小时号、对应机台以及对应料号的小时记录
                        datetime_now = datetime.fromtimestamp(
                            int(int(ts)/1000))
                        res = session.query(hourly_output).filter(
                            hourly_output.machine_id == f'{machine}', hourly_output.recorddate == datetime_now.date(), hourly_output.hourid == int(datetime_now.strftime("%H")), hourly_output.pn == item).first()
                        if res:
                            # 如果存在小时记录表记录，将本次产出的产量加到小时记录表中
                            res.uptime += duration
                            if action == 1:
                                res.output += int(routing_item['cavity'])
                        else:
                            # 如果不存在该条记录，说明是小时段内刚刚开始的新的生产，直接添加记录
                            new_record = hourly_output(recorddate=datetime_now.date(),
                                                       hourid=int(
                                datetime_now.strftime("%H")),
                                machine_id=f'{machine}', pn=f'{item}',
                                output=routing_item['cavity'],
                                uptime=duration,
                                routing=int(routing_item['moldingcycle']) /
                                int(routing_item['cavity']),
                                adjustion=0,
                                confirm_state=0)
                            session.add(new_record)
                        session.commit()

        # 将班次信息回写入redis的shift的key中
        rds.hset('SHIFTINFO', "IM"+str(machine),
                 json.dumps(shift_info, ensure_ascii=False))

        # 将模具动作写入时序队列
        rds.execute_command(f'TS.ADD CYCLE:IM{machine} {ts} {action}')

        # 将设备最后动作时间更新
        rds.hset('LASTRUN', f'IM{machine}', ts)


def myconsumer():
    try:
        pool = redis.ConnectionPool.from_url(
            my_config['REDIS_URL'], db=0, decode_responses=True)
        r = redis.Redis(connection_pool=pool)
    except Exception as message:
        print('连接服务器报错%s' % message)
    else:
        print('redis服务连接成功')
    while (True):
        popdata = r.brpop('MESSAGEQUEUE', 0)[1]
        res = ast.literal_eval(popdata)
        (data, ts) = res['data'], res['ts']
        # 如果数据包的长度是4，并且头两位介于1~25，后两位介于1~2，为合法的开合模信号
        if len(data) == 4 and int(data[0:2], 16) in range(1, 26) and int(data[2:4], 16) in range(1, 3):
            handleCycle(r, data, ts)
        # 如果数据包的长度是2，并且头两位介于1~25，为合法的模块心跳包，5分钟跳一次
        elif len(data) == 2 and int(data[0:2], 16) in range(1, 26):
            machine = int(data, 16)
            # 刷新设备上线时间
            refreshUptime(r, machine, ts)
        # 例外情况记入redis
        else:
            r.lpush(
                'ERRORDATA', f"{{'data': '{data}', 'ts': '{ts}'}}")


if __name__ == '__main__':
    threading.Thread(name='customer', target=myconsumer).start()

from flask import Blueprint, request
from app.welean.model.models_welean import Booksaddress, Booksinfo, Bookslog, Settings, Userinfo
from extensions import db
from sqlalchemy import or_, desc, func, not_
import hashlib
import traceback
from app.public.functions import responseGet, login_required, responsePost, responseError, responsePut
from app.welean.functions import getServer
from datetime import datetime, date
api = Blueprint('welean/V309/communityAPI', __name__)

borrowLimit = 2
returndes = ['1. 请确认书已经放回书架后再进行还书操作', '2. 请一定放到图书的指定库位中', '3. 成套图书请放的时候按顺序排好', '4. 如图书有丢失或损坏请联系管理员']
borrowdes = [
    '1. 输入或扫描书的条码即可完成借书',
    '2. 拿走书之前请在工具-还书界面确认已借走了',
    '3. 如果提示无法借阅可能被别人预定了',
    '4. 预定的书只对当日有效，次日自动取消预定',
    '5. 预定和借阅有上限,超过无法预定或借阅',
    '6. 预定不是必须的，可以直接扫描借书'
]


@api.route('/getMyborrows', methods=['GET'])
@login_required
def getMyborrows():
    res = request.args
    eid = res.get('eid')
    orders = db.session.query(Bookslog.Id, Bookslog.orderdate, Booksaddress.bin, Booksinfo.bookname, Booksinfo.coverpic, Bookslog.booksn, Booksinfo.author, Booksinfo.borrowed,
                              Booksinfo.category, Booksinfo.brief, Booksinfo.adddate, Booksinfo.rate, Booksinfo.status, Bookslog.borrowdate).join(
        Booksinfo, Bookslog.booksn == Booksinfo.sn).join(Booksaddress, Bookslog.booksn == Booksaddress.booksn).filter(Bookslog.eid == eid).filter(
        not_(Bookslog.borrowdate.is_(None))).filter(Bookslog.returndate.is_(None)).order_by(desc(Bookslog.borrowdate)).all()
    myBorrows = []
    for o in orders:
        myBorrows.append({
            'orderid': o.Id,
            'bookname': o.bookname,
            'author': o.author,
            'borrowed': o.borrowed,
            'category': o.category,
            'brief': o.brief,
            'borrowdate': datetime.strftime(o.borrowdate, '%Y-%m-%d'),
            'coverpic': getServer()['booksUrl']+o.coverpic if o.coverpic else '',
            'bin': o.bin,
            'orderdate': o.orderdate,
            'rate': o.rate,
            'sn': o.booksn,
            'status': o.status
        })
    return responseGet('获取成功', {'myBorrows': myBorrows, 'description': returndes})


@api.route('/getMyreturns', methods=['GET'])
@login_required
def getMyreturns():
    res = request.args
    eid = res.get('eid')
    num = res.get('num')
    orders = db.session.query(Bookslog.Id, Bookslog.orderdate, Booksaddress.bin, Booksinfo.bookname, Booksinfo.coverpic, Bookslog.booksn, Booksinfo.author, Booksinfo.borrowed,
                              Booksinfo.category, Booksinfo.brief, Booksinfo.adddate, Booksinfo.rate, Booksinfo.status, Bookslog.borrowdate, Bookslog.returndate).join(
        Booksinfo, Bookslog.booksn == Booksinfo.sn).join(Booksaddress, Bookslog.booksn == Booksaddress.booksn).filter(Bookslog.eid == eid).filter(
        not_(Bookslog.borrowdate.is_(None))).filter(not_(Bookslog.returndate.is_(None))).order_by(desc(Bookslog.returndate)).limit(num).all()
    myReturns = []
    for o in orders:
        myReturns.append({
            'orderid': o.Id,
            'bookname': o.bookname,
            'author': o.author,
            'borrowed': o.borrowed,
            'category': o.category,
            'brief': o.brief,
            'borrowdate': datetime.strftime(o.borrowdate, '%Y-%m-%d'),
            'returndate': datetime.strftime(o.returndate, '%Y-%m-%d'),
            'coverpic': getServer()['booksUrl']+o.coverpic if o.coverpic else '',
            'bin': o.bin,
            'orderdate': o.orderdate,
            'rate': o.rate,
            'sn': o.booksn,
            'status': o.status
        })
    return responseGet('获取成功', {'myReturns': myReturns})


@api.route('/getBookInfo', methods=['GET'])
@login_required
def getBookInfo():
    res = request.args
    sn = res.get('sn')
    book = db.session.query(Booksinfo.sn, Booksinfo.bookname, Booksinfo.author, Booksinfo.coverpic, Booksinfo.status, Booksaddress.bin).join(
        Booksaddress, Booksinfo.sn == Booksaddress.booksn).filter(Booksinfo.sn == sn).first()
    dic = {}
    if book:
        dic = {
            'bookname': book.bookname,
            'author': book.author,
            'coverpic': getServer()['booksUrl']+book.coverpic if book.coverpic else '',
            'bin': book.bin,
            'sn': book.sn,
            'status': book.status
        }
        return responseGet('找到书籍', {'book': dic})
    else:
        return responseError('没有找到该序列号的图书,请联系管理员')


@ api.route('/cancelOrder', methods=['PUT'])
@ login_required
def cancelOrder():
    res = request.json
    id = res.get('id')
    sn = res.get('sn')
    try:
        db.session.query(Bookslog).filter(Bookslog.Id == id).update({'returndate': date.today()})
        db.session.query(Booksinfo).filter(Booksinfo.sn == sn).update({'status': 'a'})
        db.session.commit()
        return responsePut('取消预定成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('取消出错，请联系管理员')


@api.route('/getMyorders', methods=['GET'])
@login_required
def getMyorders():
    res = request.args
    eid = res.get('eid')
    orders = db.session.query(Bookslog.Id, Bookslog.orderdate, Booksaddress.bin, Booksinfo.bookname, Booksinfo.coverpic, Bookslog.booksn, Booksinfo.author, Booksinfo.borrowed,
                              Booksinfo.category, Booksinfo.brief, Booksinfo.adddate, Booksinfo.rate, Booksinfo.status).join(
        Booksinfo, Bookslog.booksn == Booksinfo.sn).join(Booksaddress, Bookslog.booksn == Booksaddress.booksn).filter(Bookslog.eid == eid).filter(
        Bookslog.borrowdate.is_(None)).filter(Bookslog.returndate.is_(None)).all()
    myOrders = []
    for o in orders:
        myOrders.append({
            'orderid': o.Id,
            'bookname': o.bookname,
            'author': o.author,
            'borrowed': o.borrowed,
            'category': o.category,
            'brief': o.brief,
            'adddate': datetime.strftime(o.adddate, '%Y-%m-%d'),
            'coverpic': getServer()['booksUrl']+o.coverpic if o.coverpic else '',
            'bin': o.bin,
            'orderdate': o.orderdate,
            'rate': o.rate,
            'sn': o.booksn,
            'status': o.status
        })
    return responseGet('获取成功', {'myOrders': myOrders, 'description': borrowdes})


@ api.route('/orderBook', methods=['POST'])
@ login_required
def orderBook():
    res = request.json
    sn = res.get('sn')
    eid = res.get('eid')
    book = db.session.query(Booksinfo).filter(Booksinfo.sn == sn).first()
    myborrow = db.session.query(func.count(Bookslog.Id)).filter(
        Bookslog.eid == eid).filter(Bookslog.returndate.is_(None)).scalar()
    if myborrow >= borrowLimit:
        return responseError('你已经达到借阅上限，无法借阅或预定，有借有还，再借不难')
    if book.status != 'a':
        return responseError('手慢啦!此书已被预定或借走，无法预定')
    try:
        db.session.query(Booksinfo).filter(Booksinfo.sn == sn).update({'status': 'o'})
        order = Bookslog(eid=eid, booksn=sn, orderdate=date.today())
        db.session.add(order)
        db.session.commit()
        return responsePost('预定成功')
    except Exception:
        return responseError('预定出错，请重试或联系管理员')


@ api.route('/borrowBook', methods=['POST'])
@ login_required
def borrowBook():
    res = request.json
    sn = res.get('sn')
    eid = res.get('eid')
    available = checkBorrowed(sn, eid)
    if available > 0:
        try:
            if available == 1:
                myborrow = db.session.query(func.count(Bookslog.Id)).filter(
                    Bookslog.eid == eid).filter(Bookslog.returndate.is_(None)).scalar()
                if myborrow >= borrowLimit:
                    return responseError('你已经达到借阅上限，无法借阅或预定，有借有还，再借不难')
                log = Bookslog(booksn=sn, eid=eid, borrowdate=date.today())
                db.session.add(log)
            elif available == 2:
                db.session.query(Bookslog).filter(Bookslog.booksn ==
                                                  sn).update({'borrowdate': date.today()})
            db.session.query(Booksinfo).filter(Booksinfo.sn == sn).update({'status': 'b'})
            db.session.commit()
        except Exception:
            db.session.rollback()
            return responseError('借阅失败，请联系管理员')
        return responsePost('借阅成功，可以在工具-还书里查看已借阅的图书列表和进行还书')
    else:
        return responseError('此书已经被预定或借阅，请放回书架下次再借')


def checkBorrowed(sn, eid):
    book = db.session.query(Booksinfo).filter(Booksinfo.sn == sn).first()
    if book.status == 'a':
        return 1
    myorders = db.session.query(Bookslog).filter(Bookslog.booksn == sn).filter(Bookslog.eid == eid).filter(
        Bookslog.returndate.is_(None)).filter(Bookslog.borrowdate.is_(None)).all()
    for mo in myorders:
        if mo.booksn == sn:
            return 2
    return 0


@ api.route('/returnBook', methods=['POST'])
@ login_required
def returnBook():
    res = request.json
    sn = res.get('sn')
    id = res.get('id')
    rate = res.get('rate')
    book = db.session.query(Booksinfo).filter(Booksinfo.sn == sn).first()
    totalrate = book.rate
    totalborrowed = book.borrowed
    newrate = round((totalrate*totalborrowed+rate)/(totalborrowed+1), 2)
    newborrowed = totalborrowed+1
    print(totalrate, totalborrowed, newrate, newborrowed)
    try:
        db.session.query(Booksinfo).filter(Booksinfo.sn == sn).update({
            'status': 'a',
            'rate': newrate,
            'borrowed': newborrowed
        })
        db.session.query(Bookslog).filter(Bookslog.Id == id).update({
            'rate': rate,
            'returndate': date.today()
        })
        db.session.commit()
        return responsePost('还书成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('还书失败，请联系管理员')


@api.route('/getBookbySn', methods=['GET'])
@login_required
def getBookbySn():
    res = request.args
    sn = res.get('sn')
    history = []
    add = db.session.query(Booksaddress).filter(Booksaddress.booksn == sn).first()
    address = ''
    if add:
        address = add.bin
    his = db.session.query(Userinfo.cname, Bookslog.orderdate, Bookslog.borrowdate, Bookslog.returndate).join(Userinfo, Bookslog.eid == Userinfo.eid).filter(
        Bookslog.booksn == sn).order_by(desc(Bookslog.borrowdate)).all()
    for h in his:
        if h.borrowdate:
            if h.returndate:
                gap = (h.returndate-h.borrowdate).days+1
            else:
                gap = (date.today()-h.borrowdate).days+1
            if h.returndate:
                status = '已归还'
            elif h.orderdate and not h.borrowdate:
                status = '预定中'
            else:
                status = '借阅中'
            history.append({
                'cname': h.cname,
                'borrow': datetime.strftime(h.borrowdate, '%Y-%m-%d'),
                'gap': gap,
                'status': status
            })
    return responseGet('获取成功', {'address': address, 'history': history})


@ api.route('/getLibrary', methods=['POST'])
@ login_required
def getLibrary():
    res = request.json
    cates = res.get('cates')
    keywords = res.get('keywords')
    sortcate = res.get('sortcate')
    sorttype = res.get('sorttype')
    active = res.get('active')
    num = res.get('num')
    books = db.session.query(Booksinfo)
    if keywords:
        books = books.filter(or_(Booksinfo.bookname.like('%{0}%'.format(keywords)), Booksinfo.author.like(
            '%{0}%'.format(keywords)), Booksinfo.sn.like('%{0}%'.format(keywords))))
    if sortcate == 0:
        if sorttype == 0:
            books = books.order_by(Booksinfo.adddate)
        else:
            books = books.order_by(desc(Booksinfo.adddate))
    elif sortcate == 1:
        if sorttype == 0:
            books = books.order_by(Booksinfo.borrowed)
        else:
            books = books.order_by(desc(Booksinfo.borrowed))
    elif sortcate == 2:
        if sorttype == 0:
            books = books.order_by(Booksinfo.rate)
        else:
            books = books.order_by(desc(Booksinfo.rate))
    if active:
        books = books.filter(Booksinfo.status == 'a')
    if len(cates) > 0:
        books = books.filter(Booksinfo.category.in_(cates))
    books = books.limit(num).all()
    outArr = []
    for b in books:
        outArr.append({
            'Id': b.Id,
            'adddate': datetime.strftime(b.adddate, '%Y-%m-%d'),
            'author': b.author,
            'bookname': b.bookname,
            'borrowed': b.borrowed,
            'brief': b.brief,
            'category': b.category,
            'coverpic': getServer()['booksUrl']+b.coverpic if b.coverpic else '',
            'label': b.label,
            'rate': b.rate,
            'sn': b.sn,
            'status': b.status
        })
    return responseGet('成功', {'books': outArr})


@api.route('/getBooksettings', methods=['GET'])
@login_required
def getBooksettings():
    sets = db.session.query(Settings.name2).filter(
        Settings.cate == 'booktypes').filter(Settings.name1 == 'bookcate').all()
    dic = {}
    for s in sets:
        dic[s.name2] = {
            'name': s.name2,
            'active': False
        }
    options1 = [
        {
            'label': '上新时间',
            'value': 0
        },
        {
            'label': '借阅量',
            'value': 1
        },
        {
            'label': '评分',
            'value': 2
        }
    ]
    options2 = [
        {
            'label': '升序',
            'value': 0
        },
        {
            'label': '降序',
            'value': 1
        }
    ]
    return responseGet('成功', {'options1': options1, 'options2': options2, 'cateList': dic})


@ api.route('/scrapBook', methods=['POST'])
@ login_required
def scrapBook():
    res = request.json
    sn = res.get('sn')
    try:
        db.session.query(Booksaddress).filter(Booksaddress.booksn == sn).delete()
        db.session.query(Booksinfo).filter(Booksinfo.sn == sn).delete()
        db.session.commit()
        return responsePost('成功')
    except Exception:
        db.session.rollback()
        return responseError('删除失败，请联系管理员')


@api.route('/checkBin', methods=['GET'])
@login_required
def checkBin():
    res = request.args
    bin = res.get('bin')
    mybin = db.session.query(Booksaddress).filter(Booksaddress.bin == bin).first()
    if mybin:
        return responseGet('成功')
    else:
        return responseError('请注意，该库位目前没有存放任何书籍')


@ api.route('/uploadBookpic', methods=['POST'])
@ login_required
def uploadBookpic():
    file_obj = request.files.get('file')
    qid = request.headers["qid"]
    mystr = ('liberary' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        try:
            db.session.query(Booksinfo).filter(
                Booksinfo.sn == qid).update({'coverpic': name+appendix})
            db.session.commit()
            file_obj.save(getServer()['booksPath']+name+appendix)
            return responsePost("更新成功", {'upload_url': getServer()['booksUrl']+name+appendix})
        except Exception:
            db.session.rollback()
    return responseError("上传文件失败，请联系管理员")


@ api.route('/initBook', methods=['POST'])
@ login_required
def initBook():
    res = request.json
    booksn = res.get('booksn')
    bookname = res.get('bookname')
    author = res.get('author')
    category = res.get('category')
    brief = res.get('brief')
    bin = res.get('bin')
    adddate = res.get('adddate')
    if not adddate:
        adddate = date.today()
        try:
            book = Booksinfo(sn=booksn, bookname=bookname, author=author,
                             category=category, brief=brief, adddate=adddate, status='a', rate=3, borrowed=0)
            db.session.add(book)
            db.session.flush()
            address = Booksaddress(bin=bin, booksn=booksn)
            db.session.add(address)
            db.session.commit()
            return responsePost("成功", {'qid': booksn})
        except Exception:
            db.session.rollback()
            return responseError('失败,可能书的序列号有重号，请联系精益部门反馈该问题')
    else:
        try:
            db.session.query(Booksinfo).filter(Booksinfo.sn == booksn).update({
                'bookname': bookname,
                'author': author,
                'category': category,
                'brief': brief
            })
            db.session.query(Booksaddress).filter(Booksaddress.booksn == booksn).update({
                'bin': bin
            })
            db.session.commit()
            return responsePost("修改成功", {'qid': booksn})
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('修改失败，请联系精益部门反馈该问题')


@ api.route('/getBookTypes', methods=['GET'])
@ login_required
def getBookTypes():
    sets = db.session.query(Settings.name2).filter(
        Settings.cate == 'booktypes').filter(Settings.name1 == 'bookcate').all()
    outArr = []
    for dp in sets:
        dic = {
            'value': dp.name2,
            'label': dp.name2
        }
        outArr.append(dic)
    multiSelector = [
        ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
        ['1', '2', '3', '4', '5', '6'],
        ['1', '2', '3', '4', '5', '6']
    ]
    return responseGet("获取列表成功", {'bookTypes': outArr, 'multiSelector': multiSelector})


@api.route('/getBooks', methods=['GET'])
@login_required
def getBooks():
    res = request.args
    keywords = res.get('keywords')
    books = db.session.query(Booksaddress.bin, Booksaddress.booksn, Booksinfo.adddate, Booksinfo.author, Booksinfo.bookname, Booksinfo.brief, Booksinfo.label,
                             Booksinfo.category, Booksinfo.coverpic, Booksinfo.status).join(Booksinfo, Booksaddress.booksn == Booksinfo.sn).order_by(Booksaddress.bin)
    if keywords:
        books = books.filter(or_(Booksinfo.bookname.like('%{0}%'.format(keywords)), Booksinfo.author.like(
            '%{0}%'.format(keywords)), Booksinfo.sn.like('%{0}%'.format(keywords))))
    books = books.all()
    dic = {}
    for b in books:
        if b.category in dic.keys():
            dic[b.category].append({
                'bin': b.bin,
                'bookname': b.bookname,
                'author': b.author,
                'brief': b.brief,
                'category': b.category,
                'booksn': b.booksn,
                'coverpic': getServer()['baseUrl']+'books/'+b.coverpic if b.coverpic else '',
                'adddate': datetime.strftime(b.adddate, '%Y-%m-%d'),
                'status': b.status
            })
        else:
            dic[b.category] = [{
                'bin': b.bin,
                'bookname': b.bookname,
                'author': b.author,
                'brief': b.brief,
                'category': b.category,
                'booksn': b.booksn,
                'coverpic': getServer()['baseUrl']+'books/'+b.coverpic if b.coverpic else '',
                'adddate': datetime.strftime(b.adddate, '%Y-%m-%d'),
                'status': b.status
            }]
    bArr = []
    for v in dic.values():
        bArr.append(v)
    return responseGet("获取列表成功", {'books': bArr})


@api.route('/getBookshelf', methods=['GET'])
@login_required
def getBookshelf():
    res = request.args
    keywords = res.get('keywords')
    books = db.session.query(Booksaddress.bin, Booksaddress.booksn, Booksinfo.adddate, Booksinfo.author, Booksinfo.bookname, Booksinfo.brief, Booksinfo.label,
                             Booksinfo.category, Booksinfo.coverpic, Booksinfo.status).join(Booksinfo, Booksaddress.booksn == Booksinfo.sn).order_by(Booksaddress.bin)
    if keywords:
        books = books.filter(or_(Booksinfo.bookname.like('%{0}%'.format(keywords)), Booksinfo.author.like(
            '%{0}%'.format(keywords)), Booksinfo.sn.like('%{0}%'.format(keywords))))
    books = books.all()
    dic = {}
    for b in books:
        bookcase = b.bin.split('-')[0]
        booklayer = b.bin.split('-')[1]
        bookpos = b.bin.split('-')[2]
        bl = bookcase+'-'+booklayer
        print(bookcase, booklayer, bookpos)
        if bookcase in dic.keys():
            if bl in dic[bookcase]['children'].keys():
                dic[bookcase]['children'][bl]['children'].append({
                    'bin': b.bin,
                    'pos': booklayer+'-'+bookpos,
                    'bookname': b.bookname,
                    'author': b.author,
                    'brief': b.brief,
                    'category': b.category,
                    'booksn': b.booksn,
                    'coverpic': getServer()['baseUrl']+'books/'+b.coverpic if b.coverpic else '',
                    'adddate': datetime.strftime(b.adddate, '%Y-%m-%d'),
                    'status': b.status
                })
            else:
                dic[bookcase]['children'][bl] = {
                    'booklayer': bl,
                    'children': [{
                        'bin': b.bin,
                        'pos': booklayer+'-'+bookpos,
                        'bookname': b.bookname,
                        'author': b.author,
                        'brief': b.brief,
                        'category': b.category,
                        'booksn': b.booksn,
                        'coverpic': getServer()['baseUrl']+'books/'+b.coverpic if b.coverpic else '',
                        'adddate': datetime.strftime(b.adddate, '%Y-%m-%d'),
                        'status': b.status
                    }]
                }
        else:
            dic[bookcase] = {
                'bookcase': bookcase,
                'children':
                    {
                        bl: {
                            'booklayer': bl,
                            'children': [{
                                'bin': b.bin,
                                'pos': booklayer+'-'+bookpos,
                                'bookname': b.bookname,
                                'author': b.author,
                                'brief': b.brief,
                                'category': b.category,
                                'booksn': b.booksn,
                                'coverpic': getServer()['baseUrl']+'books/'+b.coverpic if b.coverpic else '',
                                'adddate': datetime.strftime(b.adddate, '%Y-%m-%d'),
                                'status': b.status
                            }]
                        }
                    },
            }
    bArr = []
    for v in dic.values():
        bArr.append(v)
    print(bArr)
    return responseGet("获取列表成功", {'books': bArr})

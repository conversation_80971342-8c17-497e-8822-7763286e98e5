from flask import Blueprint, request
from app.welean.model.models_welean import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Appvowners, Account
from openpyxl import load_workbook
from extensions import db
from sqlalchemy import or_, func, and_, desc, text
from sqlalchemy.orm import aliased
import hashlib
import traceback
from app.public.functions import responseGet, login_required, responsePost, responseError, responsePut
from app.welean.functions import getServer, TASKthread, PROGRESSthread
from datetime import datetime, timedelta
api = Blueprint('welean/V309/mroAPI', __name__)

Userinfo2 = aliased(Userinfo)
spareArr = ['备件']
mroArr = ['耗材', '文具']
mrostatus = ['open', 'appv', 'closed', 'cancel']
mrologTypes = {
    'manual': '入库',
    'bill': '领用',
    'pi': '盘点',
    'trans': '移库',
    'billreturn': '领用退回'
}


@ api.route('/downloadMRO', methods=['GET'])
@ login_required
def downloadMRO():
    res = request.args
    plant = res.get('plant')
    mrotype = res.get('mrotype') if res.get('mrotype') else 'mro'
    if mrotype == 'mro':
        sarr = mroArr
    else:
        sarr = spareArr
    sd = res.get('startDate')
    ed = res.get('endDate')
    ed = datetime.strptime(ed, '%Y-%m-%d')+timedelta(days=1)
    results = db.session.query(Mrolog.sku, Mrolog.logid, Mrosku.csku, Mrolog.transqty, Mrolog.logtype, Mrolog.transtime, Userinfo.dept1, Userinfo.cname).join(
        Userinfo, Mrolog.owner == Userinfo.eid).join(Mrosku, Mrosku.sku == Mrolog.sku).filter(
        Mrolog.transtime.between(sd, ed)).filter(Userinfo.plant == plant).all()
    results2 = db.session.query(Mrolog.sku, Mrosku.csku, func.sum(Mrolog.transqty).label('transqty'), Mrolog.logtype).join(
        Userinfo, Mrolog.owner == Userinfo.eid).join(Mrosku, Mrosku.sku == Mrolog.sku).filter(
        Mrolog.transtime.between(sd, ed)).filter(Userinfo.plant == plant).group_by(Mrolog.logtype, Mrolog.sku).order_by(Mrolog.sku).all()
    result3 = db.session.query(Mrosku.sku, Mrosku.csku, Mrosku.safetystock, Mroaddress.address, Mroaddress.stock, Mrosku.mrotype).outerjoin(
        Mroaddress, Mrosku.sku == Mroaddress.sku).filter(Mrosku.mrotype.in_(sarr)).order_by(Mrosku.sku).all()
    expense = db.session.query((Mrolog.transqty*Mrosku.price).label('expense'), Mrolog.transqty, Userinfo.dept1, Mrosku.csku, Mrolog.sku).join(
        Mrosku, Mrolog.sku == Mrosku.sku).join(
        Mrobill, Mrolog.logid == Mrobill.Id).join(Userinfo, Mrobill.owner == Userinfo.eid).filter(
        Mrolog.transtime.between(sd, ed)).filter(Userinfo.plant == plant).all()
    expDic = {}
    deptDic = {}
    for e in expense:
        eqty = e.transqty if e.transqty else 0
        eexp = e.expense if e.expense else 0
        if e.dept1 not in deptDic:
            deptDic[e.dept1] = len(deptDic)+2
        if e.sku+e.csku in expDic.keys():
            if e.dept1 in expDic[e.sku+e.csku].keys():
                expDic[e.sku+e.csku][e.dept1] = {
                    'qty': expDic[e.sku+e.csku][e.dept1]['qty']+eqty,
                    'exp': expDic[e.sku+e.csku][e.dept1]['exp']+eexp
                }
            else:
                expDic[e.sku+e.csku][e.dept1] = {
                    'qty': eqty,
                    'exp': eexp
                }
        else:
            expDic[e.sku+e.csku] = {
                e.dept1: {
                    'qty': eqty,
                    'exp': eexp
                }
            }
    if results:
        path = getServer()['downloadsPath']
        excelFile = 'mro' + datetime.now().strftime("%Y-%m-%d %H%M%S") + '.xlsx'
        createExcelmro(results, results2,  expDic, deptDic, path, excelFile, result3)
        return responseGet("获取列表成功", {'url': getServer()['downloadsUrl']+excelFile})
    else:
        return responseError("没有任何数据可以下载")


def createExcelmro(query, query2,  query3, deptDic, path, excelFile, stockquery):
    wb = load_workbook(path+'templates/mroTemplate.xlsx')
    sht = wb['明细']
    i = 2
    for q in query:
        sht.cell(row=i, column=1).value = q.transtime
        sht.cell(row=i, column=2).value = q.sku+'('+q.csku+')'
        sht.cell(row=i, column=3).value = q.transqty
        sht.cell(row=i, column=4).value = q.logid
        mtype = mrologTypes[q.logtype]
        if q.logtype == 'manual' and q.transqty > 0:
            mtype = '入库'
        sht.cell(row=i, column=5).value = mtype
        sht.cell(row=i, column=6).value = q.dept1
        sht.cell(row=i, column=7).value = q.cname
        i += 1
    sht2 = wb['汇总']
    j = 2
    for q in query2:
        sht2.cell(row=j, column=1).value = q.sku+'('+q.csku+')'
        sht2.cell(row=j, column=2).value = q.transqty
        mtype = mrologTypes[q.logtype]
        if q.logtype == 'manual' and q.transqty > 0:
            mtype = '入库'
        sht2.cell(row=j, column=3).value = mtype
        j += 1
    sht4 = wb['库存']
    k = 2
    for q in stockquery:
        sht4.cell(row=k, column=1).value = q.sku
        sht4.cell(row=k, column=2).value = q.csku
        sht4.cell(row=k, column=3).value = q.address
        sht4.cell(row=k, column=4).value = q.stock
        sht4.cell(row=k, column=5).value = q.safetystock
        sht4.cell(row=k, column=6).value = q.mrotype
        k += 1
    sht3 = wb['部门']
    skus = list(query3.keys())
    for key, val in deptDic.items():
        sht3.cell(row=1, column=val).value = key
        sht3.cell(row=1, column=len(deptDic)+val).value = key
    for k in range(len(skus)):
        sht3.cell(row=k+2, column=1).value = skus[k]
        for ll in range(len(deptDic)):
            if sht3.cell(row=1, column=ll+2).value in query3[skus[k]].keys():
                sht3.cell(row=k+2, column=ll +
                          2).value = -query3[skus[k]][sht3.cell(row=1, column=ll+2).value]['qty']
                sht3.cell(row=k+2, column=len(deptDic)+ll +
                          2).value = -query3[skus[k]][sht3.cell(row=1, column=ll+2).value]['exp']
    wb.save(path + excelFile)


@ api.route('/getMROCharts', methods=['GET'])
@ login_required
def getMROCharts():
    res = request.args
    plant = res.get('plant')
    sd = res.get('startDate')
    ed = res.get('endDate')
    # ed +1 day
    ed = datetime.strptime(ed, '%Y-%m-%d')
    ed = ed + timedelta(days=1)
    charts = getCharts(plant, sd, ed)
    return responseGet("获取列表成功", {'charts': charts})


def getCharts(plant, sd, ed):
    charts = []
    expense = db.session.query(func.sum(Mrolog.transqty*Mrosku.price).label('expense'), Userinfo.dept1).join(Mrosku, Mrolog.sku == Mrosku.sku).join(
        Mrobill, Mrolog.logid == Mrobill.Id).join(Userinfo, Mrobill.owner == Userinfo.eid).filter(
        Mrolog.transtime.between(sd, ed)).filter(Userinfo.plant == plant).group_by(Userinfo.dept1).order_by(func.sum(Mrolog.transqty*Mrosku.price).label('expense')).all()
    pie = []
    for rr in expense:
        dic = {
            'name': plant+'-'+rr.dept1,
            'value': round(-rr.expense, 2),
            "labelText": rr.dept1+':'+str(round(-rr.expense, 2))+'元',
        }
        pie.append(dic)
    charts.append({
        'title': '部门花费',
        'type': 'ring',
        'opts': {
            'rotate': False,
            'rotateLock': False,
            'color': ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
            'padding': [5, 50, 5, 50],
            'dataLabel': True,
            'legend': {
                'show': False,
                'position': "bottom",
                'lineHeight': 25
            },
            'title': {
                'name': "各部门花费",
                'fontSize': 15,
                'color': "#666666"
            },
            'subtitle': {
                'name': '',
                'fontSize': 25,
                'color': "#7cb5ec"
            },
            'extra': {
                'ring': {
                    'ringWidth': 30,
                    'activeOpacity': 0.5,
                    'activeRadius': 10,
                    'offsetAngle': 0,
                    'labelWidth': 15,
                    'border': True,
                    'borderWidth': 3,
                    'borderColor': "#FFFFFF",
                    'linearType': "custom"
                }
            }
        },
        'chartData': {
            'categories': [],
            'series': [
                {
                    'data': pie
                }
            ]
        }
    })
    return charts


@ api.route('/checkBill', methods=['GET'])
@ login_required
def checkBill():
    res = request.args
    id = res.get('id')
    status = ''
    mb = db.session.query(Mrobill).filter(Mrobill.Id == id).first()
    if mb:
        status = mb.status
    return responseGet('成功', {'status': status})


@ api.route('/getMyBills', methods=['GET'])
@ login_required
def getMyBills():
    res = request.args
    owner = res.get('eid')
    nb = int(res.get('nb'))+100
    billtype = res.get('billtype')
    mrotype = res.get('mrotype')
    ast = int(res.get('astatus'))
    if billtype == 'my':
        astatus = mrostatus[ast]
    else:
        astatus = mrostatus[ast+1]
    mros = db.session.query(Mrobill.Id, Mrobill.initdate, Mrobill.owner, Mrobill.billtype, Mrobill.reason, Mrobill.status, Mrobill.finishdate, Mrosku.desc,
                            Userinfo.dept1, Userinfo.dept2, Userinfo.cname, Mrolog.sku, Mrosku.csku, Mrolog.address, Mrobill.negreason, Userinfo2.cname.label(
                                'receiver'), Mrolog.transqty).join(Userinfo, Mrobill.owner == Userinfo.eid).outerjoin(Userinfo2, Mrobill.receiver == Userinfo2.eid).outerjoin(
        Mrolog, Mrobill.Id == Mrolog.logid).outerjoin(Mrosku, Mrolog.sku == Mrosku.sku).filter(
        Mrolog.logtype == 'bill').filter(Mrobill.status == astatus).order_by(Mrobill.initdate, Mrolog.address)
    if mrotype == 'spare':
        mros = mros.filter(Mrobill.billtype.in_(spareArr))
    elif mrotype == 'mro':
        mros = mros.filter(Mrobill.billtype.in_(mroArr))
    if owner:
        mros = mros.filter(Mrobill.owner == owner)
    mros = mros.order_by(desc(Mrobill.initdate)).limit(nb).all()
    mroDic = {}
    for m in mros:
        if m.Id in mroDic.keys():
            mroDic[m.Id]['list'].append({
                'sku': m.csku,
                'tsku': m.sku,
                'address': m.address,
                'transqty': -m.transqty,
                'desc': m.desc
            })
        else:
            mroDic[m.Id] = {
                'id': m.Id,
                'title': m.billtype+'领用申请|No.'+str(m.Id),
                'subTitle': '申请日期:'+datetime.strftime(m.initdate, "%Y-%m-%d"),
                'reason': m.reason,
                'dept1': m.dept1 if m.dept1 else '',
                'dept2': m.dept2 if m.dept2 else '',
                'cname': m.cname if m.cname else '',
                'owner': m.owner,
                'receiver': m.receiver,
                'finishdate': datetime.strftime(m.finishdate, '%Y-%m-%d %H:%M:%S') if m.finishdate else '',
                'status': m.status,
                'negreason': m.negreason,
                'list': [{
                    'sku': m.csku,
                    'tsku': m.sku,
                    'address': m.address,
                    'transqty': -m.transqty,
                    'desc': m.desc
                }]
            }
    mrocardsData = list(mroDic.values())
    print('mmmmmmmmmmmm', mrocardsData)
    return responseGet("获取列表成功", {'cardsData': mrocardsData})


@ api.route('/closePick', methods=['PUT'])
@ login_required
def closePick():
    res = request.json
    id = res.get('id')
    sender = res.get('sender')
    receiver = res.get('receiver')
    finishdate = datetime.now()
    try:
        db.session.query(Mrobill).filter(Mrobill.Id == id).update(
            {'status': 'closed', 'sender': sender, 'receiver': receiver, 'finishdate': finishdate})
        db.session.commit()
    except Exception:
        db.session.rollback()
        return responseError('发货失败，请联系管理员')
    return responsePut('发货成功')


@ api.route('/appvPick', methods=['PUT'])
@ login_required
def appvPick():
    res = request.json
    id = res.get('id')
    owner = res.get('owner')
    user = db.session.query(Account).filter(Account.eid == owner).first()
    wxid = user.wxid
    approver = res.get('approver')
    appvdate = datetime.now()
    reason = res.get('reason')
    try:
        db.session.query(Mrobill).filter(Mrobill.Id == id).update(
            {'status': 'appv', 'approver': approver, 'appvdate': appvdate})
        db.session.commit()
        otBody = {
            'content': reason[:20],
            'idate': datetime.strftime(appvdate, "%Y-%m-%d"),
            'owner': approver[:10],
            'status': '已批准',
            'comments': '你的领用请求已批准，请去MRO仓库领取'
        }
        href = 'pages/login/login'
        PROGRESSthread(otBody, wxid, href)
    except Exception:
        db.session.rollback()
        return responseError('批准出错，请联系管理员')
    return responsePut('领用成功')


@ api.route('/negPick', methods=['PUT'])
@ login_required
def negPick():
    res = request.json
    id = res.get('id')
    owner = res.get('owner')
    user = db.session.query(Account).filter(Account.eid == owner).first()
    wxid = user.wxid
    approver = res.get('approver')
    finishdate = datetime.now()
    negreason = res.get('negreason')
    reason = res.get('reason')
    try:
        picks = db.session.query(Mrolog).filter(
            Mrolog.logtype == 'bill').filter(Mrolog.logid == id).all()
        db.session.query(Mrobill).filter(Mrobill.Id == id).update(
            {'status': 'cancel', 'approver': approver, 'finishdate': finishdate, 'negreason': negreason})
        for p in picks:
            op = db.session.query(Mroaddress).filter(Mroaddress.address ==
                                                     p.address).filter(Mroaddress.sku == p.sku).first()
            if op:
                op.stock = op.stock-p.transqty
            else:
                newop = Mroaddress(address=p.address, sku=p.sku, stock=-p.transqty)
                db.session.add(newop)
            newlog = Mrolog(sku=p.sku, logtype='bill', logid=p.logid, transtime=finishdate,
                            transqty=-p.transqty, owner=approver, address=p.address)
            db.session.add(newlog)
        db.session.commit()
        otBody = {
            'content': reason[:20],
            'idate': datetime.strftime(finishdate, "%Y-%m-%d"),
            'owner': approver[:10],
            'status': '被取消',
            'comments': negreason[:20]
        }
        href = 'pages/login/login'
        PROGRESSthread(otBody, wxid, href)
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('取消领料单出错，请联系管理员')
    return responsePut('取消成功')


@ api.route('/getScanitems', methods=['GET'])
@ login_required
def getScanitems():
    res = request.args
    scan = res.get('scan')
    if not scan:
        return responseError('二维码无效，请重新扫码')
    mros = db.session.query(Mroaddress.Id, Mrosku.sku, Mroaddress.address, Mroaddress.stock, Mrosku.coverpic,
                            Mrosku.category, Mrosku.desc, Mrosku.brand, Mrosku.unit).outerjoin(
        Mroaddress, Mrosku.sku == Mroaddress.sku).filter(or_(Mroaddress.sku.like('%{}%'.format(scan)),
                                                             Mroaddress.address.like('%{}%'.format(scan)))).order_by(
        Mrosku.sku).all()
    outArr = []
    for b in mros:
        outArr.append({
            'addressid': b.Id,
            'sku': b.sku,
            'stock': b.stock,
            'address': b.address,
            'coverpic': getServer()['baseUrl']+'mros/'+b.coverpic if b.coverpic else '',
            'category': b.category,
            'desc': b.desc,
            'brand': b.brand,
            'unit': b.unit,
            'picknum': 1
        })
    return responseGet('成功', {'scans': outArr})


@ api.route('/createMrobill', methods=['POST'])
@ login_required
def createMrobill():
    res = request.json
    mros = res.get('mros')
    desc = res.get('desc')
    owner = res.get('owner')
    mrotype = res.get('mrotype')
    td = datetime.now()
    user = db.session.query(Userinfo).filter(Userinfo.eid == owner).first()
    cname = user.cname
    linename = user.dept1+'/'+user.dept2
    status = 'open'
    if mrotype != '耗材':
        status = 'appv'
    try:
        newbill = Mrobill(initdate=td, billtype=mrotype, reason=desc, owner=owner, status=status)
        db.session.add(newbill)
        db.session.flush()
        billid = newbill.Id
        for m in mros:
            skuadd = db.session.query(Mroaddress).filter(Mroaddress.Id == m['addressid']).first()
            if not skuadd:
                return responseError(str(m['address'])+'库位的物料'+m['sku']+'已经不存在，无法创建领用单')
            elif skuadd.stock < m['picknum']:
                return responseError(str(m['address'])+'库位的物料'+m['sku']+'库存已经不足，无法领用，请返回搜索页面重新选择')
            else:
                skuadd.stock = skuadd.stock-m['picknum']
            newlog = Mrolog(sku=m['sku'], logtype='bill', logid=billid, transtime=td,
                            transqty=-m['picknum'], owner=owner, address=m['address'])
            db.session.add(newlog)
        db.session.commit()
        if mrotype == '耗材':
            dealers = db.session.query(Appvowners.eid, Account.wxid).join(Account, Appvowners.eid == Account.eid).filter(Appvowners.dept == user.dept1).filter(
                Appvowners.plant == user.plant).filter(Appvowners.mroauth == 1).all()
            for dealer in dealers:
                taskBody = {
                    'idate': datetime.strftime(datetime.now(), '%Y-%m-%d %H:%M:%S'),
                    'linename': linename[:20],
                    'suggester': cname[:10],
                    'jobtype': '领用批准',
                    'content': desc[:20]
                }
                dwxid = dealer.wxid
                dhref = 'pages/login/login'
                TASKthread(taskBody, dwxid, dhref)
        return responsePost('创建领料单成功，可以在我的领用中查看和更改')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('创建领料单出错，请联系精益部门')


@ api.route('/getStockInfo', methods=['GET'])
@ login_required
def getStockInfo():
    res = request.args
    sku = res.get('sku')
    skus = db.session.query(Mroaddress).filter(Mroaddress.sku == sku).all()
    skuArr = []
    for s in skus:
        skuArr.append(s.address+'   '+str(s.stock)+'\n')
    title = sku+'全厂库存信息'
    return responseGet('成功', {'skus': skuArr, 'skumodalshow': True, 'title': title})


@ api.route('/getMros', methods=['POST'])
@ login_required
def getMros():
    res = request.json
    cates = res.get('cates')
    mrotype = res.get('mrotype')
    keywords = res.get('keywords')
    sortwh = res.get('sortwh')
    active = res.get('active')
    num = res.get('num')
    mros = db.session.query(Mroaddress.Id, Mrosku.csku, Mrosku.sku, Mroaddress.address, Mroaddress.stock, Mrosku.coverpic,
                            Mrosku.category, Mrosku.desc, Mrosku.brand, Mrosku.unit).outerjoin(
        Mroaddress, Mrosku.sku == Mroaddress.sku).filter(Mrosku.mrotype == mrotype)
    if keywords:
        mros = mros.filter(or_(Mrosku.csku.like('%{0}%'.format(keywords)), Mrosku.desc.like(
            '%{0}%'.format(keywords)), Mrosku.category.like('%{0}%'.format(keywords)), Mroaddress.address.like('%{0}%'.format(keywords))))
    if sortwh:
        mros = mros.filter(func.left(Mroaddress.address, 2) == sortwh)
    if active:
        mros = mros.filter(Mroaddress.stock > 0)
    if len(cates) > 0:
        mros = mros.filter(Mrosku.category.in_(cates))
    mros = mros.order_by(Mrosku.sku).limit(num).all()
    outArr = []
    print('aaaaaaaaa', mros, num)
    for b in mros:
        outArr.append({
            'addressid': b.Id,
            'sku': b.sku,
            'csku': b.csku,
            'stock': b.stock,
            'address': b.address,
            'coverpic': getServer()['baseUrl']+'mros/'+b.coverpic if b.coverpic else '',
            'category': b.category,
            'desc': b.desc,
            'brand': b.brand,
            'unit': b.unit,
            'picknum': 1
        })
    return responseGet('成功', {'Mros': outArr})


@api.route('/getMrosettings', methods=['GET'])
@login_required
def getMrosettings():
    res = request.args
    mrotype = res.get('mrotype')
    sets = db.session.query(Settings.name2).filter(
        Settings.cate == 'mrotypesettings').filter(Settings.name1 == mrotype).all()
    dic = {}
    for s in sets:
        dic[s.name2] = {
            'name': s.name2,
            'active': False
        }
    if mrotype in mroArr:
        mtype = 'mro'
    elif mrotype in spareArr:
        mtype = 'spare'
    options1 = getLocs(mtype)
    return responseGet('成功', {'options1': options1,  'cateList': dic})


@ api.route('/uploadSuppliers', methods=['POST'])
@ login_required
def uploadSuppliers():
    file_obj = request.files.get('file')
    plant = request.headers["plant"]
    if file_obj:
        try:
            rst = getSuppliersfromExcel(file_obj, plant)
            if rst == 'na':
                return responseError("上传文件格式不正确，请确认是否和下载文件完全一致")
            # print(rst)
            deactivesql = text("update wl_mrosupplier set isactive=0 where plant='%s'" % plant)
            db.session.execute(deactivesql)
            db.session.execute(rst)
            db.session.commit()
            return responsePost("更新成功")
        except Exception:
            db.session.rollback()
            traceback.print_exc()
    return responseError("上传文件失败，请联系管理员")


def getSuppliersfromExcel(file, plant):
    wb = load_workbook(file)
    ws = wb['suppliers']
    ck1 = (ws.cell(row=1, column=1).value == '供应商代码')
    ck2 = (ws.cell(row=1, column=2).value == '供应商名称')
    ck3 = (ws.cell(row=1, column=3).value == '类型')
    ck4 = (ws.cell(row=1, column=4).value == '备注')
    ck5 = (ws.cell(row=1, column=5).value == '联系人')
    ck6 = (ws.cell(row=1, column=6).value == '电话')
    ck7 = (ws.cell(row=1, column=7).value == '邮件')
    outArr = []
    if not (ck1 and ck2 and ck3 and ck4 and ck5 and ck6 and ck7):
        return 'na'
    for r in range(2, ws.max_row + 1):
        if ws.cell(r, 1).value:
            r1 = ws.cell(r, 1).value if ws.cell(r, 1).value else ""
            r2 = ws.cell(r, 2).value if ws.cell(r, 2).value else ""
            r3 = ws.cell(r, 3).value if ws.cell(r, 3).value else ""
            r4 = ws.cell(r, 4).value if ws.cell(r, 4).value else ""
            r5 = ws.cell(r, 5).value if ws.cell(r, 5).value else ""
            r6 = ws.cell(r, 6).value if ws.cell(r, 6).value else ""
            r7 = ws.cell(r, 7).value if ws.cell(r, 7).value else ""
            info = ("'"+str(r1)+"'", "'"+str(r2)+"'", "'"+str(r3)+"'", "'"+str(r4) +
                    "'", "'"+str(r5)+"'", "'"+str(r6)+"'", "'"+str(r7)+"'", "'"+plant+"'", '1')
            outArr.append('('+','.join(info)+')')
    outString = text("replace into wl_mrosupplier(suppliercode,suppliername,suppliertype,suppliercomments,contactors,tels,mails,plant,isactive) values " +
                     ','.join(outArr))
    return outString


@ api.route('/downloadSuppliers', methods=['GET'])
@ login_required
def downloadSuppliers():
    res = request.args
    plant = res.get('plant')
    users = db.session.query(Mrosupplier).filter(
        Mrosupplier.plant == plant).filter(Mrosupplier.isactive == 1).all()
    if users:
        path = getServer()['downloadsPath']
        excelFile = 'suppliers' + datetime.now().strftime("%Y-%m-%d %H%M%S") + '.xlsx'
        createExcel(users, path, excelFile)
        return responseGet("获取列表成功", {'url': getServer()['downloadsUrl']+excelFile})
    else:
        return responseError("没有任何数据可以下载")


def createExcel(query, path, excelFile):
    wb = load_workbook(path+'templates/supplierTemplate.xlsx')
    sht = wb['suppliers']
    i = 2
    for q in query:
        sht.cell(row=i, column=1).value = q.suppliercode
        sht.cell(row=i, column=2).value = q.suppliername
        sht.cell(row=i, column=3).value = q.suppliertype
        sht.cell(row=i, column=4).value = q.suppliercomments
        sht.cell(row=i, column=5).value = q.contactors
        sht.cell(row=i, column=6).value = q.tels
        sht.cell(row=i, column=7).value = q.mails
        i += 1
    wb.save(path + excelFile)


def getTotalstockbySku(sku):
    stocks = db.session.query(Mroaddress.stock, Mroaddress.address).filter(
        Mroaddress.sku == sku).all()
    stockloc = []
    stock = 0
    for s in stocks:
        sstock = s.stock if s.stock else 0
        stockloc.append(s.address+'  :  '+str(sstock))
        stock += sstock
    return stock, stockloc


def mroLogin(logtype, sku, address, qty, owner, logid=0):
    print(logtype, sku, address, qty, owner)
    transtime = datetime.strftime(datetime.now(), '%Y-%m-%d %H:%M:%S')
    try:
        addr = db.session.query(Mroaddress).filter(
            and_(Mroaddress.address == address, Mroaddress.sku == sku)).first()
        oldstock = addr.stock if addr.stock else 0
        newstock = oldstock+qty
        if newstock < 0:
            return False, '出库数量不能超过当前库位库存量'
        else:
            addr.stock = newstock
        newlog = Mrolog(logtype=logtype, sku=sku, logid=logid, address=address,
                        transtime=transtime, transqty=qty, owner=owner)
        db.session.add(newlog)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return False, '库存变更失败，请联系管理员'
    return True, '库存操作成功'


@ api.route('/mroStockchange', methods=['POST'])
@ login_required
def mroStockchange():
    res = request.json
    logtype = res.get('logtype')
    sku = res.get('sku')
    address = res.get('address')
    qty = float(res.get('qty'))
    owner = res.get('owner')
    mtype = res.get('type')
    if mtype == 'out':
        qty = -qty
    ck, creason = mroLogin(logtype, sku, address, qty, owner)
    if ck:
        return responsePost(creason)
    return responseError(creason)


@api.route('/getSkuinfo', methods=['GET'])
@ login_required
def getSkuinfo():
    res = request.args
    sku = res.get('sku')
    b = db.session.query(Mrosku).filter(Mrosku.sku == sku).first()
    skuInfo = {}
    if b:
        skuInfo = {
            'skuid': b.Id,
            'category': b.category,
            'bigType': b.mrotype,
            'coverpic': getServer()['baseUrl']+'mros/'+b.coverpic if b.coverpic else '',
            'sku': b.sku,
            'csku': b.csku,
            'safetystock': b.safetystock,
            'brand': b.brand,
            'desc': b.desc,
            'price': b.price,
            'unit': b.unit,
            'isactive': b.isactive,
            'supplier': b.supplier,
            'totalstock': getTotalstockbySku(sku)[0],
            'stockloc': getTotalstockbySku(sku)[1]
        }
    return responseGet('成功', {'skuInfo': skuInfo})


@ api.route('/uploadMropic', methods=['POST'])
@ login_required
def uploadMropic():
    file_obj = request.files.get('file')
    sku = request.headers["qid"]
    mystr = ('mrosku' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        try:
            db.session.query(Mrosku).filter(
                Mrosku.sku == sku).update({'coverpic': name+appendix})
            db.session.commit()
            file_obj.save(getServer()['basePath']+'mros/'+name+appendix)
            return responsePost("更新成功", {'upload_url': getServer()['baseUrl']+'mros/'+name+appendix})
        except Exception:
            db.session.rollback()
    return responseError("上传文件失败，请联系管理员")


@ api.route('/initMro', methods=['POST'])
@ login_required
def initMro():
    res = request.json
    sku = res.get('sku')
    owner = res.get('owner')
    mrotype = res.get('bigType')
    brand = res.get('brand') if res.get('brand') else ''
    category = res.get('category')
    sdesc = res.get('desc')
    delpic = res.get('delpic')
    address = res.get('bin')
    skuid = res.get('skuid')
    csku = res.get('csku')
    newstock = float(res.get('newstock')) if res.get('newstock') else 0
    addressid = res.get('addressid')
    price = res.get('price')
    stock = float(res.get('stock')) if res.get('stock') else 0
    safetystock = res.get('safetystock') if res.get('safetystock') else 0
    supplier = res.get('supplier')
    unit = res.get('unit')
    transtime = datetime.strftime(datetime.now(), '%Y-%m-%d %H:%M:%S')
    if not skuid:
        try:
            newsku = Mrosku(sku=sku, mrotype=mrotype, brand=brand, category=category, desc=sdesc, isactive=1,
                            price=price, safetystock=safetystock, supplier=supplier, unit=unit, csku=csku)
            db.session.add(newsku)
            db.session.commit()
        except Exception:
            db.session.rollback()
            return responseError('新建料号失败，请联系精益部门反馈该问题')
    else:
        try:
            db.session.query(Mrosku).filter(Mrosku.Id == skuid).update({
                'mrotype': mrotype,
                'brand': brand,
                'category': category,
                'desc': sdesc,
                'price': price,
                'safetystock': safetystock,
                'supplier': supplier,
                'unit': unit,
                'csku': csku,
                'coverpic': '' if delpic == 'y' else Mrosku.coverpic
            })
            db.session.commit()
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('修改料号失败，请联系精益部门反馈该问题')
    if not addressid:
        try:
            ostocks = 0
            addresses = db.session.query(Mroaddress).filter(
                Mroaddress.address == address).filter(Mroaddress.sku == sku).all()
            if len(addresses) > 0:
                for add in addresses:
                    ostocks = ostocks+(add.stock if add.stock else 0)
                    db.session.delete(add)
                    newlogout = Mrolog(logtype='trans', sku=add.sku, logid=0, address=address,
                                       transtime=transtime, transqty=-add.stock, owner=owner)
                    newlogin = Mrolog(logtype='trans', sku=add.sku, logid=0, address=add.address,
                                      transtime=transtime, transqty=add.stock, owner=owner)
                    db.session.add(newlogout)
                    db.session.add(newlogin)
                db.session.flush()
            newaddress = Mroaddress(address=address, sku=sku, stock=ostocks)
            db.session.add(newaddress)
            db.session.commit()
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('存储料号失败，请联系精益部门反馈该问题')
    else:
        try:
            addresses = db.session.query(Mroaddress).filter(
                Mroaddress.address == address).filter(Mroaddress.sku == sku).all()
            ostocks = 0
            for add in addresses:
                if add.Id != int(addressid):
                    ostocks = ostocks+(add.stock if add.stock else 0)
                    db.session.delete(add)
            db.session.flush()
            changeadd = db.session.query(Mroaddress).filter(Mroaddress.Id == int(addressid)).first()
            print('aaaaaaaaaa', changeadd)
            oldaddress = changeadd.address
            changeadd.address = address
            changeadd.stock = stock+ostocks
            # newlog = Mrolog(logtype='trans', sku=sku, logid=0, address=(oldaddress+','+address),
            #                 transtime=transtime, transqty=stock, owner=owner)
            if oldaddress != address:
                newlogout = Mrolog(logtype='trans', sku=sku, logid=0, address=oldaddress,
                                   transtime=transtime, transqty=-stock, owner=owner)
                newlogin = Mrolog(logtype='trans', sku=sku, logid=0, address=address,
                                  transtime=transtime, transqty=stock, owner=owner)
                db.session.add(newlogout)
                db.session.add(newlogin)
            # db.session.add(newlog)
            db.session.commit()
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('库位录入失败，请联系精益部门反馈该问题')
    if newstock:
        ck, creason = mroLogin('manual', sku, address, newstock, owner)
        if not ck:
            return responseError(creason)
    return responsePost('成功', {'qid': sku})


@ api.route('/getSupplierinfo', methods=['GET'])
@ login_required
def getSupplierinfo():
    res = request.args
    suppliername = res.get('suppliername')
    supplier = db.session.query(Mrosupplier).filter(
        Mrosupplier.suppliername == suppliername).first()
    supplierInfo = [
        '供应商:    '+supplier.suppliername,
        '代码:      '+(supplier.suppliercode if supplier.suppliercode else ''),
        '经营类别:  '+(supplier.suppliertype if supplier.suppliertype else ''),
        '备注:      '+(supplier.suppliercomments if supplier.suppliercomments else ''),
        '联系人:    '+(supplier.contactors if supplier.contactors else ''),
        '电话:      '+(supplier.tels if supplier.tels else ''),
        '邮箱:      '+(supplier.mails if supplier.mails else '')
    ]
    return responseGet('成功', {'supplierInfo': supplierInfo})


@ api.route('/scrapmro', methods=['POST'])
@ login_required
def scrapmro():
    res = request.json
    sku = res.get('sku')
    try:
        db.session.query(Mrosku).filter(Mrosku.sku == sku).update({'isactive': 0})
        db.session.commit()
        return responsePost('成功')
    except Exception:
        db.session.rollback()
        return responseError('删除失败，请联系管理员')


@ api.route('/searchSupplier', methods=['GET'])
@ login_required
def searchSupplier():
    res = request.args
    keywords = res.get('keywords')
    suppliers = db.session.query(Mrosupplier.suppliername).filter(
        Mrosupplier.suppliername.like('%{}%'.format(keywords)))
    supplierList = []
    for s in suppliers:
        supplierList.append({
            'suppliername': s.suppliername
        })
    return responseGet('成功', {'supplierList': supplierList})


@ api.route('/getMrotypes', methods=['GET'])
@ login_required
def getMrotypes():
    res = request.args
    plant = res.get('plant')
    mrotype = res.get('mrotype')
    sku = res.get('sku')
    mroTypes = []
    mroDic = {}
    settingTypes = []
    if mrotype == 'mro':
        settingTypes = mroArr
    elif mrotype == 'spare':
        settingTypes = spareArr
    mros = db.session.query(Settings).filter(Settings.cate == 'mrotypesettings').filter(
        Settings.name1.in_(settingTypes)).filter(Settings.isactive == 1).filter(Settings.plant == plant).all()
    for m in mros:
        if m.name1 in mroDic.keys():
            mroDic[m.name1]['children'].append({
                'label': m.name2,
                'value': m.name2
            })
        else:
            mroDic[m.name1] = {
                'label': m.name1,
                'value': m.name1,
                'children': [
                    {
                        'label': m.name2,
                        'value': m.name2
                    }
                ]
            }
    mroTypes = list(mroDic.values())
    us = db.session.query(Settings.name2).filter(
        and_(Settings.cate == 'mrosettings', Settings.name1 == 'unit')).filter(Settings.isactive == 1).filter(Settings.plant == plant).all()
    units = []
    for u in us:
        units.append({
            'label': u.name2,
            'value': u.name2
        })
    multiSelector = [
        ['M', 'S', 'Q'],
        ['1', '2', '3', '4'],
        ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'],
        ['1', '2', '3', '4', '5', '6', '7', '8', '9'],
        ['1', '2', '3', '4', '5', '6'],
        ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13',
            '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25']
    ]
    tup = getTotalstockbySku(sku)
    totalstock = tup[0]
    stockloc = tup[1]
    lastsku = db.session.query(Mrosku).filter(
        Mrosku.mrotype.in_(settingTypes)).order_by(desc(Mrosku.Id)).first()
    return responseGet("获取列表成功", {'mroTypes': mroTypes, 'units': units, 'multiSelector': multiSelector,  'totalstock': totalstock, 'stockloc': stockloc, 'lastsku': lastsku.sku if lastsku else ''})


def getLocs(mrotype):
    locArr = []
    locations = db.session.query(func.left(Mroaddress.address, 2).label(
        'add')).group_by(func.left(Mroaddress.address, 2))
    if mrotype == 'mro':
        locations = locations.filter(
            func.left(Mroaddress.address, 1) == 'M')
    locations = locations.group_by(func.left(Mroaddress.address, 2)).all()
    for ll in locations:
        locArr.append({
            'label': ll.add,
            'value': ll.add
        })
    locArr.append({
        'label': '全部',
        'value': ''
    })
    return locArr


@api.route('/getMroshelf', methods=['GET'])
@login_required
def getMroshelf():
    res = request.args
    keywords = res.get('keywords')
    mrotype = res.get('mrotype')  # 这里是mro和spare的大类别
    location = res.get('location')
    locArr = getLocs(mrotype)
    tpArr = spareArr
    if mrotype == 'mro':
        tpArr = mroArr
    mros = db.session.query(Mroaddress.sku, Mrosku.Id.label('skuid'), Mroaddress.address, Mroaddress.Id, Mroaddress.stock, Mrosku.safetystock, Mrosku.brand, Mrosku.desc, Mrosku.price, Mrosku.supplier,
                            Mrosku.category, Mrosku.coverpic, Mrosku.unit, Mrosku.isactive, Mrosku.csku, Mrosku.mrotype).outerjoin(Mrosku, Mrosku.sku == Mroaddress.sku).filter(
        Mrosku.isactive == 1).filter(Mrosku.mrotype.in_(tpArr)).order_by(Mroaddress.address)
    if keywords:
        mros = mros.filter(or_(Mrosku.sku.like('%{0}%'.format(keywords)), Mrosku.csku.like('%{0}%'.format(keywords)), Mrosku.desc.like(
            '%{0}%'.format(keywords)), Mrosku.category.like('%{0}%'.format(keywords))))
    if location:
        mros = mros.filter(func.left(Mroaddress.address, 2) == location)
    mros = mros.all()
    dic = {}
    for b in mros:
        stock = b.stock if b.stock else 0
        marea = b.address.split('-')[0]
        mrocase = b.address.split('-')[1]
        mrolayer = b.address.split('-')[2]
        mropos = b.address.split('-')[3]
        bl = marea+'-' + mrocase+'-'+mrolayer
        if mrocase in dic.keys():
            if bl in dic[mrocase]['children'].keys():
                dic[mrocase]['children'][bl]['children'].append({
                    'skuid': b.skuid,
                    'addressid': b.Id,
                    'bin': b.address,
                    'category': b.category,
                    'bigType': b.mrotype,
                    'coverpic': getServer()['baseUrl']+'mros/'+b.coverpic if b.coverpic else '',
                    'pos': mrolayer+'-'+mropos,
                    'sku': b.sku,
                    'csku': b.csku,
                    'safetystock': b.safetystock,
                    'brand': b.brand,
                    'desc': b.desc,
                    'price': b.price,
                    'unit': b.unit,
                    'isactive': b.isactive,
                    'supplier': b.supplier,
                    'stock': stock
                })
            else:
                dic[mrocase]['children'][bl] = {
                    'mrolayer': bl,
                    'children': [{
                        'skuid': b.skuid,
                        'addressid': b.Id,
                        'bin': b.address,
                        'category': b.category,
                        'bigType': b.mrotype,
                        'coverpic': getServer()['baseUrl']+'mros/'+b.coverpic if b.coverpic else '',
                        'pos': mrolayer+'-'+mropos,
                        'sku': b.sku,
                        'csku': b.csku,
                        'safetystock':b.safetystock,
                        'brand':b.brand,
                        'desc':b.desc,
                        'price':b.price,
                        'unit': b.unit,
                        'isactive':b.isactive,
                        'supplier':b.supplier,
                        'stock': stock
                    }]
                }
        else:
            dic[mrocase] = {
                'mrocase': mrocase,
                'children':
                    {
                        bl: {
                            'mrolayer': bl,
                            'children': [{
                                'skuid': b.skuid,
                                'addressid': b.Id,
                                'bin': b.address,
                                'category': b.category,
                                'bigType': b.mrotype,
                                'coverpic': getServer()['baseUrl']+'mros/'+b.coverpic if b.coverpic else '',
                                'pos': mrolayer+'-'+mropos,
                                'sku': b.sku,
                                'csku': b.csku,
                                'safetystock':b.safetystock,
                                'brand':b.brand,
                                'desc':b.desc,
                                'price':b.price,
                                'unit': b.unit,
                                'isactive':b.isactive,
                                'supplier':b.supplier,
                                'stock': stock
                            }]
                        }
                    },
            }
    bArr = []
    for v in dic.values():
        bArr.append(v)
    return responseGet("获取列表成功", {'mros': bArr, 'locArr': locArr})

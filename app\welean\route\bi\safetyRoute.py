from flask import Blueprint, request
from app.welean.model.models_welean import Suggest, Userinfo
from extensions import db
from sqlalchemy import func, or_, desc, text, not_, and_
from sqlalchemy.orm import aliased
from app.public.functions import responseGet
from datetime import datetime, timedelta
from app.welean.functions import getServer
api = Blueprint('welean/bi/safetyAPI', __name__)
Userinfo2 = aliased(Userinfo)
sred = '#FF0000'
sblue = '#4E80BA'
sgreen = '#00B24D'
syellow = '#FFFF00'
sorange = '#FFBD00'


@api.route('/getClose', methods=['GET'])
def getClose():
    res = request.args
    plant = res.get('plant') if res.get('plant') else 'SZ'
    year = int(res.get('year')) if res.get('year') else datetime.now().year
    start = str(year)+'-01-01'
    end = str(year+1)+'-01-01'
    suggests = db.session.query(Suggest.status, Suggest.duedate, Suggest.cfdate, Userinfo.dept1).join(
        Userinfo, Suggest.exeid == Userinfo.eid).filter(Suggest.idate.between(start, end)).filter(
        Suggest.plant == plant).filter(not_(Userinfo.dept1.is_(None))).filter(Suggest.exeid != '9999999')
    dic = {
        'All': {
            'closed': 0,
            'ongoing': 0,
            'pastdue': 0,
            'cancel': 0
        }
    }
    for s in suggests:
        status = s.status
        duedate = s.duedate
        cfdate = s.cfdate
        dept = s.dept1
        closed = 1 if status == 'closed' else 0
        ongoing = 0
        pastdue = 0
        cancel = 1 if status == 'cancel' else 0
        if not cfdate:
            cfdate = duedate
        if status == 'ongoing':
            if cfdate < datetime.now():
                pastdue = 1
            else:
                ongoing = 1
        if dept in dic.keys():
            dic[dept]['closed'] += closed
            dic[dept]['ongoing'] += ongoing
            dic[dept]['pastdue'] += pastdue
            dic[dept]['cancel'] += cancel
        else:
            dic[dept] = {
                'closed': closed,
                'ongoing': ongoing,
                'pastdue': pastdue,
                'cancel': cancel
            }
        dic['All']['closed'] += closed
        dic['All']['ongoing'] += ongoing
        dic['All']['pastdue'] += pastdue
        dic['All']['cancel'] += cancel
    outDic = {
        'dept': [],
        'closed': [],
        'ongoing': [],
        'pastdue': [],
        'cancel': [],
        'rate': []
    }
    for k, v in dic.items():
        outDic['dept'].append(k)
        outDic['closed'].append(v['closed'])
        outDic['ongoing'].append(v['ongoing'])
        outDic['pastdue'].append(v['pastdue'])
        outDic['cancel'].append(v['cancel'])
        if (v['closed']+v['pastdue'] > 0):
            outDic['rate'].append(round(v['closed']/(v['closed']+v['pastdue'])*100, 0))
        else:
            outDic['rate'].append('-')
    return responseGet('ok', {'data': outDic})


@api.route('/getCross', methods=['GET'])
def getCross():
    res = request.args
    stime = res.get('stime')
    etime = res.get('etime')
    stime = datetime.strptime(stime, '%Y-%m-%d')
    starttime = stime
    etime = datetime.strptime(etime, '%Y-%m-%d')+timedelta(days=1)
    dateArr = []
    plant = res.get('plant') if res.get('plant') else 'SZ'
    while stime < etime:
        dateArr.append(stime)
        stime += timedelta(days=1)
    linename = res.get('linename')
    sugs = db.session.query(Suggest.Id, func.date(Suggest.idate).label('idate'), Suggest.type2, Suggest.stype).filter(
        Suggest.idate.between(starttime, etime)).filter(
        Suggest.stype.in_(['安全', '安全事件', '5S'])).filter(Suggest.eid != '9999999').filter(Suggest.plant == plant)
    if linename:
        sugs = sugs.filter(Suggest.linename == linename)
    sugs = sugs.order_by(desc(Suggest.idate)).all()
    dic = {}
    for s in sugs:
        month = str(s.idate.day).zfill(2)
        sl = sgreen
        sr = sgreen
        if s.type2 == '可记录事件':
            sr = sred
        elif s.type2 == '财产损失' or s.type2 == '急救事件':
            sr = sorange

        if s.type2 == '未遂事件':
            sl = syellow
        else:
            sl = sblue
        if s.idate in dic.keys():
            ool = dic[s.idate][0]
            oor = dic[s.idate][1]
            if (ool == sgreen and sl != sgreen) or (ool == sblue and sl == syellow):
                dic[s.idate][0] = sl
            if (oor == sgreen and sr != sgreen) or (oor == sorange and sr == sred):
                dic[s.idate][1] = sr
        else:
            dic[s.idate] = [sl, sr, month, s.idate.strftime('%Y-%m-%d'), s.stype]
    outArr = []
    for dd in dateArr:
        if datetime.date(dd) in dic.keys():
            outArr.append(dic[datetime.date(dd)])
        else:
            outArr.append([sgreen, sgreen, str(dd.day).zfill(2), dd.strftime('%Y-%m-%d')])
    return responseGet('success', {'outArr': outArr})


@api.route('/getSafetydays', methods=['GET'])
def getSafetydays():
    res = request.args
    plant = res.get('plant') if res.get('plant') else 'SZ'
    sql = text("select datediff(current_date(),idate)-1 safetyDays from wl_suggest where type2='可记录事件' and plant='%s' order by idate desc limit 1" % plant)
    result = db.session.execute(sql)
    output = result.fetchone()
    if output:
        sd = output[0]
    else:
        sd = 0
    return responseGet('success', {'safetyDays': sd})


@api.route('/getSummary', methods=['GET'])
def getSummary():
    res = request.args
    year = int(res.get('year')) if res.get('year') else datetime.now().year
    start = str(year)+'-01-01'
    end = str(year+1)+'-01-01'
    plant = res.get('plant') if res.get('plant') else 'SZ'
    outDic = {
        'yearly': [],
        'status': []
    }
    years = db.session.query(Suggest.stype, func.count(Suggest.Id).label('qty')).filter(Suggest.plant == plant).filter(
        Suggest.idate.between(start, end)).filter(Suggest.exeid != '9999999').group_by(Suggest.stype).all()
    for y in years:
        outDic['yearly'].append({
            'name': y.stype,
            'value': y.qty
        })
    sts = db.session.query(Suggest.status, func.count(Suggest.Id).label('qty')).filter(Suggest.plant == plant).filter(
        Suggest.idate.between(start, end)).filter(Suggest.exeid != '9999999').group_by(
            Suggest.status).order_by(desc(Suggest.status)).all()
    for s in sts:
        outDic['status'].append({
            'name': s.status,
            'value': s.qty
        })
    return responseGet('success', outDic)


@api.route('/gett3ehs', methods=['GET'])
def gett3ehs():
    res = request.args
    year = res.get('year')
    plant = res.get('plant') if res.get('plant') else 'SZ'
    sfinding = db.session.query(func.count(Suggest.Id)).filter(Suggest.stype == '安全').filter(func.year(Suggest.idate) == year).filter(
        Suggest.plant == plant).scalar()
    efinding = db.session.query(Suggest.type2, func.count(Suggest.Id).label('qty')).filter(Suggest.stype == '安全事件').filter(func.year(Suggest.idate) == year).filter(
        Suggest.plant == plant).group_by(Suggest.type2).all()
    dic = {
        '未遂事件': {
            'name': '虚惊事件',
            'value': 3,
            'qty': 0,
            'year': year
        },
        '急救事故/财产损失': {
            'name': '急救事故/财产损失',
            'value': 2,
            'qty': 0,
            'year': year
        },
        '安全发现': {
            'name': '不安全行为或不安全状况',
            'value': 4,
            'qty': sfinding,
            'year': year
        },
        '可记录事件': {
            'name': '可记录事件',
            'value': 1,
            'qty': 0,
            'year': year
        }
    }
    for e in efinding:
        print(11111111, e.type2)
        tp = e.type2
        if tp == '急救事件' or tp == '财产损失':
            tp = '急救事故/财产损失'
        if tp in dic.keys():
            dic[tp]['qty'] = dic[tp]['qty']+e.qty
    print('aaaaa', dic)
    outArr = list(dic.values())
    words = db.session.query(Suggest.type2, func.count(Suggest.Id).label('qty')).filter(Suggest.stype == '安全').filter(func.year(Suggest.idate) == year).filter(
        Suggest.plant == plant).group_by(Suggest.type2).all()
    wordArr = []
    for word in words:
        wordArr.append({
            'value': word.qty,
            'name': word.type2
        })
    return responseGet('成功', {'pyramid': outArr, 'word': wordArr})


@api.route('/getlistbyday', methods=['GET'])
def getlistbyday():
    res = request.args
    dt = res.get('dt')
    isehs = res.get('isehs')
    tomorrow = datetime.strptime(dt, '%Y-%m-%d')+timedelta(days=1)
    plant = res.get('plant') if res.get('plant') else 'SZ'
    tier = res.get('tier') if res.get('tier') else 2
    suggests = db.session.query(Suggest.Id, func.date(Suggest.idate).label('idate'), Suggest.type2, Suggest.content,
                                Suggest.status, Suggest.beforepic, Suggest.afterpic, Suggest.duedate, Suggest.comments, Suggest.acdate, Userinfo.cname.label(
        'suggester'), Userinfo2.cname.label('executer')).join(Userinfo, Suggest.eid == Userinfo.eid).join(
        Userinfo2, Suggest.exeid == Userinfo2.eid).filter(
        Suggest.plant == plant).filter(Suggest.exeid != '9999999')
    if tier == 2:
        suggests = suggests.filter(or_(Suggest.stype == '安全事件', Suggest.stype == '安全', Suggest.stype == '5S')).filter(func.date(Suggest.idate) == dt)
    else:
        suggests = suggests.filter(and_(func.date(Suggest.idate) >= dt, func.date(Suggest.idate) <= tomorrow))
        if isehs == '1':
            suggests = suggests.filter(or_(Suggest.stype == '安全事件', Suggest.stype == '安全'))
    suggests = suggests.all()
    outArr = []
    for s in suggests:
        outArr.append({
            'Id': s.Id,
            'idate': datetime.strftime(s.idate, '%Y-%m-%d'),
            'type2': s.type2,
            'content': s.content,
            'status': s.status,
            'beforepic': getServer()['suggestionUrl']+s.beforepic.split(',')[0] if s.beforepic else '',
            'afterpic': getServer()['suggestionUrl']+s.afterpic.split(',')[0] if s.afterpic else '',
            'duedate': s.duedate,
            'comments': s.comments,
            'acdate': datetime.strftime(s.acdate, '%Y-%m-%d') if s.acdate else '',
            'suggester': s.suggester,
            'executer': s.executer,
            'picsArr': []
        })
        print(s.beforepic)
    print(11111111111111, getServer()['suggestionUrl'])
    return responseGet('成功', {'suggests': outArr})


@api.route('/gett3ehsdetail', methods=['GET'])
def gett3ehsdetail():
    res = request.args
    year = res.get('year')
    plant = res.get('plant') if res.get('plant') else 'SZ'
    type2 = res.get('type2')
    print(year, plant, type2)
    suggests = db.session.query(Suggest.Id, func.date(Suggest.idate).label('idate'), Suggest.type2, Suggest.content,
                                Suggest.status, Suggest.beforepic, Suggest.afterpic, Suggest.duedate, Suggest.comments, Suggest.acdate, Userinfo.cname.label(
        'suggester'), Userinfo2.cname.label('executer')).join(Userinfo, Suggest.eid == Userinfo.eid).join(
        Userinfo2, Suggest.exeid == Userinfo2.eid).filter(func.year(Suggest.idate) == year).filter(
        Suggest.plant == plant).filter(Suggest.stype == '安全事件')
    if type2 == '可记录事故':
        suggests = suggests.filter(Suggest.type2 == '可记录事件')
    elif type2 == '虚惊事件':
        suggests = suggests.filter(Suggest.type2 == '未遂事件')
    else:
        suggests = suggests.filter(or_(Suggest.type2 == '急救事件', Suggest.type2 == '财产损失'))
    suggests = suggests.all()
    outArr = []
    for s in suggests:
        outArr.append({
            'Id': s.Id,
            'idate': datetime.strftime(s.idate, '%Y-%m-%d'),
            'type2': s.type2,
            'content': s.content,
            'status': s.status,
            'beforepic': getServer()['suggestionUrl']+s.beforepic.split(',')[0] if s.beforepic else '',
            'afterpic': getServer()['suggestionUrl']+s.afterpic.split(',')[0] if s.afterpic else '',
            'duedate': s.duedate,
            'commments': s.comments,
            'acdate': datetime.strftime(s.acdate, '%Y-%m-%d') if s.acdate else '',
            'suggester': s.suggester,
            'executer': s.executer,
            'picsArr': []
        })
    return responseGet('成功', {'suggests': outArr})

workers = 1    # 定义同时开启的处理请求的进程数量，根据网站流量适当调整
worker_class = "gevent"   # 采用gevent库，支持异步处理请求，提高吞吐量
bind = "localhost:5000"
reload = True
#backlog = 2048
#pidfile = "log/gunicorn.pid"
#accesslog = "log/access.log"
#errorlog = "log/debug.log"
#timeout = 600
#debug=False
#capture_output = True
access_log_format="%(h)s %(r)s %(s)s %(a)s %(L)s"
logconfig_dict = {
    'version':1,
    'disable_existing_loggers': False,
    #在最新版本必须添加root配置，否则抛出Error: Unable to configure root logger
    "root": {
          "level": "DEBUG",
          "handlers": ["console"] # 对应handlers字典的键（key）
    },
    'loggers':{
        "gunicorn.error": {
            "level": "DEBUG",# 打日志的等级；
            "handlers": ["error_file"], # 对应handlers字典的键（key）；
            #是否将日志打印到控制台（console），若为True（或1），将打印在supervisor日志监控文件logfile上，对于测试非常好用；
            "propagate": 0, 
            "qualname": "gunicorn_error"
        },

        "gunicorn.access": {
            "level": "DEBUG",
            "handlers": ["access_file"],
            "propagate": 0,
            "qualname": "access"
        }
    },
    'handlers':{
        "error_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "maxBytes": 1024*1024*100,# 打日志的大小（此处限制100mb）
            "backupCount": 1,# 备份数量（若需限制日志大小，必须存在值，且为最小正整数）
            "formatter": "generic",# 对应formatters字典的键（key）
            "filename": "./logs/error.log" #若对配置无特别需求，仅需修改此路径
        },
        "access_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "maxBytes": 1024*1024*100,
            "backupCount": 1,
            "formatter": "generic",
            "filename": "./logs/access.log", #若对配置无特别需求，仅需修改此路径
        },
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'DEBUG',
            'formatter': 'generic',
        },

    },
    'formatters':{
        "generic": {
            "format": "%(asctime)s [%(process)d]: [%(levelname)s] %(message)s", # 打日志的格式
            "datefmt": "[%Y-%m-%d %H:%M:%S %z]",# 时间显示格式
            "class": "logging.Formatter"
        }
    }
}

# 自动选中最低价格功能实现

## 📋 功能概述

实现了页面打开后自动选中运费总价最低的卡片功能。系统会在重量计费和体积计费两个范围内分别找到最低价格的选项，然后选择总体最优的方案。

## 🎯 实现逻辑

### 1. **触发时机**
- 页面加载完成后
- 运费数据获取完成后
- 在 `calcFee()` 函数的回调中调用

### 2. **选择策略**
1. **分别计算**：在重量计费和体积计费中分别找到最低价格
2. **比较选择**：比较两种计费方式的最低价格，选择更优的方案
3. **自动应用**：自动选中最优卡片并更新表单数据

### 3. **计算公式**
- **重量计费**：`总价 = 货物重量 × 单价`
- **体积计费**：`总价 = 货物体积 × 单价`

## 🔧 技术实现

### 1. **核心函数**

```typescript
const autoSelectLowestPrice = () => {
  let lowestWeightPrice = Infinity;
  let lowestVolumePrice = Infinity;
  let bestWeightOption: any = null;
  let bestVolumeOption: any = null;
  let bestWeightIndex = -1;
  let bestVolumeIndex = -1;

  // 在重量计费中找到最低价格
  weight_data.value.forEach((item, index) => {
    const totalPrice = props.rowdata.weight * item.unitprice;
    if (totalPrice < lowestWeightPrice) {
      lowestWeightPrice = totalPrice;
      bestWeightOption = {
        template: item.template,
        unitprice: item.unitprice,
        freight_fee: totalPrice,
        lowerlimit: item.lowerlimit,
        upperlimit: item.upperlimit,
        calc_method: 1
      };
      bestWeightIndex = index;
    }
  });

  // 在体积计费中找到最低价格
  volume_data.value.forEach((item, index) => {
    const totalPrice = props.rowdata.volume * item.unitprice;
    if (totalPrice < lowestVolumePrice) {
      lowestVolumePrice = totalPrice;
      bestVolumeOption = {
        template: item.template,
        unitprice: item.unitprice,
        freight_fee: totalPrice,
        lowerlimit: item.lowerlimit,
        upperlimit: item.upperlimit,
        calc_method: 2
      };
      bestVolumeIndex = index;
    }
  });

  // 比较并选择最优方案
  if (bestWeightOption && bestVolumeOption) {
    if (lowestWeightPrice <= lowestVolumePrice) {
      showdata(bestWeightOption, 'weight', bestWeightIndex);
    } else {
      showdata(bestVolumeOption, 'volume', bestVolumeIndex);
    }
  } else if (bestWeightOption) {
    showdata(bestWeightOption, 'weight', bestWeightIndex);
  } else if (bestVolumeOption) {
    showdata(bestVolumeOption, 'volume', bestVolumeIndex);
  }
};
```

### 2. **集成到数据加载流程**

```typescript
const calcFee = () => {
  calcFeight(props.rowdata).then((res: any) => {
    freight_data.value = res.data;
    weight_data.value = res.data.weight;
    volume_data.value = res.data.volume;
    vehicle_data.value = res.data.vehicle;
    
    // 数据加载完成后，自动选中最低价格的卡片
    autoSelectLowestPrice();
  });
};
```

## 📊 算法流程

### 1. **重量计费分析**
```
遍历 weight_data[] {
  计算总价 = 货物重量 × 单价
  if (总价 < 当前最低重量价格) {
    更新最低重量价格
    保存最优重量方案
    记录索引位置
  }
}
```

### 2. **体积计费分析**
```
遍历 volume_data[] {
  计算总价 = 货物体积 × 单价
  if (总价 < 当前最低体积价格) {
    更新最低体积价格
    保存最优体积方案
    记录索引位置
  }
}
```

### 3. **最终选择**
```
if (有重量方案 && 有体积方案) {
  if (重量最低价 <= 体积最低价) {
    选择重量方案
  } else {
    选择体积方案
  }
} else if (只有重量方案) {
  选择重量方案
} else if (只有体积方案) {
  选择体积方案
}
```

## 🎨 用户体验

### 1. **自动化体验**
- ✅ 页面打开即有默认选择
- ✅ 无需手动比较价格
- ✅ 自动选择最优方案
- ✅ 表单数据自动填充

### 2. **视觉反馈**
- ✅ 最优卡片自动高亮显示
- ✅ 选中状态清晰可见
- ✅ 价格信息自动更新
- ✅ 计费方式自动设置

### 3. **交互保持**
- ✅ 用户仍可手动切换选择
- ✅ 选中状态正常管理
- ✅ 价格计算实时更新
- ✅ 表单验证正常工作

## 📝 数据结构

### 1. **重量计费数据**
```typescript
interface FreightItem {
  template: string;      // 物流公司模板
  unitprice: number;     // 单价（元/吨）
  province: string;      // 省份
  city: string;         // 城市
  district: string;     // 区县
  lowerlimit: number;   // 下限重量
  upperlimit: number;   // 上限重量
}
```

### 2. **最优方案对象**
```typescript
{
  template: string;        // 物流公司
  unitprice: number;       // 单价
  freight_fee: number;     // 总运费
  lowerlimit: number;      // 下限
  upperlimit: number;      // 上限
  calc_method: number;     // 计费方式 (1=重量, 2=体积)
}
```

## 🔍 示例场景

### 场景1：重量计费更优
```
货物信息：重量=2吨，体积=1.5立方米

重量计费选项：
- 公司A: 100元/吨 → 总价 200元 ✅ 最低
- 公司B: 120元/吨 → 总价 240元

体积计费选项：
- 公司C: 150元/立方米 → 总价 225元
- 公司D: 180元/立方米 → 总价 270元

结果：自动选择公司A的重量计费方案（200元）
```

### 场景2：体积计费更优
```
货物信息：重量=0.5吨，体积=3立方米

重量计费选项：
- 公司A: 200元/吨 → 总价 100元
- 公司B: 250元/吨 → 总价 125元

体积计费选项：
- 公司C: 30元/立方米 → 总价 90元 ✅ 最低
- 公司D: 35元/立方米 → 总价 105元

结果：自动选择公司C的体积计费方案（90元）
```

## ⚡ 性能优化

### 1. **算法复杂度**
- 时间复杂度：O(n + m)，n为重量选项数，m为体积选项数
- 空间复杂度：O(1)，只使用常量额外空间

### 2. **执行时机**
- 只在数据加载完成后执行一次
- 避免重复计算
- 不影响用户手动选择

## ✅ 功能特点

### 1. **智能选择**
- ✅ 自动比较所有可用选项
- ✅ 选择总价最低的方案
- ✅ 考虑重量和体积两种计费方式

### 2. **用户友好**
- ✅ 减少用户操作步骤
- ✅ 提供最优默认选择
- ✅ 保持手动选择能力

### 3. **数据准确**
- ✅ 基于实际货物重量和体积计算
- ✅ 使用准确的单价信息
- ✅ 自动更新表单数据

## 🎉 总结

自动选中最低价格功能成功实现了：

1. **智能分析**：自动分析所有可用的运费选项
2. **最优选择**：选择总价最低的计费方案
3. **自动应用**：自动选中卡片并更新表单
4. **用户体验**：提供便捷的默认选择，同时保持灵活性

这个功能大大提升了用户体验，让用户在页面打开时就能看到最优的运费方案！

import json
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.tmt.v20180321 import tmt_client, models
import openpyxl
import time
from openpyxl.styles import Alignment


def translateTencent(content, fromLang, toLang):
    try:
        cred = credential.Credential("AKIDuXtxlJ9nfGay4uz6SbbMr53D5apom0C8",
                                     "MT8XfZCj0XvxkFWxtwxhDTraCXdx0mHW")
        httpProfile = HttpProfile()
        httpProfile.endpoint = "tmt.tencentcloudapi.com"

        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        client = tmt_client.TmtClient(cred, "ap-shanghai", clientProfile)

        req = models.TextTranslateRequest()
        params = {
            "SourceText": content,
            "Source": fromLang,
            "Target": toLang,
            "ProjectId": 0,
            "UntranslatedText": "lean"
        }
        req.from_json_string(json.dumps(params))

        resp = client.TextTranslate(req).TargetText

        return resp

    except TencentCloudSDKException as err:
        print(err)


def excelTrans(srcFilename, fromLang, toLang, v):
    # rex = srcFilename[srcFilename.rfind("."):]
    # sname = srcFilename[:srcFilename.rfind(".")]
    charnum = 0
    wb = openpyxl.load_workbook(srcFilename)
    sheets = wb.sheetnames
    print("需要翻译的所有sheet列表")
    print(sheets)
    for i in range(len(sheets)):
        ws = wb[sheets[i]]
        print('正在翻译'+sheets[i]+',请耐心等待......')
        for r in range(1, ws.max_row + 1):
            for c in range(1, ws.max_column + 1):
                b = ws.cell(row=r, column=c).value
                if b is not None and len(str(b)) > 1 and "=" not in str(b):
                    charnum += len(str(b))
                    if v == 'reserve':
                        result = str(b)+"\n"+translateTencent(str(b), fromLang, toLang)
                    else:
                        result = translateTencent(str(b), fromLang, toLang)

                    ws.cell(row=r, column=c).value = str(result)

                    align = Alignment(horizontal='left', vertical='center', wrap_text=True)

                    ws.cell(row=r, column=c).alignment = align

                    time.sleep(1)

        wb.save(srcFilename)
    return charnum

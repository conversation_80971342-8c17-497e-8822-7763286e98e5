import traceback
from flask import Blueprint, request, json
from app.welean.model.models_welean import Auth, FiveSkaizen, FiveSprojects, Permission, Getscore, Userinfo, Services, FiveSgemba, Settings, Options, Suggest
from extensions import db
import hashlib
from openpyxl import load_workbook
from datetime import datetime
from sqlalchemy import func, or_, desc, and_, text
import math
import os
import openai
from app.public.functions import responseGet, login_required, responsePost, responseError, responsePut
from app.welean.functions import getServer, defaultAuth, PDFthread
api = Blueprint('welean/V309/pimsAPI', __name__)
openai.api_key = '***************************************************'
# 目前办公两种服务，services里的picToExcel和excelTrans，对应options里分类services的上锁的pics和trans
picstyles = {'马赛克': 'mosaic', '像素风': 'pixel', '写实照片': 'real', '卡通风格': 'cartoon',
             '油画风格': 'oil painting', '水墨画': 'inkwash painting', '蜡笔画': 'Wax Crayon', '素描': 'sketch black and white'}
txtstyles = {
    '智能问答': {
        'name': 'answer',
        'temp': 0.9,
        'mt': 1500,
        'stop': ['Me:', 'AI:']
    },
    '节日问候': {
        'name': 'greeting words',
        'temp': 0.7,
        'mt': 200,
        'stop': ['9999999']
    },
    '文章文案': {
        'name': 'article',
        'temp': 0.7,
        'mt': 500,
        'stop': ['999999999']
    },
    '幽默笑话': {
        'name': 'funny story',
        'temp': 0.9,
        'mt': 200,
        'stop': ['999999999999']
    }
}


@ api.route('/uploadTaskbind', methods=['POST'])
@ login_required
def uploadTaskbind():
    file_obj = request.files.get('file')
    subitem = request.form["subitem"]
    tasktype = request.form["tasktype"]
    if file_obj:
        try:
            rst = getTaskbindfromExcel(file_obj, tasktype, subitem)
            if rst == 'na':
                return responseError("上传文件格式或数据类型不正确，请确认是否和下载文件完全一致，eid是否是文本格式")
            # print(rst)
            db.session.execute(rst)
            db.session.commit()
            return responsePost("更新成功")
        except Exception:
            db.session.rollback()
            traceback.print_exc()
    return responseError("上传文件失败，请联系管理员")


def getTaskbindfromExcel(file, tasktype, subitem):
    wb = load_workbook(file)
    ws = wb['taskbind']
    eid = (ws.cell(row=1, column=1).value == 'eid')
    spv = (ws.cell(row=1, column=2).value == 'spv')
    outArr = []
    if not (eid and spv):
        return 'na'
    try:
        for r in range(2, ws.max_row + 1):
            if ws.cell(r, 1).value:
                r1 = ws.cell(r, 1).value
                r2 = ws.cell(r, 2).value if ws.cell(r, 2).value else ""
                info = ("'"+r1+"'", "'"+r2+"'",  "'"+tasktype+"'", "'"+subitem+"'")
                outArr.append('('+','.join(info)+')')
        outString = text("replace into wl_layered(eid,spv,tasktype,subitem) values " +
                         ','.join(outArr))
        return outString
    except Exception:
        traceback.print_exc()
        return 'na'


@ api.route('/openAIGPT', methods=['POST'])
@ login_required
def openAIGPT():
    res = request.json
    keywords = res.get('keywords')
    texttype = res.get('texttype') if res.get('texttype') else '智能问答'
    textresult = res.get('textresult') if res.get('textresult') else ''
    gptDic = txtstyles[texttype]
    name = gptDic['name']
    temperature = gptDic['temp']
    stop = gptDic['stop']
    mt = gptDic['mt']
    eid = res.get('eid')
    if name == 'answer':
        prompt = 'The following is a conversation with an AI assistant. The assistant is helpful, creative, clever, and very friendly.\n\n' + \
            textresult+'\n'+'Me:'+keywords+'\nAI:'
    else:
        prompt = generate_prompt(keywords, name)
    print(prompt)
    try:
        svs = Services(starttime=datetime.now(), finishtime=datetime.now(),
                       urls='gpt', eid=eid, stype='openai', status=2)
        db.session.add(svs)
        response = openai.Completion.create(
            model="text-davinci-002",
            prompt=prompt,
            temperature=temperature,
            top_p=1,
            frequency_penalty=0,
            presence_penalty=0.6,
            stop=stop,
            max_tokens=mt
        )
        db.session.commit()
        if name == 'answer':
            result = textresult+'\n'+'Me:'+keywords+'\nAI:' + \
                response.choices[0].text.replace('\n', '')+'\n'
        else:
            result = response.choices[0].text
        return responsePost('成功,请查看下方结果', {'result': result})
    except Exception:
        db.session.rollback()
    return responseError('处理失败，请联系管理员')


def generate_prompt(k, name):
    return """write a {}, the keywords is following：{}, the output should be written in Chinese""".format(name, k)


@ api.route('/openAIDELLE', methods=['POST'])
@ login_required
def openAIDELLE():
    res = request.json
    keywords = res.get('keywords')
    pictype = res.get('pictype')
    eid = res.get('eid')
    try:
        svs = Services(starttime=datetime.now(), finishtime=datetime.now(),
                       urls='delle', eid=eid, stype='openai', status=2)
        db.session.add(svs)
        response = openai.Image.create(
            prompt=keywords+','+picstyles[pictype],
            n=1,
            size="256x256"
        )
        image_url = response['data'][0]['url']
        db.session.commit()
        return responsePost('成功,请查看下方结果', {'result': image_url})
    except Exception:
        db.session.rollback()
        traceback.print_exc()
    return responseError('处理失败，请联系管理员')


def getLastseason(season):
    year = int(season[:4])
    sea = season[4:]
    if (int(sea) == 1):
        year = year-1
        sea = '04'
    else:
        sea = str(int(sea)-1).zfill(2)
    return str(year)+sea


@api.route('/getMyAI', methods=['GET'])
@login_required
def getMyAI():
    res = request.args
    eid = res.get('eid')
    drawTimes = getDrawTimes(eid)+10
    funcs = [
        {
            'value': 'delle',
            'label': '智能绘画'
        },
        {
            'value': 'gpt',
            'label': '智能助手'
        }
    ]
    txtlist = []
    piclist = []
    for item in list(txtstyles.keys()):
        txtlist.append({
            'label': item,
            'value': item
        })
    for item in list(picstyles.keys()):
        piclist.append({
            'label': item,
            'value': item
        })
    return responseGet("获取列表成功", {'txtlist': txtlist, 'piclist': piclist, 'funcs': funcs, 'drawTimes': drawTimes})


@ api.route('/delMyPTOE', methods=['PUT'])
@ login_required
def delMyPTOE():
    res = request.json
    id = res.get('id')
    url = res.get('url')
    db.session.query(Services).filter(Services.Id == id).update({'status': 2})
    db.session.commit()
    picname = url[url.rfind('/')+1:]
    excelname = picname.split('.')[0]+'.xlsx'
    p_path = getServer()['servicesPath']+picname
    e_path = getServer()['servicesPath']+excelname
    print(p_path, e_path)
    if os.path.isfile(p_path):
        abspath = os.path.abspath(p_path)
        os.remove(abspath)
    if os.path.isfile(e_path):
        abspath = os.path.abspath(e_path)
        os.remove(abspath)
    return responsePut("取消成功")


@api.route('/getMyPTOE', methods=['GET'])
@login_required
def getMyPTOE():
    res = request.args
    eid = res.get('eid')
    trans = db.session.query(Services).filter(
        Services.stype == 'picToExcel').filter(Services.eid == eid).order_by(desc(Services.starttime)).limit(5).all()
    myPTOE = []
    for t in trans:
        dic = {
            'id': t.Id,
            'starttime': datetime.strftime(t.starttime, "%Y-%m-%d %H:%M:%S"),
            'status': t.status,
            'url': getServer()['servicesUrl']+t.urls,
            'excelurl': getServer()['servicesUrl']+t.urls.split('.')[0]+'.xlsx'
        }
        myPTOE.append(dic)
    drawTimes = getDrawTimes(eid)
    return responseGet("获取列表成功", {'myPTOE': myPTOE,  'drawTimes': drawTimes})


@ api.route('/delMyTrans', methods=['PUT'])
@ login_required
def delMyTrans():
    res = request.json
    id = res.get('id')
    url = res.get('url')
    db.session.query(Services).filter(Services.Id == id).update({'status': 2})
    db.session.commit()
    c_path = getServer()['servicesPath']+url[url.rfind('/')+1:]
    if os.path.isfile(c_path):
        abspath = os.path.abspath(c_path)
        print(abspath)
        os.remove(abspath)
    return responsePut("取消成功")


@api.route('/getMyTrans', methods=['GET'])
@login_required
def getMyTrans():
    res = request.args
    eid = res.get('eid')
    trans = db.session.query(Services).filter(
        Services.stype == 'excelTrans').filter(Services.eid == eid).order_by(desc(Services.starttime)).limit(5).all()
    myTrans = []
    for t in trans:
        dic = {
            'id': t.Id,
            'starttime': datetime.strftime(t.starttime, "%Y-%m-%d %H:%M:%S"),
            'status': t.status,
            'url': getServer()['servicesUrl']+t.urls
        }
        myTrans.append(dic)
    drawTimes = getDrawTimes(eid)
    maxSize = db.session.query(func.sum(Getscore.getscore)).filter(
        Getscore.eid == eid).group_by(Getscore.eid).scalar()
    if maxSize:
        maxSize = maxSize/10
        if maxSize < 50:
            maxSize = 50
    else:
        maxSize = 50
    return responseGet("获取列表成功", {'myTrans': myTrans, 'maxSize': maxSize, 'drawTimes': drawTimes})


def getDrawTimes(eid):
    now = datetime.now()
    mstart = datetime(now.year, now.month, 1)
    drawed = getDrawed(eid, now, mstart)
    totalScore = db.session.query(func.sum(Getscore.getscore)).filter(Getscore.eid == eid).filter(
        Getscore.getdate.between(mstart, now)).group_by(Getscore.eid).scalar()
    if not totalScore:
        totalScore = 0
    myTimes = math.ceil(math.sqrt(totalScore)*0.18)-drawed
    print(totalScore, drawed, myTimes)
    return myTimes


def getDrawed(eid, now, mstart):
    getTimes = db.session.query(func.count(Services.Id)).filter(Services.eid == eid).filter(
        Services.starttime.between(mstart, now)).group_by(Services.eid).scalar()
    if not getTimes:
        getTimes = 0
    return getTimes


@ api.route('/uploadPTOE', methods=['POST'])
@ login_required
def uploadPTOE():
    file_obj = request.files.get('file')
    eid = request.headers["eid"]
    stype = request.headers["stype"]
    mystr = ('picToExcel' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    print(file_obj)
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        try:
            svs = Services(starttime=datetime.now(), urls=name +
                           appendix, eid=eid, stype=stype, status=0)
            db.session.add(svs)
            db.session.commit()
            file_obj.save(getServer()['servicesPath']+name+appendix)
            return responsePost("更新成功", {'upload_url': getServer()['servicesUrl']+name+appendix})
        except Exception:
            db.session.rollback()
    return responseError("上传文件失败，请联系管理员")


@ api.route('/uploadTrans', methods=['POST'])
@ login_required
def uploadTrans():
    file_obj = request.files.get('file')
    eid = request.headers["eid"]
    stype = request.headers["stype"]
    options = request.headers["options"]
    mystr = ('excelTrans' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        if appendix.lower() != '.xlsx':
            return responseError("上传文件失败，请联系管理员")
        try:
            svs = Services(starttime=datetime.now(), urls=name +
                           appendix, eid=eid, stype=stype, status=0, options=options)
            db.session.add(svs)
            db.session.commit()
            file_obj.save(getServer()['servicesPath']+name+appendix)
            return responsePost("更新成功", {'upload_url': getServer()['servicesUrl']+name+appendix})
        except Exception:
            db.session.rollback()
    return responseError("上传文件失败，请联系管理员")


@api.route('/cpdf', methods=['GET'])
def conpdf():
    pdfPath = getServer()['newsPath']+'000/demo.pdf'
    imagePath = getServer()['newsPath']+'000/pics/'
    PDFthread(pdfPath, imagePath)

    return responseGet("成功")


@api.route('/getTools', methods=['GET'])
@login_required
def getTools():
    res = request.args
    user = json.loads(res.get('user'))
    eid = user['eid']
    plant = user['plant']
    myAuth = db.session.query(Auth.auth).filter(Auth.eid == eid).scalar()
    if not myAuth:
        myAuth = defaultAuth
    else:
        myAuth = (defaultAuth+','+myAuth)
    myAuth = myAuth.split(',')
    permission = db.session.query(Permission).filter(Permission.ptype == 'pims').filter(Permission.pid >= 0).filter(
        or_(Permission.Id.in_(myAuth), Permission.needauth == 0)).filter(Permission.needauth != 2).filter(
        Permission.plant.like('%{0}%'.format(plant))).order_by(Permission.pid).order_by(
        Permission.ptitle).order_by(Permission.order).all()
    plist = {}
    pArr = []
    for p in permission:
        if p.pid == 0:
            plist[p.Id] = {'name': p.name, 'children': {}}
        else:
            if p.pid in plist.keys():
                if p.ptitle in plist[p.pid]['children'].keys():
                    plist[p.pid]['children'][p.ptitle].append({
                        'icon': getServer()['baseUrl']+'pimstools/'+p.icon,
                        'url': p.url,
                        'name': p.name
                    })
                else:
                    plist[p.pid]['children'][p.ptitle] = [{
                        'icon': getServer()['baseUrl']+'pimstools/'+p.icon,
                        'url': p.url,
                        'name': p.name
                    }]
            else:
                plist[p.pid] = {'name': p.name, 'children': {p.ptitle: [{
                    'icon': getServer()['baseUrl']+'pimstools/'+p.icon,
                    'url': p.url,
                    'name': p.name
                }]}}
    for v in plist.values():
        pArr.append(v)
    return responseGet("获取列表成功", {'myAuth': pArr})


@ api.route('/getMyStatus', methods=['GET'])
@ login_required
def getMyStatus():
    res = request.args
    eid = res.get('eid')
    plant = res.get('plant')
    trophyArr = []
    outArr = []
    index = 0
    totalLevel = db.session.query(func.sum(Getscore.getscore)).filter(
        Getscore.eid == eid).group_by(Getscore.eid).scalar()
    ot = totalLevel
    for i in range(1, 8):
        trophyArr.insert(0, pow(5, i))
    for j in trophyArr:
        index = totalLevel//j
        totalLevel = totalLevel % j
        outArr.append(index)
    sql = text("""
    SELECT b.* FROM (SELECT t.*, @rownum := @rownum + 1 AS rownum FROM (SELECT @rownum := 0) r,
    (select wl_userinfo.eid,sum(getscore) from wl_getscore  inner join wl_userinfo on wl_userinfo.eid=wl_getscore.eid
    where year(getdate)=%d and active=1 and plant='%s'  group by eid order by sum(getscore) desc ) AS t) AS b where eid='%s'
    """ % (datetime.now().year, plant, eid))
    cursor = db.session.execute(sql)
    result = cursor.fetchall()
    totalPeople = db.session.query(Getscore.eid).join(Userinfo, Userinfo.eid == Getscore.eid).filter(Userinfo.plant == plant).filter(Userinfo.active == 1).filter(
        func.year(Getscore.getdate) == datetime.now().year).group_by(Getscore.eid).all()
    myStatus = {
        'level': ot,
        'score': int(result[0][1]) if len(result) > 0 else '-',
        'rank': int(result[0][2]) if len(result) > 0 else '-',
        'total': len(totalPeople)
    }
    return responseGet("获取列表成功", {'myStatus': myStatus, 'tArr': outArr})


@ api.route('/getMy5Sscores', methods=['GET'])
@ login_required
def getMy5Sscores():
    res = request.args
    eid = res.get('eid')
    plant = res.get('plant')
    stype = res.get('stype')
    thisyear = datetime.today().year
    multiSelector = [[], ['Q1', 'Q2', 'Q3', 'Q4']]
    for y in range(2022, thisyear+1):
        multiSelector[0].append(y)
    dic = {}
    areas = db.session.query(Settings.name2).filter(and_(Settings.name1.in_(['VSM1', 'VSM2', 'Warehouse']),
                                                         Settings.plant == plant, Settings.isactive == 1, Settings.cate == 'areas')).group_by(Settings.name2).all()

    season = db.session.query(Options.content).filter(
        and_(Options.plant == plant, Options.cate == 'pimstools', Options.title == '5sseason')).scalar()
    lastseason = getLastseason(season)
    for a in areas:
        dic[a.name2] = {
            'area': a.name2,
            'last': 0,
            'current': 0,
            'currentId': 0,
            'lastavg': 0
        }
    avgscores = db.session.query(FiveSgemba.area, func.avg(FiveSgemba.score).label('score')).filter(
        and_(FiveSgemba.plant == plant, FiveSgemba.season == lastseason)).group_by(FiveSgemba.area).all()
    for avg in avgscores:
        if avg.area in dic.keys():
            dic[avg.area]['lastavg'] = int(avg.score)

    myscores = db.session.query(FiveSgemba.area, FiveSgemba.score, FiveSgemba.Id, FiveSgemba.season).filter(
        FiveSgemba.owner == eid).filter(FiveSgemba.season.in_([season, lastseason])).all()
    for mm in myscores:
        if mm.season == season:
            tag = 'current'
            dic[mm.area]['currentId'] = int(mm.Id)
        else:
            tag = 'last'
        if mm.area in dic.keys():
            dic[mm.area][tag] = int(mm.score)
    arr = list(dic.values())
    arr = sorted(arr, key=lambda x: x[stype], reverse=True)
    return responseGet("获取列表成功", {'scores': arr, 'season': season, 'multiSelector': multiSelector})


@ api.route('/giveScore', methods=['POST'])
@ login_required
def giveScore():
    res = request.json
    Id = res.get('Id')
    score = res.get('score')
    area = res.get('area')
    owner = res.get('owner')
    season = res.get('season')
    plant = res.get('plant')
    try:
        if Id > 0:
            db.session.query(FiveSgemba).filter(FiveSgemba.Id == Id).update({'score': score})
        else:
            new5S = FiveSgemba(score=score, area=area, owner=owner, season=season, plant=plant)
            db.session.add(new5S)
        db.session.commit()
    except Exception:
        db.session.rollback()
        return responseError('打分出错了，请联系管理员')
    return responsePost('成功')


@ api.route('/changeSeason', methods=['PUT'])
@ login_required
def changeSeason():
    res = request.json
    season = res.get('season')
    plant = res.get('plant')
    try:
        db.session.query(Options).filter(and_(Options.plant == plant, Options.cate ==
                                              'pimstools', Options.title == '5sseason')).update({'content': season})
        db.session.commit()
    except Exception:
        db.session.rollback()
        return responseError('5S季度切换出错，请联系管理员')
    return responsePut('成功')


@ api.route('/get5Sprojects', methods=['GET'])
@ login_required
def get5Sprojects():
    res = request.args
    plant = res.get('plant')
    season = db.session.query(Options.content).filter(
        and_(Options.plant == plant, Options.cate == 'pimstools', Options.title == '5sseason')).scalar()
    projects = db.session.query(FiveSprojects.Id, FiveSprojects.suggestid, FiveSprojects.area, Suggest.content, FiveSprojects.season).outerjoin(
        Suggest, FiveSprojects.suggestid == Suggest.Id).filter(FiveSprojects.season == season).order_by(FiveSprojects.area).all()
    projectsArr = []
    for p in projects:
        projectsArr.append({
            'Id': p.Id,
            'suggestid': p.suggestid,
            'area': p.area,
            'desc': p.content,
            'season': p.season
        })
    y = int(season[:4])
    m = season[4:]
    deadLines = {
        '01': str(y)+'-03-01',
        '02': str(y)+'-06-01',
        '03': str(y)+'-09-01',
        '04': str(y)+'-12-01'
    }
    td = datetime.now()
    newDisable = td > datetime.strptime(deadLines[m], '%Y-%m-%d')
    return responseGet("获取列表成功", {'season': season, 'projects': projectsArr, 'newDisable': newDisable})


@ api.route('/getFSprojects', methods=['GET'])
@ login_required
def getFSprojects():
    res = request.args
    plant = res.get('plant')
    season = db.session.query(Options.content).filter(
        and_(Options.plant == plant, Options.cate == 'pimstools', Options.title == '5sseason')).scalar()
    projects = db.session.query(FiveSprojects.Id, FiveSprojects.suggestid, FiveSprojects.area, Suggest.content, FiveSprojects.season).outerjoin(
        Suggest, FiveSprojects.suggestid == Suggest.Id).filter(FiveSprojects.season == season).order_by(FiveSprojects.area).all()
    projectsArr = []
    for p in projects:
        projectsArr.append({
            'Id': p.Id,
            'suggestid': p.suggestid,
            'area': p.area,
            'desc': p.content[0:35]+'...' if (p.content and len(p.content) > 35) else p.content,
            'season': p.season
        })
    return responseGet("获取列表成功", {'season': season, 'fsprojectlist': projectsArr})


@ api.route('/getMyfsrates', methods=['GET'])
@ login_required
def getMyfsrates():
    res = request.args
    eid = res.get('eid')
    season = res.get('season')
    projectsArr = []
    print('aaaaaaaa', res)
    projects = db.session.query(FiveSprojects.Id, FiveSprojects.suggestid, FiveSprojects.area, Suggest.content, FiveSprojects.season).outerjoin(
        FiveSkaizen, FiveSkaizen.projectid == FiveSprojects.Id).outerjoin(Suggest, FiveSprojects.suggestid == Suggest.Id).filter(
        FiveSprojects.season == season).filter(FiveSkaizen.owner == eid).all()
    for p in projects:
        projectsArr.append({
            'Id': p.Id,
            'suggestid': p.suggestid,
            'area': p.area,
            'desc': p.content[0:35]+'...' if (p.content and len(p.content) > 35) else p.content,
            'season': p.season
        })
    return responseGet("获取列表成功", {'tqlist': projectsArr})


@ api.route('/fsBind', methods=['POST'])
@ login_required
def fsBind():
    res = request.json
    tqarr = res.get('tqarr').split(',')
    season = res.get('season')
    eid = res.get('eid')
    print(tqarr, season, eid)
    try:
        idArr = []
        ids = db.session.query(FiveSprojects.Id).filter(FiveSprojects.season == season).all()
        for i in ids:
            idArr.append(i.Id)
        print(idArr)
        db.session.query(FiveSkaizen).filter(FiveSkaizen.owner ==
                                             eid).filter(FiveSkaizen.projectid.in_(idArr)).delete(synchronize_session=False)
        objects = []
        credit = 3
        for tq in tqarr:
            fk = FiveSkaizen(owner=eid, projectid=int(tq), credit=credit)
            credit = credit-1
            objects.append(fk)
        db.session.add_all(objects)
        db.session.commit()
        return responsePost("排名提交成功，正在返回")
    except Exception:
        traceback.print_exc()
        db.session.rollback()
    return responseError("绑定排名失败，请联系管理员")


@ api.route('/getFSmiss', methods=['GET'])
@ login_required
def getFSmiss():
    res = request.args
    plant = res.get('plant')
    season = res.get('season')
    area = res.get('area')
    y = int(season[:4])
    m = season[4:]
    deadLines = {
        '01': [str(y-1)+'-12-16', str(y)+'-03-15'],
        '02': [str(y)+'-03-16', str(y)+'-06-15'],
        '03': [str(y)+'-06-16', str(y)+'-09-15'],
        '04': [str(y)+'-09-16', str(y)+'-12-15']
    }
    ids = db.session.query(Suggest.Id).join(
        Settings, and_(Settings.plant == Suggest.plant, Settings.name3 == Suggest.linename)).filter(Settings.name2 == area).filter(
        Suggest.type2 == '5S').filter(and_(Settings.name1.in_(['VSM1', 'VSM2', 'Warehouse']), Settings.plant == plant, Settings.isactive == 1, Settings.cate == 'areas')).filter(
        Suggest.idate.between(deadLines[m][0], deadLines[m][1])).filter(
        or_(Suggest.status == 'ongoing', and_(func.datediff(Suggest.acdate, Suggest.appvdate) > 15, Suggest.status == 'closed'))).all()
    arr = []
    for i in ids:
        arr.append(i.Id)
    return responseGet("成功", {'ids': arr})


@ api.route('/getFSCharts', methods=['GET'])
@ login_required
def getFSCharts():
    res = request.args
    plant = res.get('plant')
    season = res.get('season')
    thisyear = datetime.today().year
    multiSelector = [[], ['Q1', 'Q2', 'Q3', 'Q4']]
    for y in range(2022, thisyear+1):
        multiSelector[0].append(y)
    if not season:
        season = db.session.query(Options.content).filter(
            and_(Options.plant == plant, Options.cate == 'pimstools', Options.title == '5sseason')).scalar()
    lastseason = getLastseason(season)
    scoreDic = {}  # 最终结果都都存在此处
    areas = db.session.query(Settings.name2).filter(and_(Settings.name1.in_(['VSM1', 'VSM2', 'Warehouse']),
                                                         Settings.plant == plant, Settings.isactive == 1, Settings.cate == 'areas')).group_by(Settings.name2).all()
    for a in areas:
        scoreDic[a.name2] = {
            'area': a.name2,
            'score': 0,
            'credit': 0,
            'seaRank': 0,
            'rankscore': 0,
            'kaizenscore': 0,
            'suggestscore': 0,
            'totalscore': 0
        }
    # 取得gemba分数，并排序，过去每个区域的分数score，(上季排名-本季排名)/2的仅部分rankscore
    gembaScores = db.session.query(FiveSgemba.area, FiveSgemba.season, func.avg(FiveSgemba.score).label('score')).filter(
        FiveSgemba.season.in_([season, lastseason])).group_by(FiveSgemba.season, FiveSgemba.area).order_by(FiveSgemba.season, desc(func.avg(FiveSgemba.score))).all()
    seaDic = {}
    lastseaDic = {}
    for g in gembaScores:
        if g.season == season:
            seaDic[g.area] = round(float(g.score), 1)
        if g.season == lastseason:
            lastseaDic[g.area] = round(float(g.score), 1)
    if len(seaDic) > 0:
        gapscore = list(seaDic.values())[0]-list(seaDic.values())[-1]
    else:
        gapscore = 0
    seaRank = 1
    for k, v in seaDic.items():
        scoreDic[k]['score'] = v
        scoreDic[k]['totalscore'] = v
        scoreDic[k]['seaRank'] = seaRank
        seaRank = seaRank+1
    lastRank = 1
    for k, v in lastseaDic.items():
        if k in scoreDic.keys():
            scoreDic[k]['rankscore'] = round((lastRank-scoreDic[k]['seaRank'])/2, 1)
            scoreDic[k]['totalscore'] = scoreDic[k]['totalscore']+scoreDic[k]['rankscore']
            lastRank = lastRank+1
    # 获取本季和上季的kaizen排名分kaizenscore，第一名gapscore，第二名gapscore*0.8，第三名gapscore*0.6，其他1分，上季未获前三名的得票会计入本季中
    kaizens = db.session.query(FiveSprojects.season, FiveSprojects.area, func.sum(FiveSkaizen.credit).label('credit')).outerjoin(FiveSprojects, FiveSprojects.Id == FiveSkaizen.projectid).filter(
        FiveSprojects.season.in_([season, lastseason])).group_by(FiveSprojects.season, FiveSprojects.area).order_by(FiveSprojects.season, desc(func.sum(FiveSkaizen.credit))).all()
    kaizenRank = 1
    lastkaizenRank = 1
    lkDic = {}
    kDic = {}
    for k in kaizens:
        if k.season == lastseason:
            if lastkaizenRank > 3:
                lkDic[k.area] = k.credit
            lastkaizenRank = lastkaizenRank+1
        elif k.season == season:
            kDic[k.area] = k.credit+(lkDic[k.area] if (k.area in lkDic.keys()) else 0)
    kArr = sorted(kDic.items(), key=lambda x: x[1], reverse=True)
    for k in kArr:
        if kaizenRank == 1:
            scoreDic[k[0]]['kaizenscore'] = round(gapscore*0.8, 1)
            scoreDic[k[0]]['totalscore'] = round(scoreDic[k[0]
                                                          ]['totalscore'] + scoreDic[k[0]]['kaizenscore'], 1)
        elif kaizenRank == 2:
            scoreDic[k[0]]['kaizenscore'] = round(gapscore*0.6, 1)
            scoreDic[k[0]]['totalscore'] = round(scoreDic[k[0]
                                                          ]['totalscore'] + scoreDic[k[0]]['kaizenscore'], 1)
        elif kaizenRank == 3:
            scoreDic[k[0]]['kaizenscore'] = round(gapscore*0.5, 1)
            scoreDic[k[0]]['totalscore'] = round(scoreDic[k[0]
                                                          ]['totalscore'] + scoreDic[k[0]]['kaizenscore'], 1)
        else:
            scoreDic[k[0]]['kaizenscore'] = 1
            scoreDic[k[0]]['totalscore'] = round(scoreDic[k[0]]['totalscore'] + 1, 1)
        kaizenRank += 1
    # 获取提案扣分，指定每个季度的截止日期为上个季度末的16日到本季度末15日，这段期间内提交的，ongoing或者closed的批准日期和实际完成日期超过15天的扣1分
    y = int(season[:4])
    m = season[4:]
    deadLines = {
        '01': [str(y-1)+'-12-16', str(y)+'-03-15'],
        '02': [str(y)+'-03-16', str(y)+'-06-15'],
        '03': [str(y)+'-06-16', str(y)+'-09-15'],
        '04': [str(y)+'-09-16', str(y)+'-12-15']
    }
    suggests = db.session.query(func.count(Suggest.Id).label('sugscore'), Settings.name2.label('area')).join(
        Settings, and_(Settings.plant == Suggest.plant, Settings.name3 == Suggest.linename)).filter(
        Suggest.type2 == '5S').filter(and_(Settings.name1.in_(['VSM1', 'VSM2', 'Warehouse']), Settings.plant == plant, Settings.isactive == 1, Settings.cate == 'areas')).filter(
        Suggest.idate.between(deadLines[m][0], deadLines[m][1])).filter(
        or_(Suggest.status == 'ongoing', and_(func.datediff(Suggest.acdate, Suggest.appvdate) > 15, Suggest.status == 'closed'))).group_by(Settings.name2).all()
    for s in suggests:
        if s.area in scoreDic.keys():
            scoreDic[s.area]['suggestscore'] = s.sugscore
            scoreDic[s.area]['totalscore'] = scoreDic[s.area]['totalscore']-s.sugscore
    scoreDic = sorted(scoreDic.values(), key=lambda x: x['totalscore'], reverse=True)
    charts = getCharts(scoreDic)
    scoreDic[0]['icon'] = getServer()['baseUrl']+'pimstools/5sdragon.png'
    scoreDic[1]['icon'] = getServer()['baseUrl']+'pimstools/5skirin.png'
    scoreDic[2]['icon'] = getServer()['baseUrl']+'pimstools/5stiger.png'
    return responseGet("获取列表成功", {'charts': charts, 'season': season, 'multiSelector': multiSelector, 'scoreDic': scoreDic})


@ api.route('/getFSCharts2022', methods=['GET'])
@ login_required
def getFSCharts2022():
    yearIndex = {
        2022: [4, 0.8, 0.6, 0.5],
        2023: [2, 1, 0.8, 0.6],
        2024: [4, 0.8, 0.6, 0.5]
    }
    res = request.args
    plant = res.get('plant')
    season = res.get('season')
    thisyear = datetime.today().year
    y = thisyear
    multiSelector = [[], ['Q1', 'Q2', 'Q3', 'Q4']]
    for yy in range(2022, thisyear+1):
        multiSelector[0].append(yy)
    if not season:
        season = db.session.query(Options.content).filter(
            and_(Options.plant == plant, Options.cate == 'pimstools', Options.title == '5sseason')).scalar()
        y = int(season[:4])
    lastseason = getLastseason(season)
    scoreDic = {}  # 最终结果都都存在此处
    areas = db.session.query(Settings.name2).filter(and_(Settings.name1.in_(['VSM1', 'VSM2', 'Warehouse']),
                                                         Settings.plant == plant, Settings.isactive == 1, Settings.cate == 'areas')).group_by(Settings.name2).all()
    for a in areas:
        scoreDic[a.name2] = {
            'area': a.name2,
            'score': 0,
            'credit': 0,
            'seaRank': 0,
            'rankscore': 0,
            'kaizenscore': 0,
            'suggestscore': 0,
            'totalscore': 0
        }
    # 取得gemba分数，并排序，过去每个区域的分数score，(上季排名-本季排名)/s0的仅部分rankscore
    gembaScores = db.session.query(FiveSgemba.area, FiveSgemba.season, func.avg(FiveSgemba.score).label('score')).filter(
        FiveSgemba.season.in_([season, lastseason])).group_by(FiveSgemba.season, FiveSgemba.area).order_by(FiveSgemba.season, desc(func.avg(FiveSgemba.score))).all()
    seaDic = {}
    lastseaDic = {}
    for g in gembaScores:
        if g.season == season:
            seaDic[g.area] = round(float(g.score), 1)
        if g.season == lastseason:
            lastseaDic[g.area] = round(float(g.score), 1)
    if len(seaDic) > 0:
        gapscore = list(seaDic.values())[0]-list(seaDic.values())[-1]
    else:
        gapscore = 0
    seaRank = 1
    for k, v in seaDic.items():
        scoreDic[k]['score'] = v
        scoreDic[k]['totalscore'] = v
        scoreDic[k]['seaRank'] = seaRank
        seaRank = seaRank+1
    lastRank = 1
    for k, v in lastseaDic.items():
        if k in scoreDic.keys():
            scoreDic[k]['rankscore'] = round((lastRank-scoreDic[k]['seaRank'])/yearIndex[y][0], 1)
            scoreDic[k]['totalscore'] = scoreDic[k]['totalscore']+scoreDic[k]['rankscore']
            lastRank = lastRank+1
    # 获取本季和上季的kaizen排名分kaizenscore，第一名gapscore，第二名gapscore*s1，第三名gapscore*s2，其他1分，上季未获前三名的得票会计入本季中
    kaizens = db.session.query(FiveSprojects.season, FiveSprojects.area, func.sum(FiveSkaizen.credit).label('credit')).outerjoin(FiveSprojects, FiveSprojects.Id == FiveSkaizen.projectid).filter(
        FiveSprojects.season.in_([season, lastseason])).group_by(FiveSprojects.season, FiveSprojects.area).order_by(FiveSprojects.season, desc(func.sum(FiveSkaizen.credit))).all()
    kaizenRank = 1
    lastkaizenRank = 1
    lkDic = {}
    kDic = {}
    for k in kaizens:
        if k.season == lastseason:
            if lastkaizenRank > 3:
                lkDic[k.area] = k.credit
            lastkaizenRank = lastkaizenRank+1
        elif k.season == season:
            kDic[k.area] = k.credit+(lkDic[k.area] if (k.area in lkDic.keys()) else 0)
    kArr = sorted(kDic.items(), key=lambda x: x[1], reverse=True)
    for k in kArr:
        if kaizenRank == 1:
            scoreDic[k[0]]['kaizenscore'] = round(gapscore*yearIndex[y][1], 1)
            scoreDic[k[0]]['totalscore'] = round(scoreDic[k[0]
                                                          ]['totalscore'] + scoreDic[k[0]]['kaizenscore'], 1)
        elif kaizenRank == 2:
            scoreDic[k[0]]['kaizenscore'] = round(gapscore*yearIndex[y][2], 1)
            scoreDic[k[0]]['totalscore'] = round(scoreDic[k[0]
                                                          ]['totalscore'] + scoreDic[k[0]]['kaizenscore'], 1)
        elif kaizenRank == 3:
            scoreDic[k[0]]['kaizenscore'] = round(gapscore*yearIndex[y][3], 1)
            scoreDic[k[0]]['totalscore'] = round(scoreDic[k[0]
                                                          ]['totalscore'] + scoreDic[k[0]]['kaizenscore'], 1)
        else:
            scoreDic[k[0]]['kaizenscore'] = 1
            scoreDic[k[0]]['totalscore'] = round(scoreDic[k[0]]['totalscore'] + 1, 1)
        kaizenRank += 1
    # 获取提案扣分，指定每个季度的截止日期为上个季度末的16日到本季度末15日，这段期间内提交的，ongoing或者closed的批准日期和实际完成日期超过15天的扣1分
    y = int(season[:4])
    m = season[4:]
    deadLines = {
        '01': [str(y-1)+'-12-16', str(y)+'-03-15'],
        '02': [str(y)+'-03-16', str(y)+'-06-15'],
        '03': [str(y)+'-06-16', str(y)+'-09-15'],
        '04': [str(y)+'-09-16', str(y)+'-12-15']
    }
    suggests = db.session.query(func.count(Suggest.Id).label('sugscore'), Settings.name2.label('area')).join(
        Settings, and_(Settings.plant == Suggest.plant, Settings.name3 == Suggest.linename)).filter(
        Suggest.type2 == '5S').filter(and_(Settings.name1.in_(['VSM1', 'VSM2', 'Warehouse']), Settings.plant == plant, Settings.isactive == 1, Settings.cate == 'areas')).filter(
        Suggest.idate.between(deadLines[m][0], deadLines[m][1])).filter(
        or_(Suggest.status == 'ongoing', and_(func.datediff(Suggest.acdate, Suggest.appvdate) > 15, Suggest.status == 'closed'))).group_by(Settings.name2).all()
    for s in suggests:
        if s.area in scoreDic.keys():
            scoreDic[s.area]['suggestscore'] = s.sugscore
            scoreDic[s.area]['totalscore'] = scoreDic[s.area]['totalscore']-s.sugscore
    scoreDic = sorted(scoreDic.values(), key=lambda x: x['totalscore'], reverse=True)
    charts = getCharts(scoreDic)
    scoreDic[0]['icon'] = getServer()['baseUrl']+'pimstools/5sdragon.png'
    scoreDic[1]['icon'] = getServer()['baseUrl']+'pimstools/5skirin.png'
    scoreDic[2]['icon'] = getServer()['baseUrl']+'pimstools/5stiger.png'
    return responseGet("获取列表成功", {'charts': charts, 'season': season, 'multiSelector': multiSelector, 'scoreDic': scoreDic})


def getCharts(dic):
    charts = []
    arr = []
    lb = []
    for v in dic:
        arr.append(round(v['totalscore'], 1))
        lb.append(v['area'])
    charts.append({
        'title': '5S实时得分',
        'opts': {
            'padding': [15, 15, 0, 5],
            'xAxis': {
                'rotateLabel': True
            },
            'yAxis': {
                'data': [
                     {'min': min(arr)-5},
                ]
            },
            'extra': {
                'column': {
                    'type': "group",
                    'width': 23,
                    'activeBgColor': "#000000",
                    'activeBgOpacity': 0.08
                }
            }
        },
        'type': 'column',
        'chartData': {
            'categories': lb,
            'series': [
                {
                    'legendText': '5S得分（季度未结束之前得分会动态变动）',
                    "data": arr
                }
            ]
        }
    })
    return charts

import requests
import datetime
id = "test1103110000"
content = [
    ['999374052739529', '116242009070186', '9999924019414112']
]
now = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')
headers = {'x-pentair-application-token': 'c2ad5d337db248c09088ae7c791e77ef'}
res1 = requests.post(
    "https://api.iot.pentair.com.cn/scan/upload", json={
        "gmt_create": now,
        "id": id,
        "count": 1,
        "gmt_submit": now,
        "sn_code": "11624",
        "content": content
    },
    headers=headers
)
content1 = res1.json()
print(content1)

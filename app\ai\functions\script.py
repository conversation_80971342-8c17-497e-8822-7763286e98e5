import os
import importlib.util
import sys


def run_script(script_path, func_name, *args):
    # 动态导入模块
    script_dir = os.path.dirname(script_path)

    # 添加脚本目录到sys.path
    if script_dir not in sys.path:
        sys.path.append(script_dir)

    spec = importlib.util.spec_from_file_location(
        os.path.basename(script_path).split('.')[0], script_path)
    script_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(script_module)

    # 调用脚本中的函数
    func = getattr(script_module, func_name)
    func(*args)

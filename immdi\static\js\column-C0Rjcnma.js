import{g as f}from"./operation-DCG_Ggeh.js";import{h as d}from"./moment-C3TZ8gAF.js";import{n as i,a2 as p,q as g,S as h,aE as u,b as o,f as r,r as n}from"./index-BnxEuBzx.js";function w(){const s=i([]),t=i(!0),l=p({selected_date:d().format("YYYY-MM-DD")});g(()=>{});const c=()=>{t.value=!0,f(h(l)).then(e=>{if(e.meta.status!=200){u(e.meta.msg,{type:"error"}),t.value=!1;return}s.value=e.data,t.value=!1}).catch(()=>{u("系统请求数据错误！",{type:"error"}),t.value=!1})},m=p({text:"正在加载换料单数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `});return{search_condition:l,search:c,loading:t,columns:[{label:"粒子料号",prop:"component",minWidth:"100"},{label:"粒子描述",prop:"component_des",minWidth:"160"},{label:"类型",prop:"component_type",width:"90",cellRenderer:({row:e})=>{if(e.component_type==1)return o(n("el-tag"),{size:"large"},{default:()=>[r("树脂")]});if(e.component_type==2)return o(n("el-tag"),{size:"large",type:"success"},{default:()=>[r("色母")]});if(e.component_type==3)return o(n("el-tag"),{size:"large",type:"warning"},{default:()=>[r("发泡剂")]})}},{label:"重量",prop:"total_weight",formatter(e,y,a){return Number.isInteger(a)?a.toLocaleString():a.toFixed(3)}},{label:"单位",prop:"unit",width:"80",formatter(){return"KG"}},{label:"不良描述",prop:"defect_des"}],dataList:s,loadingConfig:m,adaptiveConfig:{offsetBottom:30}}}export{w as useColumns};

<script setup lang="ts">
import { FormProps } from "./utils/types";
import { formRules } from "./utils/rule";
import { ref } from "vue";
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    pn: "",
    pn_des: "",
    cavity: 1,
    machine: "",
    moldingcycle: 0,
    manualcycle: 0,
    unload: 0,
    sap_value: 0
  }),
  ops: () => ({
    action_type: 0
  })
});

const newFormInline = ref(props.formInline);
const ops = ref(props.ops);

const ruleFormRef = ref();
function getRef() {
  return ruleFormRef.value;
}
defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="formRules"
    label-width="120px"
  >
    <el-form-item label="生产料号" prop="pn">
      <el-input v-model="newFormInline.pn" :readonly="ops.action_type == 1" />
    </el-form-item>
    <el-form-item label="料号描述" prop="pn_des">
      <el-input v-model="newFormInline.pn_des" />
    </el-form-item>
    <el-form-item label="模穴数" prop="cavity">
      <el-input-number v-model="newFormInline.cavity" :min="1" :max="12" />
    </el-form-item>
    <el-form-item label="机台号" prop="machine">
      <el-input v-model="newFormInline.machine" />
    </el-form-item>
    <el-form-item label="成型周期" prop="moldingcycle">
      <el-input v-model="newFormInline.moldingcycle" />
    </el-form-item>
    <el-form-item label="上下料时间" prop="manualcycle">
      <el-input v-model="newFormInline.manualcycle" />
    </el-form-item>
    <el-form-item label="自动下料" prop="unload">
      <el-switch
        v-model="newFormInline.unload"
        style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
        inline-prompt
        active-text="是"
        inactive-text="否"
        :active-value="1"
        :inactive-value="0"
      />
      <!-- <el-input v-model="newFormInline.unload" /> -->
    </el-form-item>
    <el-form-item label="SAP系统工时" prop="sap_value">
      <el-input v-model="newFormInline.sap_value" />
    </el-form-item>
  </el-form>
</template>

<style lang="scss" scoped>
.el-form {
  margin: 0 10px 0 0;
}
</style>

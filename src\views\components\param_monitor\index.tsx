import { addDialog } from "@/components/ReDialog";
import paramForm from "./param.vue";
import { useProdStoreHook } from "@/store/modules/prod";
import { h, ref, toRaw } from "vue";
import { message } from "@/utils/message";

export function show_param(machine_id: any, sku: any) {
  if (machine_id != 9) {
    message("该台设备未部署采集模块，无法查看实时参数", {
      customClass: "el",
      type: "error"
    });
    return;
  }
  addDialog({
    title: "查看" + machine_id + "#机实时参数" + sku,
    hideFooter: true,
    props: {
      queryinfo: {
        machineid: machine_id,
        sku: sku
      }
    },
    width: "60%",
    draggable: true,
    fullscreenIcon: false,
    closeOnClickModal: false,
    contentRenderer: () => h(paramForm),
    footerButtons: [],
    beforeSure: async (done, { options }) => {}
  });
}

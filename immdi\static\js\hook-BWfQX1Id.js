var l=(c,i,e)=>new Promise((a,o)=>{var t=s=>{try{r(e.next(s))}catch(d){o(d)}},n=s=>{try{r(e.throw(s))}catch(d){o(d)}},r=s=>s.done?a(s.value):Promise.resolve(s.value).then(t,n);r((e=e.apply(c,i)).next())});import{b as f,c as p}from"./system-DzNitOCO.js";import{aE as u,n as m,q as y}from"./index-BnxEuBzx.js";const g=c=>l(void 0,null,function*(){var i,e,a,o;try{const{meta:t,data:n}=yield c;if(t.status!==200){u(t.msg,{type:"error"});return}return{code:t.status,msg:t.msg,data:n}}catch(t){if(((i=t==null?void 0:t.response)==null?void 0:i.status)!==200){const n=(o=(a=(e=t.response)==null?void 0:e.data)==null?void 0:a.msg)!=null?o:t;u(n,{type:"error"})}else u(t,{type:"error"});return{code:-1,msg:"error",data:null}}}),E=(c,i)=>{const e=m([]),a=m(!0),o=s=>{s.id?c.value.showEdit(s):c.value.showEdit()},t=s=>{s.id&&i.value.showAssign(s)};function n(s){return l(this,null,function*(){const{code:d}=yield g(f({id:s.id}));d===0&&(u("删除成功",{type:"success"}),r())})}const r=()=>l(void 0,null,function*(){a.value=!0;const{data:s}=yield g(p());e.value=s,a.value=!1});return y(()=>{r()}),{dataList:e,loading:a,handleEdit:o,handleAssign:t,handleDelete:n,fetchData:r}};export{g as requestHook,E as useSysRoleManagement};

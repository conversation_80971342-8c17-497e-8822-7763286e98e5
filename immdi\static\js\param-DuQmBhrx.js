var vt=(Ml,Gl,d)=>new Promise((t,a)=>{var ut=Dl=>{try{Jl(d.next(Dl))}catch(ql){a(ql)}},rt=Dl=>{try{Jl(d.throw(Dl))}catch(ql){a(ql)}},Jl=Dl=>Dl.done?t(Dl.value):Promise.resolve(Dl.value).then(ut,rt);Jl((d=d.apply(Ml,Gl)).next())});import{aM as Kt,aN as Xt,d as Yt,n as Rl,a2 as $t,q as le,ar as te,r as ct,o as ee,g as ae,h as B,b as h,f as it,w as n,e as l,y as e,D as _,T as o,E as se,ak as oe,al as de,_ as ne}from"./index-BnxEuBzx.js";const _e=Ml=>Kt.request("get","http://10.76.1.234:8080/documents/paramAPI/getIMparam",{params:Ml}),ue=Ml=>Kt.request("get",Xt("param/getparam"),{params:Ml}),s=Ml=>(oe("data-v-fbb5f73c"),Ml=Ml(),de(),Ml),re={style:{width:"100%"}},ce=s(()=>l("thead",{class:"param_head"},[l("tr",null,[l("td",{colspan:"9"},[l("i",null,"工艺参数表基本信息")])])],-1)),pe=s(()=>l("td",null,"料号",-1)),ve={colspan:"4"},ie=s(()=>l("td",null,"机台号",-1)),he=s(()=>l("td",null,"树脂",-1)),me=s(()=>l("td",null,"描述",-1)),Me={colspan:"4"},De=s(()=>l("td",null,"机台吨位",-1)),we=s(()=>l("td",null,null,-1)),Pe=s(()=>l("td",null,"色母",-1)),be=s(()=>l("td",null,"模具号ID",-1)),je={colspan:"4"},ge=s(()=>l("td",null,"型腔数",-1)),Se=s(()=>l("td",null,"颜色",-1)),Ce=s(()=>l("td",{rowspan:"2"},"镶块/模仁",-1)),fe=s(()=>l("td",null,"1",-1)),Ie=s(()=>l("td",null,"2",-1)),ke=s(()=>l("td",null,"3",-1)),Oe=s(()=>l("td",null,"4",-1)),ye=s(()=>l("td",null,"浇口类型",-1)),He=s(()=>l("td",null,"插件",-1)),Te=s(()=>l("td",null,"单件重",-1)),Ee=s(()=>l("td",null,"一模总重",-1)),Ze={style:{width:"100%"}},ze={class:"param_head"},Be={colspan:"10"},Ve=s(()=>l("i",null,"温度 TEMPERATURE",-1)),Ae=s(()=>l("tr",null,[l("td",null,"范围"),l("td",null,"Zone1"),l("td",null,"T1"),l("td",null,"T2"),l("td",null,"T3"),l("td",null,"T4"),l("td",null,"T5"),l("td",null,"T6"),l("td",null,"Feed"),l("td",null,"Oil")],-1)),xe=s(()=>l("td",null,"±30℃",-1)),Qe={class:"monitor_row"},Fe=s(()=>l("td",null,"采集值",-1)),Ue=s(()=>l("td",null,null,-1)),qe={style:{width:"100%"}},Ne={class:"param_head"},Le={colspan:"7"},Re=s(()=>l("i",null,"熔胶",-1)),Ge=s(()=>l("tr",{class:"title"},[l("td",null,"项目"),l("td",null,"范围"),l("td",null,"1段"),l("td",null,"2段"),l("td",null,"3段"),l("td",null,"4段"),l("td",null,"5段")],-1)),Je=s(()=>l("td",null,"塑化压力",-1)),Ke={class:"monitor_row"},We=s(()=>l("td",{colspan:"2"},"采集值",-1)),Xe=s(()=>l("td",null,"背压",-1)),Ye={class:"monitor_row"},$e=s(()=>l("td",{colspan:"2"},"采集值",-1)),la=s(()=>l("td",null,"转速",-1)),ta={class:"monitor_row"},ea=s(()=>l("td",{colspan:"2"},"采集值",-1)),aa=s(()=>l("td",null,"转换点",-1)),sa={class:"monitor_row"},oa=s(()=>l("td",{colspan:"2"},"采集值",-1)),da={style:{width:"100%"}},na=s(()=>l("tr",{class:"title"},[l("td",null,"螺杆松退"),l("td",null,"压力"),l("td",null,"速度"),l("td",null,"位置")],-1)),_a=s(()=>l("td",null,"标准值",-1)),ua={class:"monitor_row"},ra=s(()=>l("td",null,"采集值",-1)),ca={style:{width:"100%"}},pa=s(()=>l("thead",{class:"param_head"},[l("tr",null,[l("td",{colspan:"12"},[l("i",null,"注塑保压过程")])])],-1)),va=s(()=>l("tr",{class:"title"},[l("td",null,"项目"),l("td",null,"范围"),l("td",null,"1段"),l("td",null,"2段"),l("td",null,"3段"),l("td",null,"4段"),l("td",null,"5段"),l("td",null,"6段"),l("td",null,"7段"),l("td",null,"8段"),l("td",null,"9段"),l("td",null,"10段")],-1)),ia=s(()=>l("td",null,"注射压力",-1)),ha={class:"monitor_row"},ma=s(()=>l("td",{colspan:"2"},"采集值",-1)),Ma=s(()=>l("td",null,null,-1)),Da=s(()=>l("td",null,null,-1)),wa=s(()=>l("td",null,null,-1)),Pa=s(()=>l("td",null,null,-1)),ba=s(()=>l("td",null,null,-1)),ja=s(()=>l("td",null,"注射速度",-1)),ga={class:"monitor_row"},Sa=s(()=>l("td",{colspan:"2"},"采集值",-1)),Ca=s(()=>l("td",null,null,-1)),fa=s(()=>l("td",null,null,-1)),Ia=s(()=>l("td",null,null,-1)),ka=s(()=>l("td",null,null,-1)),Oa=s(()=>l("td",null,null,-1)),ya=s(()=>l("td",null,"转换点",-1)),Ha={class:"monitor_row"},Ta=s(()=>l("td",{colspan:"2"},"采集值",-1)),Ea=s(()=>l("td",null,null,-1)),Za=s(()=>l("td",null,null,-1)),za=s(()=>l("td",null,null,-1)),Ba=s(()=>l("td",null,null,-1)),Va=s(()=>l("td",null,null,-1)),Aa={style:{width:"100%"}},xa={class:"title"},Qa=s(()=>l("td",{rowspan:"2"},"保压压力",-1)),Fa={rowspan:"2"},Ua=s(()=>l("td",null,"HP1",-1)),qa=s(()=>l("td",null,"HP2",-1)),Na=s(()=>l("td",null,"HP3",-1)),La=s(()=>l("td",null,"HP4",-1)),Ra=s(()=>l("td",null,"HP5",-1)),Ga=s(()=>l("td",null,"HV1",-1)),Ja=s(()=>l("td",null,"HV2",-1)),Ka=s(()=>l("td",null,"HV3",-1)),Wa=s(()=>l("td",null,"HV4",-1)),Xa=s(()=>l("td",null,"HV5",-1)),Ya={class:"monitor_row"},$a=s(()=>l("td",{colspan:"2"},"采集值",-1)),ls={style:{width:"100%"}},ts=s(()=>l("tr",{class:"title"},[l("td",{colspan:"2",rowspan:"2"},"保压时间"),l("td",null,"范围"),l("td",{colspan:"2"},"1段"),l("td",{colspan:"2"},"2段"),l("td",{colspan:"2"},"3段"),l("td",{colspan:"2"},"4段"),l("td",{colspan:"2"},"5段")],-1)),es={colspan:"2"},as={colspan:"2"},ss={colspan:"2"},os={colspan:"2"},ds={colspan:"2"},ns={class:"monitor_row"},_s=s(()=>l("td",{colspan:"3"},"采集值",-1)),us={style:{width:"100%"}},rs=s(()=>l("tr",{class:"title"},[l("td",{colspan:"2",rowspan:"2"},"其它"),l("td",null,"项目"),l("td",{colspan:"2"},"注射延迟"),l("td",{colspan:"2"},"注射时间"),l("td",{colspan:"2"},"冷却时间"),l("td",{colspan:"2"},"周期"),l("td",{colspan:"2"},"保压模式")],-1)),cs=s(()=>l("td",null,"标准值",-1)),ps={colspan:"2"},vs={class:"monitor_row"},is=s(()=>l("td",{colspan:"3"},"采集值",-1)),hs=s(()=>l("td",{colspan:"2"},"-",-1)),ms=s(()=>l("td",{colspan:"2"},"-",-1)),Ms={colspan:"2"},Ds={width:"100%"},ws=s(()=>l("thead",{class:"param_head"},[l("tr",null,[l("td",{colspan:"11"},"热烧道控制")])],-1)),Ps=s(()=>l("tr",{class:"title"},[l("td",null,"Zone"),l("td",null,"1"),l("td",null,"2"),l("td",null,"3"),l("td",null,"4"),l("td",null,"5"),l("td",null,"6"),l("td",null,"7"),l("td",null,"8"),l("td",null,"9"),l("td",null,"10")],-1)),bs=s(()=>l("td",null,"标准值",-1)),js={class:"monitor_row"},gs=s(()=>l("td",null,"采集值",-1)),Ss=s(()=>l("td",null,"-",-1)),Cs=s(()=>l("td",null,"-",-1)),fs=s(()=>l("td",null,"-",-1)),Is=s(()=>l("td",null,"-",-1)),ks=s(()=>l("td",null,"-",-1)),Os=s(()=>l("td",null,"-",-1)),ys=s(()=>l("td",null,"-",-1)),Hs=s(()=>l("td",null,"-",-1)),Ts=s(()=>l("td",null,"-",-1)),Es=s(()=>l("td",null,"-",-1)),Zs=[gs,Ss,Cs,fs,Is,ks,Os,ys,Hs,Ts,Es],zs=s(()=>l("tr",{class:"param_head"},[l("td",null,"Zone"),l("td",null,"11"),l("td",null,"12"),l("td",null,"13"),l("td",null,"14"),l("td",null,"15"),l("td",null,"16"),l("td",null,"17"),l("td",null,"18"),l("td",null,"19"),l("td",null,"20")],-1)),Bs=s(()=>l("td",null,"标准值",-1)),Vs={class:"monitor_row"},As=s(()=>l("td",null,"采集值",-1)),xs=s(()=>l("td",null,"-",-1)),Qs=s(()=>l("td",null,"-",-1)),Fs=s(()=>l("td",null,"-",-1)),Us=s(()=>l("td",null,"-",-1)),qs=s(()=>l("td",null,"-",-1)),Ns=s(()=>l("td",null,"-",-1)),Ls=s(()=>l("td",null,"-",-1)),Rs=s(()=>l("td",null,"-",-1)),Gs=s(()=>l("td",null,"-",-1)),Js=s(()=>l("td",null,"-",-1)),Ks=[As,xs,Qs,Fs,Us,qs,Ns,Ls,Rs,Gs,Js],Ws={style:{width:"100%"}},Xs=s(()=>l("thead",{class:"param_head"},[l("tr",null,[l("td",{colspan:"7"},[l("i",null,"合模")])])],-1)),Ys=s(()=>l("tr",{class:"title"},[l("td",null,"项目"),l("td",null,"范围"),l("td",null,"1段->"),l("td",null,"2段->"),l("td",null,"3段->"),l("td",null,"4段->"),l("td",null,"5段")],-1)),$s=s(()=>l("td",null,"压力",-1)),lo={class:"monitor_row"},to=s(()=>l("td",{colspan:"2"},"采集值",-1)),eo=s(()=>l("td",null,"速度",-1)),ao={class:"monitor_row"},so=s(()=>l("td",{colspan:"2"},"采集值",-1)),oo=s(()=>l("td",null,"位置",-1)),no={class:"monitor_row"},_o=s(()=>l("td",{colspan:"2"},"采集值",-1)),uo={style:{width:"100%"}},ro=s(()=>l("thead",{class:"param_head"},[l("tr",null,[l("td",{colspan:"7"},[l("i",null,"开模")])])],-1)),co=s(()=>l("tr",{class:"title"},[l("td",null,"项目"),l("td",null,"范围"),l("td",null,"5段"),l("td",null,"<-4段"),l("td",null,"<-3段"),l("td",null,"<-2段"),l("td",null,"<-1段")],-1)),po=s(()=>l("td",null,"压力",-1)),vo={class:"monitor_row"},io=s(()=>l("td",{colspan:"2"},"采集值",-1)),ho=s(()=>l("td",null,"速度",-1)),mo={class:"monitor_row"},Mo=s(()=>l("td",{colspan:"2"},"采集值",-1)),Do=s(()=>l("td",null,"位置",-1)),wo={class:"monitor_row"},Po=s(()=>l("td",{colspan:"2"},"采集值",-1)),bo={style:{width:"100%"}},jo=s(()=>l("thead",{class:"param_head"},[l("tr",null,[l("td",{colspan:"5"},[l("i",null,"顶出")])])],-1)),go=s(()=>l("tr",{class:"title"},[l("td",{colspan:"5",style:{"text-align":"left"}},">>顶针前进")],-1)),So=s(()=>l("tr",{class:"title"},[l("td",null,"项目"),l("td",null,"范围"),l("td",null,"1段"),l("td",null,"2段"),l("td",null,"3段")],-1)),Co=s(()=>l("td",null,"前进压力",-1)),fo={class:"monitor_row"},Io=s(()=>l("td",{colspan:"2"},"采集值",-1)),ko=s(()=>l("td",null,"-",-1)),Oo=s(()=>l("td",null,"前进速度",-1)),yo={class:"monitor_row"},Ho=s(()=>l("td",{colspan:"2"},"采集值",-1)),To=s(()=>l("td",null,"-",-1)),Eo=s(()=>l("td",null,"前进位置",-1)),Zo={class:"monitor_row"},zo=s(()=>l("td",{colspan:"2"},"采集值",-1)),Bo=s(()=>l("td",null,"-",-1)),Vo=s(()=>l("tr",null,[l("td",{colspan:"5",style:{"text-align":"left"}},">>顶针后退")],-1)),Ao=s(()=>l("tr",null,[l("td",null,"项目"),l("td",null,"范围"),l("td",null,"1段"),l("td",null,"2段"),l("td",null,"3段")],-1)),xo={class:"monitor_row"},Qo=s(()=>l("td",{colspan:"2"},"采集值",-1)),Fo=s(()=>l("td",null,"1段",-1)),Uo=s(()=>l("td",null,"2段",-1)),qo=s(()=>l("td",null,"3段",-1)),No=[Qo,Fo,Uo,qo],Lo=s(()=>l("td",null,"后退压力",-1)),Ro=s(()=>l("td",null,null,-1)),Go={class:"monitor_row"},Jo=s(()=>l("td",{colspan:"2"},"采集值",-1)),Ko=s(()=>l("td",null,"-",-1)),Wo=s(()=>l("td",null,"后退速度",-1)),Xo=s(()=>l("td",null,null,-1)),Yo={class:"monitor_row"},$o=s(()=>l("td",{colspan:"2"},"采集值",-1)),ld=s(()=>l("td",null,"-",-1)),td=s(()=>l("td",null,"后退位置",-1)),ed=s(()=>l("td",null,null,-1)),ad={class:"monitor_row"},sd=s(()=>l("td",{colspan:"2"},"采集值",-1)),od=s(()=>l("td",null,"-",-1)),dd={style:{width:"100%"}},nd=s(()=>l("thead",{class:"param_head"},[l("tr",null,[l("td",{colspan:"7"},"抽芯")])],-1)),_d=s(()=>l("tr",{class:"title"},[l("td",null,"抽芯号"),l("td",null,"1 IN"),l("td",null,"1 OUT"),l("td",null,"2 IN"),l("td",null,"2 OUT"),l("td",null,"3 IN"),l("td",null,"3 OUT")],-1)),ud=s(()=>l("td",null,"抽芯压力",-1)),rd={class:"monitor_row"},cd=s(()=>l("td",null,"采集值",-1)),pd=s(()=>l("td",null,"抽芯速度",-1)),vd={class:"monitor_row"},id=s(()=>l("td",null,"采集值",-1)),hd=s(()=>l("td",null,"抽芯位置",-1)),md={class:"monitor_row"},Md=s(()=>l("td",null,"采集值",-1)),Dd=s(()=>l("td",null,"抽芯监控",-1)),wd={class:"monitor_row"},Pd=s(()=>l("td",null,"采集值",-1)),bd=s(()=>l("td",null,"-",-1)),jd=s(()=>l("td",null,"-",-1)),gd=s(()=>l("td",null,"-",-1)),Sd=s(()=>l("td",null,"-",-1)),Cd=s(()=>l("td",null,"-",-1)),fd=s(()=>l("td",null,"-",-1)),Id=[Pd,bd,jd,gd,Sd,Cd,fd],kd={style:{width:"100%"}},Od=s(()=>l("thead",{class:"param_head"},[l("tr",null,[l("td",{colspan:"4"},"吹气")])],-1)),yd=s(()=>l("tr",{class:"title"},[l("td",null,"吹气号"),l("td",null,"吹气1"),l("td",null,"吹气2"),l("td",null,"吹气3")],-1)),Hd=s(()=>l("td",null,"吹气开始位置",-1)),Td={class:"monitor_row"},Ed=s(()=>l("td",null,"采集值",-1)),Zd=s(()=>l("td",null,"吹气停止位置",-1)),zd={class:"monitor_row"},Bd=s(()=>l("td",null,"采集值",-1)),Vd=s(()=>l("td",null,"-",-1)),Ad=s(()=>l("td",null,"-",-1)),xd=s(()=>l("td",null,"-",-1)),Qd=[Bd,Vd,Ad,xd],Fd=s(()=>l("td",null,"吹气时间",-1)),Ud={class:"monitor_row"},qd=s(()=>l("td",null,"采集值",-1)),Nd=Yt({__name:"param",props:["queryinfo"],setup(Ml){Rl(10),Rl();const Gl=Ml,d=$t({showbasic:!1,showothers:!0,showmonitor:!0}),t=Rl({}),a=Rl({});le(()=>{Jl()});const ut=Rl(null),rt=Rl(null),Jl=()=>{ql(),Dl(),ut.value=setInterval(()=>{ql()},3e3),rt.value=setInterval(()=>{Dl()},6e4)};te(()=>{clearInterval(ut.value),clearInterval(rt.value)});const Dl=()=>vt(this,null,function*(){const Nl=yield _e({machine_id:Gl.queryinfo.machineid,sku:Gl.queryinfo.sku});Nl.meta.status===200&&(t.value=Nl.data.paraitem[0])}),ql=()=>vt(this,null,function*(){const Nl=yield ue({machineid:Gl.queryinfo.machineid});Nl.meta.status===200&&(a.value=Nl.data)});return(Nl,Pl)=>{const pt=ct("el-switch"),Ll=ct("el-col"),wl=ct("el-row"),Wt=ct("el-form");return ee(),ae(Wt,{ref:"vForm","label-position":"right","label-width":"100px",size:"large",onSubmit:Pl[3]||(Pl[3]=se(()=>{},["prevent"]))},{default:B(()=>[h(wl,null,{default:B(()=>[h(Ll,{md:4,xs:4},{default:B(()=>[it("显示基本信息"),h(pt,{modelValue:d.showbasic,"onUpdate:modelValue":Pl[0]||(Pl[0]=u=>d.showbasic=u)},null,8,["modelValue"])]),_:1}),h(Ll,{md:4,xs:4},{default:B(()=>[it("显示采集信息"),h(pt,{modelValue:d.showmonitor,"onUpdate:modelValue":Pl[1]||(Pl[1]=u=>d.showmonitor=u)},null,8,["modelValue"])]),_:1}),h(Ll,{md:4,xs:4},{default:B(()=>[it("显示所有信息"),h(pt,{modelValue:d.showothers,"onUpdate:modelValue":Pl[2]||(Pl[2]=u=>d.showothers=u)},null,8,["modelValue"])]),_:1}),h(Ll,{md:24,xs:24},{default:B(()=>[n(l("table",re,[ce,l("tbody",null,[l("tr",null,[pe,l("td",ve,e(t.value.sku),1),ie,l("td",null,e(t.value.machine_id)+"号机",1),he,l("td",null,e(t.value.resin),1)]),l("tr",null,[me,l("td",Me,e(t.value.sku_des),1),De,we,Pe,l("td",null,e(t.value.coloran),1)]),l("tr",null,[be,l("td",je,e(t.value.mold_id),1),ge,l("td",null,e(t.value.cavity),1),Se,l("td",null,e(t.value.color),1)]),l("tr",null,[Ce,fe,Ie,ke,Oe,ye,l("td",null,e(t.value.sprue),1),He,l("td",null,e(t.value.plugin),1)]),l("tr",null,[l("td",null,e(t.value.moldcore1),1),l("td",null,e(t.value.moldcore2),1),l("td",null,e(t.value.moldcore3),1),l("td",null,e(t.value.moldcore4),1),Te,l("td",null,e(t.value.unit_weight),1),Ee,l("td",null,e(t.value.total_weight),1)])])],512),[[_,d.showbasic]])]),_:1})]),_:1}),h(wl,null,{default:B(()=>[h(Ll,{span:12,offset:0},{default:B(()=>[h(wl,null,{default:B(()=>{var u,r,c,p,v,i,m,M,D,w,P,b,j,g,S,C,f,I,k,O,y,H,T,E,Z,z;return[l("table",Ze,[l("thead",ze,[l("tr",null,[l("td",Be,[Ve,n(l("span",null,"采集时间："+e((r=(u=a.value)==null?void 0:u.CycleData)==null?void 0:r.updatetime),513),[[_,d.showmonitor]])])])]),l("tbody",null,[Ae,l("tr",null,[xe,l("td",null,e(t.value.temp_zone1),1),l("td",null,e(t.value.temp_t1),1),l("td",null,e(t.value.temp_t2),1),l("td",null,e(t.value.temp_t3),1),l("td",null,e(t.value.temp_t4),1),l("td",null,e(t.value.temp_t5),1),l("td",null,e(t.value.temp_t6),1),l("td",null,e(t.value.temp_feed),1),l("td",null,e(t.value.temp_oil),1)]),n(l("tr",Qe,[Fe,Ue,l("td",{class:o(Math.abs(((p=(c=a.value)==null?void 0:c.CycleData)==null?void 0:p.Z_QDTEMPZ01)-t.value.temp_t1)>30?"alert_state":"")},e((i=(v=a.value)==null?void 0:v.CycleData)==null?void 0:i.Z_QDTEMPZ01),3),l("td",{class:o(Math.abs(((M=(m=a.value)==null?void 0:m.CycleData)==null?void 0:M.Z_QDTEMPZ02)-t.value.temp_t2)>30?"alert_state":"")},e((w=(D=a.value)==null?void 0:D.CycleData)==null?void 0:w.Z_QDTEMPZ02),3),l("td",{class:o(Math.abs(((b=(P=a.value)==null?void 0:P.CycleData)==null?void 0:b.Z_QDTEMPZ03)-t.value.temp_t3)>30?"alert_state":"")},e((g=(j=a.value)==null?void 0:j.CycleData)==null?void 0:g.Z_QDTEMPZ03),3),l("td",{class:o(Math.abs(((C=(S=a.value)==null?void 0:S.CycleData)==null?void 0:C.Z_QDTEMPZ04)-t.value.temp_t4)>30?"alert_state":"")},e((I=(f=a.value)==null?void 0:f.CycleData)==null?void 0:I.Z_QDTEMPZ04),3),l("td",{class:o(Math.abs(((O=(k=a.value)==null?void 0:k.CycleData)==null?void 0:O.Z_QDTEMPZ05)-t.value.temp_t5)>30?"alert_state":"")},e((H=(y=a.value)==null?void 0:y.CycleData)==null?void 0:H.Z_QDTEMPZ05),3),l("td",{class:o(Math.abs(((E=(T=a.value)==null?void 0:T.CycleData)==null?void 0:E.Z_QDTEMPZ06)-t.value.temp_t6)>30?"alert_state":"")},e((z=(Z=a.value)==null?void 0:Z.CycleData)==null?void 0:z.Z_QDTEMPZ06),3),l("td",null,e(t.value.temp_feed),1),l("td",null,e(t.value.temp_oil),1)],512),[[_,d.showmonitor]])])])]}),_:1}),h(wl,null,{default:B(()=>{var u,r,c,p,v,i,m,M,D,w,P,b,j,g,S,C,f,I,k,O,y,H,T,E,Z,z,V,A,x,Q,F,U,q,N,L,R,G,J,K,W,X,Y,$,ll,tl,el,al,sl,ol,dl,nl,_l,ul,rl,cl,pl,vl,il,hl,ml,bl,jl,gl,Sl,Cl,fl,Il,kl,Ol,yl,Hl,Tl,El,Zl,zl,Bl,Vl,Al,xl,Ql,Fl,Ul;return[l("table",qe,[l("thead",Ne,[l("tr",null,[l("td",Le,[Re,n(l("span",null,"采集时间："+e((r=(u=a.value)==null?void 0:u.MoldData)==null?void 0:r.updatetime),513),[[_,d.showmonitor]])])])]),l("tbody",null,[Ge,l("tr",null,[Je,l("td",null,"±"+e(t.value.range_pre_plastic)+"bar",1),l("td",null,e(t.value.pre_plastic_1),1),l("td",null,e(t.value.pre_plastic_2),1),l("td",null,e(t.value.pre_plastic_3),1),l("td",null,e(t.value.pre_plastic_4),1),l("td",null,e(t.value.pre_plastic_5),1)]),n(l("tr",Ke,[We,l("td",{class:o(Math.abs(((p=(c=a.value)==null?void 0:c.MoldData)==null?void 0:p.Plast1Pres)-t.value.pre_plastic_1)>t.value.range_pre_plastic?"alert_state":"")},e((i=(v=a.value)==null?void 0:v.MoldData)==null?void 0:i.Plast1Pres),3),l("td",{class:o(Math.abs(((M=(m=a.value)==null?void 0:m.MoldData)==null?void 0:M.Plast2Pres)-t.value.pre_plastic_2)>t.value.range_pre_plastic?"alert_state":"")},e((w=(D=a.value)==null?void 0:D.MoldData)==null?void 0:w.Plast2Pres),3),l("td",{class:o(Math.abs(((b=(P=a.value)==null?void 0:P.MoldData)==null?void 0:b.Plast3Pres)-t.value.pre_plastic_3)>t.value.range_pre_plastic?"alert_state":"")},e((g=(j=a.value)==null?void 0:j.MoldData)==null?void 0:g.Plast3Pres),3),l("td",{class:o(Math.abs(((C=(S=a.value)==null?void 0:S.MoldData)==null?void 0:C.Plast4Pres)-t.value.pre_plastic_4)>t.value.range_pre_plastic?"alert_state":"")},e((I=(f=a.value)==null?void 0:f.MoldData)==null?void 0:I.Plast4Pres),3),l("td",{class:o(Math.abs(((O=(k=a.value)==null?void 0:k.MoldData)==null?void 0:O.Plast5Pres)-t.value.pre_plastic_5)>t.value.range_pre_plastic?"alert_state":"")},e((H=(y=a.value)==null?void 0:y.MoldData)==null?void 0:H.Plast5Pres),3)],512),[[_,d.showmonitor]]),l("tr",null,[Xe,l("td",null,"± "+e(t.value.range_backpre)+"bar",1),l("td",null,e(t.value.backpre_plastic_1),1),l("td",null,e(t.value.backpre_plastic_2),1),l("td",null,e(t.value.backpre_plastic_3),1),l("td",null,e(t.value.backpre_plastic_4),1),l("td",null,e(t.value.backpre_plastic_5),1)]),n(l("tr",Ye,[$e,l("td",{class:o(Math.abs(((E=(T=a.value)==null?void 0:T.MoldData)==null?void 0:E.Plast1BackPres)-t.value.backpre_plastic_1)>t.value.range_backpre?"alert_state":"")},e((z=(Z=a.value)==null?void 0:Z.MoldData)==null?void 0:z.Plast1BackPres),3),l("td",{class:o(Math.abs(((A=(V=a.value)==null?void 0:V.MoldData)==null?void 0:A.Plast2BackPres)-t.value.backpre_plastic_2)>t.value.range_backpre?"alert_state":"")},e((Q=(x=a.value)==null?void 0:x.MoldData)==null?void 0:Q.Plast2BackPres),3),l("td",{class:o(Math.abs(((U=(F=a.value)==null?void 0:F.MoldData)==null?void 0:U.Plast3BackPres)-t.value.backpre_plastic_3)>t.value.range_backpre?"alert_state":"")},e((N=(q=a.value)==null?void 0:q.MoldData)==null?void 0:N.Plast3BackPres),3),l("td",{class:o(Math.abs(((R=(L=a.value)==null?void 0:L.MoldData)==null?void 0:R.Plast4BackPres)-t.value.backpre_plastic_4)>t.value.range_backpre?"alert_state":"")},e((J=(G=a.value)==null?void 0:G.MoldData)==null?void 0:J.Plast4BackPres),3),l("td",{class:o(Math.abs(((W=(K=a.value)==null?void 0:K.MoldData)==null?void 0:W.Plast5BackPres)-t.value.backpre_plastic_5)>t.value.range_backpre?"alert_state":"")},e((Y=(X=a.value)==null?void 0:X.MoldData)==null?void 0:Y.Plast5BackPres),3)],512),[[_,d.showmonitor]]),l("tr",null,[la,l("td",null,"± "+e(t.value.range_spd_screw)+"mm/s",1),l("td",null,e(t.value.spd_screw_1),1),l("td",null,e(t.value.spd_screw_2),1),l("td",null,e(t.value.spd_screw_3),1),l("td",null,e(t.value.spd_screw_4),1),l("td",null,e(t.value.spd_screw_5),1)]),n(l("tr",ta,[ea,l("td",{class:o(Math.abs(((ll=($=a.value)==null?void 0:$.MoldData)==null?void 0:ll.Plast1Speed)-t.value.spd_screw_1)>t.value.range_spd_screw?"alert_state":"")},e((el=(tl=a.value)==null?void 0:tl.MoldData)==null?void 0:el.Plast1Speed),3),l("td",{class:o(Math.abs(((sl=(al=a.value)==null?void 0:al.MoldData)==null?void 0:sl.Plast2Speed)-t.value.spd_screw_2)>t.value.range_spd_screw?"alert_state":"")},e((dl=(ol=a.value)==null?void 0:ol.MoldData)==null?void 0:dl.Plast2Speed),3),l("td",{class:o(Math.abs(((_l=(nl=a.value)==null?void 0:nl.MoldData)==null?void 0:_l.Plast3Speed)-t.value.spd_screw_3)>t.value.range_spd_screw?"alert_state":"")},e((rl=(ul=a.value)==null?void 0:ul.MoldData)==null?void 0:rl.Plast3Speed),3),l("td",{class:o(Math.abs(((pl=(cl=a.value)==null?void 0:cl.MoldData)==null?void 0:pl.Plast4Speed)-t.value.spd_screw_4)>t.value.range_spd_screw?"alert_state":"")},e((il=(vl=a.value)==null?void 0:vl.MoldData)==null?void 0:il.Plast4Speed),3),l("td",{class:o(Math.abs(((ml=(hl=a.value)==null?void 0:hl.MoldData)==null?void 0:ml.Plast5Speed)-t.value.spd_screw_5)>t.value.range_spd_screw?"alert_state":"")},e((jl=(bl=a.value)==null?void 0:bl.MoldData)==null?void 0:jl.Plast5Speed),3)],512),[[_,d.showmonitor]]),l("tr",null,[aa,l("td",null,"±"+e(t.value.range_swpoint_inj)+"mm/s",1),l("td",null,e(t.value.swpoint_plastic_1),1),l("td",null,e(t.value.swpoint_plastic_2),1),l("td",null,e(t.value.swpoint_plastic_3),1),l("td",null,e(t.value.swpoint_plastic_4),1),l("td",null,e(t.value.swpoint_plastic_5),1)]),n(l("tr",sa,[oa,l("td",{class:o(Math.abs(((Sl=(gl=a.value)==null?void 0:gl.MoldData)==null?void 0:Sl.Plast1Pos)-t.value.swpoint_plastic_1)>t.value.range_swpoint_inj?"alert_state":"")},e((fl=(Cl=a.value)==null?void 0:Cl.MoldData)==null?void 0:fl.Plast1Pos),3),l("td",{class:o(Math.abs(((kl=(Il=a.value)==null?void 0:Il.MoldData)==null?void 0:kl.Plast2Pos)-t.value.swpoint_plastic_2)>t.value.range_swpoint_inj?"alert_state":"")},e((yl=(Ol=a.value)==null?void 0:Ol.MoldData)==null?void 0:yl.Plast2Pos),3),l("td",{class:o(Math.abs(((Tl=(Hl=a.value)==null?void 0:Hl.MoldData)==null?void 0:Tl.Plast3Pos)-t.value.swpoint_plastic_3)>t.value.range_swpoint_inj?"alert_state":"")},e((Zl=(El=a.value)==null?void 0:El.MoldData)==null?void 0:Zl.Plast3Pos),3),l("td",{class:o(Math.abs(((Bl=(zl=a.value)==null?void 0:zl.MoldData)==null?void 0:Bl.Plast4Pos)-t.value.swpoint_plastic_4)>t.value.range_swpoint_inj?"alert_state":"")},e((Al=(Vl=a.value)==null?void 0:Vl.MoldData)==null?void 0:Al.Plast4Pos),3),l("td",{class:o(Math.abs(((Ql=(xl=a.value)==null?void 0:xl.MoldData)==null?void 0:Ql.Plast5Pos)-t.value.swpoint_plastic_5)>t.value.range_swpoint_inj?"alert_state":"")},e((Ul=(Fl=a.value)==null?void 0:Fl.MoldData)==null?void 0:Ul.Plast5Pos),3)],512),[[_,d.showmonitor]])])])]}),_:1}),h(wl,null,{default:B(()=>{var u,r,c,p,v,i;return[l("table",da,[na,l("tr",null,[_a,l("td",null,e(t.value.pre_screwback),1),l("td",null,e(t.value.spd_screwback),1),l("td",null,e(t.value.pos_screwback),1)]),n(l("tr",ua,[ra,l("td",null,e((r=(u=a.value)==null?void 0:u.MoldData)==null?void 0:r.DecompPres),1),l("td",null,e((p=(c=a.value)==null?void 0:c.MoldData)==null?void 0:p.DecompSpeed),1),l("td",null,e((i=(v=a.value)==null?void 0:v.MoldData)==null?void 0:i.DecompPos),1)],512),[[_,d.showmonitor]])])]}),_:1}),h(wl,null,{default:B(()=>{var u,r,c,p,v,i,m,M,D,w,P,b,j,g,S,C,f,I,k,O,y,H,T,E,Z,z,V,A,x,Q,F,U,q,N,L,R,G,J,K,W,X,Y,$,ll,tl,el,al,sl,ol,dl,nl,_l,ul,rl,cl,pl,vl,il,hl,ml,bl,jl,gl,Sl,Cl,fl,Il,kl,Ol,yl,Hl,Tl,El,Zl,zl,Bl,Vl,Al,xl,Ql,Fl,Ul,Kl,Wl,Xl,Yl,$l,lt,tt,et,at,st,ot,dt,nt,_t,ht,mt,Mt,Dt,wt,Pt,bt,jt,gt,St,Ct,ft,It,kt,Ot,yt,Ht,Tt,Et,Zt,zt,Bt,Vt,At,xt,Qt,Ft,Ut,qt,Nt,Lt,Rt,Gt,Jt;return[l("table",ca,[pa,va,l("tr",null,[ia,l("td",null,"±"+e(t.value.range_pre_inj)+"bar",1),l("td",null,e(t.value.pre_inj_1),1),l("td",null,e(t.value.pre_inj_2),1),l("td",null,e(t.value.pre_inj_3),1),l("td",null,e(t.value.pre_inj_4),1),l("td",null,e(t.value.pre_inj_5),1),l("td",null,e(t.value.pre_inj_6),1),l("td",null,e(t.value.pre_inj_7),1),l("td",null,e(t.value.pre_inj_8),1),l("td",null,e(t.value.pre_inj_9),1),l("td",null,e(t.value.pre_inj_10),1)]),n(l("tr",ha,[ma,l("td",{class:o(Math.abs(((r=(u=a.value)==null?void 0:u.MoldData)==null?void 0:r.Inject1Pres)-t.value.pre_inj_1)>t.value.range_pre_inj?"alert_state":"")},e((p=(c=a.value)==null?void 0:c.MoldData)==null?void 0:p.Inject1Pres),3),l("td",{class:o(Math.abs(((i=(v=a.value)==null?void 0:v.MoldData)==null?void 0:i.Inject2Pres)-t.value.pre_inj_2)>t.value.range_pre_inj?"alert_state":"")},e((M=(m=a.value)==null?void 0:m.MoldData)==null?void 0:M.Inject2Pres),3),l("td",{class:o(Math.abs(((w=(D=a.value)==null?void 0:D.MoldData)==null?void 0:w.Inject3Pres)-t.value.pre_inj_3)>t.value.range_pre_inj?"alert_state":"")},e((b=(P=a.value)==null?void 0:P.MoldData)==null?void 0:b.Inject3Pres),3),l("td",{class:o(Math.abs(((g=(j=a.value)==null?void 0:j.MoldData)==null?void 0:g.Inject4Pres)-t.value.pre_inj_4)>t.value.range_pre_inj?"alert_state":"")},e((C=(S=a.value)==null?void 0:S.MoldData)==null?void 0:C.Inject4Pres),3),l("td",{class:o(Math.abs(((I=(f=a.value)==null?void 0:f.MoldData)==null?void 0:I.Inject5Pres)-t.value.pre_inj_5)>t.value.range_pre_inj?"alert_state":"")},e((O=(k=a.value)==null?void 0:k.MoldData)==null?void 0:O.Inject5Pres),3),Ma,Da,wa,Pa,ba],512),[[_,d.showmonitor]]),l("tr",null,[ja,l("td",null,"±"+e(t.value.range_spd_inj)+"mm/s",1),l("td",null,e(t.value.spd_inj_1),1),l("td",null,e(t.value.spd_inj_2),1),l("td",null,e(t.value.spd_inj_3),1),l("td",null,e(t.value.spd_inj_4),1),l("td",null,e(t.value.spd_inj_5),1),l("td",null,e(t.value.spd_inj_6),1),l("td",null,e(t.value.spd_inj_7),1),l("td",null,e(t.value.spd_inj_8),1),l("td",null,e(t.value.spd_inj_9),1),l("td",null,e(t.value.spd_inj_10),1)]),n(l("tr",ga,[Sa,l("td",{class:o(Math.abs(((H=(y=a.value)==null?void 0:y.MoldData)==null?void 0:H.Inject1Speed)-t.value.spd_inj_1)>t.value.range_spd_inj?"alert_state":"")},e((E=(T=a.value)==null?void 0:T.MoldData)==null?void 0:E.Inject1Speed),3),l("td",{class:o(Math.abs(((z=(Z=a.value)==null?void 0:Z.MoldData)==null?void 0:z.Inject2Speed)-t.value.spd_inj_2)>t.value.range_spd_inj?"alert_state":"")},e((A=(V=a.value)==null?void 0:V.MoldData)==null?void 0:A.Inject2Speed),3),l("td",{class:o(Math.abs(((Q=(x=a.value)==null?void 0:x.MoldData)==null?void 0:Q.Inject3Speed)-t.value.spd_inj_3)>t.value.range_spd_inj?"alert_state":"")},e((U=(F=a.value)==null?void 0:F.MoldData)==null?void 0:U.Inject3Speed),3),l("td",{class:o(Math.abs(((N=(q=a.value)==null?void 0:q.MoldData)==null?void 0:N.Inject4Speed)-t.value.spd_inj_4)>t.value.range_spd_inj?"alert_state":"")},e((R=(L=a.value)==null?void 0:L.MoldData)==null?void 0:R.Inject4Speed),3),l("td",{class:o(Math.abs(((J=(G=a.value)==null?void 0:G.MoldData)==null?void 0:J.Inject5Speed)-t.value.spd_inj_5)>t.value.range_spd_inj?"alert_state":"")},e((W=(K=a.value)==null?void 0:K.MoldData)==null?void 0:W.Inject5Speed),3),Ca,fa,Ia,ka,Oa],512),[[_,d.showmonitor]]),l("tr",null,[ya,l("td",null,"±"+e(t.value.range_swpoint_inj)+"mm",1),l("td",null,e(t.value.swpoint_inj_1),1),l("td",null,e(t.value.swpoint_inj_2),1),l("td",null,e(t.value.swpoint_inj_3),1),l("td",null,e(t.value.swpoint_inj_4),1),l("td",null,e(t.value.swpoint_inj_5),1),l("td",null,e(t.value.swpoint_inj_6),1),l("td",null,e(t.value.swpoint_inj_7),1),l("td",null,e(t.value.swpoint_inj_8),1),l("td",null,e(t.value.swpoint_inj_9),1),l("td",null,e(t.value.swpoint_inj_10),1)]),n(l("tr",Ha,[Ta,l("td",{class:o(Math.abs(((Y=(X=a.value)==null?void 0:X.MoldData)==null?void 0:Y.Inject1Pos)-t.value.swpoint_inj_1)>t.value.range_swpoint_inj?"alert_state":"")},e((ll=($=a.value)==null?void 0:$.MoldData)==null?void 0:ll.Inject1Pos),3),l("td",{class:o(Math.abs(((el=(tl=a.value)==null?void 0:tl.MoldData)==null?void 0:el.Inject2Pos)-t.value.swpoint_inj_2)>t.value.range_swpoint_inj?"alert_state":"")},e((sl=(al=a.value)==null?void 0:al.MoldData)==null?void 0:sl.Inject2Pos),3),l("td",{class:o(Math.abs(((dl=(ol=a.value)==null?void 0:ol.MoldData)==null?void 0:dl.Inject3Pos)-t.value.swpoint_inj_3)>t.value.range_swpoint_inj?"alert_state":"")},e((_l=(nl=a.value)==null?void 0:nl.MoldData)==null?void 0:_l.Inject3Pos),3),l("td",{class:o(Math.abs(((rl=(ul=a.value)==null?void 0:ul.MoldData)==null?void 0:rl.Inject4Pos)-t.value.swpoint_inj_4)>t.value.range_swpoint_inj?"alert_state":"")},e((pl=(cl=a.value)==null?void 0:cl.MoldData)==null?void 0:pl.Inject4Pos),3),l("td",{class:o(Math.abs(((il=(vl=a.value)==null?void 0:vl.MoldData)==null?void 0:il.Inject5Pos)-t.value.swpoint_inj_5)>t.value.range_swpoint_inj?"alert_state":"")},e((ml=(hl=a.value)==null?void 0:hl.MoldData)==null?void 0:ml.Inject5Pos),3),Ea,Za,za,Ba,Va],512),[[_,d.showmonitor]])]),l("table",Aa,[l("tr",xa,[Qa,l("td",Fa,"±"+e(t.value.range_pre_hold)+"bar",1),Ua,qa,Na,La,Ra,Ga,Ja,Ka,Wa,Xa]),l("tr",null,[l("td",null,e(t.value.pre_hold_1),1),l("td",null,e(t.value.pre_hold_2),1),l("td",null,e(t.value.pre_hold_3),1),l("td",null,e(t.value.pre_hold_4),1),l("td",null,e(t.value.pre_hold_5),1),l("td",null,e(t.value.spd_hold_1),1),l("td",null,e(t.value.spd_hold_2),1),l("td",null,e(t.value.spd_hold_3),1),l("td",null,e(t.value.spd_hold_4),1),l("td",null,e(t.value.spd_hold_5),1)]),n(l("tr",Ya,[$a,l("td",{class:o(Math.abs(((jl=(bl=a.value)==null?void 0:bl.MoldData)==null?void 0:jl.Hold1Pres)-t.value.pre_hold_1)>t.value.range_pre_hold?"alert_state":"")},e((Sl=(gl=a.value)==null?void 0:gl.MoldData)==null?void 0:Sl.Hold1Pres),3),l("td",{class:o(Math.abs(((fl=(Cl=a.value)==null?void 0:Cl.MoldData)==null?void 0:fl.Hold2Pres)-t.value.pre_hold_2)>t.value.range_pre_hold?"alert_state":"")},e((kl=(Il=a.value)==null?void 0:Il.MoldData)==null?void 0:kl.Hold2Pres),3),l("td",{class:o(Math.abs(((yl=(Ol=a.value)==null?void 0:Ol.MoldData)==null?void 0:yl.Hold3Pres)-t.value.pre_hold_3)>t.value.range_pre_hold?"alert_state":"")},e((Tl=(Hl=a.value)==null?void 0:Hl.MoldData)==null?void 0:Tl.Hold3Pres),3),l("td",{class:o(Math.abs(((Zl=(El=a.value)==null?void 0:El.MoldData)==null?void 0:Zl.Hold4Pres)-t.value.pre_hold_4)>t.value.range_pre_hold?"alert_state":"")},e((Bl=(zl=a.value)==null?void 0:zl.MoldData)==null?void 0:Bl.Hold4Pres),3),l("td",{class:o(Math.abs(((Al=(Vl=a.value)==null?void 0:Vl.MoldData)==null?void 0:Al.Hold5Pres)-t.value.pre_hold_5)>t.value.range_pre_hold?"alert_state":"")},e((Ql=(xl=a.value)==null?void 0:xl.MoldData)==null?void 0:Ql.Hold5Pres),3),l("td",{class:o(Math.abs(((Ul=(Fl=a.value)==null?void 0:Fl.MoldData)==null?void 0:Ul.Hold1Speed)-t.value.spd_hold_1)>t.value.range_pre_hold?"alert_state":"")},e((Wl=(Kl=a.value)==null?void 0:Kl.MoldData)==null?void 0:Wl.Hold1Speed),3),l("td",{class:o(Math.abs(((Yl=(Xl=a.value)==null?void 0:Xl.MoldData)==null?void 0:Yl.Hold2Speed)-t.value.spd_hold_2)>t.value.range_pre_hold?"alert_state":"")},e((lt=($l=a.value)==null?void 0:$l.MoldData)==null?void 0:lt.Hold2Speed),3),l("td",{class:o(Math.abs(((et=(tt=a.value)==null?void 0:tt.MoldData)==null?void 0:et.Hold3Speed)-t.value.spd_hold_3)>t.value.range_pre_hold?"alert_state":"")},e((st=(at=a.value)==null?void 0:at.MoldData)==null?void 0:st.Hold3Speed),3),l("td",{class:o(Math.abs(((dt=(ot=a.value)==null?void 0:ot.MoldData)==null?void 0:dt.Hold4Speed)-t.value.spd_hold_4)>t.value.range_pre_hold?"alert_state":"")},e((_t=(nt=a.value)==null?void 0:nt.MoldData)==null?void 0:_t.Hold4Speed),3),l("td",{class:o(Math.abs(((mt=(ht=a.value)==null?void 0:ht.MoldData)==null?void 0:mt.Hold5Speed)-t.value.spd_hold_5)>t.value.range_pre_hold?"alert_state":"")},e((Dt=(Mt=a.value)==null?void 0:Mt.MoldData)==null?void 0:Dt.Hold5Speed),3)],512),[[_,d.showmonitor]])]),l("table",ls,[ts,l("tr",null,[l("td",null,"±"+e(t.value.range_time_holdpre)+"S",1),l("td",es,e(t.value.time_holdpre_1),1),l("td",as,e(t.value.time_holdpre_2),1),l("td",ss,e(t.value.time_holdpre_3),1),l("td",os,e(t.value.time_holdpre_4),1),l("td",ds,e(t.value.time_holdpre_5),1)]),n(l("tr",ns,[_s,l("td",{colspan:"2",class:o(Math.abs(((Pt=(wt=a.value)==null?void 0:wt.MoldData)==null?void 0:Pt.Hold1Time)-t.value.time_holdpre_1)>t.value.range_time_holdpre?"alert_state":"")},e((jt=(bt=a.value)==null?void 0:bt.MoldData)==null?void 0:jt.Hold1Time),3),l("td",{colspan:"2",class:o(Math.abs(((St=(gt=a.value)==null?void 0:gt.MoldData)==null?void 0:St.Hold2Time)-t.value.time_holdpre_2)>t.value.range_time_holdpre?"alert_state":"")},e((ft=(Ct=a.value)==null?void 0:Ct.MoldData)==null?void 0:ft.Hold2Time),3),l("td",{colspan:"2",class:o(Math.abs(((kt=(It=a.value)==null?void 0:It.MoldData)==null?void 0:kt.Hold3Time)-t.value.time_holdpre_3)>t.value.range_time_holdpre?"alert_state":"")},e((yt=(Ot=a.value)==null?void 0:Ot.MoldData)==null?void 0:yt.Hold3Time),3),l("td",{colspan:"2",class:o(Math.abs(((Tt=(Ht=a.value)==null?void 0:Ht.MoldData)==null?void 0:Tt.Hold4Time)-t.value.time_holdpre_4)>t.value.range_time_holdpre?"alert_state":"")},e((Zt=(Et=a.value)==null?void 0:Et.MoldData)==null?void 0:Zt.Hold4Time),3),l("td",{colspan:"2",class:o(Math.abs(((Bt=(zt=a.value)==null?void 0:zt.MoldData)==null?void 0:Bt.Hold5Time)-t.value.time_holdpre_5)>t.value.range_time_holdpre?"alert_state":"")},e((At=(Vt=a.value)==null?void 0:Vt.MoldData)==null?void 0:At.Hold5Time),3)],512),[[_,d.showmonitor]])]),l("table",us,[rs,l("tr",null,[cs,l("td",null,e(t.value.time_injdelay),1),l("td",null,"±"+e(t.value.range_time_injdelay)+"s",1),l("td",null,e(t.value.time_inj),1),l("td",null,"±"+e(t.value.range_time_inj)+"s",1),l("td",null,e(t.value.time_cooling),1),l("td",null,"±"+e(t.value.range_time_cooling)+"s",1),l("td",null,e(t.value.time_cycle),1),l("td",null,"±"+e(t.value.range_time_cycle)+"s",1),l("td",ps,e(t.value.mode_hold),1)]),n(l("tr",vs,[is,hs,l("td",{colspan:"2",class:o(Math.abs(((Qt=(xt=a.value)==null?void 0:xt.MoldData)==null?void 0:Qt.MaxInjTime)-t.value.time_inj)>t.value.range_time_injdelay?"alert_state":"")},e((Ut=(Ft=a.value)==null?void 0:Ft.MoldData)==null?void 0:Ut.MaxInjTime),3),l("td",{colspan:"2",class:o(Math.abs(((Nt=(qt=a.value)==null?void 0:qt.MoldData)==null?void 0:Nt.CoolingTime)-t.value.time_cooling)>t.value.range_time_cooling?"alert_state":"")},e((Rt=(Lt=a.value)==null?void 0:Lt.MoldData)==null?void 0:Rt.CoolingTime),3),ms,l("td",Ms,e((Jt=(Gt=a.value)==null?void 0:Gt.MoldData)==null?void 0:Jt.HoldChange),1)],512),[[_,d.showmonitor]])]),l("table",Ds,[ws,l("tbody",null,[Ps,l("tr",null,[bs,l("td",null,e(t.value.hotrunner_zone1),1),l("td",null,e(t.value.hotrunner_zone2),1),l("td",null,e(t.value.hotrunner_zone3),1),l("td",null,e(t.value.hotrunner_zone4),1),l("td",null,e(t.value.hotrunner_zone5),1),l("td",null,e(t.value.hotrunner_zone6),1),l("td",null,e(t.value.hotrunner_zone7),1),l("td",null,e(t.value.hotrunner_zone8),1),l("td",null,e(t.value.hotrunner_zone9),1),l("td",null,e(t.value.hotrunner_zone10),1)]),n(l("tr",js,Zs,512),[[_,d.showmonitor]]),zs,l("tr",null,[Bs,l("td",null,e(t.value.hotrunner_zone11),1),l("td",null,e(t.value.hotrunner_zone12),1),l("td",null,e(t.value.hotrunner_zone13),1),l("td",null,e(t.value.hotrunner_zone14),1),l("td",null,e(t.value.hotrunner_zone15),1),l("td",null,e(t.value.hotrunner_zone16),1),l("td",null,e(t.value.hotrunner_zone17),1),l("td",null,e(t.value.hotrunner_zone18),1),l("td",null,e(t.value.hotrunner_zone19),1),l("td",null,e(t.value.hotrunner_zone20),1)]),n(l("tr",Vs,Ks,512),[[_,d.showmonitor]])])])]}),_:1})]),_:1}),h(Ll,{span:12,offset:0},{default:B(()=>[h(wl,null,{default:B(()=>{var u,r,c,p,v,i,m,M,D,w,P,b,j,g,S,C,f,I,k,O,y,H,T,E,Z,z,V,A,x,Q,F,U,q,N,L,R,G,J,K,W,X,Y,$,ll,tl,el,al,sl,ol,dl,nl,_l,ul,rl,cl,pl,vl,il,hl,ml;return[l("table",Ws,[Xs,l("tbody",null,[Ys,l("tr",null,[$s,l("td",null,"±"+e(t.value.range_pre_moldclose)+"bar",1),l("td",null,e(t.value.pre_moldclose_1),1),l("td",null,e(t.value.pre_moldclose_2),1),l("td",null,e(t.value.pre_moldclose_3),1),l("td",null,e(t.value.pre_moldclose_4),1),l("td",null,e(t.value.pre_moldclose_5),1)]),n(l("tr",lo,[to,l("td",{class:o(Math.abs(((r=(u=a.value)==null?void 0:u.MoldData)==null?void 0:r.Clamp1Pres)-t.value.pre_moldclose_1)>t.value.range_pre_moldclose?"alert_state":"")},e((p=(c=a.value)==null?void 0:c.MoldData)==null?void 0:p.Clamp1Pres),3),l("td",{class:o(Math.abs(((i=(v=a.value)==null?void 0:v.MoldData)==null?void 0:i.Clamp2Pres)-t.value.pre_moldclose_2)>t.value.range_pre_moldclose?"alert_state":"")},e((M=(m=a.value)==null?void 0:m.MoldData)==null?void 0:M.Clamp2Pres),3),l("td",{class:o(Math.abs(((w=(D=a.value)==null?void 0:D.MoldData)==null?void 0:w.Clamp3Pres)-t.value.pre_moldclose_3)>t.value.range_pre_moldclose?"alert_state":"")},e((b=(P=a.value)==null?void 0:P.MoldData)==null?void 0:b.Clamp3Pres),3),l("td",{class:o(Math.abs(((g=(j=a.value)==null?void 0:j.MoldData)==null?void 0:g.ClampLpPres)-t.value.pre_moldclose_4)>t.value.range_pre_moldclose?"alert_state":"")},e((C=(S=a.value)==null?void 0:S.MoldData)==null?void 0:C.ClampLpPres),3),l("td",{class:o(Math.abs(((I=(f=a.value)==null?void 0:f.MoldData)==null?void 0:I.ClampHpPres)-t.value.pre_moldclose_5)>t.value.range_pre_moldclose?"alert_state":"")},e((O=(k=a.value)==null?void 0:k.MoldData)==null?void 0:O.ClampHpPres),3)],512),[[_,d.showmonitor]]),l("tr",null,[eo,l("td",null,"±"+e(t.value.range_spd_moldclose)+"mm/s",1),l("td",null,e(t.value.spd_moldclose_1),1),l("td",null,e(t.value.spd_moldclose_2),1),l("td",null,e(t.value.spd_moldclose_3),1),l("td",null,e(t.value.spd_moldclose_4),1),l("td",null,e(t.value.spd_moldclose_5),1)]),n(l("tr",ao,[so,l("td",{class:o(Math.abs(((H=(y=a.value)==null?void 0:y.MoldData)==null?void 0:H.Clamp1Speed)-t.value.spd_moldclose_1)>t.value.range_spd_moldclose?"alert_state":"")},e((E=(T=a.value)==null?void 0:T.MoldData)==null?void 0:E.Clamp1Speed),3),l("td",{class:o(Math.abs(((z=(Z=a.value)==null?void 0:Z.MoldData)==null?void 0:z.Clamp2Speed)-t.value.spd_moldclose_2)>t.value.range_spd_moldclose?"alert_state":"")},e((A=(V=a.value)==null?void 0:V.MoldData)==null?void 0:A.Clamp2Speed),3),l("td",{class:o(Math.abs(((Q=(x=a.value)==null?void 0:x.MoldData)==null?void 0:Q.Clamp3Speed)-t.value.spd_moldclose_3)>t.value.range_spd_moldclose?"alert_state":"")},e((U=(F=a.value)==null?void 0:F.MoldData)==null?void 0:U.Clamp3Speed),3),l("td",{class:o(Math.abs(((N=(q=a.value)==null?void 0:q.MoldData)==null?void 0:N.ClampLpSpeed)-t.value.spd_moldclose_4)>t.value.range_spd_moldclose?"alert_state":"")},e((R=(L=a.value)==null?void 0:L.MoldData)==null?void 0:R.ClampLpSpeed),3),l("td",{class:o(Math.abs(((J=(G=a.value)==null?void 0:G.MoldData)==null?void 0:J.ClampHpSpeed)-t.value.spd_moldclose_5)>t.value.range_spd_moldclose?"alert_state":"")},e((W=(K=a.value)==null?void 0:K.MoldData)==null?void 0:W.ClampHpSpeed),3)],512),[[_,d.showmonitor]]),l("tr",null,[oo,l("td",null,"±"+e(t.value.range_pos_moldclose)+"mm",1),l("td",null,e(t.value.pos_moldclose_1),1),l("td",null,e(t.value.pos_moldclose_2),1),l("td",null,e(t.value.pos_moldclose_3),1),l("td",null,e(t.value.pos_moldclose_4),1),l("td",null,e(t.value.pos_moldclose_5),1)]),n(l("tr",no,[_o,l("td",{class:o(Math.abs(((Y=(X=a.value)==null?void 0:X.MoldData)==null?void 0:Y.Clamp1Pos)-t.value.spd_moldclose_1)>t.value.range_spd_moldclose?"alert_state":"")},e((ll=($=a.value)==null?void 0:$.MoldData)==null?void 0:ll.Clamp1Pos),3),l("td",{class:o(Math.abs(((el=(tl=a.value)==null?void 0:tl.MoldData)==null?void 0:el.Clamp2Pos)-t.value.spd_moldclose_2)>t.value.range_spd_moldclose?"alert_state":"")},e((sl=(al=a.value)==null?void 0:al.MoldData)==null?void 0:sl.Clamp2Pos),3),l("td",{class:o(Math.abs(((dl=(ol=a.value)==null?void 0:ol.MoldData)==null?void 0:dl.Clamp3Pos)-t.value.spd_moldclose_3)>t.value.range_spd_moldclose?"alert_state":"")},e((_l=(nl=a.value)==null?void 0:nl.MoldData)==null?void 0:_l.Clamp3Pos),3),l("td",{class:o(Math.abs(((rl=(ul=a.value)==null?void 0:ul.MoldData)==null?void 0:rl.ClampLpPos)-t.value.spd_moldclose_4)>t.value.range_spd_moldclose?"alert_state":"")},e((pl=(cl=a.value)==null?void 0:cl.MoldData)==null?void 0:pl.ClampLpPos),3),l("td",{class:o(Math.abs(((il=(vl=a.value)==null?void 0:vl.MoldData)==null?void 0:il.ClampHpPos)-t.value.spd_moldclose_5)>t.value.range_spd_moldclose?"alert_state":"")},e((ml=(hl=a.value)==null?void 0:hl.MoldData)==null?void 0:ml.ClampHpPos),3)],512),[[_,d.showmonitor]])])])]}),_:1}),h(wl,null,{default:B(()=>{var u,r,c,p,v,i,m,M,D,w,P,b,j,g,S,C,f,I,k,O,y,H,T,E,Z,z,V,A,x,Q,F,U,q,N,L,R,G,J,K,W,X,Y,$,ll,tl,el,al,sl,ol,dl,nl,_l,ul,rl,cl,pl,vl,il,hl,ml;return[l("table",uo,[ro,l("tbody",null,[co,l("tr",null,[po,l("td",null,"±"+e(t.value.range_pre_moldopen)+"bar",1),l("td",null,e(t.value.pre_moldopen_5),1),l("td",null,e(t.value.pre_moldopen_4),1),l("td",null,e(t.value.pre_moldopen_3),1),l("td",null,e(t.value.pre_moldopen_2),1),l("td",null,e(t.value.pre_moldopen_1),1)]),n(l("tr",vo,[io,l("td",{class:o(Math.abs(((r=(u=a.value)==null?void 0:u.MoldData)==null?void 0:r.Open1Pres)-t.value.pre_moldopen_1)>t.value.range_pre_moldopen?"alert_state":"")},e((p=(c=a.value)==null?void 0:c.MoldData)==null?void 0:p.Open1Pres),3),l("td",{class:o(Math.abs(((i=(v=a.value)==null?void 0:v.MoldData)==null?void 0:i.Open2Pres)-t.value.pre_moldopen_2)>t.value.range_pre_moldopen?"alert_state":"")},e((M=(m=a.value)==null?void 0:m.MoldData)==null?void 0:M.Open2Pres),3),l("td",{class:o(Math.abs(((w=(D=a.value)==null?void 0:D.MoldData)==null?void 0:w.Open3Pres)-t.value.pre_moldopen_3)>t.value.range_pre_moldopen?"alert_state":"")},e((b=(P=a.value)==null?void 0:P.MoldData)==null?void 0:b.Open3Pres),3),l("td",{class:o(Math.abs(((g=(j=a.value)==null?void 0:j.MoldData)==null?void 0:g.OpenFastPres)-t.value.pre_moldopen_4)>t.value.range_pre_moldopen?"alert_state":"")},e((C=(S=a.value)==null?void 0:S.MoldData)==null?void 0:C.OpenFastPres),3),l("td",{class:o(Math.abs(((I=(f=a.value)==null?void 0:f.MoldData)==null?void 0:I.OpenSlowPres)-t.value.pre_moldopen_5)>t.value.range_pre_moldopen?"alert_state":"")},e((O=(k=a.value)==null?void 0:k.MoldData)==null?void 0:O.OpenSlowPres),3)],512),[[_,d.showmonitor]]),l("tr",null,[ho,l("td",null,"±"+e(t.value.range_spd_moldopen)+"mm/s",1),l("td",null,e(t.value.spd_moldopen_5),1),l("td",null,e(t.value.spd_moldopen_4),1),l("td",null,e(t.value.spd_moldopen_3),1),l("td",null,e(t.value.spd_moldopen_2),1),l("td",null,e(t.value.spd_moldopen_1),1)]),n(l("tr",mo,[Mo,l("td",{class:o(Math.abs(((H=(y=a.value)==null?void 0:y.MoldData)==null?void 0:H.Open1Speed)-t.value.spd_moldopen_1)>t.value.range_spd_moldopen?"alert_state":"")},e((E=(T=a.value)==null?void 0:T.MoldData)==null?void 0:E.Open1Speed),3),l("td",{class:o(Math.abs(((z=(Z=a.value)==null?void 0:Z.MoldData)==null?void 0:z.Open2Speed)-t.value.spd_moldopen_2)>t.value.range_spd_moldopen?"alert_state":"")},e((A=(V=a.value)==null?void 0:V.MoldData)==null?void 0:A.Open2Speed),3),l("td",{class:o(Math.abs(((Q=(x=a.value)==null?void 0:x.MoldData)==null?void 0:Q.Open3Speed)-t.value.spd_moldopen_3)>t.value.range_spd_moldopen?"alert_state":"")},e((U=(F=a.value)==null?void 0:F.MoldData)==null?void 0:U.Open3Speed),3),l("td",{class:o(Math.abs(((N=(q=a.value)==null?void 0:q.MoldData)==null?void 0:N.OpenFastSpeed)-t.value.spd_moldopen_4)>t.value.range_spd_moldopen?"alert_state":"")},e((R=(L=a.value)==null?void 0:L.MoldData)==null?void 0:R.OpenFastSpeed),3),l("td",{class:o(Math.abs(((J=(G=a.value)==null?void 0:G.MoldData)==null?void 0:J.OpenSlowSpeed)-t.value.spd_moldopen_5)>t.value.range_spd_moldopen?"alert_state":"")},e((W=(K=a.value)==null?void 0:K.MoldData)==null?void 0:W.OpenSlowSpeed),3)],512),[[_,d.showmonitor]]),l("tr",null,[Do,l("td",null,"±"+e(t.value.range_pos_moldopen)+"mm",1),l("td",null,e(t.value.pos_moldopen_5),1),l("td",null,e(t.value.pos_moldopen_4),1),l("td",null,e(t.value.pos_moldopen_3),1),l("td",null,e(t.value.pos_moldopen_2),1),l("td",null,e(t.value.pos_moldopen_1),1)]),n(l("tr",wo,[Po,l("td",{class:o(Math.abs(((Y=(X=a.value)==null?void 0:X.MoldData)==null?void 0:Y.Open1Pos)-t.value.spd_moldopen_1)>t.value.range_spd_moldopen?"alert_state":"")},e((ll=($=a.value)==null?void 0:$.MoldData)==null?void 0:ll.Open1Pos),3),l("td",{class:o(Math.abs(((el=(tl=a.value)==null?void 0:tl.MoldData)==null?void 0:el.Open2Pos)-t.value.spd_moldopen_2)>t.value.range_spd_moldopen?"alert_state":"")},e((sl=(al=a.value)==null?void 0:al.MoldData)==null?void 0:sl.Open2Pos),3),l("td",{class:o(Math.abs(((dl=(ol=a.value)==null?void 0:ol.MoldData)==null?void 0:dl.Open3Pos)-t.value.spd_moldopen_3)>t.value.range_spd_moldopen?"alert_state":"")},e((_l=(nl=a.value)==null?void 0:nl.MoldData)==null?void 0:_l.Open3Pos),3),l("td",{class:o(Math.abs(((rl=(ul=a.value)==null?void 0:ul.MoldData)==null?void 0:rl.OpenFastPos)-t.value.spd_moldopen_4)>t.value.range_spd_moldopen?"alert_state":"")},e((pl=(cl=a.value)==null?void 0:cl.MoldData)==null?void 0:pl.OpenFastPos),3),l("td",{class:o(Math.abs(((il=(vl=a.value)==null?void 0:vl.MoldData)==null?void 0:il.OpenSlowPos)-t.value.spd_moldopen_5)>t.value.range_spd_moldopen?"alert_state":"")},e((ml=(hl=a.value)==null?void 0:hl.MoldData)==null?void 0:ml.OpenSlowPos),3)],512),[[_,d.showmonitor]])])])]}),_:1}),h(wl,null,{default:B(()=>{var u,r,c,p,v,i,m,M,D,w,P,b,j,g,S,C,f,I,k,O,y,H,T,E,Z,z,V,A,x,Q,F,U,q,N,L,R,G,J,K,W,X,Y,$,ll,tl,el,al,sl,ol,dl,nl,_l,ul,rl,cl,pl,vl,il,hl,ml,bl,jl,gl,Sl,Cl,fl,Il,kl,Ol,yl,Hl,Tl,El,Zl,zl,Bl,Vl,Al,xl,Ql,Fl,Ul,Kl,Wl,Xl,Yl,$l,lt,tt,et,at,st,ot,dt,nt,_t;return[l("table",bo,[jo,l("tbody",null,[go,So,l("tr",null,[Co,l("td",null,"±"+e(t.value.range_pre_ejectorforw)+"bar",1),l("td",null,e(t.value.pre_ejectorforw_1),1),l("td",null,e(t.value.pre_ejectorforw_2),1),l("td",null,e(t.value.pre_ejectorforw_3),1)]),n(l("tr",fo,[Io,l("td",{class:o(Math.abs(((r=(u=a.value)==null?void 0:u.MoldData)==null?void 0:r.EjectOut1Pres)-t.value.pre_ejectorforw_1)>t.value.range_pre_ejectorforw?"alert_state":"")},e((p=(c=a.value)==null?void 0:c.MoldData)==null?void 0:p.EjectOut1Pres),3),l("td",{class:o(Math.abs(((i=(v=a.value)==null?void 0:v.MoldData)==null?void 0:i.EjectOut2Pres)-t.value.pre_ejectorforw_2)>t.value.range_pre_ejectorforw?"alert_state":"")},e((M=(m=a.value)==null?void 0:m.MoldData)==null?void 0:M.EjectOut2Pres),3),ko],512),[[_,d.showmonitor]]),l("tr",null,[Oo,l("td",null,"±"+e(t.value.range_spd_ejectorforw)+"mm/s",1),l("td",null,e(t.value.spd_ejectorforw_1),1),l("td",null,e(t.value.spd_ejectorforw_2),1),l("td",null,e(t.value.spd_ejectorforw_3),1)]),n(l("tr",yo,[Ho,l("td",{class:o(Math.abs(((w=(D=a.value)==null?void 0:D.MoldData)==null?void 0:w.EjectOut1Speed)-t.value.spd_ejectorforw_1)>t.value.range_spd_ejectorforw?"alert_state":"")},e((b=(P=a.value)==null?void 0:P.MoldData)==null?void 0:b.EjectOut1Speed),3),l("td",{class:o(Math.abs(((g=(j=a.value)==null?void 0:j.MoldData)==null?void 0:g.EjectOut2Speed)-t.value.spd_ejectorforw_2)>t.value.range_spd_ejectorforw?"alert_state":"")},e((C=(S=a.value)==null?void 0:S.MoldData)==null?void 0:C.EjectOut2Speed),3),To],512),[[_,d.showmonitor]]),l("tr",null,[Eo,l("td",null,"±"+e(t.value.range_pos_ejectorforw)+"mm",1),l("td",null,e(t.value.pos_ejectorforw_1),1),l("td",null,e(t.value.pos_ejectorforw_2),1),l("td",null,e(t.value.pos_ejectorforw_3),1)]),n(l("tr",Zo,[zo,l("td",{class:o(Math.abs(((I=(f=a.value)==null?void 0:f.MoldData)==null?void 0:I.EjectOut1Pos)-t.value.pos_ejectorforw_1)>t.value.range_pos_ejectorforw?"alert_state":"")},e((O=(k=a.value)==null?void 0:k.MoldData)==null?void 0:O.EjectOut1Pos),3),l("td",{class:o(Math.abs(((H=(y=a.value)==null?void 0:y.MoldData)==null?void 0:H.EjectOut2Pos)-t.value.pos_ejectorforw_2)>t.value.range_pos_ejectorforw?"alert_state":"")},e((E=(T=a.value)==null?void 0:T.MoldData)==null?void 0:E.EjectOut2Pos),3),Bo],512),[[_,d.showmonitor]]),Vo,Ao,n(l("tr",xo,No,512),[[_,d.showmonitor]]),l("tr",null,[Lo,l("td",null,"±"+e(t.value.range_pre_ejectorbackw)+"bar",1),l("td",null,e(t.value.pre_ejectorbackw_1),1),l("td",null,e(t.value.pre_ejectorbackw_2),1),Ro]),n(l("tr",Go,[Jo,l("td",{class:o(Math.abs(((z=(Z=a.value)==null?void 0:Z.MoldData)==null?void 0:z.EjectIn1Pres)-t.value.pre_ejectorbackw_1)>t.value.range_pre_ejectorbackw?"alert_state":"")},e((A=(V=a.value)==null?void 0:V.MoldData)==null?void 0:A.EjectIn1Pres),3),l("td",{class:o(Math.abs(((Q=(x=a.value)==null?void 0:x.MoldData)==null?void 0:Q.EjectIn2Pres)-t.value.pre_ejectorbackw_2)>t.value.range_pre_ejectorbackw?"alert_state":"")},e((U=(F=a.value)==null?void 0:F.MoldData)==null?void 0:U.EjectIn2Pres),3),Ko],512),[[_,d.showmonitor]]),l("tr",null,[Wo,l("td",null,"±"+e(t.value.range_spd_ejectorbackw)+"mm/s",1),l("td",null,e(t.value.spd_ejectorbackw_1),1),l("td",null,e(t.value.spd_ejectorbackw_2),1),Xo]),n(l("tr",Yo,[$o,l("td",{class:o(Math.abs(((N=(q=a.value)==null?void 0:q.MoldData)==null?void 0:N.EjectIn1Speed)-t.value.spd_ejectorbackw_1)>t.value.range_spd_ejectorbackw?"alert_state":"")},e((R=(L=a.value)==null?void 0:L.MoldData)==null?void 0:R.EjectIn1Speed),3),l("td",{class:o(Math.abs(((J=(G=a.value)==null?void 0:G.MoldData)==null?void 0:J.EjectIn2Speed)-t.value.spd_ejectorbackw_2)>t.value.range_spd_ejectorbackw?"alert_state":"")},e((W=(K=a.value)==null?void 0:K.MoldData)==null?void 0:W.EjectIn2Speed),3),ld],512),[[_,d.showmonitor]]),l("tr",null,[td,l("td",null,"±"+e(t.value.range_pos_ejectorbackw)+"mm",1),l("td",null,e(t.value.pos_ejectorbackw_1),1),l("td",null,e(t.value.pos_ejectorbackw_2),1),ed]),n(l("tr",ad,[sd,l("td",{class:o(Math.abs(((Y=(X=a.value)==null?void 0:X.MoldData)==null?void 0:Y.EjectIn1Pos)-t.value.pos_ejectorbackw_1)>t.value.range_pos_ejectorbackw?"alert_state":"")},e((ll=($=a.value)==null?void 0:$.MoldData)==null?void 0:ll.EjectIn1Pos),3),l("td",{class:o(Math.abs(((el=(tl=a.value)==null?void 0:tl.MoldData)==null?void 0:el.EjectIn2Pos)-t.value.pos_ejectorbackw_2)>t.value.range_pos_ejectorbackw?"alert_state":"")},e((sl=(al=a.value)==null?void 0:al.MoldData)==null?void 0:sl.EjectIn2Pos),3),od],512),[[_,d.showmonitor]])])]),l("table",dd,[nd,l("tbody",null,[_d,l("tr",null,[ud,l("td",null,e(t.value.pre_core1_in),1),l("td",null,e(t.value.pre_core1_out),1),l("td",null,e(t.value.pre_core2_in),1),l("td",null,e(t.value.pre_core2_out),1),l("td",null,e(t.value.pre_core3_in),1),l("td",null,e(t.value.pre_core3_out),1)]),n(l("tr",rd,[cd,l("td",null,e((dl=(ol=a.value)==null?void 0:ol.MoldData)==null?void 0:dl.CoreAInPres),1),l("td",null,e((_l=(nl=a.value)==null?void 0:nl.MoldData)==null?void 0:_l.CoreAOutPres),1),l("td",null,e((rl=(ul=a.value)==null?void 0:ul.MoldData)==null?void 0:rl.CoreBInPres),1),l("td",null,e((pl=(cl=a.value)==null?void 0:cl.MoldData)==null?void 0:pl.CoreBOutPres),1),l("td",null,e((il=(vl=a.value)==null?void 0:vl.MoldData)==null?void 0:il.CoreCInPres),1),l("td",null,e((ml=(hl=a.value)==null?void 0:hl.MoldData)==null?void 0:ml.CoreCOutPres),1)],512),[[_,d.showmonitor]]),l("tr",null,[pd,l("td",null,e(t.value.spd_core1_in),1),l("td",null,e(t.value.spd_core1_out),1),l("td",null,e(t.value.spd_core2_in),1),l("td",null,e(t.value.spd_core2_out),1),l("td",null,e(t.value.spd_core3_in),1),l("td",null,e(t.value.spd_core3_out),1)]),n(l("tr",vd,[id,l("td",null,e((jl=(bl=a.value)==null?void 0:bl.MoldData)==null?void 0:jl.CoreAInSpeed),1),l("td",null,e((Sl=(gl=a.value)==null?void 0:gl.MoldData)==null?void 0:Sl.CoreAOutSpeed),1),l("td",null,e((fl=(Cl=a.value)==null?void 0:Cl.MoldData)==null?void 0:fl.CoreBInSpeed),1),l("td",null,e((kl=(Il=a.value)==null?void 0:Il.MoldData)==null?void 0:kl.CoreBOutSpeed),1),l("td",null,e((yl=(Ol=a.value)==null?void 0:Ol.MoldData)==null?void 0:yl.CoreCInSpeed),1),l("td",null,e((Tl=(Hl=a.value)==null?void 0:Hl.MoldData)==null?void 0:Tl.CoreCOutSpeed),1)],512),[[_,d.showmonitor]]),l("tr",null,[hd,l("td",null,e(t.value.pos_core1_in),1),l("td",null,e(t.value.pos_core1_out),1),l("td",null,e(t.value.pos_core2_in),1),l("td",null,e(t.value.pos_core2_out),1),l("td",null,e(t.value.pos_core3_in),1),l("td",null,e(t.value.pos_core3_out),1)]),n(l("tr",md,[Md,l("td",null,e((Zl=(El=a.value)==null?void 0:El.MoldData)==null?void 0:Zl.CoreAInPos),1),l("td",null,e((Bl=(zl=a.value)==null?void 0:zl.MoldData)==null?void 0:Bl.CoreAOutPos),1),l("td",null,e((Al=(Vl=a.value)==null?void 0:Vl.MoldData)==null?void 0:Al.CoreBInPos),1),l("td",null,e((Ql=(xl=a.value)==null?void 0:xl.MoldData)==null?void 0:Ql.CoreBOutPos),1),l("td",null,e((Ul=(Fl=a.value)==null?void 0:Fl.MoldData)==null?void 0:Ul.CoreCInPos),1),l("td",null,e((Wl=(Kl=a.value)==null?void 0:Kl.MoldData)==null?void 0:Wl.CoreCOutPos),1)],512),[[_,d.showmonitor]]),l("tr",null,[Dd,l("td",null,e(t.value.monitor_core1_in),1),l("td",null,e(t.value.monitor_core1_out),1),l("td",null,e(t.value.monitor_core2_in),1),l("td",null,e(t.value.monitor_core2_out),1),l("td",null,e(t.value.monitor_core3_in),1),l("td",null,e(t.value.monitor_core3_out),1)]),n(l("tr",wd,Id,512),[[_,d.showmonitor]])])]),l("table",kd,[Od,l("tbody",null,[yd,l("tr",null,[Hd,l("td",null,e(t.value.pos_start_air1),1),l("td",null,e(t.value.pos_start_air2),1),l("td",null,e(t.value.pos_start_air3),1)]),n(l("tr",Td,[Ed,l("td",null,e((Yl=(Xl=a.value)==null?void 0:Xl.MoldData)==null?void 0:Yl.Air1Pos),1),l("td",null,e((lt=($l=a.value)==null?void 0:$l.MoldData)==null?void 0:lt.Air2Pos),1),l("td",null,e((et=(tt=a.value)==null?void 0:tt.MoldData)==null?void 0:et.Air3Pos),1)],512),[[_,d.showmonitor]]),l("tr",null,[Zd,l("td",null,e(t.value.pos_end_air1),1),l("td",null,e(t.value.pos_end_air2),1),l("td",null,e(t.value.pos_end_air3),1)]),n(l("tr",zd,Qd,512),[[_,d.showmonitor]]),l("tr",null,[Fd,l("td",null,e(t.value.time_air1),1),l("td",null,e(t.value.time_air2),1),l("td",null,e(t.value.time_air3),1)]),n(l("tr",Ud,[qd,l("td",null,e((st=(at=a.value)==null?void 0:at.MoldData)==null?void 0:st.Air1Time),1),l("td",null,e((dt=(ot=a.value)==null?void 0:ot.MoldData)==null?void 0:dt.Air2Time),1),l("td",null,e((_t=(nt=a.value)==null?void 0:nt.MoldData)==null?void 0:_t.Air3Time),1)],512),[[_,d.showmonitor]])])])]}),_:1})]),_:1})]),_:1})]),_:1},512)}}}),Gd=ne(Nd,[["__scopeId","data-v-fbb5f73c"]]);export{Gd as default};

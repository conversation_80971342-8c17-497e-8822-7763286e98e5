from extensions import db


# 修改时间 2023.8.17
class User(db.Model):
    __bind_key__ = "carbonmix"
    __tablename__ = "cm_user"
    Id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(50))
    role = db.Column(db.String(50))
    name = db.Column(db.String(50))
    eid = db.Column(db.String(7))
    status = db.Column(db.SmallInteger)
    createtime = db.Column(db.DateTime())
    token = db.Column(db.String(255))


class Formular(db.Model):
    __bind_key__ = "carbonmix"
    __tablename__ = "cm_formular"
    Id = db.Column(db.Integer, primary_key=True)
    mixno = db.Column(db.String(20))
    itemname = db.Column(db.String(10))
    itemno = db.Column(db.String(20))
    rate = db.Column(db.SmallInteger)
    status = db.Column(db.SmallInteger)


class Mixrecord(db.Model):
    __bind_key__ = "carbonmix"
    __tablename__ = "cm_mixrecord"
    Id = db.Column(db.Integer, primary_key=True)
    mixid = db.Column(db.Integer)
    mixdate = db.Column(db.DateTime())
    mixno = db.Column(db.String(20))
    itemname = db.Column(db.String(10))
    itemno = db.Column(db.String(20))
    itemsn = db.Column(db.String(30))
    itemname = db.Column(db.String(10))
    packno = db.Column(db.SmallInteger)
    itemweight = db.Column(db.Float)

﻿from sqlalchemy.ext.automap import automap_base
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
import sys
import os
# 获取当前文件所在的目录路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取上一层目录路径
parent_dir = os.path.dirname(current_dir)
# 将上一层目录路径添加到模块搜索路径script_config中
sys.path.append(parent_dir)
from flask import Flask  # noqa
from config import config  # noqa
from config import env  # noqa
from app.im.model.models_im import db  # noqa

app = Flask(__name__)
app.config.from_object(config[env])  # 获取配置信息中的开发环境
my_config = app.config

import type {
  AdaptiveConfig,
  PaginationProps,
  LoadingConfig
} from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { handlemix } from "@/api/operation";
import { ref, onMounted, reactive, h } from "vue";
import { addDialog } from "@/components/ReDialog";
import moment from "moment";
import { toRaw } from "vue";
import { message } from "@/utils/message";

export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);
  const search_condition = reactive({
    material: "",
    quantity: 0
  });

  const search = () => {
    loading.value = true;
    handlemix(toRaw(search_condition))
      .then((res: { data: any; meta: any }) => {
        if (res.meta.status != 200) {
          message(res.meta.msg, { type: "error" });
          loading.value = false;
          return;
        }
        dataList.value = res.data;
        loading.value = false;
      })
      .catch(() => {
        message("系统请求数据错误！", { type: "error" });
        loading.value = false;
      });
  };

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载拌料数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  /** 撑满内容区自适应高度相关配置 */
  const adaptiveConfig: AdaptiveConfig = {
    /** 表格距离页面底部的偏移量，默认值为 `96` */
    // offsetBottom: 100
    /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
    // fixHeader: true
    /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
    // timeout: 60
    /** 表头的 `z-index`，默认值为 `100` */
    // zIndex: 100
  };

  const columns: TableColumnList = [
    {
      label: "粒子料号",
      prop: "component",
      minWidth: "100"
    },
    {
      label: "粒子描述",
      prop: "component_des",
      minWidth: "160"
    },
    {
      label: "类型",
      prop: "component_type",
      width: "90",
      cellRenderer: ({ row }) => {
        if (row.component_type == 1) {
          return <el-tag size="large">树脂</el-tag>;
        } else if (row.component_type == 2) {
          return (
            <el-tag size="large" type="success">
              色母
            </el-tag>
          );
        } else if (row.component_type == 3) {
          return (
            <el-tag size="large" type="warning">
              发泡剂
            </el-tag>
          );
        }
      }
    },
    {
      label: "用量",
      prop: "usage",
      formatter(row, column, cellValue) {
        if (Number.isInteger(cellValue)) {
          return cellValue.toLocaleString();
        } else {
          return cellValue.toFixed(3);
        }
      }
    },
    {
      label: "单位",
      prop: "unit",
      width: "80"
    },
    {
      label: "重量配比",
      prop: "percent",
      formatter(row, column, cellValue) {
        return (cellValue * 100).toFixed(2) + "%";
      }
    },
    {
      label: "更新时间",
      prop: "updatetime",
      formatter(row, column, cellValue) {
        return moment(cellValue).format("YYYY-MM-DD HH:mm:ss");
      }
    }
  ];

  return {
    search_condition,
    search,
    loading,
    columns,
    dataList,
    loadingConfig,
    adaptiveConfig
  };
}

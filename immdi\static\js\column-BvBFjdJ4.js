import{h as d}from"./operation-DCG_Ggeh.js";import{h as g}from"./moment-C3TZ8gAF.js";import{n as p,a2 as u,S as h,aE as m,b as o,f as r,r as n}from"./index-BnxEuBzx.js";function C(){const s=p([]),a=p(!0),l=u({material:"",quantity:0}),c=()=>{a.value=!0,d(h(l)).then(e=>{if(e.meta.status!=200){m(e.meta.msg,{type:"error"}),a.value=!1;return}s.value=e.data,a.value=!1}).catch(()=>{m("系统请求数据错误！",{type:"error"}),a.value=!1})},f=u({text:"正在加载拌料数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `});return{search_condition:l,search:c,loading:a,columns:[{label:"粒子料号",prop:"component",minWidth:"100"},{label:"粒子描述",prop:"component_des",minWidth:"160"},{label:"类型",prop:"component_type",width:"90",cellRenderer:({row:e})=>{if(e.component_type==1)return o(n("el-tag"),{size:"large"},{default:()=>[r("树脂")]});if(e.component_type==2)return o(n("el-tag"),{size:"large",type:"success"},{default:()=>[r("色母")]});if(e.component_type==3)return o(n("el-tag"),{size:"large",type:"warning"},{default:()=>[r("发泡剂")]})}},{label:"用量",prop:"usage",formatter(e,i,t){return Number.isInteger(t)?t.toLocaleString():t.toFixed(3)}},{label:"单位",prop:"unit",width:"80"},{label:"重量配比",prop:"percent",formatter(e,i,t){return(t*100).toFixed(2)+"%"}},{label:"更新时间",prop:"updatetime",formatter(e,i,t){return g(t).format("YYYY-MM-DD HH:mm:ss")}}],dataList:s,loadingConfig:f,adaptiveConfig:{}}}export{C as useColumns};

import{n as s,a2 as u,G as f,b as a,r as l,S as d,aX as _,aj as g}from"./index-BnxEuBzx.js";import{h as v}from"./moment-C3TZ8gAF.js";import{b as y}from"./dashboard-dtTxmf4X.js";import{_ as b}from"./index.vue_vue_type_script_setup_true_lang-CPCUbDea.js";import"./columns-BirzvcTI.js";import"./prod-CfeywgVC.js";function R(){const o=s([]),r=s(!0),t=u({search_condition:{selecteddate:v().format("YYYY-MM-DD"),machine:5,shift:null}}),i=u({hourid:null,show_view:!1}),c=e=>{_({title:"查看"+t.search_condition.machine+"#节拍",props:{search_condition:d(t.search_condition),hour_id:e},width:"60%",draggable:!0,fullscreenIcon:!1,closeOnClickModal:!0,contentRenderer:()=>g(b)})},p=[{label:"小时号",prop:"hourid",formatter(e){return e.hourid+":00 ~"+(e.hourid+1)+":00"}},{label:"料号",prop:"pn",minWidth:"150px"},{label:"产量",prop:"",cellRenderer({row:e}){return e.output==0&&e.adjustion==0?a("div",null,null):a(l("el-tag"),{size:"large",type:e.output+e.adjustion>=e.computed_output?"success":"danger",style:"width:100%",onClick:()=>{c(e.hourid)}},{default:()=>[e.output+e.adjustion]})}},{label:"标准产量",prop:"computed_output",formatter(e){return e.computed_output.toFixed(0)}},{label:"产量累积",prop:"acumulated_output",cellRenderer({row:e}){if(e.acumulated_output==0)return a("div",null,null);if(e.acumulated_output>=e.acumulated_computedvalue)return a(l("el-tag"),{size:"large",type:"success",style:"width:60%"},{default:()=>[e.acumulated_output]});if(e.acumulated_output<e.acumulated_computedvalue)return a(l("el-tag"),{size:"large",type:"danger",style:"width:60%"},{default:()=>[e.acumulated_output]})}},{label:"标准累积",prop:"acumulated_computedvalue",formatter(e){return e.acumulated_computedvalue.toFixed(0)}}],m=u({text:"正在加载小时记录表数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `}),h={fixHeader:!0},n=()=>{y(d(t.search_condition)).then(e=>{o.value=e.data,r.value=!1})};return f(t,()=>{n()}),{query_cycle_param:i,refreshData:n,query_param:t,loading:r,columns:p,dataList:o,loadingConfig:m,adaptiveConfig:h}}export{R as useColumns};

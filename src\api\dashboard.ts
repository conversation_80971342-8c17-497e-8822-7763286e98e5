import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export const getmachinelist = (querydata?: object) => {
  return http.request("get", baseUrlApi("production/getlist"), {
    params: querydata
  });
};

export const getstatetype = () => {
  return http.request("get", baseUrlApi("production/getstatetype"));
};

export const addstate = (data: object) => {
  return http.request("post", baseUrlApi("shift/addstate"), {
    data
  });
};

export const updatestate = (data: object) => {
  return http.request("post", baseUrlApi("shift/updatestate"), {
    data
  });
};

export const getpnlistbyprefix = (data: object) => {
  return http.request("get", baseUrlApi("production/getpnlistbyprefix"), {
    params: data
  });
};

export const getdesbypn = (data: object) => {
  return http.request("get", baseUrlApi("production/getdesbypn"), {
    params: data
  });
};

export const changepn = (data: object) => {
  return http.request("post", baseUrlApi("production/changepn"), {
    data
  });
};

export const gethourlydata = (data: object) => {
  return http.request("get", baseUrlApi("hourlyinfo/getlist"), {
    params: data
  });
};

// 根据提供的日期返回有产出的机台列表
export const getmachineidbyoutput = (data: object) => {
  return http.request("get", baseUrlApi("hourlyinfo/getmachineidbyoutput"), {
    params: data
  });
};

// 返回计划数据
export const getplanlist = () => {
  return http.request("get", baseUrlApi("plan/getlist"));
};

// 返回包装计划数据
export const getpackingplanlist = () => {
  return http.request("get", baseUrlApi("plan/getpackinglist"));
};

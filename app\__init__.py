from flask import Flask
from config import config
from extensions import init_ext
from app.ai import importImRoute


def create_app(config_name):
    app = Flask(__name__, static_folder='../static', static_url_path='/static')
    app.config.from_object(config[config_name])  # 获取配置信息中的开发环境
    config[config_name].init_app(app)  # 初始化app
    init_ext(app)  # 载入依赖包
    importImRoute(app)  # 注塑蓝图
    return app

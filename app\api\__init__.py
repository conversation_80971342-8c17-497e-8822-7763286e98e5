from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.sms.v20190711 import sms_client, models
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.common import credential
from flask import Blueprint, request
api = Blueprint('api', __name__)


@api.route('/getAlarm', methods=['GET'])
def getAlarm():
    res = request.args
    ck = res.get('ck')
    linename = res.get('linename')
    # name = res.get('name')
    # mailsenders = ['<EMAIL>']
    phone = ["+8618550208313"]
    # phone = ["+8618550208313", "+8615250032931", "+8613862581563"]
    # mailsenders = ['<EMAIL>', '<EMAIL>']
    # cards = {
    #     '161173162': '张海棠'
    # }
    # if name in cards.keys():
    #     cname = name
    # else:
    #     cname = name
    if ck == '1':
        pass
    #     sendMailTread(mailsenders, '产线' + linename + '发出的安灯警报已消除', "消除人为："+cname)
    else:
        try:
            cred = credential.Credential(
                "AKIDUL5UeX1zZ4HItGqFLIacP0LQFKGx3RLy", "bxNWLvGOIwKn02qNVLuPgs93mMlu9CxV")
            httpProfile = HttpProfile()
            httpProfile.reqMethod = "POST"  # POST 请求（默认为 POST 请求）
            httpProfile.reqTimeout = 30  # 请求超时时间，单位为秒（默认60秒）
            httpProfile.endpoint = "sms.tencentcloudapi.com"  # 指定接入地域域名（默认就近接入）
            # 实例化一个客户端配置对象，可以指定超时时间等配置
            clientProfile = ClientProfile()
            clientProfile.signMethod = "TC3-HMAC-SHA256"  # 指定签名算法
            clientProfile.language = "en-US"
            clientProfile.httpProfile = httpProfile
            client = sms_client.SmsClient(cred, "ap-guangzhou", clientProfile)
            req = models.SendSmsRequest()
            req.SmsSdkAppid = "1400149076"
            req.Sign = "welean"
            req.SessionContext = "xxx"
            req.PhoneNumberSet = phone
            req.TemplateID = "208852"
            req.TemplateParamSet = [linename, "产线安灯警报", "产线员工"]
            resp = client.SendSms(req)
            # sendMailTread(mailsenders, '收到产线' + linename + '发出的安灯警报', '请及时去产线处理')
            # 输出 JSON 格式的字符串回包
            print(resp.to_json_string(indent=2))
        except TencentCloudSDKException as err:
            print(err)
            return 'fail'
    return 'ok'


@api.route('/getBI')
def getBI():
    return 11


@api.route('/')
def index():
    return 'ok等等310'

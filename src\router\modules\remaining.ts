import { $t } from "@/plugins/i18n";
const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: $t("menus.pureLogin"),
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: $t("status.pureLoad"),
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  },
  {
    name: "ExtenalPage",
    path: "/frontend",
    meta: {
      title: "外部模块",
      showLink: true,
      rank: 9,
      icon: "la:external-link-alt"
    },
    children: [
      {
        path: "/frontend/index",
        name: "Frontend",
        component: () => import("@/views/frontend/index.vue"),
        meta: {
          title: "单页链接",
          hiddenTag: true,
          roles: ["admin"],
          icon: "ic:round-edit-note"
        },
        children: [
          {
            name: "production",
            path: "/frontend/pages/prod",
            component: () => import("@/views/frontend/pages/prod.vue"),
            meta: {
              title: "生产编辑页",
              roles: ["admin"],
              showParent: false
            }
          }
        ]
      },
      {
        path: "/machinelist",
        name: "machinelist",
        component: () => import("@/views/dashboard/machinelist/index.vue"),
        meta: {
          title: "设备列表页",
          roles: ["admin"],
          showParent: true,
          showLink: true,
          icon: "ic:round-edit-note"
        }
      },
      {
        path: "/plan",
        name: "plan",
        component: () => import("@/views/dashboard/delivery/index.vue"),
        meta: {
          title: "生产计划页",
          roles: ["admin"],
          showParent: true,
          showLink: true,
          icon: "ic:round-edit-note"
        }
      }
    ]
  }
] satisfies Array<RouteConfigsTable>;

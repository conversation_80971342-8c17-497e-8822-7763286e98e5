<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { calcFeight } from "@/api/order";

import { useColumns } from "../columns";

// 定义数据类型接口
interface FreightItem {
  template: string;
  unitprice: number;
  province: string;
  city: string;
  district: string;
  lowerlimit: number;
  upperlimit: number;
}

interface VehicleItem {
  template: string;
  unitprice: number;
  truck: string;
  lowerlimit: number;
  upperlimit: number;
}

interface SelectedCard {
  type: string;
  template: string;
  index: number;
}

const { feedata } = useColumns();
const props = defineProps({
  rowdata: Object
});
const freight_data = ref<any>();
const volume_data = ref<FreightItem[]>([]);
const weight_data = ref<FreightItem[]>([]);
const vehicle_data = ref<Record<string, VehicleItem[]>>({});

// 选中状态管理
const selectedCard = ref<SelectedCard>({
  type: "", // 'weight', 'volume', 'vehicle'
  template: "",
  index: -1
});

const dataInit = () => {
  console.log(props.rowdata.freight_calc_method);
  feedata.freight_fee = props.rowdata.freight_fee;
  feedata.fee_remark = props.rowdata.fee_remark;
  feedata.freight_calc_method = props.rowdata.freight_calc_method;
  feedata.freight_unit_price = props.rowdata.freight_unit_price;
  feedata.freight_adjust = props.rowdata.feight_adjust ?? 0;
  feedata.freight_adjust_reason = props.rowdata.freight_adjust_reason;
  feedata.logistic_remark = props.rowdata.logistic_remark;
  feedata.logistic_company = props.rowdata.logistic_company;
};

const calcFee = () => {
  calcFeight(props.rowdata).then((res: any) => {
    freight_data.value = res.data;
    weight_data.value = res.data.weight;
    volume_data.value = res.data.volume;
    vehicle_data.value = res.data.vehicle;
  });
};

onMounted(() => {
  dataInit();
  calcFee();
});

const showdata = (obj: any, type: string, index: number) => {
  feedata.logistic_company = obj.template;
  feedata.freight_unit_price = obj.unitprice;
  feedata.freight_fee = obj.freight_fee;
  feedata.freight_calc_method = obj.calc_method;

  // 更新选中状态
  selectedCard.value = {
    type: type,
    template: obj.template,
    index: index
  };

  console.log(obj.unitprice);
};

// 检查卡片是否被选中
const isCardSelected = (
  type: string,
  template: string,
  index: number
): boolean => {
  return (
    selectedCard.value.type === type &&
    selectedCard.value.template === template &&
    selectedCard.value.index === index
  );
};
</script>

<template>
  <div style="margin-bottom: 10px">
    <el-descriptions :column="4" label-width="100px" border>
      <el-descriptions-item>
        <template #label> <div>出货地</div> </template>
        <el-text>{{ props.rowdata.BU_NO }}</el-text>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label> <div>送达城市</div> </template>
        <el-text
          >{{ props.rowdata.receiver_province }}/{{
            props.rowdata.receiver_city
          }}/{{ props.rowdata.receiver_district }}</el-text
        >
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label> <div>货物价值</div> </template>
        <el-text
          >{{ props.rowdata.goods_value }} {{ props.rowdata.currency }}</el-text
        >
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label> <div>货物重量</div> </template>
        <el-text>{{ props.rowdata.weight }}吨</el-text>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label> <div>货物体积</div> </template>
        <el-text>{{ props.rowdata.volume }}立方米</el-text>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label> <div>经销商</div> </template>
        <el-text>{{ props.rowdata.payer_company }}</el-text>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label> <div>收货地</div> </template>
        <el-text>{{ props.rowdata.receiver_address }}</el-text>
      </el-descriptions-item>
    </el-descriptions>
    <el-form label-position="top">
      <el-row :gutter="20">
        <el-col :span="24"
          ><el-form-item label="重量计费">
            <el-row :gutter="20" style="width: 100%">
              <el-col
                v-for="(item, index) in weight_data"
                :key="index"
                :span="6"
              >
                <el-card
                  :class="{
                    'card-hover': true,
                    'card-selected': isCardSelected(
                      'weight',
                      item.template,
                      index
                    )
                  }"
                  @click="
                    showdata(
                      {
                        template: item.template,
                        unitprice: item.unitprice,
                        freight_fee: props.rowdata.weight * item.unitprice,
                        lowerlimit: item.lowerlimit,
                        upperlimit: item.upperlimit,
                        calc_method: 1
                      },
                      'weight',
                      index
                    )
                  "
                >
                  <template #header>
                    <el-tag>{{ item.template }} </el-tag>
                  </template>
                  <div>
                    <div>
                      匹配地址：{{ item.province }}/{{ item.city }}/{{
                        item.district
                      }}
                    </div>
                    <div>
                      定价区间：{{ item.lowerlimit }}T - {{ item.upperlimit }}T
                    </div>
                    <div>运费单价：{{ item.unitprice }}元/吨</div>
                    <div>
                      <div>运费总价：</div>
                      <span style="font-size: 24px; font-weight: 400"
                        >{{ props.rowdata.weight * item.unitprice }}元</span
                      >
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="24"
          ><el-form-item label="体积计费">
            <el-row :gutter="20" style="width: 100%">
              <el-col
                v-for="(item, index) in volume_data"
                :key="index"
                :span="6"
              >
                <el-card
                  :class="{
                    'card-hover': true,
                    'card-selected': isCardSelected(
                      'volume',
                      item.template,
                      index
                    )
                  }"
                  @click="
                    showdata(
                      {
                        template: item.template,
                        unitprice: item.unitprice,
                        freight_fee: props.rowdata.volume * item.unitprice,
                        lowerlimit: item.lowerlimit,
                        upperlimit: item.upperlimit,
                        calc_method: 2
                      },
                      'volume',
                      index
                    )
                  "
                >
                  <template #header>
                    <el-tag>{{ item.template }} </el-tag>
                  </template>
                  <div>
                    <div>
                      匹配地址：{{ item.province }}/{{ item.city }}/{{
                        item.district
                      }}
                    </div>
                    <div>
                      定价区间：{{ item.lowerlimit }}立方米 -
                      {{ item.upperlimit }}立方米
                    </div>
                    <div>运费单价：{{ item.unitprice }}元/立方米</div>
                    <div>
                      <div>运费总价：</div>
                      <span style="font-size: 24px; font-weight: 400"
                        >{{ props.rowdata.weight * item.unitprice }}元</span
                      >
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="车型计费">
            <el-row :gutter="20" style="width: 100%">
              <el-col
                v-for="(items, templateName) in vehicle_data"
                :key="templateName"
                :span="12"
              >
                <el-card>
                  <template #header
                    ><el-tag>{{ templateName }}</el-tag></template
                  >
                  <div style="display: flex; flex-direction: row">
                    <el-card
                      v-for="(item, index) in items"
                      :key="index"
                      :class="{
                        'card-hover': true,
                        'card-selected': isCardSelected(
                          'vehicle',
                          `${templateName}-${item.truck}`,
                          index
                        )
                      }"
                      style="margin: 5px 5px"
                      @click="
                        showdata(
                          {
                            template: item.template,
                            unitprice: item.unitprice,
                            freight_fee: props.rowdata.weight * item.unitprice,
                            lowerlimit: item.lowerlimit,
                            upperlimit: item.upperlimit,
                            calc_method: 3
                          },
                          'vehicle',
                          index
                        )
                      "
                    >
                      <div
                        style="
                          display: flex;
                          flex-direction: column;
                          align-items: center;
                        "
                      >
                        <el-tag>{{ item.truck }}米</el-tag>
                        <div style="font-size: 18px">
                          {{ item.unitprice }}元
                        </div>
                      </div>
                    </el-card>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-row :gutter="20" style="width: 100%">
            <el-col :span="4">
              <el-form-item label="物流公司">
                <el-input v-model="feedata.logistic_company" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item label="计价方式">
                <!-- <el-input v-model="feedata.freight_calc_method" disabled /> -->
                <el-select
                  v-model.number="feedata.freight_calc_method"
                  disabled
                >
                  <el-option label="重量" :value="1" />
                  <el-option label="体积" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="运费价格">
                <el-input-number v-model.number="feedata.freight_fee" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="价格调整">
                <el-input-number v-model="feedata.freight_adjust" :step="10" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="价格调整原因">
                <el-input v-model="feedata.freight_adjust_reason" />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item label="总价">
                <span style="font-size: 24px">{{
                  feedata.freight_fee + feedata.freight_adjust
                }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="物流备注">
                <el-input v-model="feedata.logistic_remark" type="textarea" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 0 10px;
}
:deep(.el-card__body) {
  div div {
    display: flex;
    align-items: center;
  }
  padding: 5px 15px;
}
:deep(.el-form-item__label) {
  margin-bottom: 0;
}
.el-form-item {
  margin-bottom: 0;
}

/* 卡片悬停效果 */
.card-hover {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #409eff;
  }
}

/* 卡片选中效果 */
.card-selected {
  border-color: #409eff !important;
  background-color: #f0f9ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);

  :deep(.el-card__header) {
    background-color: #409eff;
    color: white;

    .el-tag {
      background-color: white;
      color: #409eff;
      border-color: white;
    }
  }

  :deep(.el-card__body) {
    background-color: #f0f9ff;
  }
}

/* 车型卡片特殊样式 */
.card-hover[style*="margin: 5px 5px"] {
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.card-selected[style*="margin: 5px 5px"] {
  :deep(.el-tag) {
    background-color: #409eff;
    color: white;
    border-color: #409eff;
  }
}
</style>

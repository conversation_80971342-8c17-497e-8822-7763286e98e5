import{h as o}from"./moment-C3TZ8gAF.js";const i=(t,n)=>{const r=new Date().getHours(),e=r>=0&&r<8?"C":r>=8&&r<16?"A":r>=16&&r<24?"B":"",s=o().format("YYYY-MM-DD"),a=o().subtract(1,"days").format("YYYY-MM-DD");return n===e?e==="C"&&t===a||(e==="A"||e==="B")&&t===s:!1},f=()=>{const t=new Date().getHours();return t>=0&&t<=7?"C":t>=8&&t<=15?"A":t>=16&&t<=23?"B":""},c=t=>{switch(t){case"C":return"夜班(次日0:00~8:00)";case"A":return"早班(8:00~16:00)";case"B":return"中班(16:00~24:00)";default:return""}},h=t=>{const n=Math.floor(t/3600),r=Math.floor(t%3600/60),e=t%60;return n>0?`${n}小时${r.toString().padStart(2,"0")}分${e.toString().padStart(2,"0")}秒`:r>0?`${r}分${e.toString().padStart(2,"0")}秒`:`${e}秒`};export{c as a,h as f,f as g,i};

from flask import Blueprint, jsonify, request
import datetime
import pymysql
import pymssql
from app.public.functions import responseGet
from config import config, env
from extensions import db
from sqlalchemy import func,  and_
from app.receiving.model.models_receiving import Ncmr, Ncmrauth, Lineinfo
from app.receiving.functions import getServer
from app.receiving.schemas import ncmrs_schema, ncmrauth_schema
api = Blueprint('bi/wrqualityAPI', __name__)


@api.route('/getvoc', methods=['GET'])
def getvoc():
    res = request.args
    line = res.get('line')
    checked = res.get('checked').split(',')
    print('bbbbbbb', checked)
    ckarr = []
    if checked[0] == 'true':
        ckarr.append("'open'")
    if checked[1] == 'true':
        ckarr.append("'ongoing'")
    if checked[2] == 'true':
        ckarr.append("'closed'")
    if checked[3] == 'true':
        ckarr.append("'cancel'")
    print('cccccc', ckarr)
    connect = pymssql.connect(config[env].vocdb['host'],
                              config[env].vocdb['user'], config[env].vocdb['password'], config[env].vocdb['database'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    sql1 = "select  status, receivedate, line, fg, qa, vocdesc from dvoc where vsm = 'VSM1'"
    if line:
        sql1 = sql1+" and area='%s'" % line
    if len(ckarr) > 0:
        sql1 = sql1 + " and status in ("+','.join(ckarr)+')'
    sql1 = sql1+" order by receivedate desc "
    print('aaaaaaa', sql1)
    cursor.execute(sql1)
    r1 = cursor.fetchall()
    outArr = []
    for r in r1:
        d = {
            'status': r[0],
            'receivedate': datetime.datetime.strftime(r[1], '%Y-%m-%d'),
            'line': r[2],
            'fg': r[3],
            'qa': r[4],
            'vocdesc': r[5]
        }
        outArr.append(d)
    sql2 = "select area from dline where vsm='VSM1' group by area order by area"
    cursor.execute(sql2)
    r2 = cursor.fetchall()
    lines = []
    for r in r2:
        lines.append(r[0])
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data=outArr, lines=lines)


@ api.route('/getNCMR', methods=['GET'])
def getNCMR():
    res = request.args
    query = res.get('query')
    ncmr = db.session.query(Ncmr).outerjoin(Lineinfo, Ncmr.project == Lineinfo.line).filter(Lineinfo.vsm == 'VSM1').filter(Ncmr.pqccate == 0).filter(Ncmr.sku.like('%{0}%'.format(query))).filter(
        and_(Ncmr.status != 'closed', Ncmr.status != 'cancel', Ncmr.status != 'PQC', Ncmr.ncmrtype == 'PQC')).order_by(
        Ncmr.status, Ncmr.actiondate.is_(None), Ncmr.actiondate, Ncmr.initiatedate).all()
    total = db.session.query(func.count(Ncmr.Id)).outerjoin(Lineinfo, Ncmr.project == Lineinfo.line).filter(Lineinfo.vsm == 'VSM1').filter(Ncmr.pqccate == 0).filter(
        Ncmr.sku.like('%{0}%'.format(query))).filter(
        and_(Ncmr.status != 'closed', Ncmr.status != 'cancel', Ncmr.status != 'PQC', Ncmr.ncmrtype == 'PQC')).scalar()
    ncmrlist = ncmrs_schema.dump(ncmr)
    for item in ncmrlist:
        ncmrAuth = db.session.query(Ncmrauth).filter(
            Ncmrauth.ncmrid == item['Id']).filter(Ncmrauth.dept != 'WH').all()
        if ncmrAuth:
            ncmrAuthlist = ncmrauth_schema.dump(ncmrAuth)
            item['ncmrAuthlist'] = ncmrAuthlist
        if item['pics']:
            p = item['pics'].split(',')
            for i in range(len(p)):
                p[i] = getServer()['picUrl']+p[i]
            item['pics'] = ','.join(p)
        else:
            item['pics'] = getServer()['picUrl']+'nophoto.jpg'
    data = {'total': total, 'ncmrlist': ncmrlist}
    return responseGet("获取NCMR待处理列表成功", data)


@api.route('/getLinegroup', methods=['GET'])
def getLinegroup():
    connect = pymysql.connect(host=config[env].wrdb['host'], port=config[env].wrdb['port'], user=config[env].wrdb['user'],
                              password=config[env].wrdb['password'], database=config[env].wrdb['database'], charset=config[env].wrdb['charset'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    sql = "select linegroup from mdi_lineinfo where active=1 group by linegroup"
    cursor.execute(sql)
    rst = cursor.fetchall()
    outData = []
    for r in rst:
        outData.append({'name': r[0], 'value': r[0]})
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data=outData)


@api.route('/getQmonth', methods=['GET'])
def getCross():
    r = request.args
    print(r)
    stime = r.get('stime')
    etime = r.get('etime')
    area = r.get('area')
    if area:
        sql = """
        select right(date,5) as dt, qq*95 as red,qq*5 as yellow, round(if((act-defect)/act,(act-defect)/act,1)*100,1) ftt from
        (select date(starttime) as date,qtarget*earnhour qq,earnhour,
        sum(actualout) act,sum(round(requireout,0)) req,mdi_lineinfo.linegroup as linegroup
        from mdi_hourinfo left join mdi_shiftinfo on mdi_hourinfo.shiftid=mdi_shiftinfo.id
        left join mdi_lineinfo on mdi_shiftinfo.lineid=mdi_lineinfo.id
        left join mdi_kpi on mdi_kpi.lineid=mdi_lineinfo.linegroup
        where linegroup='%s' and date(starttime) between '%s' and  '%s'
        group by date(starttime),linegroup) as A
        left join
        (select sum(qty) as defect,linegroup as linegroup,date(recordtime) as date2 from mdi_issuelog
        left join mdi_lineinfo on mdi_issuelog.linename=mdi_lineinfo.linename
        where sqdctype='质量' and linegroup='%s' and date(recordtime) between '%s' and  '%s'
        group by date(recordtime),linegroup) as B
        on A.date=B.date2 and A.linegroup=B.linegroup
        """ % (area, stime, etime, area, stime, etime)
        sql2 = """select sum(qty) as qt,round(avg(mdi_probleminfo.trigger),0) as tg,mdi_issuelog.sqdctype as stype,mdi_issuelog.problemtype as ptype,linegroup
        from mdi_issuelog left join mdi_lineinfo on mdi_issuelog.linename=mdi_lineinfo.linename
        left join mdi_probleminfo on mdi_issuelog.problemtype=mdi_probleminfo.problemtype and mdi_issuelog.sqdctype=mdi_probleminfo.sqdctype
        and (mdi_issuelog.linename=mdi_probleminfo.linename or mdi_probleminfo.linename='all')
        where mdi_issuelog.sqdctype='质量' AND date(mdi_issuelog.recordtime) between '%s' and  '%s' and linegroup='%s'
        group by mdi_issuelog.problemtype""" % (stime, etime, area)
        sql3 = """
        select date(recordtime) as recorddate,`desc` as descd,mdi_lineinfo.linename as linename,qty,problemtype
        from mdi_issuelog left join mdi_lineinfo on mdi_lineinfo.linename=mdi_issuelog.linename
        where date(recordtime) between '%s' and '%s' and linegroup='%s' and sqdctype='质量'
        order by recorddate desc,linename""" % (stime, etime, area)
    else:
        sql = """select right(date,5) as dt, qq*95 as red,qq*5 as yellow, round(if((act-defect)/act,(act-defect)/act,1)*100,1) ftt
        from (select date(starttime) as date, qtarget*earnhour qq, earnhour,
      sum(actualout) act, sum(round(requireout, 0)) req, mdi_lineinfo.linegroup from mdi_hourinfo
      left join mdi_shiftinfo on mdi_hourinfo.shiftid=mdi_shiftinfo.id left join mdi_lineinfo on mdi_shiftinfo.lineid=mdi_lineinfo.id
      left join mdi_kpi on mdi_kpi.lineid=mdi_lineinfo.linegroup where date(starttime) between '%s' and '%s'
      group by date(starttime)) as A left join (select sum(qty) as defect, linegroup, date(recordtime) as date2 from mdi_issuelog
      left join mdi_lineinfo on mdi_issuelog.linename=mdi_lineinfo.linename
      where sqdctype='质量' and date(recordtime) between '%s' and '%s'
      group by date(recordtime)) as B
      on A.date = B.date2 and A.linegroup = B.linegroup""" % (stime, etime, stime, etime)
        sql2 = """
        select sum(qty) as qt,round(avg(mdi_probleminfo.trigger),0) as tg,mdi_issuelog.sqdctype as stype,mdi_issuelog.problemtype as ptype,linegroup
        from mdi_issuelog left join mdi_lineinfo on mdi_issuelog.linename=mdi_lineinfo.linename
        left join mdi_probleminfo on mdi_issuelog.problemtype=mdi_probleminfo.problemtype and mdi_issuelog.sqdctype=mdi_probleminfo.sqdctype
        and (mdi_issuelog.linename=mdi_probleminfo.linename or mdi_probleminfo.linename='all')
        where mdi_issuelog.sqdctype='质量' AND date(mdi_issuelog.recordtime) between '%s' and  '%s'
        group by mdi_issuelog.problemtype""" % (stime, etime)
        sql3 = """
        select date(recordtime) as recorddate,`desc` as descd,mdi_lineinfo.linename as linename,qty,problemtype
        from mdi_issuelog left join mdi_lineinfo on mdi_lineinfo.linename=mdi_issuelog.linename
        where date(recordtime) between '%s' and '%s' and sqdctype='质量'
        order by recorddate desc,linename""" % (stime, etime)
    connect = pymysql.connect(host=config[env].wrdb['host'], port=config[env].wrdb['port'], user=config[env].wrdb['user'],
                              password=config[env].wrdb['password'], database=config[env].wrdb['database'], charset=config[env].wrdb['charset'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    cursor.execute(sql)
    rst = cursor.fetchall()
    outArr = {
        'fttx': [],
        'red': [],
        'yellow': [],
        'green': [],
        'ftt': [],
        'fttmin': 0,
        'paretox': [],
        'paretotarget': [],
        'paretodata': [],
        'listData': []
    }
    for r in rst:
        outArr['fttx'].append(r[0])
        outArr['red'].append(float(r[1]))
        outArr['yellow'].append(float(r[2]))
        outArr['green'].append(100-float(r[1])-float(r[2]))
        outArr['ftt'].append(float(r[3]))
    outArr['fttmin'] = min(outArr['ftt'])-1 if len(outArr['ftt']) > 0 else 0

    cursor.execute(sql2)
    rst2 = cursor.fetchall()
    for r in rst2:
        paretox = r[3] if r[3] else '其他'
        target = int(r[1]) if r[1] else 5
        pdata = int(r[0]) if r[0] else 0
        outArr['paretox'].append(paretox)
        outArr['paretotarget'].append(target)
        outArr['paretodata'].append(
            {
                'value': pdata,
                'itemStyle': {
                    'color': '#A2323D' if pdata > target else '#7CFFB2'
                }
            }
        )
    cursor.execute(sql3)
    rst3 = cursor.fetchall()
    for r in rst3:
        outArr['listData'].append(
            {
                'recorddate': datetime.datetime.strftime(r[0], "%Y-%m-%d"),
                'desc': r[1],
                'linename': r[2],
                'qty': r[3],
                'problemtype': r[4] if r[4] else '其他'
            }
        )
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data=outArr)


@api.route('/getQday', methods=['GET'])
def getQday():
    connect = pymysql.connect(host=config[env].wrdb['host'], port=config[env].wrdb['port'], user=config[env].wrdb['user'],
                              password=config[env].wrdb['password'], database=config[env].wrdb['database'], charset=config[env].wrdb['charset'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    sql2 = '''select sum(actualout) as produceQty,sum(requireout) as requireQty from mdi_hourinfo where date(mdi_hourinfo.hourstart)=current_date()'''
    cursor.execute(sql2)
    r2 = cursor.fetchone()[0]
    pQty = int(r2) if r2 else 0
    sql3 = '''select sum(qty) as defectQty from mdi_issuelog where date(mdi_issuelog.recordtime)=current_date()'''
    cursor.execute(sql3)
    r3 = cursor.fetchone()[0]
    dQty = int(r3) if r3 else 0
    qRate = round((pQty-dQty)/pQty*100, 1) if pQty else 0

    sql4 = "select * from mdi_lineinfo where active=1"
    cursor.execute(sql4)
    rst = cursor.fetchall()
    outArr = []
    for r in rst:
        shift = r[4]
        linename = r[2]
        defectQty = 0
        produceQty = 0
        if shift > 0:
            sql5 = """select sum(qty) from mdi_issuelog where sqdctype='质量'
            and linename='%s' and date(recordtime)=CURDATE() group by linename""" % linename
            cursor2 = connect.cursor()
            a = cursor2.execute(sql5)
            if a:
                defectQty = int(cursor2.fetchone()[0])
            sql6 = """select sum(scanqty) from mdi_scaninfo where shiftid=%d group by shiftid""" % shift
            cursor3 = connect.cursor()
            b = cursor3.execute(sql6)
            if b:
                produceQty = int(cursor3.fetchone()[0])
        dic = {
            'linename': linename,
            'vsm': r[3],
            'shift': shift,
            'linegroup': r[6],
            'area': r[7],
            'space': r[8],
            'defectQty': defectQty,
            'produceQty': produceQty
        }
        outArr.append(dic)
    dt = gemList(outArr)
    return responseGet("获取WR-EMDI数据成功", {'produceQty': str(pQty), 'defectQty': str(dQty),
                                         'qRate': str(qRate), 'outArr': dt})


def gemList(arr):
    dics = {}
    dics2 = {}
    for a in arr:
        if a['linegroup'] in dics.keys():
            dics[a['linegroup']]['children'].append({'value': [a['space'], a['produceQty'], a['defectQty'],
                                                               -a['defectQty'] if a['defectQty'] else a['produceQty']], 'name': a['linename']})
            dics[a['linegroup']]['value'][0] = dics[a['linegroup']]['value'][0]+a['space']
        else:
            dics[a['linegroup']] = {
                'area': a['area'],
                'name': a['linegroup'],
                'value': [a['space'], 0, 0, 0],
                'children': [
                    {'value': [a['space'], a['produceQty'], a['defectQty'], -a['defectQty'] if a['defectQty'] else a['produceQty']], 'name':a['linename']}]
            }
    subArr = dics.values()
    for s in subArr:
        if s['area'] in dics2.keys():
            dics2[s['area']]['children'].append(s)
            dics2[s['area']]['value'][0] = dics2[s['area']]['value'][0]+s['value'][0]
        else:
            dics2[s['area']] = {
                'area': s['area'],
                'name': s['area'],
                'value': [s['value'][0], 0, 0, 0],
                'children': [
                    s
                ]
            }
    outArr = list(dics2.values())
    return outArr

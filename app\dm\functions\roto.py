import random
from copy import deepcopy
# from itertools import permutations


class Roto:
    def __init__(self, productList, processTimeDic, containersDic, armsDic):
        self.typesDic = {}
        self.chartData = []
        self.starttimes = [0, 0, 0]
        self.currentArm = 0
        self.totalTime = 0
        self.productListsource = productList
        self.processTimeDic = processTimeDic
        self.containersDicsource = containersDic
        self.containersDic = deepcopy(containersDic)
        self.armsSource = armsDic
        self.armsDic = deepcopy(armsDic)
        self.setTypes()

    def getProcessTime(self, product, process):
        # 获取指定产品在指定工站的加工时间
        return self.processTimeDic[product.split('$')[0].split('#')[0]][process]

    def getContainerByProduct(self, product):
        # 根据产品找到匹配的容器
        isvalid = False
        for container in self.containersDic.values():
            if product.split('#')[0] in container['products']:
                if container['status'] == 'empty':
                    return container
                else:
                    isvalid = True
        if isvalid:
            return 'full'
        return None

    def containerinproductlist(self, container, productlist):
        # print(11, container, self.containersDic)
        # 判断容器是否在产品列表中
        for product in productlist:
            if product.split('$')[0].split('#')[0] in self.containersDic[container]['products']:
                return product
        return False

    def setTypes(self):
        self.typesDic = {
            'unload': 'grey',
            'load': '#7b9ce1',
            'bake': '#bd6d6c',
            'cool': '#75d874',
            'wait': '#fff',
            'changeover': '#e0bc78',
        }

    def hasEmptycontainer(self, pdList):
        # 判断产品列表中是否有空的容器
        for product in pdList:
            if self.getContainerByProduct(product) and self.getContainerByProduct(product) != 'full':
                return True
        return False

    def gemChartData(self, armIndex, processname, processtime, sku, container):
        self.chartData.append({
            'name': processname,
            'armIndex': self.armsDic[armIndex]['name'],
            'processtime': processtime,
            'value': [armIndex, self.starttimes[armIndex], self.starttimes[armIndex] + processtime, processtime],
            'sku': sku.split('$')[0],
            'container': container,
            'itemStyle': {
                'normal': {
                    'color': self.typesDic[processname]
                }
            }
        })
        self.starttimes[armIndex] = self.starttimes[armIndex] + processtime

    def loadProduct(self, product=''):
        if not product:
            loadTime = 0
        else:
            loadTime = self.getProcessTime(product, 'load')
        bakeime = self.getProcessTime(
            self.armsDic[self.getBakearm(self.currentArm)]['product'], 'bake') if self.armsDic[self.getBakearm(self.currentArm)]['product'] else 0
        cooltime = self.getProcessTime(
            self.armsDic[self.getNextarm(self.currentArm)]['product'], 'cool') if self.armsDic[self.getNextarm(self.currentArm)]['product'] else 0
        unloadTime = self.getProcessTime(
            self.armsDic[self.getNextarm(self.currentArm)]['product'], 'unload') if self.armsDic[self.getNextarm(self.currentArm)]['product'] else 0
        coolwaitTime = 0
        if cooltime < loadTime:
            coolwaitTime = loadTime-cooltime
        maxtime = max(bakeime, loadTime, cooltime+unloadTime+coolwaitTime)
        # maxtime = maxtime2 + unloadTime

        self.totalTime = self.totalTime + maxtime
        self.gemChartData(self.currentArm, 'load', loadTime,
                          self.armsDic[self.currentArm]['product'], self.armsDic[self.currentArm]['container'])
        self.gemChartData(self.getBakearm(self.currentArm), 'bake', bakeime, self.armsDic[self.getBakearm(
            self.currentArm)]['product'], self.armsDic[self.getBakearm(self.currentArm)]['container'])
        self.gemChartData(self.getNextarm(self.currentArm), 'cool', cooltime, self.armsDic[self.getNextarm(
            self.currentArm)]['product'], self.armsDic[self.getNextarm(self.currentArm)]['container'])
        if cooltime < loadTime:
            self.gemChartData(self.getNextarm(self.currentArm), 'wait', coolwaitTime, self.armsDic[self.getNextarm(
                self.currentArm)]['product'], self.armsDic[self.getNextarm(self.currentArm)]['container'])
        self.gemChartData(self.getNextarm(self.currentArm), 'unload', unloadTime, self.armsDic[self.getNextarm(
            self.currentArm)]['product'], self.armsDic[self.getNextarm(self.currentArm)]['container'])
        if loadTime < maxtime:
            self.gemChartData(self.currentArm, 'wait', maxtime-loadTime,
                              self.armsDic[self.currentArm]['product'], self.armsDic[self.currentArm]['container'])
        if bakeime < maxtime:
            self.gemChartData(self.getBakearm(self.currentArm), 'wait', maxtime-bakeime, self.armsDic[self.getBakearm(
                self.currentArm)]['product'], self.armsDic[self.getBakearm(self.currentArm)]['container'])
        if coolwaitTime+unloadTime+cooltime < maxtime:
            self.gemChartData(self.getNextarm(self.currentArm), 'wait', maxtime-cooltime-coolwaitTime-unloadTime, self.armsDic[self.getNextarm(
                self.currentArm)]['product'], self.armsDic[self.getNextarm(self.currentArm)]['container'])
        # if unloadTime < maxtime:
        #     self.gemChartData(self.getNextarm(self.currentArm), 'wait', maxtime-unloadTime, self.armsDic[self.getNextarm(
        #         self.currentArm)]['product'], self.armsDic[self.getNextarm(self.currentArm)]['container'])
        if self.armsDic[self.currentArm]['container']:
            self.containersDic[self.armsDic[self.currentArm]['container']]['status'] = 'full'
        nextArm = self.getNextarm(self.currentArm)
        if self.armsDic[nextArm]['container']:
            self.containersDic[self.armsDic[nextArm]
                               ['container']]['status'] = 'empty'
        self.armsDic[nextArm]['product'] = ''
        self.currentArm = nextArm

    def changeover(self, currentContainer):
        self.totalTime += self.containersDic[currentContainer['name']
                                             ]['changeovertime']
        self.gemChartData(self.currentArm, 'changeover',
                          self.containersDic[currentContainer['name']]['changeovertime'], '', '')
        self.gemChartData(self.getBakearm(self.currentArm), 'wait', self.containersDic[currentContainer['name']]['changeovertime'], self.armsDic[self.getBakearm(
            self.currentArm)]['product'], self.armsDic[self.getBakearm(self.currentArm)]['container'])
        self.gemChartData(self.getNextarm(self.currentArm), 'wait', self.containersDic[currentContainer['name']]['changeovertime'],
                          self.armsDic[self.getNextarm(self.currentArm)]['product'], self.armsDic[self.getNextarm(
                              self.currentArm)]['container'])
        if self.armsDic[self.currentArm]['container']:
            self.containersDic[currentContainer['name']]['status'] = 'empty'
        self.armsDic[self.currentArm]['product'] = ''
        self.armsDic[self.currentArm]['container'] = ''

    def getNextarm(self, currentarm):
        if currentarm == 0:
            return 1
        elif currentarm == 1:
            return 2
        else:
            return 0

    def getBakearm(self, currentarm):
        if currentarm == 0:
            return 2
        elif currentarm == 1:
            return 0
        else:
            return 1

    def notmoreforcontainer(self, productList, containers):
        for container in containers:
            if self.containerinproductlist(container, productList):
                return False
        return True

    def resetChart(self):
        self.chartData.clear()
        self.starttimes[0] = 0
        self.starttimes[1] = 0
        self.starttimes[2] = 0
        self.armsDic = deepcopy(self.armsSource)
        self.containersDic = deepcopy(self.containersDicsource)
        self.totalTime = 0

    def getTimeinseq(self, pdList, thisstart):
        self.resetChart()
        self.totalTime = 0
        self.currentArm = thisstart
        productList = deepcopy(pdList)
        while len(productList) > 0 or not (self.armsDic[0]['product'] == '' and self.armsDic[1]['product'] == '' and self.armsDic[2]['product'] == ''):
            if len(productList) == 0:
                self.loadProduct()
                continue
            product = productList[0]
            currentProduct = product.split('$')[0]
            currentContainer = self.getContainerByProduct(currentProduct)
            if not currentContainer:
                return False, [f'该产品{currentProduct}没有找到匹配的模具，请确认']
            if currentContainer == 'full':
                self.loadProduct()
                continue
            if currentContainer['name'] in self.armsDic[self.currentArm]['containers']:
                if self.armsDic[self.currentArm]['container'] == '' or self.armsDic[self.currentArm]['container'] == currentContainer['name']:
                    self.armsDic[self.currentArm]['product'] = currentProduct
                    self.armsDic[self.currentArm]['container'] = currentContainer['name']
                    self.loadProduct(currentProduct)
                    productList.remove(product)
                    continue
                else:
                    self.changeover(currentContainer)
                    continue
            self.loadProduct()
        newlist = []
        for item in pdList:
            newlist.append(item.split('$')[0])
        return True, [thisstart, newlist, self.totalTime, self.chartData]

    def gettotalTime(self, pdList):
        self.resetChart()
        # 初始化总时间为0
        seqList = []
        # 获取随机0，1或2
        thisstart = random.randint(0, 2)
        self.currentArm = thisstart
        triggered = 0
        triggered2 = 0
        productList = deepcopy(pdList)
        while len(productList) > 0 or not (self.armsDic[0]['product'] == '' and self.armsDic[1]['product'] == '' and self.armsDic[2]['product'] == ''):
            # print(self.armsDic, len(productList))
            if len(productList) == 0:
                self.loadProduct()
                continue
            product = productList[0]
            currentProduct = product.split('$')[0]
            currentContainer = self.getContainerByProduct(currentProduct)
            if not currentContainer:
                return False, [f'该产品{currentProduct}没有找到匹配的模具，请确认']
            if currentContainer == 'full':
                # print('isfull', self.currentArm)
                if not self.hasEmptycontainer(productList) and triggered > len(productList):
                    # print('isfull', 1)
                    self.loadProduct()
                    triggered = 0
                    triggered2 = 0
                else:
                    # print('isfull', 2)
                    productList.remove(product)
                    productList.append(product)
                    triggered += 1
                continue
            if currentContainer['name'] in self.armsDic[self.currentArm]['containers']:
                # print(22222222)
                if self.armsDic[self.currentArm]['container'] == '':
                    seqList.append(currentProduct)
                    self.armsDic[self.currentArm]['product'] = currentProduct
                    self.armsDic[self.currentArm]['container'] = currentContainer['name']
                    self.loadProduct(currentProduct)
                    productList.remove(product)
                    continue
                else:
                    # print('loadbackup')
                    showproduct = self.containerinproductlist(
                        self.armsDic[self.currentArm]['container'], productList)
                    if showproduct is not False:
                        productList.remove(showproduct)
                        seqList.append(showproduct.split('$')[0])
                        self.armsDic[self.currentArm]['product'] = showproduct
                        self.armsDic[self.currentArm]['container'] = self.armsDic[self.currentArm]['container']
                        self.loadProduct(showproduct.split('$')[0])
                        continue
                    else:
                        self.changeover(currentContainer)
                        continue

            else:
                # print('eske')
                if self.notmoreforcontainer(productList, self.armsDic[self.currentArm]['containers']) or triggered2 > len(productList):
                    # print('eske2')
                    self.loadProduct()
                    triggered = 0
                    triggered2 = 0
                else:
                    # print('eska3', productList)
                    productList.remove(product)
                    productList.append(product)
                    # print('eska33', productList)
                    triggered2 += 1
                continue
        return True, [self.totalTime, seqList,  self.chartData, thisstart]

    def getShortestTime_dp(self, times):
        minTime = float('inf')
        bestOrder = []
        for i in range(times):
            random.shuffle(self.productListsource)
            # print(11, self.productListsource)
            tag, resultList = self.gettotalTime(
                self.productListsource)
            # print(11, tag)
            if not tag:
                return False, [resultList[0]]
            if resultList[0] < minTime:
                minTime = resultList[0]
                bestOrder = resultList[1]
                chartData = deepcopy(resultList[2])
                thisstart = resultList[3]

        return True, [thisstart, bestOrder, minTime, chartData]

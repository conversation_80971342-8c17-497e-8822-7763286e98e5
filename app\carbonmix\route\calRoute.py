from flask import Blueprint, request, send_from_directory
from extensions import db
from app.public.functions import responseGet, responsePost, responseError
from app.carbonmix.functions import getServer, login_required
from sqlalchemy import or_
from ultils.log_helper import ProjectLogger
from app.carbonmix.model.models_cm import Mixrecord, Formular
import traceback
import datetime
import os
from openpyxl import load_workbook
api = Blueprint('carbonmix/calAPI', __name__)

mylogger = ProjectLogger()
kiloTopand = 2.2046
gramTopand = 0.0022046


@api.route('/submitRecord', methods=['POST'])
def submitRecord():
    res = request.json
    mixno = res.get('mixno')
    packno = res.get('packno')
    items = res.get('items')
    row = {}
    lastmixid = db.session.query(Mixrecord).order_by(
        Mixrecord.mixid.desc()).first().mixid+1
    try:
        for i in items:
            record = Mixrecord(mixid=lastmixid, mixno=mixno, packno=packno, itemname=i['itemname'],
                               itemno=i['itemno'], itemweight=i['weight'], itemsn=i['batchno'], mixdate=datetime.datetime.now())
            db.session.add(record)
            db.session.flush()
            if(i['itemname'] == '主碳粉'):
                row['mixid'] = record.mixid
                row['mixno'] = record.mixno
                row['packno'] = record.packno
                row['itemname'] = record.itemname
                row['itemno'] = record.itemno
                row['itemsn'] = record.itemsn
        db.session.commit()
        return responsePost('状态变更成功', {'row': row})
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('录入失败,重试或联系管理员')


@api.route('/submitFormular', methods=['POST'])
@login_required
def submitFormular():
    res = request.json
    mixno = res.get('mixno')
    status = res.get('status')
    items = res.get('items')
    db.session.query(Formular).filter(
        Formular.mixno == mixno).delete(synchronize_session=False)
    try:
        for i in items:
            formular = Formular(mixno=mixno, itemname=i['itemname'],
                                itemno=i['itemno'], rate=i['rate'], status=status)
            db.session.add(formular)
            db.session.commit()
            return responsePost('状态变更成功')
    except Exception:
        db.session.rollback()
        return responseError('状态变更失败,请检查是否有重复的料号或内容')


@api.route('/getFormularbymixno', methods=['GET'])
def getFormularbyid():
    res = request.args
    mixno = res.get('mixno')
    item = db.session.query(Formular).filter(Formular.mixno == mixno).all()
    row = {
        'mainrate': 0,
        'mixno': '',
        'status': '',
        'items': []
    }
    for i in item:
        if i.itemname == '主碳粉':
            row['mainrate'] = i.rate
            row['mixno'] = i.mixno
            row['status'] = i.status
        row['items'].append(
            {'itemname': i.itemname, 'itemno': i.itemno, 'rate': i.rate})
    return responseGet('成功', {'row': row})


@api.route('/getFormulars', methods=['GET'])
@login_required
def getFormulars():
    res = request.args
    keywords = res.get('keywords')
    ftype = res.get('ftype')
    items = db.session.query(Formular)
    if keywords:
        items = items.filter(Formular.mixno.like('%{0}%'.format(keywords)))
    items = items.order_by(Formular.mixno, Formular.itemname)
    if ftype == 'cal':
        items = items.filter(Formular.status == 1)
    items = items.all()
    dic = {}
    for i in items:
        mainrate = i.rate if i.itemname == '主碳粉' else 0
        if i.mixno in dic.keys():
            dic[i.mixno]['mainrate'] += mainrate
            dic[i.mixno]['mixno'] = i.mixno
            dic[i.mixno]['status'] = i.status
            dic[i.mixno]['items'].append(
                {'itemname': i.itemname, 'itemno': i.itemno, 'rate': i.rate})
        else:
            dic[i.mixno] = {'mixno': i.mixno, 'status': i.status, 'mainrate': mainrate, 'items': [
                {'itemname': i.itemname, 'itemno': i.itemno, 'rate': i.rate}]}
    return responseGet('成功', {'formulars': list(dic.values())})


@api.route('/getRecords', methods=['GET'])
@login_required
def getRecords():
    res = request.args
    keywords = res.get('keywords')
    start = res.get('start')
    end = res.get('end')
    outArr = getOutArr(keywords, start, end)
    return responseGet('成功', {'records': outArr})


def getOutArr(keywords, start, end):
    items = db.session.query(Mixrecord)
    if keywords:
        items = items.filter(or_(Mixrecord.mixno.like('%{0}%'.format(
            keywords)), Mixrecord.mixid == keywords))
    else:
        items = items.filter(Mixrecord.mixdate.between(start, end))
    items = items.order_by(Mixrecord.mixdate.desc()).all()
    dic = {}
    for i in items:
        if i.mixid in dic.keys():
            if i.itemname == '主碳粉':
                dic[i.mixid]['mixid'] = i.mixid
                dic[i.mixid]['mixno'] = i.mixno
                dic[i.mixid]['packno'] = i.packno
                dic[i.mixid]['itemname'] = i.itemname
                dic[i.mixid]['itemno'] = i.itemno
                dic[i.mixid]['itemsn'] = i.itemsn
                dic[i.mixid]['mixdate'] = datetime.datetime.strftime(
                    i.mixdate, '%Y-%m-%d %H:%M:%S')
            dic[i.mixid]['itemweight'] = round(
                dic[i.mixid]['itemweight']+i.itemweight, 2)
        else:
            if i.itemname == '主碳粉':
                dic[i.mixid] = {
                    'mixid': i.mixid,
                    'mixno': i.mixno,
                    'packno': i.packno,
                    'itemname': i.itemname,
                    'itemno': i.itemno,
                    'itemsn': i.itemsn,
                    'mixdate': datetime.datetime.strftime(i.mixdate, '%Y-%m-%d %H:%M:%S'),
                    'itemweight': i.itemweight
                }
            else:
                dic[i.mixid] = {
                    'itemweight': i.itemweight
                }
    outArr = list(dic.values())
    return outArr


@api.route('/downloadrecord', methods=['GET'])
def downloadrecord():
    mylogger.debug('downloadrecord')
    res = request.args
    keywords = res.get('query[keywords]')
    start = res.get('query[start]')
    end = res.get('query[end]')
    outArr = getOutArr(keywords, start, end)
    filename = 'carbonrecord.xlsx'
    path = getServer()['basePath']
    newFilename = recordExcelissue(
        path, path+'templates/'+filename, outArr, start, end)
    if os.path.isfile(os.path.join(path, newFilename)):
        response = send_from_directory(path, newFilename, as_attachment=True)
        response.headers["Access-Control-Expose-Headers"] = "fname"
        response.headers['fname'] = newFilename
        return response
    return responseError('没有找到下载文件')


def recordExcelissue(path, file, arr, sd, ed):
    wb = load_workbook(file)
    ws = wb['Record']
    for i in range(len(arr)):
        ws.cell(i+2, 1).value = arr[i]['mixid']
        ws.cell(i+2, 2).value = arr[i]['mixdate']
        ws.cell(i+2, 3).value = arr[i]['mixno']
        ws.cell(i+2, 4).value = arr[i]['itemweight']
    newFilename = datetime.datetime.strftime(
        datetime.date.today(), '%Y-%m-%d')+' - '+'Suggest.xlsx'
    wb.save(path+newFilename)
    wb.close()
    return newFilename


@api.route('/getRecordbyid', methods=['GET'])
@login_required
def getRecordbyid():
    res = request.args
    mixid = res.get('mixid')
    items = db.session.query(Mixrecord).filter(Mixrecord.mixid == mixid).all()
    dic = {}
    for i in items:
        if i.mixno in dic.keys():
            dic[i.mixno]['mixno'] = i.mixno
            dic[i.mixno]['items'].append(
                {'itemname': i.itemname, 'itemno': i.itemno, 'itemsn': i.itemsn, 'packno': i.packno, 'itemweight': i.itemweight})
        else:
            dic[i.mixno] = {'mixno': i.mixno, 'items': [
                {'itemname': i.itemname, 'itemno': i.itemno, 'itemsn': i.itemsn, 'packno': i.packno, 'itemweight': i.itemweight}]}
    if len(list(dic.values())) > 0:
        return responseGet('成功', {'record': list(dic.values())[0]})
    else:
        return responseError('没有找到记录')

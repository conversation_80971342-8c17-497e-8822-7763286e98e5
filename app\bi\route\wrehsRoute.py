from flask import Blueprint, jsonify, request
import requests
import os
import json
import threading
from config import config, env

api = Blueprint('bi/wrehsAPI', __name__)


@api.route('/')
def index():
    return 'ok等等'


@api.route('/getAppv', methods=['GET'])
def getAppv():
    res = requests.get('https://welean.pentair.cn/xcx/Code/BI/getappv.php')
    # res = requests.get('http://localhost/BI/getappv.php')
    content = res.text
    if content.startswith(u'\ufeff'):
        content = content.encode('utf8')[3:].decode('utf8')
    if res.status_code == 200:
        meta = {'status': 200, 'msg': '成功'}
        return jsonify(meta=meta, data=json.loads(content))
    else:
        meta2 = {'status': 204, 'msg': '失败'}
        return jsonify(meta=meta2)


@api.route('/getCross', methods=['GET'])
def getCross():
    r = request.args
    print(r)
    stime = r.get('stime')
    etime = r.get('etime')
    area = r.get('area')
    url = 'https://welean.pentair.cn/xcx/Code/BI/getcross.php?stime=' + stime+'&etime='+etime+'&area='+area
    print(url)
    res = requests.get(url)
    content = res.text
    if content.startswith(u'\ufeff'):
        content = content.encode('utf8')[3:].decode('utf8')
    if res.status_code == 200:
        meta = {'status': 200, 'msg': '成功'}
        return jsonify(meta=meta, data=json.loads(content))
    else:
        meta2 = {'status': 204, 'msg': '失败'}
        return jsonify(meta=meta2)


@api.route('/getClose', methods=['GET'])
def getClose():
    res = requests.get('https://welean.pentair.cn/xcx/Code/BI/getclose.php')
    # res = requests.get('http://localhost/BI/getclose.php')
    content = res.text
    if content.startswith(u'\ufeff'):
        content = content.encode('utf8')[3:].decode('utf8')
    if res.status_code == 200:
        meta = {'status': 200, 'msg': '成功'}
        return jsonify(meta=meta, data=json.loads(content))
    else:
        meta2 = {'status': 204, 'msg': '失败'}
        return jsonify(meta=meta2)


@api.route('/getArea', methods=['GET'])
def getArea():
    res = requests.get('https://welean.pentair.cn/xcx/Code/BI/getarea.php')
    # res = requests.get('http://localhost/BI/getarea.php')
    content = res.text
    if content.startswith(u'\ufeff'):
        content = content.encode('utf8')[3:].decode('utf8')
    if res.status_code == 200:
        meta = {'status': 200, 'msg': '成功'}
        return jsonify(meta=meta, data=json.loads(content))
    else:
        meta2 = {'status': 204, 'msg': '失败'}
        return jsonify(meta=meta2)


def download_img(url, path):
    # 下载图片
    r = requests.get(url)
    print(url, path, r.status_code, os.path.exists(path))  # 返回状态码
    if r.status_code == 200:
        img = r.content
        if not os.path.exists(path):
            with open(path, 'wb') as f:
                f.write(img)


@api.route('/getDetails', methods=['GET'])
def getDetails():
    imgPath = config[env].localPath+"BI/img/"
    r = request.args
    idate = r.get('idate')
    url = 'https://welean.pentair.cn/xcx/Code/BI/getdetails.php?idate=' + idate
    # url = 'http://localhost/BI/getdetails.php?idate=' + idate
    res = requests.get(url)
    content = res.text
    if content.startswith(u'\ufeff'):
        content = content.encode('utf8')[3:].decode('utf8')
    if res.status_code == 200:
        meta = {'status': 200, 'msg': '成功'}
        dts = json.loads(content)
        for d in dts:
            p = d['beforepic']
            pa = d['afterpic']
            picName = p[p.rfind('/')+1:].split('.')[0]
            picAdx = p[p.rfind('/')+1:].split('.')[1]
            print(imgPath, picName)
            if p:
                t = threading.Thread(target=download_img,
                                     args=(p, imgPath+picName+'.'+picAdx))
                t.setDaemon(True)
                t.start()
                # download_img(p, imgPath+picName+'.'+picAdx)
            if pa:
                t = threading.Thread(target=download_img,
                                     args=(pa, imgPath+picName+'after.'+picAdx))
                t.setDaemon(True)
                t.start()
                # download_img(pa, imgPath+picName+'after.'+picAdx)
            d['beforepic'] = config[env].base_url+'BI/img/'+picName+'.'+picAdx
            d['afterpic'] = config[env].base_url+'BI/img/'+picName+'after.'+picAdx
        return jsonify(meta=meta, data=dts)
    else:
        meta2 = {'status': 204, 'msg': '失败'}
        return jsonify(meta=meta2)

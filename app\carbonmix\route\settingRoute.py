from flask import Blueprint, request, send_from_directory
import os
from app.public.functions import responseError
from app.carbonmix.functions import getServer, login_required
from ultils.log_helper import ProjectLogger
api = Blueprint('carbonmix/settingAPI', __name__)

mylogger = ProjectLogger()


@api.route('/downloadrecord', methods=['GET'])
def downloadrecord():
    mylogger.debug('downloadrecord')
    res = request.args
    mylogger.debug(res)
    # mylogger.debug(res.get('url'))
    # newFilename = res.get('url').split('/')[-1].split('\\')[-1]
    newFilename = 'RCCM_188fbf292a2266bb.txt'
    path = getServer()['uploadPath']+'rccm/'
    if os.path.isfile(os.path.join(path, newFilename)):
        response = send_from_directory(path, newFilename, as_attachment=True)
        response.headers["Access-Control-Expose-Headers"] = "fname"
        response.headers['fname'] = newFilename
        return response
    return responseError('没有找到下载文件')

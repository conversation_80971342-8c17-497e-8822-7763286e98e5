var I=(n,u,e)=>new Promise((l,d)=>{var f=t=>{try{a(e.next(t))}catch(i){d(i)}},c=t=>{try{a(e.throw(t))}catch(i){d(i)}},a=t=>t.done?l(t.value):Promise.resolve(t.value).then(f,c);a((e=e.apply(n,u)).next())});import{n as S,aX as D,aj as q,S as O,aE as b}from"./index-BnxEuBzx.js";import k from"./editDefectForm-CA2niQRC.js";import{a as C}from"./front-CiGk0t8u.js";import{u as s}from"./prod-CmDsiAIL.js";import"./moment-C3TZ8gAF.js";S();function A(n,u,e,l){var d,f,c,a,t,i;D({title:(n=="edit"?"调整":"新增")+u+"#机不良数据",props:{shiftinfo:{selecteddate:s().selectedDate,shift:s().shift,machineid:s().machineId},action:n,formInline:{hourid:(d=e==null?void 0:e.hourid)!=null?d:"",pn:(f=e==null?void 0:e.pn)!=null?f:"",output:(c=e==null?void 0:e.output)!=null?c:"",unit:(a=e==null?void 0:e.unit)!=null?a:"EA",defect_qty:(t=e==null?void 0:e.defect_count)!=null?t:0,defect_type:(i=e==null?void 0:e.defect_type)!=null?i:0}},width:"30%",draggable:!0,fullscreenIcon:!1,closeOnClickModal:!1,contentRenderer:()=>q(k),beforeSure:(M,P)=>I(this,[M,P],function*(m,{options:p}){var y,_;const h={selected_date:s().selectedDate,hourid:(y=e==null?void 0:e.hourid)!=null?y:"",machine_id:s().machineId,pn:(_=e==null?void 0:e.pn)!=null?_:"",defect_qty:p.props.formInline.defect_qty,defect_type:p.props.formInline.defect_type,unit:p.props.formInline.unit};JSON.stringify(O(p.props.formInline))!=JSON.stringify(h)?C(h).then(g=>{g.meta.status==201&&(l(),b("变更成功！",{customClass:"el",type:"success"}),m())}):m()})})}export{A as changedefectdata};

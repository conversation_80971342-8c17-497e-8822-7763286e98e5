import{u as s}from"./hooks-BOphKYQG.js";import{d as D,n as u,q as L,a2 as U,r as t,o as B,c as E,b as e,h as l,u as o,f as r,ba as F,_ as I}from"./index-BnxEuBzx.js";import{d as A}from"./refresh-C_2cW1e5.js";const N={width:24,height:24,body:'<path fill="currentColor" d="M12 14v2a6 6 0 0 0-6 6H4a8 8 0 0 1 8-8Zm0-1c-3.315 0-6-2.685-6-6s2.685-6 6-6s6 2.685 6 6s-2.685 6-6 6Zm0-2c2.21 0 4-1.79 4-4s-1.79-4-4-4s-4 1.79-4 4s1.79 4 4 4Zm9 6h1v5h-8v-5h1v-1a3 3 0 1 1 6 0v1Zm-2 0v-1a1 1 0 1 0-2 0v1h2Z"/>'},P=N,S={width:24,height:24,body:'<path fill="currentColor" d="M18 8h2a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h2V7a6 6 0 1 1 12 0v1ZM5 10v10h14V10H5Zm6 4h2v2h-2v-2Zm-4 0h2v2H7v-2Zm8 0h2v2h-2v-2Zm1-6V7a4 4 0 0 0-8 0v1h8Z"/>'},V=S,q={width:1024,height:1024,body:'<path fill="currentColor" d="M176 416a112 112 0 1 1 0 224a112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224a112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224a112 112 0 0 1 0-224z"/>'},T=q,j={width:1024,height:1024,body:'<path fill="currentColor" d="M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32zm192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32z"/>'},G=j,J={width:1024,height:1024,body:'<path fill="currentColor" d="m199.04 672.64l193.984 112l224-387.968l-193.92-112l-224 388.032zm-23.872 60.16l32.896 148.288l144.896-45.696L175.168 732.8zM455.04 229.248l193.92 112l56.704-98.112l-193.984-112l-56.64 98.112zM104.32 708.8l384-665.024l304.768 175.936L409.152 884.8h.064l-248.448 78.336L104.32 708.8zm384 254.272v-64h448v64h-448z"/>'},K=J,O={width:24,height:24,body:'<path fill="currentColor" d="M11 11V7h2v4h4v2h-4v4h-2v-4H7v-2h4Zm1 11C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10Zm0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16Z"/>'},Q=O,W=D({name:"SystemUser",__name:"index",setup(X){u();const h=u(),k=u(),m=u(!1),v=u([]),w=[{label:"工号",prop:"eid"},{label:"邮箱",prop:"email"},{label:"角色",prop:"role"},{label:"英文名",prop:"name"},{label:"中文名",prop:"cnname"},{label:"状态",prop:"is_active",cellRenderer:({row:n})=>n.is_active==1?e(t("el-tag"),{type:"success"},{default:()=>[r("活动")]}):e(t("el-tag"),{type:"danger"},{default:()=>[r("禁用")]})},{label:"手机号码",prop:"phone"},{label:"上次登录",prop:"last_login"},{label:"操作",fixed:"right",width:180,slot:"operation"}],$=()=>{m.value=!0,F().then(n=>{v.value=n.data,m.value=!1}).finally(()=>{m.value=!1})};L(()=>{$()});const b=n=>{},Z=n=>{n.value.resetFields()},c=U({eid:"",name:"",is_active:"",status:!0});return(n,i)=>{const y=t("el-input"),p=t("el-form-item"),g=t("el-option"),z=t("el-select"),d=t("el-button"),x=t("el-form"),C=t("el-popconfirm"),_=t("el-dropdown-item"),M=t("el-dropdown-menu"),H=t("el-dropdown"),R=t("pure-table");return B(),E("div",null,[e(x,{ref_key:"formRef",ref:h,inline:!0,model:c,class:"search-form bg-bg_color"},{default:l(()=>[e(p,{label:"用户EID：",prop:"username"},{default:l(()=>[e(y,{modelValue:c.eid,"onUpdate:modelValue":i[0]||(i[0]=a=>c.eid=a),placeholder:"工号数字不含E",clearable:"",class:"!w-[180px]"},null,8,["modelValue"])]),_:1}),e(p,{label:"姓名",prop:"name"},{default:l(()=>[e(y,{modelValue:c.name,"onUpdate:modelValue":i[1]||(i[1]=a=>c.name=a),placeholder:"中文或英文部分即可",clearable:"",class:"!w-[180px]"},null,8,["modelValue"])]),_:1}),e(p,{label:"状态",prop:"cname"},{default:l(()=>[e(z,{modelValue:c.is_active,"onUpdate:modelValue":i[2]||(i[2]=a=>c.is_active=a),clearable:"",class:"!w-[180px]"},{default:l(()=>[e(g,{label:"已开启",value:"1"}),e(g,{label:"已禁用",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(p,null,{default:l(()=>[e(d,{type:"primary",icon:o(s)("ri:search-line"),loading:m.value},{default:l(()=>[r(" 搜索 ")]),_:1},8,["icon","loading"])]),_:1}),e(p,null,{default:l(()=>[e(d,{icon:o(s)(o(A)),onClick:i[3]||(i[3]=a=>Z(h.value))},{default:l(()=>[r(" 重置 ")]),_:1},8,["icon"])]),_:1}),e(p,null,{default:l(()=>[e(d,{type:"primary",icon:o(s)(o(Q)),onClick:i[4]||(i[4]=a=>n.openDialog())},{default:l(()=>[r(" 新增用户 ")]),_:1},8,["icon"])]),_:1})]),_:1},8,["model"]),e(R,{ref_key:"tableRef",ref:k,"row-key":"id",adaptive:"",adaptiveConfig:{offsetBottom:108},"align-whole":"center","table-layout":"auto",loading:m.value,data:v.value,columns:w,"header-cell-style":{background:"var(--el-fill-color-light)",color:"var(--el-text-color-primary)"}},{operation:l(({row:a})=>[e(d,{class:"reset-margin",link:"",type:"primary",icon:o(s)(o(K)),onClick:f=>n.openDialog("修改",a)},{default:l(()=>[r(" 修改 ")]),_:2},1032,["icon","onClick"]),e(C,{width:"250px",title:`是否确认删除用户${a.cnname}`,onConfirm:f=>b(a)},{reference:l(()=>[e(d,{class:"reset-margin",link:"",type:"primary",icon:o(s)(o(G))},{default:l(()=>[r(" 删除 ")]),_:1},8,["icon"])]),_:2},1032,["title","onConfirm"]),e(H,{trigger:"click"},{dropdown:l(()=>[e(M,null,{default:l(()=>[e(_,null,{default:l(()=>[e(d,{link:"",type:"primary",icon:o(s)(o(V)),onClick:f=>n.handleReset(a)},{default:l(()=>[r(" 重置密码 ")]),_:2},1032,["icon","onClick"])]),_:2},1024),e(_,null,{default:l(()=>[e(C,{width:"250px",title:`是否确认${a.is_active==1?"禁用":"启用"}用户${a.cnname}`,onConfirm:f=>b(a)},{reference:l(()=>[e(d,{link:"",type:"primary",icon:o(s)(o(V))},{default:l(()=>[r(" 禁用/启用 ")]),_:1},8,["icon"])]),_:2},1032,["title","onConfirm"])]),_:2},1024),e(_,null,{default:l(()=>[e(d,{link:"",type:"primary",icon:o(s)(o(P)),onClick:f=>n.handleRole(a)},{default:l(()=>[r(" 分配角色 ")]),_:2},1032,["icon","onClick"])]),_:2},1024)]),_:2},1024)]),default:l(()=>[e(d,{class:"ml-3 mt-[2px]",link:"",type:"primary",icon:o(s)(o(T))},null,8,["icon"])]),_:2},1024)]),_:1},8,["loading","data","header-cell-style"])])}}}),oe=I(W,[["__scopeId","data-v-f58ecb5d"]]);export{oe as default};

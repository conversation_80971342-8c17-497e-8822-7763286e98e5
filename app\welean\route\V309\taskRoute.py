from flask import Blueprint, request
from app.welean.model.models_welean import Layeredlist, Layered, Userinfo, Questiontask, Layeredresult, Suggest, Account
from extensions import db
from datetime import datetime
from sqlalchemy.orm import aliased
from sqlalchemy import func,  or_, and_, desc, text
from app.public.functions import responseError, responsePost, responseGet,  login_required
from app.welean.functions import getServer, PROGRESSthread, resultStatus
import hashlib
import traceback
api = Blueprint('welean/V309/taskAPI', __name__)
Userinfo2 = aliased(Userinfo)
creditDic = {
    'check': '直接确认',
    'input': '仅记录',
    'option': '选择',
    'range': '数字范围',
    'photo': '拍照上传',
    'training': '培训验证'
}
creditDic2 = {
    'check': '直接确认',
    'input': '仅记录',
    'option': '选择',
    'issue': '问题汇报',
    'photo': '拍照上传',
    'audit': '层级审核'
}
creditDic3 = {
    'check': '直接确认',
    'input': '仅记录',
    'option': '选择',
    'range': '数字范围',
    'photo': '拍照上传',
    'audit': '层级审核'
}

auditTypes = {
    'training': '培训任务',
    'layered': '层级审核',
    'PM': 'PM维护',
    'poll': '调查表'
}


@ api.route('/getIssueResult', methods=['GET'])
@ login_required
def getIssueResult():
    res = request.args
    listid = res.get('listid')
    eid = res.get('eid')
    rst = db.session.query(Suggest).filter(Suggest.audittype == 5,
                                           Suggest.eid == eid, Suggest.auditid == listid).first()
    if rst:
        return responseGet("提交成功")
    return responseError('你还没有提交问题，请提交后再点击确认')


@ api.route('/getLayeredChart', methods=['GET'])
@ login_required
def getLayeredChart():
    res = request.args
    plant = res.get('plant')
    sd = res.get('startDate')
    ed = res.get('endDate')
    tp = res.get('tp')
    subitem = res.get('subitem')
    charts = []
    if sd:
        charts = getCharts(plant, sd, ed, tp, subitem)
    subs = db.session.query(Layeredlist.subitem).filter(
        Layeredlist.tasktype == tp).group_by(Layeredlist.subitem).all()
    options = []
    for s in subs:
        options.append({
            'label': s.subitem,
            'value': s.subitem
        })
    return responseGet("获取列表成功", {'charts': charts, 'options': options})


def getCharts(plant, sd, ed, tp, subitem):
    charts = []
    results = db.session.query(func.count(Layeredresult.Id).label('total'),
                               func.sum(func.if_(Layeredresult.status.in_([1, 2]), 1, 0)).label('finished'), Userinfo.dept1).join(
        Userinfo, Layeredresult.eid == Userinfo.eid).join(Layeredlist, Layeredresult.listid == Layeredlist.Id).filter(Userinfo.plant == plant).filter(
        Layeredresult.starttime.between(sd, ed)).filter(Layeredlist.tasktype == tp)
    if subitem:
        results = results.filter(Layeredlist.subitem == subitem)
    results = results.group_by(Userinfo.dept1).all()
    bar = []
    print
    for rr in results:
        ddic = {
            'name': rr.dept1,
            'value': round(rr.finished/rr.total*100, 1)
        }
        bar.append(ddic)
    charts.append({
        'title': '部门完成率',
        'type': 'mount',
        'opts': {
            'xAxis': {
                  'disableGrid': True,
                  'rotateLabel': True
                  },
            'legend': {
                'show': False,
                'position': "bottom",
                'lineHeight': 25
            },
            'yAxis': {
                'data': [
                    {
                        'min': 0
                    }
                ]
            },
            'extra': {
                'mount': {
                    'type': "mount",
                    'widthRatio': 1.5
                }
            }
        },
        'chartData': {
            'series': [
                {
                    'data': bar
                }
            ]
        }
    })
    return charts


@ api.route('/submitFeedback', methods=['POST'])
@ login_required
def submitFeedback():
    res = request.json
    tasktype = res.get('tasktype')
    subitem = res.get('subitem')
    eid = res.get('eid')
    exeid = res.get('exeid')
    starttime = res.get('starttime')
    astatus = int(res.get('current'))
    feedback = res.get('feedback') if res.get('feedback') else ''
    items = db.session.query(Layeredresult.Id).join(Layeredlist, Layeredresult.listid == Layeredlist.Id).filter(Layeredlist.tasktype == tasktype).filter(
        Layeredlist.subitem == subitem).filter(Layeredresult.eid == eid).filter(Layeredresult.starttime == starttime)
    if astatus == 0:
        items = items.filter(Layeredresult.status == 0)
    elif astatus == 1:
        items = items.filter(Layeredresult.status == 3)
    elif astatus == 2:
        items = items.filter(Layeredresult.status.in_([1, 2, 4, 5]))
    items = items.all()
    ids = []
    for i in items:
        ids.append(i.Id)
    try:
        db.session.query(Layeredresult).filter(Layeredresult.Id.in_(ids)).update(
            {
                'comments': feedback,
                'spv': exeid
            },
            synchronize_session=False
        )
        if feedback:
            user = db.session.query(Account).filter(Account.eid == eid).first()
            otBody = {
                'content': subitem[:20],
                'idate': starttime,
                'owner': '工作任务',
                'status': '主管对你工作做出评价',
                'comments': feedback[:20]
            }
            href = 'pages/login/login'
            PROGRESSthread(otBody, user.wxid, href)
        db.session.commit()
        return responsePost('成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('失败')


@ api.route('/confirmTask', methods=['POST'])
@ login_required
def confirmTask():
    res = request.json
    Id = res.get('Id')
    sid = res.get('sid') if res.get('sid') else 0
    finishtime = datetime.now()
    result = res.get('result')
    try:
        rst = db.session.query(Layeredresult).filter(Layeredresult.Id == Id).first()
        oldstatus = rst.status
        newstatus = 2 if sid > 0 else 1
        if oldstatus == 3:
            newstatus = 5 if sid > 0 else 4
        db.session.query(Layeredresult).filter(Layeredresult.Id == Id).update(
            {
                'sugid': sid,
                'result': result,
                'finishtime': finishtime,
                'status': newstatus
            }
        )
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('完成此项目失败，请联系管理员')
    return responsePost('成功')


@ api.route('/uploadJobpic', methods=['POST'])
@ login_required
def uploadJobpic():
    file_obj = request.files.get('file')
    sid = request.form.get('sid')
    mystr = ('jobs' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        sql = text("update wl_layeredresult set pics=(case when pics is null then '%s' else concat(pics,',','%s') end) where Id=%d" % (
            name+appendix, name+appendix, int(sid)))
        db.session.execute(sql)
        db.session.commit()
        file_obj.save(getServer()['basePath']+'jobs/' + name+appendix)
        return responsePost("更新成功", {'upload_url': getServer()['baseUrl']+'jobs/'+name+appendix, 'img': name+appendix})
    return responseError("上传文件失败，请联系管理员")


@ api.route('/getWorktask', methods=['GET'])
@ login_required
def getWorktask():
    res = request.args
    tasktype = res.get('tasktype')
    subitem = res.get('subitem').split('/')[0]
    eid = res.get('eid')
    starttime = res.get('starttime')
    astatus = int(res.get('current'))
    cardsData = []
    rsts = db.session.query(Layeredresult.Id).join(Layeredlist, Layeredresult.listid == Layeredlist.Id).filter(
        Layeredlist.credittype == 'option').filter(Layeredresult.sugid > 0).filter(Layeredresult.status == 0).filter(
        or_(Layeredlist.content == '是:否:不适用', Layeredlist.content == '是:否')).all()
    finishedIds = []
    for r in rsts:
        finishedIds.append(r.Id)
    if len(finishedIds) > 0:
        db.session.query(Layeredresult).filter(Layeredresult.Id.in_(finishedIds)).update(
            {
                'status': 2,
                'finishtime': datetime.now(),
                'result': '否'
            },
            synchronize_session=False
        )
        db.session.commit()
    items = db.session.query(Layeredresult.Id, Layeredresult.sugid, Layeredresult.result, Layeredlist.stdtime, Layeredlist.area, Layeredresult.area.label('larea'),
                             Layeredresult.pics, func.max(Layeredresult.status).label(
                                 'status'), Layeredlist.content, Layeredresult.starttime, Layeredlist.freq, Layeredresult.eid,
                             Layeredlist.name, Layeredlist.des, Layeredlist.credittype, Layeredlist.high, Layeredlist.low, Suggest.Id.label(
                                 'sid'), Layeredlist.content).join(Layeredlist, Layeredresult.listid == Layeredlist.Id).outerjoin(
        Suggest, and_(Suggest.audittype == 5, Suggest.auditid == Layeredresult.Id)).filter(Layeredresult.starttime == starttime).filter(
        Layeredlist.tasktype == tasktype).filter(Layeredlist.subitem == subitem).filter(Layeredresult.eid == eid).group_by(Layeredresult.Id)
    if astatus == 0:
        items = items.filter(Layeredresult.status == 0)
    elif astatus == 1:
        items = items.filter(Layeredresult.status == 3)
    elif astatus == 2:
        items = items.filter(Layeredresult.status.in_([1, 2, 4, 5]))
    items = items.all()
    for i in items:
        nid = 0
        taskid = 0
        tasktitle = ''
        auditsData = []
        myresult = i.result
        if i.credittype == 'training':
            train = db.session.query(Questiontask).filter(Questiontask.Id == int(i.content)).first()
            nid = train.newsid
            taskid = train.Id
            tasktitle = train.tasktitle
            myresult = None
        elif i.credittype == 'audit':
            audits = db.session.query(Layeredlist.tasktype, Userinfo.eid, Layeredresult.status, Layeredresult.starttime, Layeredlist.subitem, Userinfo.cname,
                                      Userinfo.dept1).join(
                Layeredresult, Layeredlist.Id == Layeredresult.listid).join(Layered, Layeredresult.eid == Layered.eid).join(
                Userinfo, Userinfo.eid == Layeredresult.eid).filter(Layered.spv == eid).filter(Layeredresult.status.in_([1, 2, 4, 5])).filter(
                Layered.tasktype == tasktype).filter(and_(func.datediff(i.starttime, Layeredresult.starttime) < i.freq*2,
                                                          func.datediff(i.starttime, Layeredresult.starttime) >= 0)).filter(Layeredresult.eid != eid).filter(
                and_(Layeredlist.tasktype == tasktype, Layeredlist.subitem == i.content)).filter(
                Layeredresult.spv.is_(None)).group_by(Layeredresult.eid).all()
            for s in audits:
                dic = {
                    'status': resultStatus[s.status],
                    'dealer': s.dept1+'/'+s.cname,
                    'exeid': s.eid,
                    'title': auditTypes[s.tasktype],
                    'starttime': datetime.strftime(s.starttime, "%Y-%m-%d %H:%M:%S"),
                    'subTitle': '开始日期:'+datetime.strftime(s.starttime, "%Y-%m-%d %H:%M:%S"),
                    'thumb': '/static/icon/user-on.png',
                    'subitem': s.subitem,
                    'tasktype': s.tasktype,
                    'url': getServer()['baseUrl']+'pimstools/' + 'task_'+s.tasktype+'.png',
                }
                auditsData.append(dic)
        picArr = []
        if i.pics:
            picArr = i.pics.split(',')
        for kk in range(len(picArr)):
            picArr[kk] = getServer()['baseUrl']+'jobs/'+picArr[kk]
        cardsData.append(
            {
                'Id': i.Id,
                'sid': i.sid,
                'sugid': i.sugid,
                'result': myresult,
                'stdtime': i.stdtime,
                'name': i.name,
                'area': i.area+(('/'+i.larea) if i.larea else ''),
                'des': i.des,
                'credittype': i.credittype,
                'high': i.high,
                'low': i.low,
                'content': i.content,
                'nid': nid,
                'taskid': taskid,
                'tasktitle': tasktitle,
                'pics': picArr,
                'status': resultStatus[i.status],
                'auditsData': auditsData
            }
        )
    return responseGet("获取列表成功", {'cardsData': cardsData})


@ api.route('/getWorktasks', methods=['GET'])
@ login_required
def getWorktasks():
    res = request.args
    eid = res.get('eid')
    plant = res.get('plant')
    keywords = res.get('keywords')
    astatus = int(res.get('astatus'))  # 0=待完成，1=已错过对应3状态,2=已完成，对应1,2状态和4状态
    nb = res.get('nb')
    tasks = db.session.query(Layeredresult.status, Userinfo.cname, Userinfo.dept1, Layeredlist.tasktype, Layeredlist.subitem, Userinfo2.cname.label('spv'), Layeredresult.comments,
                             Layeredresult.starttime, Layeredresult.area, Layeredlist.freq).join(Layeredlist, Layeredresult.listid == Layeredlist.Id).join(
        Userinfo, Layeredresult.eid == Userinfo.eid).outerjoin(Userinfo2, Userinfo2.eid == Layeredresult.spv).group_by(Layeredlist.tasktype, Layeredlist.subitem, Layeredresult.starttime)
    if keywords:
        tasks = tasks.filter(Layeredlist.subitem.like('%{0}%'.format(keywords)))
    if plant:
        tasks = tasks.filter(Layeredlist.plant == plant)
    if eid:
        tasks = tasks.filter(Layeredresult.eid == eid)
    if astatus == 0:
        tasks = tasks.filter(Layeredresult.status == 0)
    elif astatus == 1:
        tasks = tasks.filter(Layeredresult.status == 3).filter(Layeredlist.tasktype != 'poll')
    elif astatus == 2:
        tasks = tasks.filter(Layeredresult.status.in_(
            [1, 2, 4, 5])).filter(Layeredlist.tasktype != 'poll')
    tasks = tasks.limit(nb).all()
    cardsData = []
    for s in tasks:
        dic = {
            'status': resultStatus[s.status],
            'spv': s.spv if s.spv else '',
            'comments': s.comments,
            'dealer': s.dept1+'/'+s.cname,
            'exeid': eid,
            'title': auditTypes[s.tasktype],
            'starttime': datetime.strftime(s.starttime, "%Y-%m-%d %H:%M:%S"),
            'subTitle': '开始日期:'+datetime.strftime(s.starttime, "%Y-%m-%d %H:%M:%S"),
            'thumb': '/static/icon/ongoing.png' if s.freq > 0 else '',
            'subitem': s.subitem+(('/'+s.area) if s.area else ''),
            'tasktype': s.tasktype,
            'url': getServer()['baseUrl']+'pimstools/' + 'task_'+s.tasktype+'.png',
        }
        cardsData.append(dic)
    cardsData = sorted(cardsData, key=lambda dt: dt['starttime'], reverse=True)
    return responseGet("获取列表成功", {'cardsData': cardsData})


@ api.route('/getTaskcount', methods=['GET'])
@ login_required
def getTaskcount():
    res = request.args
    eid = res.get('eid')
    all = db.session.query(Layeredresult).join(Layeredlist, Layeredresult.listid == Layeredlist.Id).filter(
        Layeredresult.eid == eid).filter(Layeredresult.status == 0).group_by(Layeredlist.tasktype, Layeredlist.subitem).all()
    tasknb = len(all)
    myList = [
        {
            'name': '未完成',
            'count': tasknb if tasknb else 0
        },
        {
            'name': '已错过'
        },
        {
            'name': '已完成'
        }
    ]
    return responseGet("获取列表成功", {'list': myList})


@ api.route('/subTaskitem', methods=['POST'])
@ login_required
def subTaskitem():
    res = request.json
    Id = res.get('Id')
    area = res.get('area')
    plant = res.get('plant')
    content = res.get('content') if res.get('content') else ''
    credittype = res.get('credittype')
    des = res.get('des')
    freq = res.get('freq')
    high = res.get('high') if res.get('high') else ''
    low = res.get('low') if res.get('low') else ''
    name = res.get('name')
    stdtime = res.get('stdtime') if res.get('stdtime') else ''
    subitem = res.get('subitem')
    tasktype = res.get('tasktype')
    trainingcontent = res.get('trainingcontent')
    if credittype == 'training':
        content = trainingcontent.split(')')[0].split('(')[1]
    if not Id:
        try:
            nitem = Layeredlist(plant=plant, tasktype=tasktype,
                                subitem=subitem, area=area, freq=freq, name=name, des=des, credittype=credittype,
                                content=content, low=low, high=high, stdtime=stdtime)
            db.session.add(nitem)
            db.session.commit()
            return responsePost('新建成功')
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('新建失败，请查看是否已经存在重复的项目内容,如存在，请点开该项目编辑')
    else:
        try:
            item = db.session.query(Layeredlist).filter(Layeredlist.Id == Id).first()
            item.tasktype = tasktype
            item.subitem = subitem
            item.area = area
            item.freq = freq
            item.name = name
            item.des = des
            item.credittype = credittype
            item.content = content
            item.low = low
            item.high = high
            item.stdtime = stdtime
            db.session.commit()
            return responsePost('编辑成功')
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('编辑失败，请联系管理员')


@ api.route('/getTaskItems', methods=['GET'])
@ login_required
def getTaskItems():
    res = request.args
    tasktype = res.get('tasktype')
    subitem = res.get('subitem')
    plant = res.get('plant')
    items = db.session.query(Layeredlist).filter(Layeredlist.tasktype ==
                                                 tasktype).filter(Layeredlist.subitem == subitem).all()
    itemArr = []
    itemTypes = []
    for i in items:
        qname = ''
        if i.credittype == 'training':
            question = db.session.query(Questiontask).filter(Questiontask.Id == i.content).first()
            qname = question.tasktitle
        itemArr.append({
            'Id': i.Id,
            'name': i.name,
            'area': i.area,
            'des': i.des,
            'freq': i.freq,
            'credittype': i.credittype,
            'creditname': creditDic2[i.credittype] if i.tasktype == 'layered' else creditDic[i.credittype],
            'content': i.content,
            'trainingcontent': '('+i.content+')'+qname if qname else i.content,
            'low': i.low,
            'high': i.high,
            'stdtime': i.stdtime,
            'lastfinish': i.lastfinish,
        })
    if tasktype == 'layered':
        myTypes = creditDic2
    elif tasktype == 'training':
        myTypes = creditDic
    else:
        myTypes = creditDic
    for k, v in myTypes.items():
        itemTypes.append({
            'label': v,
            'value': k
        })
    questions = db.session.query(Questiontask).filter(Questiontask.tasktype == '特定人群').filter(
        or_(Questiontask.plant == plant, Questiontask.plant == 'ALL')).order_by(desc(Questiontask.Id)).all()
    qArr = []
    for q in questions:
        qArr.append({
            'qid': q.Id,
            'qcontent':  '('+str(q.Id)+')'+q.tasktitle
        })
    return responseGet('成功', {'itemArr': itemArr, 'itemTypes': itemTypes, 'qArr': qArr})


@ api.route('/delOwner', methods=['POST'])
@ login_required
def delOwner():
    res = request.json
    Id = res.get('Id')
    try:
        db.session.query(Layered).filter(Layered.Id == Id).delete()
        db.session.commit()
        return responsePost('删除成功')
    except Exception:
        db.session.rollback()
        return responseError('删除失败，请联系管理员')


@ api.route('/addAssignOwner', methods=['POST'])
@ login_required
def addAssignOwner():
    res = request.json
    tasktype = res.get('tasktype')
    subitem = res.get('subitem')
    owner = res.get('owner')
    spv = res.get('spv')
    spv = spv if spv else ''
    try:
        newowner = Layered(eid=owner, spv=spv, tasktype=tasktype, subitem=subitem)
        db.session.add(newowner)
        db.session.commit()
        return responsePost('成功')
    except Exception:
        db.session.rollback()
        return responseError('员工添加失败，请查看员工是否重复或联系管理员')


@ api.route('/getAssignOwners', methods=['GET'])
@ login_required
def getAssignOwners():
    res = request.args
    tasktype = res.get('tasktype')
    subitem = res.get('subitem')
    owners = db.session.query(Layered.Id, Account.wxid, Layered.eid, Userinfo.active, Userinfo.cname, Userinfo2.cname.label('spv'),
                              Userinfo.dept1).outerjoin(Userinfo, Userinfo.eid == Layered.eid).outerjoin(
        Userinfo2, Userinfo2.eid == Layered.spv).outerjoin(Account, Layered.eid == Account.eid).filter(
        Layered.subitem == subitem).filter(Layered.tasktype == tasktype).all()
    outArr = []
    for o in owners:
        outArr.append({
            'Id': o.Id,
            'wxid': o.wxid,
            'active': o.active,
            'owner': o.dept1+'/'+o.cname+'/'+o.eid if (o.dept1 and o.cname) else '未知'+'/'+o.eid,
            'spv': o.spv
        })
    return responseGet('成功', {'owners': outArr})


@ api.route('/getAssignTasks', methods=['GET'])
@ login_required
def getAssignTasks():
    res = request.args
    nb = int(res.get('nb'))
    keywords = res.get('keywords')
    plant = res.get('plant')
    tasktype = res.get('tasktype')
    assignTasks = []
    items = db.session.query(Layeredlist.subitem, func.count(Layeredlist.Id).label('itemqty')).filter(Layeredlist.tasktype == tasktype).filter(
        Layeredlist.plant == plant).group_by(Layeredlist.subitem)
    tasks = db.session.query(Layeredlist.tasktype, Layeredlist.subitem, Layeredlist.area, func.count(Layered.eid).label('ops')).outerjoin(
        Layered, and_(Layered.subitem == Layeredlist.subitem, Layered.tasktype == Layeredlist.tasktype)).filter(
        Layeredlist.tasktype == tasktype).filter(Layeredlist.plant == plant).group_by(Layeredlist.subitem)
    if keywords:
        tasks = tasks.filter(Layeredlist.subitem.like('%{0}%'.format(keywords)))
        items = items.filter(Layeredlist.subitem.like('%{0}%'.format(keywords)))
    tasks = tasks.order_by(desc(Layeredlist.Id)).limit(nb).all()
    items = items.all()
    itemDic = {}
    for i in items:
        itemDic[i.subitem] = i.itemqty
    for t in tasks:
        dic = {
            'subitem': t.subitem,
            'tasktype': t.tasktype,
            'area': t.area,
            'ops': t.ops / itemDic.get(t.subitem, 1),
        }
        assignTasks.append(dic)
    print(11111111111, assignTasks)
    return responseGet("获取列表成功", {'assignTasks': assignTasks})

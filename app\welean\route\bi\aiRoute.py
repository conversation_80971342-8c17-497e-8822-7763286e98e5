from flask import Blueprint, request
from app.public.functions import responsePost, responseError
from openai import OpenAI
import httpx
api = Blueprint('welean/bi/aiAPI', __name__)


@api.route('/getAISPC', methods=['POST'])
def getAISPC():
    res = request.json
    atype = res.get('atype')
    data = res.get('data')
    try:
        client_ds = OpenAI(
            api_key="sk-788eac8f6d3040e19a739fb622e68cb1",
            base_url="https://api.deepseek.com",
            http_client=httpx.Client()  # 自定义 httpx 客户端
        )
    except Exception as e:
        return responseError(str(e))
    output = f'产线{atype}的数据如下：{data}\n请对此{atype}的数据进行评判，如果有问题请指出问题所在'
    response = client_ds.chat.completions.create(
        model="deepseek-chat",
        messages=[
            {"role": "system", "content": "你是一个质量专家"},
            {"role": "user", "content": output},
        ],
        stream=False
    )
    content = response.choices[0].message.content
    return responsePost('成功', {'content': content})


@api.route('/')
def index():
    return 'ok等等'

import{useColumns as p}from"./column-E-czkXnl.js";import{d as s,q as l,r as c,o as m,g as d,u as o,_ as u}from"./index-BnxEuBzx.js";import"./dashboard-dtTxmf4X.js";import"./front-CiGk0t8u.js";import"./index-BNcz4cED.js";import"./editDataForm.vue_vue_type_script_setup_true_lang-Dl-sZNXt.js";import"./prod-CmDsiAIL.js";import"./moment-C3TZ8gAF.js";import"./index-QtNKVBLo.js";import"./editDefectForm-CA2niQRC.js";const f=s({__name:"hourtable",setup(g){const{dataList:t,columns:a,loadingConfig:e,adaptiveConfig:n,loading:i}=p();return l(()=>{}),(_,h)=>{const r=c("pure-table");return m(),d(r,{ref:"tableRef",border:"",stripe:"",adaptive:"",adaptiveConfig:o(n),alignWhole:"center",showOverflowTooltip:"",loading:o(i),"loading-config":o(e),data:o(t),columns:o(a),"max-height":"100%",height:"100%"},null,8,["adaptiveConfig","loading","loading-config","data","columns"])}}}),M=u(f,[["__scopeId","data-v-a13b14cc"]]);export{M as default};

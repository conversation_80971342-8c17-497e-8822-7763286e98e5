from flask import Blueprint, request
from app.procedure.functions import dmIssueChange, dmIssueSubmit, downLoadPics
from app.public.functions import responseError, responseGet, responsePost, responsePut
import requests
from config import config, myEnv
import datetime
import traceback
import os
api = Blueprint('procedure/costAPI', __name__)


@api.route('/acceptBoard', methods=['PUT'])  # 发送云端结束一个提案，状态会标记为closed，结束人时工号为9999999的EMDI用户
def acceptBoard():
    res = request.json
    id = res.get('Id')
    exeid = res.get('exeid')[1]
    comments = res.get('comments')
    content = res.get('content')
    res1 = requests.put(
        config[myEnv].cloud_url+"welean/bi/issueAPI/acceptSuggest", json={
            'id': id,
            'comments': comments,
            'content': content,
            'exeid': exeid
        })
    content1 = res1.json()
    if content1["meta"]["status"] == 202:
        return responsePut("更新成功", {'info': 'success'})
    else:
        return responseError('获取问题列表失败，请尝试刷新或联系管理员')


@api.route('/newBoard', methods=['POST'])  # 新建一个提案给云端
def newBoard():
    res = request.json
    Id = res.get('Id')
    try:
        if Id:
            check = dmIssueChange(res)
            if check:
                return responsePost('更新成功')
            else:
                return responseError('更新失败，请联系管理员')
        else:
            check = dmIssueSubmit(res)
            if check:
                return responsePost('插入成功')
            else:
                return responseError('插入失败，请联系管理员')
    except Exception:
        traceback.print_exc()
        return responseError('更新失败,请联系管理员')


@api.route('/getBoards', methods=['GET'])   # 获取accountbility board的列表数据，产线分类
def getBoards():
    res = request.args
    query = res.get('query')
    line = res.get('line')
    area = res.get('area')
    res = requests.get(
        config[myEnv].cloud_url+"welean/bi/issueAPI/getAccountsWX", params={
            'query': query,
            'line': line,
            'area': area
        })
    content = res.json()
    issues = []
    if content["meta"]["status"] == 200:
        issues = content['data']['suggests']
        issues = downLoadPics(issues)
    else:
        return responseError('获取问题列表失败，请尝试刷新或联系管理员')
    dt = getLines(area)
    lines = dt[0]
    stypes = dt[1]
    owners = dt[2]
    total = len(issues)
    return responseGet('成功', {'data': issues, 'lines': lines, 'total': total, 'stypes': stypes, 'owners': owners})


@api.route('/getAccounts', methods=['GET'])  # 获得accountbility board的部门分类数据，产线分类
def getAccounts():
    res = request.args
    query = res.get('query')
    line = res.get('line')
    area = res.get('area')
    try:
        res = requests.get(
            config[myEnv].cloud_url+"welean/bi/issueAPI/getAccountsWX", params={
                'query': query,
                'line': line,
                'area': area
            })
        content = res.json()
        issues = []
        if content["meta"]["status"] == 200:
            issues = content['data']['suggests']
            issues = downLoadPics(issues)
        else:
            return responseError('获取问题列表失败，请尝试刷新或联系管理员')
        wkArr = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
        url = config[myEnv].pentair_url+'procedure/boardimg/'
        path = config[myEnv].pentair_path+'procedure/boardimg/'
        boardDept = {
            'EHS': 'EHS',
            'Production': '生产',
            'Lean': 'Lean',
            'ME': '工程',
            'R&D': '研发',
            'F&M': '维修',
            'Planning': '计划',
            'Quality': '质量',
            'M&L': '仓库',
            '其他': '采购'
        }
        rowDic = {}
        for b, v in boardDept.items():
            result = find_file(v, path, url)
            rowDic[b] = {
                'dept': v,
                'deptimg': result
            }
        today = datetime.date.today()
        thisyear = str(today.year)
        dateArr = []
        weekArr = []
        for i in range(14):
            dd = today+datetime.timedelta(days=i)
            wd = dd.weekday()
            if wd in [0, 1, 2, 3, 4]:
                dateArr.append(dd.strftime('%m-%d'))
                weekArr.append(wkArr[wd])
        for o in issues:
            if o['cfdate'] == '0000-00-00':
                o['cfdate'] = ''
            if o['dept1'] in rowDic.keys():
                if o['cfdate'] < thisyear+'-'+dateArr[0]:
                    if '过期未处理' in rowDic[o['dept1']].keys():
                        rowDic[o['dept1']]['过期未处理'].append(o)
                    else:
                        rowDic[o['dept1']]['过期未处理'] = [o]
                elif o['cfdate'] > thisyear+'-'+dateArr[len(dateArr)-1]:
                    if '两周后' in rowDic[o['dept1']].keys():
                        rowDic[o['dept1']]['两周后'].append(o)
                    else:
                        rowDic[o['dept1']]['两周后'] = [o]
                else:
                    if o['cfdate'][5:] in rowDic[o['dept1']].keys():
                        rowDic[o['dept1']][o['cfdate'][5:]].append(o)
                    else:
                        rowDic[o['dept1']][o['cfdate'][5:]] = [o]

        dateArr.append('两周后')
        dateArr.insert(0, '过期未处理')
        weekArr.append(' ')
        weekArr.insert(0, ' ')
        dt = getLines(area)
        lines = dt[0]
        stypes = dt[1]
        owners = dt[2]
        rowArr = list(rowDic.values())
        return responseGet('成功', {'data': rowArr, 'lines': lines, 'pp': dateArr, 'ww': weekArr, 'stypes': stypes, 'owners': owners})
    except Exception:
        traceback.print_exc()
        return responseError('更新失败,请联系管理员')


def getLines(area):   # 获取产线列表，会根据用户信息的Dept2进行切分，未激活和未注册的用户无法出现在选单
    res = requests.get(
        config[myEnv].cloud_url+"welean/bi/issueAPI/getLinesWX", params={
            'area': area
        })
    content = res.json()
    lines = []
    if content["meta"]["status"] == 200:
        lines = content['data']['lines']
        owners = content['data']['owners']
        types = content['data']['types']
    lineData = []
    for r in lines:
        lineData.append({'name': r, 'value': r})
    return [lineData, types, owners]


def find_file(dept, path, url):
    i = 0
    result = []
    for root, lists, files in os.walk(path):
        for file in files:
            if dept in file:
                i = i + 1
                write = url+file
                result.append(write)
    return result

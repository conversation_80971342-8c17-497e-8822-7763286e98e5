import{aM as e,aN as s}from"./index-BnxEuBzx.js";const a=t=>e.request("get",s("production/getlist"),{params:t}),n=()=>e.request("get",s("production/getstatetype")),p=t=>e.request("post",s("shift/addstate"),{data:t}),u=t=>e.request("post",s("shift/updatestate"),{data:t}),o=t=>e.request("get",s("production/getpnlistbyprefix"),{params:t}),i=t=>e.request("get",s("production/getdesbypn"),{params:t}),g=t=>e.request("post",s("production/changepn"),{data:t}),c=t=>e.request("get",s("hourlyinfo/getlist"),{params:t}),d=t=>e.request("get",s("hourlyinfo/getmachineidbyoutput"),{params:t}),l=()=>e.request("get",s("plan/getlist")),h=()=>e.request("get",s("plan/getpackinglist"));export{h as a,c as b,p as c,g as d,n as e,o as f,l as g,i as h,a as i,d as j,u};

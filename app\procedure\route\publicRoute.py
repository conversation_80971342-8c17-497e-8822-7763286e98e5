from flask import Blueprint, request
from extensions import db
from app.public.functions import responseGet
from app.procedure.model.models_procedure import Station
from app.procedure.functions.jwt import login_required

api = Blueprint('procedure/publicAPI', __name__)

plantDic = {
    'Suzhou': 'SZ',
    'Wuxi': 'WX',
    'Ningbo': 'NB'
}


@api.route('/getLinesbyArea', methods=['GET'])
@login_required
def getLinesbyArea():
    res = request.args
    plant = res.get('plant')
    area = res.get('area') if res.get('area') else '车间'
    lines = db.session.query(Station).filter(Station.plant == plant, Station.area == area).all()
    outArr = []
    for ll in lines:
        outArr.append({
            'value': ll.station,
            'label': ll.station
        })
    return responseGet('获取成功', {'lineList': outArr})

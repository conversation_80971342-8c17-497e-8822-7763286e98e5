from extensions import db


class Epeisku(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_epeisku"
    Id = db.Column(db.Integer, primary_key=True)
    area =  db.Column(db.String(20))
    pdsku =  db.Column(db.String(30))
    usesku=  db.Column(db.String(30))
    useqty = db.Column(db.SmallInteger)


class Epeistock(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_epeistock"
    Id = db.Column(db.Integer, primary_key=True)
    sku =  db.Column(db.String(50))
    countdate=  db.Column(db.DateTime())
    countstock = db.Column(db.Integer)
    cyclestock = db.Column(db.Integer)
    varstock = db.Column(db.Integer)
    safetystock = db.Column(db.Integer)
    machine= db.Column(db.String(10))
    mold= db.Column(db.String(50))
    av = db.Column(db.SmallInteger)
    packqty = db.Column(db.Integer)
    area = db.Column(db.String(20))


class Pcoinfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_pcoinfo"
    Id = db.Column(db.Integer, primary_key=True)
    pcono =  db.Column(db.String(10))
    fileurl= db.Column(db.String(30))
    pcotime=  db.Column(db.DateTime())
    owner = db.Column(db.String(64))
    active = db.Column(db.SmallInteger)
    content= db.Column(db.String(255))

class Pcosku(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_pcosku"
    Id = db.Column(db.Integer, primary_key=True)
    pcoid = db.Column(db.Integer)
    sku = db.Column(db.String(50))
    ishift = db.Column(db.Integer)
    itime=  db.Column(db.DateTime())
    isnew = db.Column(db.SmallInteger)
    evidence = db.Column(db.String(30))
    checked = db.Column(db.SmallInteger)
    confirmer = db.Column(db.String(64))


class Pcoskupass(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_pcoskupass"
    Id = db.Column(db.Integer, primary_key=True)
    pcono =  db.Column(db.String(10))
    sku = db.Column(db.String(50))
    duetime=  db.Column(db.DateTime())
    owner = db.Column(db.String(64))


class Asnvendor(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_asnvendor"
    Id = db.Column(db.Integer, primary_key=True)
    vendorid = db.Column(db.String(10))
    name= db.Column(db.String(50))
    shortname= db.Column(db.String(10))
    mprc= db.Column(db.String(5))
    lt= db.Column(db.SmallInteger)

class Asn(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_asn"
    Id = db.Column(db.Integer, primary_key=True)
    plant = db.Column(db.String(10))
    asnno= db.Column(db.String(50))
    vendorid= db.Column(db.String(10))
    cdate= db.Column(db.Date())
    adate= db.Column(db.Date())


class Printlabel(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_printlabel"
    Id = db.Column(db.Integer, primary_key=True)
    pn= db.Column(db.String(20))
    width= db.Column(db.String(10))
    height= db.Column(db.String(10))
    gaph= db.Column(db.String(10))
    gapv= db.Column(db.String(10))
    waterproof= db.Column(db.SmallInteger)
    color= db.Column(db.String(1))
    duplicate= db.Column(db.SmallInteger)

class Printbom(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_printbom"
    Id = db.Column(db.Integer, primary_key=True)
    sku= db.Column(db.String(50))
    linegroup = db.Column(db.String(20))
    revisiondate=  db.Column(db.Date())
    revision = db.Column(db.String(2))
    isactive= db.Column(db.SmallInteger)


class Printbomtemplate(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_printbomtemplate"
    Id = db.Column(db.Integer, primary_key=True)
    skuid= db.Column(db.Integer)
    templateid = db.Column(db.Integer)
    duplicate= db.Column(db.SmallInteger)


class Printgraph(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_printgraph"
    Id = db.Column(db.Integer, primary_key=True)
    templateid = db.Column(db.Integer)
    graph= db.Column(db.Text)
    font= db.Column(db.SmallInteger)
    fixedH= db.Column(db.SmallInteger)
    isgraph= db.Column(db.SmallInteger)


class Printparam(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_printparam"
    Id = db.Column(db.Integer, primary_key=True)
    skuid= db.Column(db.Integer)
    name= db.Column(db.String(20))
    value= db.Column(db.String(255))

class Printtemplate(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_printtemplate"
    Id = db.Column(db.Integer, primary_key=True)
    pn= db.Column(db.String(20))
    linegroup = db.Column(db.String(20))
    des= db.Column(db.String(30))
    code= db.Column(db.Text)
    img= db.Column(db.String(50))
    isactive= db.Column(db.SmallInteger)
    revision= db.Column(db.String(20))
    rotated= db.Column(db.SmallInteger)


class Tankrotoarm(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_tankrotoarm"
    Id = db.Column(db.Integer, primary_key=True)
    arm = db.Column(db.String(20))
    mold = db.Column(db.String(20))


class Tankrotomold(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_tankrotomold"
    Id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(50))
    mold = db.Column(db.String(20))
    co = db.Column(db.SmallInteger)


class Tankrotosku(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_tankrotosku"
    Id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(50))
    unload = db.Column(db.SmallInteger)
    load = db.Column(db.SmallInteger)
    bake = db.Column(db.SmallInteger)
    cool = db.Column(db.SmallInteger)


class Tankplan(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_tankplan"
    Id = db.Column(db.Integer, primary_key=True)
    mo = db.Column(db.String(30))
    modate = db.Column(db.Date())
    duedate = db.Column(db.Date())
    finishdate = db.Column(db.Date())
    moqty = db.Column(db.SmallInteger)
    status = db.Column(db.String(10))
    sku = db.Column(db.String(50))
    linename = db.Column(db.String(50))
    packed = db.Column(db.SmallInteger)


class Tankplan2(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_tankplan2"
    Id = db.Column(db.Integer, primary_key=True)
    machine = db.Column(db.String(50))
    pdate = db.Column(db.Date())
    plan = db.Column(db.String(50))


class Tankplanact(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_tankplanact"
    Id = db.Column(db.Integer, primary_key=True)
    mo = db.Column(db.String(30))
    shiftid = db.Column(db.Integer)
    qty = db.Column(db.Integer)


class Otd(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_otd"
    Id = db.Column(db.Integer, primary_key=True)
    otddate = db.Column(db.Date())
    promisstotal = db.Column(db.Integer)
    promissmiss = db.Column(db.Integer)
    promissnew = db.Column(db.Integer)
    requesttotal = db.Column(db.Integer)
    requestmiss = db.Column(db.Integer)
    requestnew = db.Column(db.Integer)


class Otdmiss(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_otdmiss"
    Id = db.Column(db.Integer, primary_key=True)
    misslines = db.Column(db.SmallInteger)
    otddate = db.Column(db.Date())
    area = db.Column(db.String(50))
    comments = db.Column(db.String(50))
    solvedate = db.Column(db.Date())


class Temprecord(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_temprecord"
    Id = db.Column(db.Integer, primary_key=True)
    issues = db.Column(db.String(255))
    linegroup = db.Column(db.String(20))
    planqty = db.Column(db.Integer)
    actqty = db.Column(db.Integer)
    recorddate = db.Column(db.Date())


class Andon(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_andon"
    Id = db.Column(db.Integer, primary_key=True)
    isactive = db.Column(db.SmallInteger)
    starttime = db.Column(db.DateTime())
    endtime = db.Column(db.DateTime())
    cname = db.Column(db.String(10))
    linename = db.Column(db.String(50))
    suggestid = db.Column(db.Integer)


class Appversion(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_appversion"
    Id = db.Column(db.Integer, primary_key=True)
    appname = db.Column(db.String(20))
    version = db.Column(db.String(10))
    active = db.Column(db.SmallInteger)
    releasedate = db.Column(db.Date())
    desc = db.Column(db.Text)


class Appdevices(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_appdevices"
    Id = db.Column(db.Integer, primary_key=True)
    deviceId = db.Column(db.String(50))
    owner = db.Column(db.String(64))
    area = db.Column(db.String(20))
    plant = db.Column(db.String(20))
    lastline = db.Column(db.String(20))
    lastdate = db.Column(db.DateTime())
    lastcount = db.Column(db.Integer)
    lastversion = db.Column(db.String(10))


class Ctqinfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_ctqinfo"
    Id = db.Column(db.Integer, primary_key=True)
    skuctqid = db.Column(db.SmallInteger)
    ctqname = db.Column(db.String(20))
    sku = db.Column(db.String(30))
    va1 = db.Column(db.String(20))
    va2 = db.Column(db.String(20))
    va3 = db.Column(db.String(20))
    ctqtype = db.Column(db.String(20))
    ctqstation= db.Column(db.SmallInteger)
    ctqstationname = db.Column(db.String(10))


class Ctqsn(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_ctqsn"
    Id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(30))
    ctqstation= db.Column(db.SmallInteger)
    sn = db.Column(db.String(50))
    scantime= db.Column(db.DateTime())
    linename = db.Column(db.String(50))


class Ctqrecord(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_ctqrecord"
    Id = db.Column(db.Integer, primary_key=True)
    sn = db.Column(db.String(50))
    recordtime = db.Column(db.DateTime())
    ctqid = db.Column(db.SmallInteger)
    actual1 = db.Column(db.String(20))
    actual2 = db.Column(db.String(20))
    status = db.Column(db.String(10))
    sku = db.Column(db.String(30))


class Flexinfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_flexinfo"
    Id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(50))
    linename = db.Column(db.String(15))
    headcount = db.Column(db.Integer)
    hourlyrate = db.Column(db.Float(7, 4))
    revision = db.Column(db.Date())


class Kpi(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_kpi"
    Id = db.Column(db.Integer, primary_key=True)
    linegroup = db.Column(db.String(20))
    targetyear = db.Column(db.SmallInteger)
    qtarget = db.Column(db.Float(4, 3))
    dtarget = db.Column(db.Float(4, 3))
    ctarget = db.Column(db.Float(4, 3))
    utiltarget = db.Column(db.Float(4, 3))


class Lineinfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_lineinfo"
    Id = db.Column(db.Integer, primary_key=True)
    isactive = db.Column(db.SmallInteger)
    linename = db.Column(db.String(50))
    VSM = db.Column(db.String(10))
    currentshift = db.Column(db.Integer)
    linegroup = db.Column(db.String(20))
    area = db.Column(db.String(20))
    space = db.Column(db.SmallInteger)
    plant = db.Column(db.String(20))
    owner = db.Column(db.String(255))
    manager = db.Column(db.String(255))
    mditype = db.Column(db.SmallInteger)
    autoclose = db.Column(db.SmallInteger)
    fai = db.Column(db.SmallInteger)


class Palletinfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_palletinfo"
    Id = db.Column(db.Integer, primary_key=True)
    palletsn = db.Column(db.String(30))
    starttime = db.Column(db.DateTime())
    endtime = db.Column(db.DateTime())
    sku = db.Column(db.String(50))
    ptype = db.Column(db.String(5))
    setqty = db.Column(db.Integer)


class Permission(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_permission"
    Id = db.Column(db.Integer, primary_key=True)
    ismenu = db.Column(db.SmallInteger)
    pid = db.Column(db.SmallInteger)
    name = db.Column(db.String(20))
    order = db.Column(db.SmallInteger)
    path = db.Column(db.String(20))


class Restinfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_restinfo"
    Id = db.Column(db.Integer, primary_key=True)
    resttime = db.Column(db.Time())
    restmin = db.Column(db.Integer)
    linegroup = db.Column(db.String(10))


class Role(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_role"
    Id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(10))
    defaultauth = db.Column(db.String(200))
    plant = db.Column(db.String(20))


class Routing(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_routing"
    Id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(50))
    routing = db.Column(db.Float(8, 6))
    revision = db.Column(db.Date())


class Scaninfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_scaninfo"
    Id = db.Column(db.Integer, primary_key=True)
    sn = db.Column(db.String(30))
    shiftid = db.Column(db.Integer)
    scantime = db.Column(db.DateTime(), primary_key=True)
    sku = db.Column(db.String(30))
    scanqty = db.Column(db.Integer)


class Scansub(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_scansub"
    Id = db.Column(db.Integer, primary_key=True)
    sn = db.Column(db.String(30))
    subname = db.Column(db.String(10))
    subsn = db.Column(db.String(30))


class Scanpack(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_scanpack"
    Id = db.Column(db.Integer, primary_key=True)
    sn = db.Column(db.String(30))
    palletsn = db.Column(db.String(20))
    boxsn = db.Column(db.String(20))


class Shiftinfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_shiftinfo"
    Id = db.Column(db.Integer, primary_key=True)
    shifttype = db.Column(db.String(2))
    scantemplate = db.Column(db.String(10))
    headcount = db.Column(db.Float(3, 1))
    lineleader = db.Column(db.String(64))
    team = db.Column(db.String(200))
    starttime = db.Column(db.DateTime())
    finishtime = db.Column(db.DateTime())
    linename = db.Column(db.String(50))
    sku = db.Column(db.String(50))
    routing = db.Column(db.Float(8, 6))


class Skuinfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_skuinfo"
    Id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(50))
    snverify = db.Column(db.String(50))
    lenverify = db.Column(db.Integer)
    des = db.Column(db.String(100))
    mgroup = db.Column(db.String(20))
    color = db.Column(db.String(10))
    typegroup = db.Column(db.String(20))
    code69 = db.Column(db.String(50))
    code69sn = db.Column(db.String(50))
    prefix = db.Column(db.String(5))


class Fairecord(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_fairecord"
    Id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(50))
    code69 = db.Column(db.String(50))
    code69sn = db.Column(db.String(50))
    faitime = db.Column(db.DateTime())
    linename = db.Column(db.String(50))


class User(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_user"
    Id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(64))
    roleid = db.Column(db.SmallInteger)
    auth = db.Column(db.String(255))
    isactive = db.Column(db.SmallInteger)
    plant = db.Column(db.String(20))
    phone = db.Column(db.String(11))


class Probleminfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_probleminfo"
    Id = db.Column(db.Integer, primary_key=True)
    sqdctype = db.Column(db.String(10))
    trigger = db.Column(db.SmallInteger)
    problemtype = db.Column(db.String(30))
    linename = db.Column(db.String(10))


class Probleminfooee(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_probleminfooee"
    Id = db.Column(db.Integer, primary_key=True)
    sqdctype = db.Column(db.String(10))
    trigger = db.Column(db.SmallInteger)
    problemtype = db.Column(db.String(30))
    linename = db.Column(db.String(10))


class Issuelog(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_issuelog"
    Id = db.Column(db.Integer, primary_key=True)
    sqdctype = db.Column(db.String(10))
    recorder = db.Column(db.String(64))
    problemtype = db.Column(db.String(30))
    linename = db.Column(db.String(10))
    desc = db.Column(db.String(200))
    qty = db.Column(db.Integer)
    recordtime = db.Column(db.DateTime())
    shifttype = db.Column(db.String(2))
    shiftdate = db.Column(db.Date())
    sku = db.Column(db.String(50))
    issuemin = db.Column(db.SmallInteger)


class Ctqtemplate(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_ctqtemplate"
    Id = db.Column(db.Integer, primary_key=True)
    templatename = db.Column(db.String(20))
    ctqid = db.Column(db.SmallInteger)
    ctqname = db.Column(db.String(20))
    ctqtype = db.Column(db.String(20))
    ctqstation= db.Column(db.SmallInteger)
    ctqstationname = db.Column(db.String(10))


class Planinfo(db.Model):
    __bind_key__ = "dm"
    __tablename__ = "dm_planinfo"
    Id = db.Column(db.Integer, primary_key=True)
    mo = db.Column(db.String(20))
    shifttype = db.Column(db.String(2))
    linename = db.Column(db.String(50))
    sequence = db.Column(db.SmallInteger)
    sku = db.Column(db.String(50))
    planner = db.Column(db.String(64))
    qty = db.Column(db.Integer)
    plandate = db.Column(db.Date())
    status = db.Column(db.SmallInteger)

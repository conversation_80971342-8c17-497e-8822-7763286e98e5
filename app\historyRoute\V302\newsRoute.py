from flask import Blueprint, request
from app.welean.model.models_welean import Newspaper, Userinfo, Rates
from extensions import db
import os
from datetime import datetime
from sqlalchemy import or_, desc
from app.public.functions import responseError,  responseGet, login_required
from app.welean.functions import getServer
api = Blueprint('welean/V302/newsAPI', __name__)


@api.route('/getCourseslist', methods=['GET'])
@login_required
def getCourseslist():
    res = request.args
    plant = res.get('plant')
    keywords = res.get('keywords')
    nb = int(res.get('nb'))
    dataList = []
    news = db.session.query(Newspaper.Id, Newspaper.subcate, Newspaper.reservedate, Newspaper.postdate, Newspaper.piccover, Newspaper.title, Userinfo.dept1, Userinfo.cname
                            ).join(Userinfo, Userinfo.eid == Newspaper.eid).filter(
        or_(Newspaper.plant == plant, Newspaper.plant == 'ALL')).filter(Newspaper.cate == 'training').order_by(desc(Newspaper.reservedate))
    if keywords:
        news = news.filter(Newspaper.title.like('%{0}%'.format(keywords)))
    news = news.limit(nb).all()
    for n in news:
        coverpath = getServer()['newsPath']+str(n.Id)+'/'+n.piccover
        if os.path.exists(coverpath):
            newscover = getServer()['newsUrl']+str(n.Id)+'/'+n.piccover
        else:
            newscover = getServer()['baseUrl']+'nophoto.png'
        dic = {
            'id': n.Id,
            'name': n.title,
            'subcate': n.subcate,
            'updatedate': datetime.strftime(n.postdate, "%Y-%m-%d"),
            'src': newscover,
        }
        dataList.append(dic)
    return responseGet("获取列表成功", {'contents': dataList})


@api.route('/getLikeContents', methods=['GET'])
@login_required
def getLikeContents():
    res = request.args
    eid = res.get('eid')
    nb = int(res.get('nb'))
    keywords = res.get('keywords')
    dataList = []
    news = db.session.query(Newspaper.Id, Newspaper.subcate, Newspaper.reservedate, Newspaper.postdate, Newspaper.piccover, Newspaper.title).join(Rates, Rates.sid == Newspaper.Id).filter(
        Rates.eid == eid).filter(Newspaper.cate == 'news').filter(Rates.cate == 'news').filter(Rates.score == 5)
    if keywords:
        news = news.filter(Newspaper.title.like('%{0}%'.format(keywords)))
    news = news.order_by(desc(Newspaper.reservedate)).limit(nb).all()
    for n in news:
        coverpath = getServer()['newsPath']+str(n.Id)+'/'+n.piccover
        if os.path.exists(coverpath):
            newscover = getServer()['newsUrl']+str(n.Id)+'/'+n.piccover
        else:
            newscover = getServer()['baseUrl']+'nophoto.png'
        dic = {
            'id': n.Id,
            'icon': 'heart-fill',
            'cate': 'news',
            'name': n.title,
            'subcate': n.subcate,
            'updatedate': datetime.strftime(n.postdate, "%Y-%m-%d"),
            'src': newscover,
        }
        dataList.append(dic)
    return responseGet("获取列表成功", {'contents': dataList})


@api.route('/getContents', methods=['GET'])
@login_required
def getContents():
    res = request.args
    plant = res.get('plant')
    keywords = res.get('keywords')
    nb = int(res.get('nb'))
    dataList = []
    news = db.session.query(Newspaper.Id, Newspaper.subcate, Newspaper.postdate, Newspaper.piccover, Newspaper.title).filter(
        or_(Newspaper.plant == plant, Newspaper.plant == 'ALL')).filter(Newspaper.cate == 'news').order_by(desc(Newspaper.reservedate))
    if keywords:
        news = news.filter(Newspaper.title.like('%{0}%'.format(keywords)))
    news = news.limit(nb).all()
    for n in news:
        coverpath = getServer()['newsPath']+str(n.Id)+'/'+n.piccover
        if os.path.exists(coverpath):
            newscover = getServer()['newsUrl']+str(n.Id)+'/'+n.piccover
        else:
            newscover = getServer()['baseUrl']+'nophoto.png'
        dic = {
            'id': n.Id,
            'name': n.title,
            'subcate': n.subcate,
            'updatedate': datetime.strftime(n.postdate, "%Y-%m-%d"),
            'src': newscover,
        }
        dataList.append(dic)
    return responseGet("获取列表成功", {'contents': dataList})


@ api.route('/getNewsByid', methods=['GET'])
@ login_required
def getNewsByid():
    res = request.args
    id = res.get('id')
    news = db.session.query(Newspaper.Id, Newspaper.videourl, Newspaper.subcate, Newspaper.content, Newspaper.title, Newspaper.postdate, Userinfo.dept1, Userinfo.cname).outerjoin(
        Userinfo, Newspaper.eid == Userinfo.eid).filter(Newspaper.Id == id).first()
    if news:
        picsLocation = getServer()['newsPath']+str(news.Id)+'/pics/'
        directory = os.listdir(picsLocation)
        if directory:
            directory.sort()
        pics = []
        contentDic = {
            'head': []
        }
        if news.content:
            contentArr = news.content.split('$p')
            for c in contentArr:
                index = c[:2]
                content = c
                try:
                    k = str(int(index)-1)
                    content = c[2:]
                    if k in contentDic.keys():
                        contentDic[k].append(content)
                    else:
                        contentDic[k] = [content]
                except Exception:
                    contentDic['head'].append(content)
        for dir in directory:
            pics.append(getServer()['newsUrl']+str(news.Id)+'/pics/'+dir)
        h5url = ''
        if news.videourl:
            # r = news.videourl
            # wxurl = r[r.rfind('/')+2:r.rfind('.')]
            h5url = 'https://v.qq.com/iframe/player.html?vid='+news.videourl
        newspaper = {
            'cate': news.subcate,
            'title': news.title,
            'postdate': datetime.strftime(news.postdate, "%Y-%m-%d"),
            'author': news.dept1+'/'+news.cname,
            'pics': pics,
            'videourl': news.videourl,
            'h5url': h5url,
            'content': contentDic
        }
        print(newspaper)
        return responseGet("获取列表成功", {'newspaper': newspaper})
    return responseError('未找到该新闻')

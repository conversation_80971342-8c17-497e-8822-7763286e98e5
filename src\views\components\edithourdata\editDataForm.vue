<script setup lang="ts">
import { ref } from "vue";
import { FormProps } from "./types";
const props = withDefaults(defineProps<FormProps>(), {
  shiftinfo: () => ({
    selecteddate: "",
    shift: "",
    machineid: ""
  }),
  action: "",
  formInline: () => ({
    hourid: 0,
    pn: "",
    output: 0,
    adjustion: 0
  })
});
const ruleFormRef = ref();
const newFormInline = ref(props.formInline);
function getRef() {
  return ruleFormRef.value;
}

function getVal() {
  return newFormInline;
}

defineExpose({ getRef, getVal });
</script>

<template>
  <div>
    <el-form ref="ruleFormRef" label-width="80px" style="font-size: 24px">
      <el-form-item label="小时号" prop="hourid">
        {{ newFormInline.hourid + ":00~" + (newFormInline.hourid + 1) + ":00" }}
      </el-form-item>
      <el-form-item label="料号" prop="pn">
        <el-input
          :disabled="props.action == 'edit'"
          v-model="newFormInline.pn"
          placeholder="料号"
        />
      </el-form-item>
      <el-form-item label="产出" prop="output">
        <el-input-number
          :disabled="props.action == 'edit'"
          v-model="newFormInline.output"
          placeholder="产出"
        />
      </el-form-item>
      <el-form-item label="调整值" prop="adjustion">
        <el-input-number
          v-model="newFormInline.adjustion"
          placeholder="调整值"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped></style>

var r=(y,p,t)=>new Promise((f,e)=>{var l=s=>{try{i(t.next(s))}catch(o){e(o)}},m=s=>{try{i(t.throw(s))}catch(o){e(o)}},i=s=>s.done?f(s.value):Promise.resolve(s.value).then(l,m);i((t=t.apply(y,p)).next())});import{l as R,p as M,a as H}from"./system-DzNitOCO.js";import{d as K,n as u,q as N,r as v,o as T,g as q,h as d,b as g,f as h,y as A,u as b,z as B,a3 as D,H as S,aE as $}from"./index-BnxEuBzx.js";import{requestHook as k}from"./hook-BWfQX1Id.js";const U=K({name:"SysRoleManagementAssign",__name:"assign",setup(y,{expose:p}){const t=u(!1),f=u(""),e=u(),l=u(),m=u(),i=()=>r(this,null,function*(){const{data:a}=yield k(R());m.value=a}),s=a=>{l.value=a,f.value=`${a.name} - 权限指派`,x(),t.value=!0},o=()=>{l.value={},t.value=!1},x=()=>r(this,null,function*(){S(()=>r(this,null,function*(){yield e.value.setCheckedKeys([]);const{data:a}=yield k(M({id:l.value.id}));for(const c of a){const n=e.value.getNode(c);e.value.setChecked(n,!0),n!=null&&n.isLeaf&&e.value.setChecked(n,!0)}}))});N(()=>{i()});const w=()=>r(this,null,function*(){const a={menu_ids:[...e.value.getCheckedKeys(),...e.value.getHalfCheckedKeys()],id:l.value.id};if(a.menu_ids.length>0){const{code:c}=yield k(H(a));c===0&&($("提交成功",{type:"success"}),o())}});return p({showAssign:s}),(a,c)=>{const n=v("el-tree"),C=v("el-button"),V=v("el-dialog");return T(),q(V,{modelValue:t.value,"onUpdate:modelValue":c[0]||(c[0]=_=>t.value=_),title:f.value,width:"350px","before-close":o,draggable:!0},{footer:d(()=>[g(C,{onClick:o},{default:d(()=>[h("取 消")]),_:1}),g(C,{type:"primary",onClick:w},{default:d(()=>[h("确 定")]),_:1})]),default:d(()=>[g(n,{ref_key:"treeRef",ref:e,data:m.value,"default-expand-all":"",height:600,"node-key":"id",props:{children:"children",label:"title"},"show-checkbox":""},{default:d(({node:_})=>[h(A(b(B)(b(D)(_.label))),1)]),_:1},8,["data"])]),_:1},8,["modelValue","title"])}}});export{U as _};

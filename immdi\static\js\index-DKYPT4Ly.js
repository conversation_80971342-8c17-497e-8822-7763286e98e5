import{useColumns as l}from"./columns-CXNQLMMN.js";import{d as p,r,o as _,c as m,e as a,b as f,u as o,ak as g,al as u,_ as h}from"./index-BnxEuBzx.js";import"./runlog-CNDP2hwV.js";import"./prod-CmDsiAIL.js";import"./moment-C3TZ8gAF.js";const v=e=>(g("data-v-a62ce220"),e=e(),u(),e),C={class:"main"},b=v(()=>a("div",{class:"header"},[a("span",{class:"lightfont"},"设备停机记录表")],-1)),x={class:"content"},k=p({__name:"index",setup(e){const{dataList:t,columns:s,loadingConfig:n,adaptiveConfig:i,loading:c}=l();return(w,I)=>{const d=r("pure-table");return _(),m("div",C,[b,a("div",x,[f(d,{ref:"tableRef",border:"",stripe:"",adaptive:"",adaptiveConfig:o(i),"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:o(c),"loading-config":o(n),data:o(t),columns:o(s),height:"100%"},null,8,["adaptiveConfig","loading","loading-config","data","columns"])])])}}}),E=h(k,[["__scopeId","data-v-a62ce220"]]);export{E as default};

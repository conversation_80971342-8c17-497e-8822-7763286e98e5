import{e as U}from"./front-CiGk0t8u.js";import{d as B,n as r,q as G,r as n,o as s,c as _,b as t,h as o,f as d,y as v,e as I,F as K,t as w,g as C,_ as D}from"./index-BnxEuBzx.js";const N={class:"content"},R=B({__name:"editDefectForm",props:{shiftinfo:{default:()=>({selecteddate:"",shift:"",machineid:""})},action:{default:""},formInline:{default:()=>({hourid:0,pn:"",output:0,defect_type:0,unit:"KG",defect_qty:0})}},setup(V,{expose:y}){const b=V,i=r([]),x=()=>{U().then(p=>{p.meta.status==200&&(i.value=p.data)})};G(()=>{x()});const m=r(),e=r(b.formInline);function E(){return m.value}function A(){return e}return y({getRef:E,getVal:A}),(p,a)=>{const u=n("el-form-item"),g=n("el-input"),c=n("el-input-number"),h=n("el-option"),F=n("el-select"),f=n("el-radio"),k=n("el-radio-group"),q=n("el-form");return s(),_("div",null,[t(q,{ref_key:"ruleFormRef",ref:m,"label-width":"80px",style:{"font-size":"24px"}},{default:o(()=>[t(u,{label:"小时号",prop:"hourid"},{default:o(()=>[d(v(e.value.hourid+":00~"+(e.value.hourid+1)+":00"),1)]),_:1}),t(u,{label:"料号",prop:"pn"},{default:o(()=>[t(g,{disabled:!0,modelValue:e.value.pn,"onUpdate:modelValue":a[0]||(a[0]=l=>e.value.pn=l),placeholder:"料号",class:"element"},null,8,["modelValue"])]),_:1}),t(u,{label:"产出",prop:"output"},{default:o(()=>[t(c,{disabled:!0,modelValue:e.value.output,"onUpdate:modelValue":a[1]||(a[1]=l=>e.value.output=l),placeholder:"产出"},null,8,["modelValue"]),d(" EA ")]),_:1}),t(u,{label:"不良类型"},{default:o(()=>[t(F,{placeholder:"选择不良类型",modelValue:e.value.defect_type,"onUpdate:modelValue":a[2]||(a[2]=l=>e.value.defect_type=l),class:"element"},{default:o(()=>[I("div",N,[(s(!0),_(K,null,w(i.value,l=>(s(),C(h,{class:"item",key:l.id,label:l.des,value:l.id},null,8,["label","value"]))),128))])]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"单位",prop:"unit"},{default:o(()=>[t(k,{modelValue:e.value.unit,"onUpdate:modelValue":a[3]||(a[3]=l=>e.value.unit=l),class:"element"},{default:o(()=>[t(f,{value:"EA"},{default:o(()=>[d("计数(EA)")]),_:1}),t(f,{value:"KG"},{default:o(()=>[d("计重(KG)")]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"不良数量",prop:"defect_qty"},{default:o(()=>[t(c,{modelValue:e.value.defect_qty,"onUpdate:modelValue":a[4]||(a[4]=l=>e.value.defect_qty=l),placeholder:e.value.unit=="EA"?"不良数量":"不良重量",min:0,max:e.value.unit=="EA"?e.value.output:99999,precision:e.value.unit=="EA"?0:3,step:e.value.unit=="EA"?1:.1},null,8,["modelValue","placeholder","max","precision","step"]),d(" "+v(e.value.unit=="EA"?"EA":"KG"),1)]),_:1})]),_:1},512)])}}}),M=D(R,[["__scopeId","data-v-c875da56"]]);export{M as default};

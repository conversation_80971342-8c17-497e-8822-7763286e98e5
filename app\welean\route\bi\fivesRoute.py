from flask import Blueprint, request
from app.welean.model.models_welean import FiveSstandard, FiveSaudit, FiveSdaily, Userinfo, Layeredresult
from extensions import db
from sqlalchemy import func, desc, and_
from app.public.functions import responseGet
import datetime
from app.welean.functions import getServer
api = Blueprint('welean/bi/fivesAPI', __name__)


@api.route('/get5Sstandard', methods=['GET'])
# @ login_required
def get5Sstandard():
    res = request.args
    linename = res.get('linename')
    auditlist = []
    tasks = db.session.query(FiveSstandard).filter(FiveSstandard.linename == linename).order_by(FiveSstandard.station).all()
    for t in tasks:
        auditlist.append({
            'Id': t.Id,
            'station': t.station,
            'picurl': getServer()['fivesauditsUrl']+'standards/'+t.picurl if t.picurl else '',
            'duration': t.duration,
            'requirement': t.requirement,
            'comment': t.comment
        })
    print(111, tasks)
    return responseGet("获取成功", {'standard': auditlist})


@api.route('/get5Scheck', methods=['GET'])
# @ login_required
def get5Scheck():
    res = request.args
    linename = res.get('linename')
    startdate = res.get('startdate')
    enddate = res.get('enddate')
    enddatetommorrow = datetime.datetime.strptime(enddate, '%Y-%m-%d')+datetime.timedelta(days=1)
    checkDic = {}
    audits = db.session.query(FiveSdaily.Id, FiveSdaily.checktime).filter(FiveSdaily.linename ==
                                                                          linename).filter(FiveSdaily.checktime.between(startdate, enddatetommorrow))
    for a in audits:
        cd = datetime.datetime.strftime(a.checktime, '%Y-%m-%d')
        if cd in checkDic.keys():
            checkDic[cd] += 1
        else:
            checkDic[cd] = 1
    return responseGet("获取成功", {'checkDic': checkDic})


@api.route('/get5Scheckbyid', methods=['GET'])
# @ login_required
def get5Scheckbyid():
    res = request.args
    linename = res.get('linename')
    checkdate = res.get('checkdate')
    enddatetommorrow = datetime.datetime.strptime(checkdate, '%Y-%m-%d')+datetime.timedelta(days=1)
    checkArr = []
    audits = db.session.query(FiveSdaily.Id, FiveSdaily.checktime, Userinfo.cname, FiveSstandard.picurl.label('benchurl'),
                              FiveSdaily.picurl).join(Userinfo, FiveSdaily.eid == Userinfo.eid).join(
        FiveSstandard, and_(FiveSdaily.linename == FiveSstandard.linename, FiveSdaily.station == FiveSstandard.station)).filter(
            FiveSdaily.linename == linename).filter(FiveSdaily.checktime.between(checkdate, enddatetommorrow))
    for a in audits:
        cktime = datetime.datetime.strftime(a.checktime, '%Y-%m-%d %H:%M')
        checkArr.append({
            'Id': a.Id,
            'checktime': cktime,
            'cname': a.cname,
            'picurl': getServer()['fivesauditsUrl']+'daily/'+a.picurl if a.picurl else '',
            'benchurl': getServer()['fivesauditsUrl']+'standards/'+a.benchurl if a.benchurl else '',
            'picArr': []
        })
    return responseGet("获取成功", {'checkArr': checkArr})


@api.route('/get5Saudit', methods=['GET'])
# @ login_required
def get5Saudit():
    res = request.args
    linename = res.get('linename')
    year = res.get('year')
    checkArr = []
    audits = db.session.query(func.sum(FiveSaudit.markqty).label('qty'), FiveSaudit.layeredid, Userinfo.cname,
                              Layeredresult.finishtime).join(Userinfo, FiveSaudit.eid == Userinfo.eid).join(
        Layeredresult, FiveSaudit.layeredid == Layeredresult.Id).filter(FiveSaudit.linename == linename).filter(
        func.year(Layeredresult.starttime) == year).filter(Layeredresult.status != 0).order_by(desc(Layeredresult.finishtime)).group_by(FiveSaudit.layeredid).all()
    for a in audits:
        cd = datetime.datetime.strftime(a.finishtime, '%Y-%m-%d') if a.finishtime else ''
        checkArr.append({
            'cname': a.cname,
            'layeredid': a.layeredid,
            'qty': a.qty,
            'checktime': cd
        })
    return responseGet("获取成功", {'checkArr': checkArr})


@api.route('/get5Sauditbyid', methods=['GET'])
# @ login_required
def get5Sauditbyid():
    res = request.args
    layeredid = res.get('layeredid')
    sw = int(res.get('sw'))
    left = int(res.get('left'))
    top = int(res.get('top'))
    print(left, top)
    checkArr = []
    audits = db.session.query(FiveSaudit).filter(FiveSaudit.layeredid == layeredid).filter(FiveSaudit.markqty > 0).all()
    for t in audits:
        marks = t.marks.split(',')
        spoints = t.spoint.split(':')
        markArr = []
        for m in marks:
            markArr.append({
                'x': rpxTopx(int(m.split(':')[0])-int(spoints[0]), sw)+left,
                'y': rpxTopx(int(m.split(':')[1])-int(spoints[1]), sw)+top
            })
        checkArr.append({
            'marks': markArr,
            'markqty': t.markqty,
            'comment': t.comment,
            'picurl': getServer()['fivesauditsUrl']+t.auditpic if t.auditpic else '',
            'benchurl': getServer()['fivesauditsUrl']+t.benchpic if t.benchpic else '',
            'picArr': []
        })
    return responseGet("获取成功", {'checkArr': checkArr})


def rpxTopx(rpx, sw):
    return round(rpx*sw/750, 0)

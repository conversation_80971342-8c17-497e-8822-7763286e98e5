import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export interface CustomerData {
  id?: number;
  company: string;
  code: string;
  province?: string;
  city?: string;
  district?: string;
  address?: string;
  email?: string;
}

export interface CustomerListParams {
  search?: string;
}

export interface ApiResult {
  code: number;
  message: string;
  data?: any;
}

/** 获取客户列表 */
export const getCustomerList = (params?: CustomerListParams) => {
  return http.request<ApiResult>("get", baseUrlApi("/tms/customer/getcustomerlist"), {
    params
  });
};

/** 获取单个客户详情 */
export const getCustomer = (customerId: number) => {
  return http.request<ApiResult>("get", baseUrlApi(`/tms/customer/getcustomer/${customerId}`));
};

/** 创建新客户 */
export const createCustomer = (data: CustomerData) => {
  return http.request<ApiResult>("post", baseUrlApi("/tms/customer/createcustomer"), {
    data
  });
};

/** 更新客户信息 */
export const updateCustomer = (customerId: number, data: Partial<CustomerData>) => {
  return http.request<ApiResult>("put", baseUrlApi(`/tms/customer/updatecustomer/${customerId}`), {
    data
  });
};

/** 删除客户（软删除） */
export const deleteCustomer = (customerId: number) => {
  return http.request<ApiResult>("delete", baseUrlApi(`/tms/customer/deletecustomer/${customerId}`));
};

/** 批量删除客户 */
export const batchDeleteCustomers = (customerIds: number[]) => {
  return http.request<ApiResult>("delete", baseUrlApi("/tms/customer/batchdeletecustomers"), {
    data: { customer_ids: customerIds }
  });
};

/** 获取客户统计信息 */
export const getCustomerStatistics = () => {
  return http.request<ApiResult>("get", baseUrlApi("/tms/customer/getcustomerstatistics"));
};

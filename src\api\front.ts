import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 返回生产页的基本信息
export const getbasicinfo = (data: object) => {
  return http.request("get", baseUrlApi("front/getbasicinfo"), {
    params: data
  });
};

// 确认小时记录表数据
export const confirmhourdata = (data: object) => {
  return http.request("post", baseUrlApi("hourtable/confirmdata"), { data });
};

// 更新小时记录表数据
export const updatehourdata = (data: object) => {
  return http.request("post", baseUrlApi("hourtable/updatehourdata"), { data });
};

// 获取不良明细
export const getdefectlist = (data: object) => {
  return http.request("get", baseUrlApi("quality/getdefectbyshift"), {
    params: data
  });
};

// 获取不良分类
export const getdefecttype = () => {
  return http.request("get", baseUrlApi("quality/getdefecttypelist"));
};

//添加小时不良数据
export const updatedefectdata = (data: object) => {
  return http.request("post", baseUrlApi("quality/updatedefectdata"), {
    data
  });
};

// 删除设备运行状态记录
export const deleteState = (data: object) => {
  return http.request("post", baseUrlApi("shift/deletestate"), { data });
};

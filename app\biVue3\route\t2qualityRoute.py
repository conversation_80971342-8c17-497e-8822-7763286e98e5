from flask import Blueprint, jsonify, request
import datetime
import pymssql
from app.public.functions import responseGet, responsePost, responseError
from config import config, env
from extensions import db
from sqlalchemy import func,  and_, or_, desc
from app.receiving.model.models_receiving import Ncmr, Nc<PERSON>ra<PERSON>, Lineinfo, Inspect
from app.receiving.functions import getServer
from app.receiving.schemas import ncmrs_schema, ncmrauth_schema, ncmr_schema
from app.dm.model.models_dm import Scaninfo, Shiftinfo, Issuelog,  Lineinfo as linfo
from app.dm.functions import getkpi, getproblems, getproblemsoee, dmIssueSubmit
api = Blueprint('bi3/t2qualityAPI', __name__)


@ api.route("/toAccount", methods=["POST"])  # 登记QDC问题
def toAccount():
    res = request.json
    check = dmIssueSubmit(res)
    if check:
        return responsePost('插入成功')
    else:
        return responseError('插入失败，请联系管理员')


@ api.route('/getNCMRbyid', methods=['GET'])  # 获取该区域的所有未结PQC稽核的NCMR信息
def getNCMRbyid():
    res = request.args
    ncmrid = res.get('ncmrid')
    ncmr = db.session.query(Ncmr).filter(Ncmr.Id == ncmrid).first()
    item = ncmr_schema.dump(ncmr)
    ncmrAuth = db.session.query(Ncmrauth).filter(
        Ncmrauth.ncmrid == item['Id']).filter(Ncmrauth.dept != 'WH').all()
    if ncmrAuth:
        ncmrAuthlist = ncmrauth_schema.dump(ncmrAuth)
        item['ncmrAuthlist'] = ncmrAuthlist
    if item['pics']:
        p = item['pics'].split(',')
        for i in range(len(p)):
            p[i] = getServer()['picUrl']+p[i]
        item['pics'] = ','.join(p)
    else:
        item['pics'] = getServer()['picUrl']+'nophoto.jpg'
    data = {'ncmrinfo': item}
    return responseGet("获取NCMR待处理列表成功", data)


@api.route('/getIQCPD', methods=['GET'])   # 从VOC系统内获取EPPM数据，待替换成别的
def getIQCPD():
    res = request.args
    area = res.get('area')
    inspects = db.session.query(Inspect.Id, Inspect.receive_date, Inspect.sku, Inspect.sku_desc, Inspect.inspector, Inspect.pastdue_type, Ncmr.Id.label('ncmrid')).outerjoin(
        Lineinfo, Inspect.mgroup == Lineinfo.mgroup).outerjoin(Ncmr, Inspect.Id == Ncmr.inspectid).filter(
        or_(Inspect.status == 'open', Inspect.status == 'ongoing')).filter(Lineinfo.area == area).filter(func.datediff(datetime.datetime.now(), Inspect.receive_date) > 2).all()
    outArr = []
    for i in inspects:
        outArr.append({
            'Id': i.Id,
            'receive_date': datetime.datetime.strftime(i.receive_date, '%m-%d'),
            'sku': i.sku,
            'sku_desc': i.sku_desc,
            'inspector': i.inspector,
            'pastdue_type': i.pastdue_type,
            'ncmrid': i.ncmrid
        })
    print('area', outArr)
    return responseGet('成功', {'pastdues': outArr})


@api.route('/getEPPM', methods=['GET'])   # 从VOC系统内获取EPPM数据，待替换成别的
def getEPPM():
    connect = pymssql.connect(config[env].vocdb['host'],
                              config[env].vocdb['user'], config[env].vocdb['password'], config[env].vocdb['database'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    sql1 = '''
    SELECT    dmgroup.vsm, AVG(dfmonth.month) AS month, SUM(dvoc.eppmqty) AS dppmqty,SUM(case when dvoc.eppmtype='3rd Party' then dvoc.eppmqty else 0 end) as eppmqty
        FROM         dfmonth LEFT OUTER JOIN
                              dvoc ON dvoc.dateeppm >= dfmonth.startday AND dvoc.dateeppm < DATEADD(DAY, 1, dfmonth.endday)
                    LEFT OUTER JOIN
                              dline ON dvoc.line=dline.linename
                    LEFT OUTER JOIN
                              dmgroup ON dline.mgroup=dmgroup.mgroup
        WHERE     (dfmonth.year = 2021)  and (etype not in ('电商退货','Case pool'))
        GROUP BY dmgroup.vsm,dfmonth.month
        UNION
        SELECT     dmgroup.vsm,YEAR(getdate()) AS month, SUM(eppmqty) AS dppmqty, SUM(case when eppmtype='3rd Party' then eppmqty else 0 end) as eppmqty
        FROM         dvoc
                    LEFT OUTER JOIN
                              dline ON dvoc.line=dline.linename
                    LEFT OUTER JOIN
                              dmgroup ON dline.mgroup=dmgroup.mgroup
        WHERE     (YEAR(dateeppm) = YEAR(getdate()))  and (etype not in ('电商退货','Case pool')) group by dmgroup.vsm
    '''
    cursor.execute(sql1)
    r1 = cursor.fetchall()
    sql2 = """
    select vsm,issuetype,SUM(eppm) as cqty,COUNT(vid) as nb from
    (select dvoc.vsm vsm, issuetype,avg(eppmqty) as eppm,dvoc.id as vid
    from dvoc left join dsku on dvoc.id=dsku.vocid
    where Year(dvoc.receivedate)=YEAR(getdate()) and issuetype!=''  group by dvoc.vsm,dvoc.id,issuetype) as A
    group by issuetype,vsm  order by issuetype,vsm desc
    """
    cursor.execute(sql2)
    r2 = cursor.fetchall()
    bdic = {
        'title': 'BIBO',
        'radarValue': [],
        'radarCount': [],
        'radarData': [],
        'lineData': [['Month'], ['EPPM'], ['DPPM']]
    }
    vsm1dic = {
        'title': 'VSM1',
        'radarValue': [],
        'radarCount': [],
        'radarData': [],
        'lineData': [['Month'], ['EPPM'], ['DPPM']]
    }
    vsm2dic = {
        'title': 'VSM2',
        'radarValue': [],
        'radarCount': [],
        'radarData': [],
        'lineData': [['Month'], ['EPPM'], ['DPPM']]
    }
    for r in r1:
        if r[0] == 'BIBO':
            bdic['lineData'][0].append(r[1])
            bdic['lineData'][1].append(r[2])
            bdic['lineData'][2].append(r[3])
        elif r[0] == 'VSM1':
            vsm1dic['lineData'][0].append(r[1])
            vsm1dic['lineData'][1].append(r[2])
            vsm1dic['lineData'][2].append(r[3])
        elif r[0] == 'VSM2':
            vsm2dic['lineData'][0].append(r[1])
            vsm2dic['lineData'][1].append(r[2])
            vsm2dic['lineData'][2].append(r[3])
    maxb = 0
    max1 = 0
    max2 = 0
    for r in r2:
        if r[0] == 'BIBO':
            if r[2] > maxb:
                maxb = r[2]
        elif r[0] == 'VSM1':
            if r[2] > max1:
                max1 = r[2]
        elif r[0] == 'VSM2':
            if r[2] > max2:
                max2 = r[2]
    for r in r2:
        if r[0] == 'BIBO':
            bdic['radarValue'].append(r[2])
            bdic['radarCount'].append(r[3])
            bdic['radarData'].append({'name': r[1], 'max': maxb})
        elif r[0] == 'VSM1':
            vsm1dic['radarValue'].append(r[2])
            vsm1dic['radarCount'].append(r[3])
            vsm1dic['radarData'].append({'name': r[1], 'max': max1})
        elif r[0] == 'VSM2':
            vsm2dic['radarValue'].append(r[2])
            vsm2dic['radarCount'].append(r[3])
            vsm2dic['radarData'].append({'name': r[1], 'max': max2})
    myData = [bdic, vsm1dic, vsm2dic]
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data=myData)


@api.route('/getvoc', methods=['GET'])   # 从VOC系统里获取最近的客诉信息列表
def getvoc():
    res = request.args
    area = res.get('area')
    checked = res.get('checked').split(',')
    ckarr = []
    if checked[0] == 'true':
        ckarr.append("'open'")
    if checked[1] == 'true':
        ckarr.append("'ongoing'")
    if checked[2] == 'true':
        ckarr.append("'closed'")
    if checked[3] == 'true':
        ckarr.append("'cancel'")
    connect = pymssql.connect(config[env].vocdb['host'],
                              config[env].vocdb['user'], config[env].vocdb['password'], config[env].vocdb['database'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    sql1 = """select Top 100 status, receivedate, dline.linename, fg, qa, vocdesc,dsku.sku,"from",dvoc.id from dvoc
    left join dline on
    dvoc.mgroupnumber = dline.mgroup left join dsku on dsku.vocid=dvoc.id where (dvoc.area = '%s' or dline.area='%s')""" % (
        area, area)
    if area == 'B4WR':
        sql1 = """select Top 100  status, receivedate, dline.linename, fg, qa, vocdesc,dsku.sku,"from",dvoc.id
        from dvoc left join dline on dvoc.mgroupnumber = dline.mgroup left join dsku on dsku.vocid=dvoc.id where
        ((dvoc.area = 'B4WR' or dvoc.area='B4UF') or (dline.area = 'B4WR' or dline.area='B4UF')) """
    if len(ckarr) > 0:
        sql1 = sql1 + " and status in ("+','.join(ckarr)+')'
    sql1 = sql1+" order by receivedate desc "
    cursor.execute(sql1)
    r1 = cursor.fetchall()
    outArr = []
    dic = {}
    for r in r1:
        if r[8] is not None and r[8] in dic.keys():
            dic[r[8]] = {
                'status': r[0],
                'receivedate': datetime.datetime.strftime(r[1], '%Y-%m-%d') if r[1] else '',
                'line': dic[r[8]]['line'] if dic[r[8]]['line'] else r[2],
                'fg': r[3],
                'qa': r[4],
                'vocdesc': r[5],
                'sku': dic[r[8]]['sku'] if dic[r[8]]['sku'] else r[6],
                'vfrom': r[7]
            }
        else:
            dic[r[8]] = {
                'status': r[0],
                'receivedate': datetime.datetime.strftime(r[1], '%Y-%m-%d') if r[1] else '',
                'line': r[2] if r[2] else 'line',
                'fg': r[3],
                'qa': r[4],
                'vocdesc': r[5],
                'sku': r[6],
                'vfrom': r[7]
            }
    outArr = list(dic.values())
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data=outArr)


@ api.route('/getNCMR', methods=['GET'])  # 获取该区域的所有未结PQC稽核的NCMR信息
def getNCMR():
    res = request.args
    query = res.get('query')
    area = res.get('area')
    ncmr = db.session.query(Ncmr).outerjoin(Lineinfo, Ncmr.project == Lineinfo.line).filter(Lineinfo.area == area).filter(Ncmr.pqccate == 0).filter(Ncmr.sku.like('%{0}%'.format(query))).filter(
        and_(Ncmr.status != 'closed', Ncmr.status != 'cancel', Ncmr.status != 'PQC', Ncmr.ncmrtype == 'PQC')).order_by(
        Ncmr.status, Ncmr.actiondate.is_(None), Ncmr.actiondate, Ncmr.initiatedate).all()
    total = db.session.query(func.count(Ncmr.Id)).outerjoin(Lineinfo, Ncmr.project == Lineinfo.line).filter(Lineinfo.area == area).filter(Ncmr.pqccate == 0).filter(
        Ncmr.sku.like('%{0}%'.format(query))).filter(
        and_(Ncmr.status != 'closed', Ncmr.status != 'cancel', Ncmr.status != 'PQC', Ncmr.ncmrtype == 'PQC')).scalar()
    ncmrlist = ncmrs_schema.dump(ncmr)
    for item in ncmrlist:
        ncmrAuth = db.session.query(Ncmrauth).filter(
            Ncmrauth.ncmrid == item['Id']).filter(Ncmrauth.dept != 'WH').all()
        if ncmrAuth:
            ncmrAuthlist = ncmrauth_schema.dump(ncmrAuth)
            item['ncmrAuthlist'] = ncmrAuthlist
        if item['pics']:
            p = item['pics'].split(',')
            for i in range(len(p)):
                p[i] = getServer()['picUrl']+p[i]
            item['pics'] = ','.join(p)
        else:
            item['pics'] = getServer()['picUrl']+'nophoto.jpg'
    data = {'total': total, 'ncmrlist': ncmrlist}
    return responseGet("获取NCMR待处理列表成功", data)


@api.route('/getQmonth', methods=['GET'])  # 获得质量相关的趋势图，柏拉图和问题清单
def getQmonth():
    r = request.args
    stime = r.get('stime')
    etime = r.get('etime')
    ttime = datetime.datetime.strptime(
        etime, '%Y-%m-%d')+datetime.timedelta(days=1)
    days = (datetime.datetime.strptime(etime, '%Y-%m-%d') -
            datetime.datetime.strptime(stime, '%Y-%m-%d')).days
    linegroup = r.get('area')
    area = r.get('group')
    typeline = []
    alllines = db.session.query(linfo.linegroup).filter(
        linfo.area == area).all()
    kpi = getkpi(stime.split('-')[0], linegroup)['qtarget']
    shifts = db.session.query(func.date(Shiftinfo.starttime).label('shiftdate'), func.sum(Scaninfo.scanqty).label('pqty')).join(
        Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(linfo, Shiftinfo.linename == linfo.linename).filter(and_(
            Shiftinfo.starttime >= stime, Shiftinfo.finishtime <= ttime)).group_by(func.date(Shiftinfo.starttime))
    if linegroup:
        shifts = shifts.filter(linfo.linegroup == linegroup)
        typeline.append(linegroup)
    else:
        shifts = shifts.filter(linfo.area == area)
        for a in alllines:
            typeline.append(a[0])
    shifts = shifts.all()
    print('sssssssssssss', typeline)
    data = {
        'ftt': [],
        'pd': [],
        'fttmin': 50,
        'fttx': [],
        'green': [],
        'red': [],
        'yellow': [],
        'listData': [],
        'paretodata': [],
        'paretotarget': [],
        'paretox': [],
        'defect': []
    }
    for p in shifts:
        sdate = datetime.datetime.strftime(p.shiftdate, '%m-%d')
        data['fttx'].append(sdate)
        data['pd'].append(p.pqty)
        data['ftt'].append(100)
        data['defect'].append(0)
        data['green'].append(100-kpi*100)
        data['red'].append(kpi*95)
        data['yellow'].append(kpi*5)
    problems = getproblems(typeline, '质量', days)
    for p in problems:
        data['paretox'].append(p['ptype'])
        data['paretotarget'].append(p['trigger'])
        data['paretodata'].append(0)
    data['paretox'].append('未分类')
    data['paretotarget'].append(round(5/7*days))
    data['paretodata'].append(0)
    issues = db.session.query(Issuelog).join(linfo, Issuelog.linename == linfo.linename).filter(
        Issuelog.shiftdate.between(stime, etime)).filter(Issuelog.sqdctype == '质量')
    if linegroup:
        issues = issues.filter(linfo.linegroup == linegroup)
    else:
        issues = issues.filter(linfo.area == area)
    issues = issues.order_by(desc(Issuelog.recordtime)).all()
    for i in issues:
        if i.problemtype in data['paretox']:
            data['paretodata'][data['paretox'].index(i.problemtype)] += i.qty
        else:
            data['paretodata'][len(data['paretodata'])-1] += i.qty
        data['listData'].append({
            'recorddate': datetime.datetime.strftime(i.shiftdate, "%Y-%m-%d"),
            'desc': i.desc,
            'linename': i.linename,
            'qty': i.qty,
            'problemtype': i.problemtype if i.problemtype else '其他'
        })
        sftdate = datetime.datetime.strftime(i.shiftdate, '%m-%d')
        if sftdate in data['fttx']:
            idx = data['fttx'].index(sftdate)
            data['defect'][idx] += i.qty
    for i in range(len(data['defect'])):
        if data['defect'][i] > 0:
            data['ftt'][i] = round(
                (data['pd'][i]-data['defect'][i])/data['pd'][i]*100, 1)
    # print(data)
    maxqty = 100
    if len(data['ftt']) > 0:
        data['fttmin'] = min(data['ftt']) if min(data['ftt']) < 100 else 80
        maxqty = max(data['ftt']) if max(data['ftt']) > 100 else 100
    for j in range(len(data['green'])):
        data['green'][j] = int(maxqty)-kpi*100 + \
            5 if (kpi*100 < int(maxqty)) else 5
    indexArr = []
    for i in range(len(data['paretodata'])):
        if data['paretodata'][i] == 0:
            indexArr.append(i)
    print(111111111111, indexArr)
    data['paretodata'] = [data['paretodata'][i]
                          for i in range(len(data['paretodata'])) if (i not in indexArr)]
    data['paretox'] = [data['paretox'][i]
                       for i in range(len(data['paretox'])) if (i not in indexArr)]
    data['paretotarget'] = [data['paretotarget'][i]
                            for i in range(len(data['paretotarget'])) if (i not in indexArr)]
    return responseGet('获取成功', data)


@api.route('/getQmonthoee', methods=['GET'])  # 获得质量相关的趋势图，柏拉图和问题清单
def getQmonthoee():
    r = request.args
    stime = r.get('stime')
    etime = r.get('etime')
    ttime = datetime.datetime.strptime(
        etime, '%Y-%m-%d')+datetime.timedelta(days=1)
    days = (datetime.datetime.strptime(etime, '%Y-%m-%d') -
            datetime.datetime.strptime(stime, '%Y-%m-%d')).days
    linegroup = r.get('area')
    area = r.get('group')
    typeline = []
    alllines = db.session.query(linfo.linegroup).filter(
        linfo.area == area).all()
    kpi = getkpi(stime.split('-')[0], linegroup)['qtarget']
    shifts = db.session.query(func.date(Shiftinfo.starttime).label('shiftdate'), func.sum(Scaninfo.scanqty).label('pqty')).outerjoin(
        Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(linfo, Shiftinfo.linename == linfo.linename).filter(and_(
            Shiftinfo.starttime >= stime, Shiftinfo.finishtime <= ttime)).group_by(func.date(Shiftinfo.starttime))
    if linegroup:
        shifts = shifts.filter(linfo.linegroup == linegroup)
        typeline.append(linegroup)
    else:
        shifts = shifts.filter(linfo.area == area)
        for a in alllines:
            typeline.append(a[0])
    shifts = shifts.all()
    print(shifts)
    print('sssssssssssss', typeline)
    data = {
        'ftt': [],
        'pd': [],
        'fttmin': 50,
        'defect': [],
        'fttx': [],
        'green': [],
        'red': [],
        'yellow': [],
        'listData': [],
        'paretodata': [],
        'paretotarget': [],
        'paretox': []
    }
    for p in shifts:
        sdate = datetime.datetime.strftime(p.shiftdate, '%m-%d')
        data['fttx'].append(sdate)
        data['pd'].append(p.pqty)
        data['ftt'].append(100)
        data['defect'].append(0)
        data['green'].append(100-kpi*100)
        data['red'].append(kpi*95)
        data['yellow'].append(kpi*5)
    problems = getproblemsoee(typeline, '质量不良', days)
    for p in problems:
        data['paretox'].append(p['ptype'])
        data['paretotarget'].append(p['trigger'])
        data['paretodata'].append(0)
    data['paretox'].append('未分类')
    data['paretotarget'].append(round(5/7*days))
    data['paretodata'].append(0)
    issues = db.session.query(Issuelog).join(linfo, Issuelog.linename == linfo.linename).filter(
        Issuelog.shiftdate.between(stime, etime)).filter(Issuelog.sqdctype == '质量不良')
    if linegroup:
        issues = issues.filter(linfo.linegroup == linegroup)
    else:
        issues = issues.filter(linfo.area == area)
    issues = issues.order_by(desc(Issuelog.recordtime)).all()
    for i in issues:
        if i.problemtype in data['paretox']:
            data['paretodata'][data['paretox'].index(i.problemtype)] += i.qty
        else:
            data['paretodata'][len(data['paretodata'])-1] += i.qty
        data['listData'].append({
            'recorddate': datetime.datetime.strftime(i.shiftdate, "%Y-%m-%d"),
            'desc': i.desc,
            'linename': i.linename,
            'qty': i.qty,
            'problemtype': i.problemtype if i.problemtype else '其他'
        })
        sftdate = datetime.datetime.strftime(i.shiftdate, '%m-%d')
        if sftdate in data['fttx']:
            idx = data['fttx'].index(sftdate)
            data['defect'][idx] += i.qty
    for i in range(len(data['defect'])):
        if data['defect'][i] > 0:
            print(data['defect'][i], data['pd'][i], data['fttx'][i])
            data['ftt'][i] = round(
                (data['pd'][i]-data['defect'][i])/data['pd'][i]*100, 0)
            # print(data['ftt'][idx])
    # print(data)
    maxqty = 100
    if len(data['ftt']) > 0:
        data['fttmin'] = min(data['ftt']) if min(data['ftt']) < 100 else 80
        maxqty = max(data['ftt']) if max(data['ftt']) > 100 else 100
    for j in range(len(data['green'])):
        data['green'][j] = int(maxqty)-kpi*100 + \
            5 if (kpi*100 < int(maxqty)) else 5
    indexArr = []
    for i in range(len(data['paretodata'])):
        if data['paretodata'][i] == 0:
            indexArr.append(i)
    data['paretodata'] = [data['paretodata'][i]
                          for i in range(len(data['paretodata'])) if (i not in indexArr)]
    data['paretox'] = [data['paretox'][i]
                       for i in range(len(data['paretox'])) if (i not in indexArr)]
    data['paretotarget'] = [data['paretotarget'][i]
                            for i in range(len(data['paretotarget'])) if (i not in indexArr)]
    return responseGet('获取成功', data)


@api.route('/getQday', methods=['GET'])  # 获取当日的质量情况和各个产线面积划分，前端显示未一个treemap
def getQday():
    res = request.args
    area = res.get('area')
    qdate = datetime.date.today()
    if area == 'B1Tank':
        qdate = datetime.date.today()-datetime.timedelta(days=1)
    # qdate = '2021-09-01'
    pdsDic, issuesDic, dt = getQdaynormal(area, qdate)
    totalpd = sum(list(pdsDic.values()))
    totaldefect = sum(list(issuesDic.values()))
    qrate = round((totalpd-totaldefect)/totalpd*100, 1) if totalpd else '-'
    return responseGet("获取数据成功", {'produceQty': str(totalpd), 'defectQty': str(totaldefect),
                                  'qRate': str(qrate), 'outArr': dt})


def getQdaynormal(area, qdate):
    pds = db.session.query(linfo.area, linfo.linegroup, linfo.linename,  linfo.space,
                           func.sum(Scaninfo.scanqty).label('scanqty')).join(Shiftinfo, Scaninfo.shiftid == Shiftinfo.Id).join(
        linfo, linfo.linename == Shiftinfo.linename).filter(func.date(Shiftinfo.starttime) == qdate).filter(linfo.area == area).group_by(linfo.linename).all()
    pdsDic = {}
    for p in pds:
        pdsDic[p.linename] = p.scanqty
    issues = db.session.query(linfo.linename, func.sum(Issuelog.qty).label('fqty')).join(linfo, Issuelog.linename == linfo.linename).filter(
        linfo.area == area).filter(or_(Issuelog.sqdctype == '质量', Issuelog.sqdctype == '质量不良')).filter(Issuelog.shiftdate == qdate).group_by(linfo.linename).all()
    issuesDic = {}
    for i in issues:
        issuesDic[i.linename] = i.fqty
    lines = db.session.query(linfo).filter(
        linfo.isactive == 1).filter(linfo.area == area).all()
    outArr = []
    for ll in lines:
        dic = {
            'linename': ll.linename,
            'shift': ll.currentshift,
            'linegroup': ll.linegroup,
            'area': ll.area,
            'space': ll.space,
            'defectQty': issuesDic[ll.linename] if (ll.linename in issuesDic.keys()) else 0,
            'produceQty': pdsDic[ll.linename] if (ll.linename in pdsDic.keys()) else 0
        }
        outArr.append(dic)
    dt = gemList(outArr)
    return pdsDic, issuesDic, dt


def gemList(arr):    # 把数据调整成前端需要的树状机构
    dics = {}
    dics2 = {}
    for a in arr:
        dft = a['produceQty']
        if a['defectQty'] > 0:
            dft = -a['defectQty']
        arr = [int(a['space']), int(a['produceQty']),
               int(a['defectQty']), int(dft)]
        if a['linegroup'] in dics.keys():
            dics[a['linegroup']]['children'].append(
                {'value': arr, 'name': a['linename']})
            dics[a['linegroup']]['value'][0] = dics[a['linegroup']
                                                    ]['value'][0]+a['space']
        else:
            dics[a['linegroup']] = {
                'area': a['area'],
                'name': a['linegroup'],
                'value': [a['space'], 0, 0, 0],
                'children': [
                    {'value': arr, 'name': a['linename']}]
            }
    subArr = dics.values()
    for s in subArr:
        if s['area'] in dics2.keys():
            dics2[s['area']]['children'].append(s)
            dics2[s['area']]['value'][0] = dics2[s['area']
                                                 ]['value'][0]+s['value'][0]
        else:
            dics2[s['area']] = {
                'area': s['area'],
                'name': s['area'],
                'value': [s['value'][0], 0, 0, 0],
                'children': [
                    s
                ]
            }
    outArr = list(dics2.values())
    return outArr

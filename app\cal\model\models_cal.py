from unicodedata import name
from extensions import db


# 修改时间 2022.10.17
class Dcal(db.Model):
    __tablename__ = "dcal_cal2"
    id = db.Column(db.Integer, primary_key=True)
    instrumentno = db.Column(db.String(50))
    instrumentname = db.Column(db.String(50))
    factorysign = db.Column(db.String(20))
    type = db.Column(db.String(20))
    factoryno = db.Column(db.String(20))
    calperiod = db.Column(db.Integer)
    caldata = db.Column(db.Date)
    checkstatus = db.Column(db.Integer)
    instepid = db.Column(db.Integer)
    checkresultall = db.Column(db.String(20))
    locno = db.Column(db.String(50))
    location = db.Column(db.String(50))
    duedate = db.Column(db.Date)
    refdoc = db.Column(db.String(50))
    calunit = db.Column(db.String(20))
    checkmode = db.Column(db.String(20))
    failpic = db.Column(db.String(255))
    measurestatus = db.Column(db.String(20))
    calcost = db.Column(db.Integer)
    precal = db.Column(db.String(50))
    caltype = db.Column(db.String(50))
    keeper = db.Column(db.String(50))
    deptment = db.Column(db.String(50))
    comment = db.Column(db.String(50))
    measureparameter = db.Column(db.String(50))
    offset = db.Column(db.String(50))
    showradio = db.Column(db.String(50))
    grade = db.Column(db.String(50))
    appearancefailpic = db.Column(db.String(50))
    appearance = db.Column(db.Integer)
    ngreason = db.Column(db.String(150))
    opensingnl = db.Column(db.String(50))
    opendate = db.Column(db.String(50))
    checkbox1 = db.Column(db.Integer)
    checkbox2 = db.Column(db.Integer)
    abstatus = db.Column(db.Integer)
    checkbox3 = db.Column(db.Integer)
    checkbox4 = db.Column(db.Integer)
    deptA = db.Column(db.String(50))
    deptB = db.Column(db.String(50))
    deptC = db.Column(db.String(50))
    deptD = db.Column(db.String(50))
    austatus = db.Column(db.Integer)
    recallev = db.Column(db.String(255))
    rootcase = db.Column(db.String(255))
    processre = db.Column(db.String(50))
    classify = db.Column(db.String(50))
    verpersion = db.Column(db.String(50))
    verdate = db.Column(db.Date)
    damagedes = db.Column(db.String(255))
    outconfirm = db.Column(db.Integer)
    temperature = db.Column(db.String(20))
    humidity = db.Column(db.String(20))
    addreason = db.Column(db.String(20))

class Dcal_instep(db.Model):
    __tablename__ = "dcal_instep"
    id = db.Column(db.Integer, primary_key=True)
    calid = db.Column(db.Integer)
    checkname = db.Column(db.String(50))
    checkresult = db.Column(db.String(50))
    spec = db.Column(db.String(50))
    standard = db.Column(db.Float)
    reading = db.Column(db.Float)
    upline = db.Column(db.String(10))
    downline = db.Column(db.Float)
    version = db.Column(db.Integer)


class Dcal_standcaleq(db.Model):
    __tablename__ = "dcal_standcaleq"
    id = db.Column(db.Integer, primary_key=True)
    calid = db.Column(db.Integer)
    sname = db.Column(db.String(50))
    insno = db.Column(db.String(50))
    description = db.Column(db.String(50))
    mfg_model = db.Column(db.String(50))
    serno = db.Column(db.String(50))
    duedate = db.Column(db.Date)


class Dcal_user(db.Model):
    __tablename__ = "dcal_user"
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(50))
    eid = db.Column(db.String(20))
    power = db.Column(db.String(50))
    poweredit = db.Column(db.String(50))
    dept = db.Column(db.String(50))
    powerlist = db.Column(db.String(255))
    # deptarr = db.Column(db.String(50))


class Dcal_cal_version(db.Model):
    __tablename__ = "dcal_cal_version"
    id = db.Column(db.Integer, primary_key=True)
    calid = db.Column(db.Integer)
    version = db.Column(db.Integer)
    caldata = db.Column(db.Date)
    checkstatus = db.Column(db.Integer)
    checkresultall = db.Column(db.String(20))
    duedate = db.Column(db.Date)
    checkmode = db.Column(db.String(20))
    failpic = db.Column(db.String(255))
    measurestatus = db.Column(db.String(20))
    calcost = db.Column(db.Integer)
    appearancefailpic = db.Column(db.String(50))
    appearance = db.Column(db.Integer)
    ngreason = db.Column(db.String(150))
    opensingnl = db.Column(db.String(50))
    opendate = db.Column(db.String(50))
    checkbox1 = db.Column(db.Integer)
    checkbox2 = db.Column(db.Integer)
    abstatus = db.Column(db.Integer)
    checkbox3 = db.Column(db.Integer)
    checkbox4 = db.Column(db.Integer)
    deptA = db.Column(db.String(50))
    deptB = db.Column(db.String(50))
    deptC = db.Column(db.String(50))
    deptD = db.Column(db.String(50))
    austatus = db.Column(db.Integer)
    recallev = db.Column(db.String(255))
    rootcase = db.Column(db.String(255))
    processre = db.Column(db.String(50))
    classify = db.Column(db.String(50))
    verpersion = db.Column(db.String(50))
    verdate = db.Column(db.Date)
    damagedes = db.Column(db.String(255))
    outconfirm = db.Column(db.Integer)
    temperature = db.Column(db.String(20))
    humidity = db.Column(db.String(20))
    carstatus = db.Column(db.Integer)
    carrootcase = db.Column(db.String(255))
    carcorrective = db.Column(db.String(255))
    carstandardization = db.Column(db.String(255))
    carimprovetag = db.Column(db.Integer)
    carevidence = db.Column(db.String(255))
    caraccepter = db.Column(db.String(20))
    carclosedate = db.Column(db.Date)
    calperiod = db.Column(db.Integer)
    repaircost = db.Column(db.Integer)
    newbuycost = db.Column(db.Integer)
    ngconfirm = db.Column(db.String(255))
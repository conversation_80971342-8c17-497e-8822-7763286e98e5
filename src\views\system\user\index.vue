<script setup lang="tsx">
import { ref, reactive, onMounted, computed } from "vue";
import { useUser } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getUserList } from "@/api/user";

import Upload from "@iconify-icons/ri/upload-line";
import Role from "@iconify-icons/ri/admin-line";
import Password from "@iconify-icons/ri/lock-password-line";
import More from "@iconify-icons/ep/more-filled";
import Delete from "@iconify-icons/ep/delete";
import EditPen from "@iconify-icons/ep/edit-pen";
import Refresh from "@iconify-icons/ep/refresh";
import AddFill from "@iconify-icons/ri/add-circle-line";

defineOptions({
  name: "SystemUser"
});

const treeRef = ref();
const formRef = ref();
const tableRef = ref();
const loading = ref(false);
const dataList = ref([]);

const columns: TableColumnList = [
  { label: "工号", prop: "eid" },
  { label: "邮箱", prop: "email" },
  { label: "角色", prop: "role" },
  { label: "英文名", prop: "name" },
  { label: "中文名", prop: "cnname" },
  {
    label: "状态",
    prop: "is_active",
    cellRenderer: ({ row }) => {
      if (row.is_active == 1) {
        return <el-tag type="success">活动</el-tag>;
      } else {
        return <el-tag type="danger">禁用</el-tag>;
      }
    }
  },
  { label: "手机号码", prop: "phone" },
  { label: "上次登录", prop: "last_login" },
  {
    label: "操作",
    fixed: "right",
    width: 180,
    slot: "operation"
  }
];

const getlist = () => {
  loading.value = true;
  getUserList()
    .then((res: { data: any }) => {
      dataList.value = res.data;
      loading.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
};

onMounted(() => {
  getlist();
});

const handleDelete = (row: any) => {
  console.log(row);
};

const resetForm = (formRef: any) => {
  formRef.value.resetFields();
};

const search_field = reactive({
  eid: "",
  name: "",
  is_active: "",
  status: true
});
</script>

<template>
  <div>
    <el-form
      ref="formRef"
      :inline="true"
      :model="search_field"
      class="search-form bg-bg_color"
    >
      <el-form-item label="用户EID：" prop="username">
        <el-input
          v-model="search_field.eid"
          placeholder="工号数字不含E"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="search_field.name"
          placeholder="中文或英文部分即可"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="状态" prop="cname">
        <el-select
          v-model="search_field.is_active"
          clearable
          class="!w-[180px]"
        >
          <el-option label="已开启" value="1" />
          <el-option label="已禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
        >
          搜索
        </el-button>
      </el-form-item>

      <el-form-item>
        <el-button :icon="useRenderIcon(Refresh)" @click="resetForm(formRef)">
          重置
        </el-button>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(AddFill)"
          @click="openDialog()"
        >
          新增用户
        </el-button>
      </el-form-item>
    </el-form>
    <pure-table
      ref="tableRef"
      row-key="id"
      adaptive
      :adaptiveConfig="{ offsetBottom: 108 }"
      align-whole="center"
      table-layout="auto"
      :loading="loading"
      :data="dataList"
      :columns="columns"
      :header-cell-style="{
        background: 'var(--el-fill-color-light)',
        color: 'var(--el-text-color-primary)'
      }"
    >
      <template #operation="{ row }">
        <el-button
          class="reset-margin"
          link
          type="primary"
          :icon="useRenderIcon(EditPen)"
          @click="openDialog('修改', row)"
        >
          修改
        </el-button>
        <el-popconfirm
          width="250px"
          :title="`是否确认删除用户${row.cnname}`"
          @confirm="handleDelete(row)"
        >
          <template #reference>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :icon="useRenderIcon(Delete)"
            >
              删除
            </el-button>
          </template>
        </el-popconfirm>
        <el-dropdown trigger="click">
          <el-button
            class="ml-3 mt-[2px]"
            link
            type="primary"
            :icon="useRenderIcon(More)"
          />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>
                <el-button
                  link
                  type="primary"
                  :icon="useRenderIcon(Password)"
                  @click="handleReset(row)"
                >
                  重置密码
                </el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-popconfirm
                  width="250px"
                  :title="`是否确认${row.is_active == 1 ? '禁用' : '启用'}用户${row.cnname}`"
                  @confirm="handleDelete(row)"
                >
                  <template #reference>
                    <el-button
                      link
                      type="primary"
                      :icon="useRenderIcon(Password)"
                    >
                      禁用/启用
                    </el-button></template
                  >
                </el-popconfirm>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button
                  link
                  type="primary"
                  :icon="useRenderIcon(Role)"
                  @click="handleRole(row)"
                >
                  分配角色
                </el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </pure-table>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-dropdown-menu__item i) {
  margin: 0;
}

:deep(.el-button:focus-visible) {
  outline: none;
}

.main-content {
  margin: 10px !important;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 8px;
    margin-top: 8px;
  }
}
</style>

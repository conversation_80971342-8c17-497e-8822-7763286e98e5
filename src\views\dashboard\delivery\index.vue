<script setup lang="tsx">
import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";
import { ref, reactive, onMounted, watch, computed, nextTick } from "vue";
import { plan_upload_url, packing_plan_upload_url } from "@/api/fileupload";
import { ElMessage, UploadUserFile } from "element-plus";
import { getplanlist, getpackingplanlist } from "@/api/dashboard";
const fileList = ref<UploadUserFile[]>([]);
const loading = ref(true);
const table_name = ref("plan_im");
const dataList = ref([]);
const packing_dataList = ref([]);
const start_date = ref("");
const plan_upload_time = ref("");
const include_empty = ref(false);
const packing_upload_time = ref("");
import { useRoute } from "vue-router";
import moment from "moment";

const adaptiveConfig: AdaptiveConfig = {
  offsetBottom: 12,
  fixHeader: true
};

const loadingConfig = reactive<LoadingConfig>({
  text: "正在加载生产计划数据...",
  viewBox: "-10, -10, 50, 50",
  spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
});

const filter_data = computed(() => {
  //去除dataList中每个子项前13个元素键对应的值之和为0的项
  if (include_empty.value) {
    return dataList.value;
  } else {
    return dataList.value.filter(item => {
      const dateValues = Object.keys(item)
        .filter(key => key.match(/^\d{4}-\d{2}-\d{2}$/))
        .map(key => item[key]);
      const sum = dateValues.reduce(
        (acc, val) => acc + (val ? parseInt(val, 10) : 0),
        0
      );
      return sum !== 0;
    });
  }
});

const date_arry = computed(() => {
  const arr = [];
  for (let i = 0; i < 27; i++) {
    const date = new Date(start_date.value);
    date.setDate(date.getDate() + i);
    arr.push(date.toLocaleDateString());
  }
  return arr;
});

// 假设 formatDate 函数定义如下
function formatDate(dateString, format = "YYYY-MM-DD") {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  if (format === "YYYY-MM-DD") {
    return `${year}-${month}-${day}`;
  } else if (format === "MM/DD") {
    return `${month}/${day}`;
  }
  return dateString;
}

const columns = ref([]);

watch(date_arry, (newVal, oldVal) => {
  columns.value = [
    {
      label: "注塑机台",
      prop: "machine",
      width: "120px",
      fixed: "left",
      class: "header-td",
      cellRenderer: ({ row }) => {
        return row.machine.split("\n").map((item, index) => {
          return <div key={index}>{item}</div>;
        });
      }
    },
    {
      label: "料号",
      prop: "pn",
      width: "180px",
      fixed: "left",
      class: "material"
    },
    {
      label: "描述",
      prop: "remark",
      width: "250px",
      fixed: "left"
    }
  ];
  date_arry.value.forEach(item => {
    let datevalue = new Date(item).toISOString();
    // if (!moment(datevalue).isBefore(moment().subtract(2, "days"))) {
    columns.value.push({
      label: formatDate(datevalue, "MM/DD"),
      prop: formatDate(datevalue),
      width: "80px"
    });
    // }
  });
  columns.value.push({
    label: "类型",
    prop: "type",
    width: "80px"
  });
});

interface SpanMethodProps {
  row: any;
  column: any;
  rowIndex: number;
  columnIndex: number;
}
let dynamic_rowindex = 0;
const columnSpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex
}: SpanMethodProps) => {
  if (columnIndex === 0) {
    console.log(columnIndex);
    if (rowIndex == 0) {
      if (filter_data.value[0].machine == filter_data.value[1].machine) {
        let span = 1;
        for (let i = 0; i < filter_data.value.length - 1; i++) {
          if (
            filter_data.value[i].machine == filter_data.value[i + 1].machine
          ) {
            span++;
          } else {
            break;
          }
        }
        dynamic_rowindex += span;
        return {
          rowspan: span,
          colspan: 1
        };
      }
    } else if (rowIndex > 0 && rowIndex < filter_data.value.length - 1) {
      let span = 1;
      if (
        filter_data.value[rowIndex - 1].machine !=
          filter_data.value[rowIndex].machine &&
        filter_data.value[rowIndex].machine ==
          filter_data.value[rowIndex + 1].machine
      ) {
        for (let i = rowIndex; i < filter_data.value.length - 1; i++) {
          if (
            filter_data.value[i].machine == filter_data.value[i + 1].machine
          ) {
            span++;
          } else {
            break;
          }
        }
        dynamic_rowindex += span;
        return {
          rowspan: span,
          colspan: 1
        };
      } else if (
        filter_data.value[rowIndex - 1].machine ==
          filter_data.value[rowIndex].machine &&
        filter_data.value[rowIndex].machine ==
          filter_data.value[rowIndex + 1].machine
      ) {
        return { rowspan: 0, colspan: 0 };
      } else if (
        filter_data.value[rowIndex - 1].machine ==
          filter_data.value[rowIndex].machine &&
        filter_data.value[rowIndex].machine !=
          filter_data.value[rowIndex + 1].machine
      ) {
        return { rowspan: 0, colspan: 0 };
      } else {
        return { rowspan: 1, colspan: 1 };
      }
    } else {
      return { rowspan: 1, colspan: 1 };
    }
  }
};

const packing_columns: TableColumnList = [
  {
    label: "工单",
    prop: "mo",
    width: "100px",
    sortable: true
  },
  {
    label: "料号",
    prop: "sku",
    sortable: true
  },
  {
    label: "数量",
    prop: "qty",
    width: "80px",
    sortable: true
  },
  {
    label: "交期",
    prop: "due",
    width: "110px",
    sortable: true
  },
  {
    label: "类别",
    prop: "category",
    width: "140px"
  },
  {
    label: "盖子/棉/碳棒",
    prop: "lid"
  },
  {
    label: "罐子",
    prop: "sump"
  },
  {
    label: "备注",
    prop: "remark"
  }
];
const route = useRoute();
onMounted(() => {
  if (route.fullPath == "/plan") {
    document.documentElement.classList.add("dark");
  }
  getlist();
  getpackinglist();
});

const getlist = () => {
  loading.value = true;
  getplanlist().then((res: any) => {
    dataList.value = res.data.res;
    start_date.value = res.data.start_date;
    plan_upload_time.value = res.data.upload_time;
  });
  loading.value = false;
};

const getpackinglist = () => {
  loading.value = true;
  getpackingplanlist().then((res: any) => {
    packing_dataList.value = res.data.res;
    packing_upload_time.value = res.data.upload_time;
  });
  loading.value = false;
};

const handleSuccess = (response: any) => {
  fileList.value = [];
  if (table_name.value == "plan_im") {
    getlist();
  } else {
    getpackinglist();
  }
  return ElMessage({
    duration: 20000,
    type: response.meta.status !== 201 ? "error" : "success",
    message: response.meta.msg
  });
};

const handleError = (response: any) => {
  fileList.value = [];
  return ElMessage({
    duration: 2000,
    type: "error",
    message: "系统出错，上传失败！"
  });
};

const setStyle = ({ column }) => {
  if (column.label.split("/").length > 1) {
    const date = moment(column.property);
    if (date.isValid()) {
      //当天
      if (date.isSame(moment(), "day")) {
        return {
          backgroundColor: "var(--el-color-success-light-5)",
          color: "var(--el-color-white)"
        };
        //周末
      } else if (date.day() === 0 || date.day() === 6) {
        return {
          backgroundColor: "rgba(128, 128, 128, 0.8)",
          color: "var(--el-color-white)"
        };
      }
      return {};
    }
  }
};
</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" class="search-form bg-bg_color">
      <el-form-item>
        <el-radio-group v-model="table_name">
          <el-radio value="plan_im" label="plan_im">注塑计划</el-radio>
          <el-radio v-if="false" value="plan_packing" label="plan_packing"
            >包装计划</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-upload
          ref="upload"
          :file-list="fileList"
          :on-success="handleSuccess"
          :on-error="handleError"
          :action="
            table_name == 'plan_im' ? plan_upload_url : packing_plan_upload_url
          "
          class="upload-box"
          :limit="1"
          :show-file-list="true"
          :auto-upload="true"
        >
          <template #trigger>
            <el-button type="primary">上传文件更新</el-button>
          </template>
        </el-upload></el-form-item
      >
      <el-form-item v-if="table_name == 'plan_im'">
        <el-checkbox v-model="include_empty" label="包含空计划" size="large" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="table_name == 'plan_im' ? getlist() : getpackinglist()"
        >
          刷新
        </el-button>
      </el-form-item>
      <el-form-item>
        更新时间:{{
          table_name == "plan_im" ? plan_upload_time : packing_upload_time
        }}
      </el-form-item>
      <el-form-item>
        <el-button type="primary">显示全部</el-button>
      </el-form-item>
    </el-form>

    <pure-table
      v-show="table_name == 'plan_im'"
      ref="table_im"
      border
      stripe
      adaptive
      :header-cell-style="setStyle"
      :cell-style="setStyle"
      :adaptiveConfig="adaptiveConfig"
      row-key="id"
      alignWhole="center"
      showOverflowTooltip
      :loading="loading"
      :loading-config="loadingConfig"
      :data="filter_data"
      :columns="columns"
      :span-method="columnSpanMethod"
    />
    <pure-table
      v-show="table_name == 'plan_packing'"
      ref="table_packing"
      class="table_packing"
      border
      stripe
      adaptive
      :adaptiveConfig="adaptiveConfig"
      row-key="id"
      alignWhole="center"
      showOverflowTooltip
      :loading="loading"
      :loading-config="loadingConfig"
      :data="packing_dataList"
      :columns="packing_columns"
    />
  </div>
</template>

<style lang="scss" scoped>
.search-form {
  padding: 6px 0 6px 10px;
}
.el-form {
  display: flex;
  .el-form-item {
    margin-bottom: 0;
  }
}

.upload-box {
  display: flex;
  align-items: center;
  justify-content: center;
  :deep(.el-upload-list) {
    margin: 0;
  }
}

.table_packing {
  :deep(.el-table__row) {
    background-color: transparent;
  }
}
</style>

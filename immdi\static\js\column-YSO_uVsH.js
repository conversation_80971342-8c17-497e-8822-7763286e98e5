import{b as i}from"./operation-DCG_Ggeh.js";import{h as u}from"./moment-C3TZ8gAF.js";import{n as l,a2 as s,q as d,S as c,aE as r}from"./index-BnxEuBzx.js";function _(){const a=l([]),e=l(!0),o=s({selected_date:u().format("YYYY-MM-DD")});d(()=>{});const n=()=>{e.value=!0,i(c(o)).then(t=>{if(t.meta.status!=200){r(t.meta.msg,{type:"error"}),e.value=!1;return}a.value=t.data,e.value=!1}).catch(()=>{r("系统请求数据错误！",{type:"error"}),e.value=!1})},p=s({text:"正在加载产量数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `});return{search_condition:o,search:n,loading:e,columns:[{label:"成品料号",prop:"pn",minWidth:"100"},{label:"描述",prop:"pn_des",minWidth:"160"},{label:"产量(EA)",children:[{label:"早班",prop:"shiftA_output",width:"90"},{label:"中班",prop:"shiftB_output",width:"90"},{label:"夜班",prop:"shiftC_output",width:"90"}]},{label:"汇总(EA)",prop:"total_output"},{label:"不良(EA)",prop:"defect_qty",width:"80"},{label:"良品数量(EA)",formatter(t){return(t.total_output-t.defect_qty).toLocaleString()}}],dataList:a,loadingConfig:p,adaptiveConfig:{offsetBottom:30}}}export{_ as useColumns};

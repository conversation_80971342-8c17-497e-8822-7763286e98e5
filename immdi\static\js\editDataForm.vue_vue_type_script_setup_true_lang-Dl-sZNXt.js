import{d as V,n as i,r as u,o as v,c as h,b as l,h as n,f as x,y as j}from"./index-BnxEuBzx.js";const g=V({__name:"editDataForm",props:{shiftinfo:{default:()=>({selecteddate:"",shift:"",machineid:""})},action:{default:""},formInline:{default:()=>({hourid:0,pn:"",output:0,adjustion:0})}},setup(s,{expose:m}){const d=s,r=i(),e=i(d.formInline);function f(){return r.value}function _(){return e}return m({getRef:f,getVal:_}),(y,o)=>{const a=u("el-form-item"),c=u("el-input"),p=u("el-input-number"),b=u("el-form");return v(),h("div",null,[l(b,{ref_key:"ruleFormRef",ref:r,"label-width":"80px",style:{"font-size":"24px"}},{default:n(()=>[l(a,{label:"小时号",prop:"hourid"},{default:n(()=>[x(j(e.value.hourid+":00~"+(e.value.hourid+1)+":00"),1)]),_:1}),l(a,{label:"料号",prop:"pn"},{default:n(()=>[l(c,{disabled:d.action=="edit",modelValue:e.value.pn,"onUpdate:modelValue":o[0]||(o[0]=t=>e.value.pn=t),placeholder:"料号"},null,8,["disabled","modelValue"])]),_:1}),l(a,{label:"产出",prop:"output"},{default:n(()=>[l(p,{disabled:d.action=="edit",modelValue:e.value.output,"onUpdate:modelValue":o[1]||(o[1]=t=>e.value.output=t),placeholder:"产出"},null,8,["disabled","modelValue"])]),_:1}),l(a,{label:"调整值",prop:"adjustion"},{default:n(()=>[l(p,{modelValue:e.value.adjustion,"onUpdate:modelValue":o[2]||(o[2]=t=>e.value.adjustion=t),placeholder:"调整值"},null,8,["modelValue"])]),_:1})]),_:1},512)])}}});export{g as _};

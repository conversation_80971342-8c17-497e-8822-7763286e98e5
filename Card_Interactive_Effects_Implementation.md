# 卡片交互效果实现说明

## 📋 实现概述

为 `feeForm/index.vue` 中的所有 `el-card` 组件添加了鼠标悬停和点击选中效果，提升用户体验。

## 🎯 实现的效果

### 1. **鼠标悬停效果 (Hover)**
- 🎨 **视觉变化**：
  - 卡片向上移动 2px (`transform: translateY(-2px)`)
  - 增强阴影效果 (`box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15)`)
  - 边框变为蓝色 (`border-color: #409eff`)
  - 平滑过渡动画 (`transition: all 0.3s ease`)

- 🖱️ **交互反馈**：
  - 鼠标指针变为手型 (`cursor: pointer`)
  - 明确提示用户可以点击

### 2. **点击选中效果 (Selected)**
- 🎨 **视觉变化**：
  - 蓝色边框高亮 (`border-color: #409eff`)
  - 浅蓝色背景 (`background-color: #f0f9ff`)
  - 增强阴影 (`box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3)`)
  - 头部变为蓝色背景，白色文字
  - 标签变为白色背景，蓝色文字

### 3. **状态管理**
- 📊 **选中状态跟踪**：
  - 记录选中卡片的类型 (`weight`, `volume`, `vehicle`)
  - 记录选中卡片的模板名称
  - 记录选中卡片的索引

## 🔧 技术实现

### 1. **响应式状态管理**

```typescript
// 选中状态管理
const selectedCard = ref({
  type: '', // 'weight', 'volume', 'vehicle'
  template: '',
  index: -1
});
```

### 2. **选中状态检查函数**

```typescript
// 检查卡片是否被选中
const isCardSelected = (type, template, index) => {
  return selectedCard.value.type === type && 
         selectedCard.value.template === template && 
         selectedCard.value.index === index;
};
```

### 3. **更新的点击处理函数**

```typescript
const showdata = (obj, type, index) => {
  feedata.logistic_company = obj.template;
  feedata.freight_unit_price = obj.unitprice;
  feedata.freight_fee = obj.freight_fee;
  feedata.freight_calc_method = obj.calc_method;
  
  // 更新选中状态
  selectedCard.value = {
    type: type,
    template: obj.template,
    index: index
  };
};
```

### 4. **CSS样式实现**

```scss
/* 卡片悬停效果 */
.card-hover {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #409eff;
  }
}

/* 卡片选中效果 */
.card-selected {
  border-color: #409eff !important;
  background-color: #f0f9ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  
  :deep(.el-card__header) {
    background-color: #409eff;
    color: white;
    
    .el-tag {
      background-color: white;
      color: #409eff;
      border-color: white;
    }
  }
  
  :deep(.el-card__body) {
    background-color: #f0f9ff;
  }
}
```

## 📱 应用的卡片类型

### 1. **重量计费卡片**
- 位置：重量计费区域
- 标识：`type: 'weight'`
- 计算方式：`props.rowdata.weight * item.unitprice`

### 2. **体积计费卡片**
- 位置：体积计费区域
- 标识：`type: 'volume'`
- 计算方式：`props.rowdata.volume * item.unitprice`

### 3. **车型计费卡片**
- 位置：车型计费区域
- 标识：`type: 'vehicle'`
- 特殊标识：`${templateName}-${item.truck}`
- 较小的悬停效果（适应嵌套布局）

## 🎨 视觉设计特点

### 1. **颜色方案**
- 主色调：Element Plus 蓝色 (`#409eff`)
- 背景色：浅蓝色 (`#f0f9ff`)
- 阴影：半透明黑色和蓝色

### 2. **动画效果**
- 过渡时间：0.3秒
- 缓动函数：`ease`
- 变换：垂直移动和阴影变化

### 3. **层次感**
- 悬停时：向上移动 + 增强阴影
- 选中时：边框高亮 + 背景色变化
- 嵌套卡片：较小的变换幅度

## 🔄 交互流程

### 1. **初始状态**
```
卡片显示 → 透明边框 → 基础阴影
```

### 2. **鼠标悬停**
```
鼠标进入 → 蓝色边框 → 向上移动 → 增强阴影
鼠标离开 → 恢复初始状态
```

### 3. **点击选中**
```
点击卡片 → 更新选中状态 → 应用选中样式 → 更新表单数据
```

### 4. **切换选择**
```
点击其他卡片 → 取消前一个选中 → 选中新卡片 → 更新表单数据
```

## 📊 用户体验提升

### 1. **视觉反馈**
- ✅ 明确的悬停状态
- ✅ 清晰的选中状态
- ✅ 平滑的过渡动画

### 2. **交互反馈**
- ✅ 鼠标指针变化
- ✅ 即时的视觉响应
- ✅ 状态持久化

### 3. **可用性改进**
- ✅ 更容易识别可点击元素
- ✅ 更清楚当前选中的选项
- ✅ 更好的视觉层次

## 🚀 使用方法

### 1. **开发者**
- 卡片会自动应用悬停和选中效果
- 选中状态会自动管理
- 样式会根据卡片类型自动调整

### 2. **用户**
- 鼠标悬停查看可点击的卡片
- 点击卡片进行选择
- 选中的卡片会保持高亮状态
- 表单数据会自动更新

## ✅ 完成状态

- ✅ 重量计费卡片交互效果
- ✅ 体积计费卡片交互效果  
- ✅ 车型计费卡片交互效果
- ✅ 选中状态管理
- ✅ CSS样式实现
- ✅ 响应式设计
- ✅ 平滑动画过渡

现在所有的 `el-card` 组件都具有了专业的交互效果，大大提升了用户体验！

<template>
  <dv-full-screen-container class="bg">
    <!-- <dv-loading v-if="myData.loading">Loading...</dv-loading> -->
    <div class="host">
      <div class="host-header">
        <el-row>
          <el-col :span="6" :offset="0" class="left_banner">
            <dv-decoration-10 style="width: 100%; height: 20px" />
            <div class="parent">
              <el-date-picker
                v-model="querydata.selectedDate"
                type="date"
                placeholder="选择日期"
                :clearable="false"
                style="width: 135px"
                @change="changeData"
              />
              <el-select
                v-model="querydata.shift"
                placeholder="班次"
                size="default"
                style="width: 100px"
                @change="changeData"
                ><div class="shift_content">
                  <el-option
                    class="item"
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </div>
              </el-select>
              <el-select
                v-model="querydata.machineId"
                placeholder="机台"
                style="width: 90px"
                @change="changeData"
              >
                <div class="content">
                  <el-option
                    class="item"
                    v-for="item in machineids"
                    :key="item"
                    :label="item + '号机'"
                    :value="item"
                  /></div
              ></el-select>
              <el-button @click="init_query" :icon="RefreshLeft"></el-button>
            </div>
          </el-col>
          <el-col :span="12" :offset="0">
            <div class="d-flex-row">
              <dv-decoration-8
                :color="['#568aea', '#000000'] as any"
                style="width: 30%; height: 50px; justify-self: left"
              />
              <div class="title">
                <div class="title-text">注塑EMDI系统</div>
                <dv-decoration-3
                  class="title-bottom ic"
                  :color="['#50e3c2', '#67a1e5'] as any"
                  @click="handleFullScreen"
                />
              </div>
              <dv-decoration-8
                :reverse="true"
                :color="['#568aea', '#000000'] as any"
                style="width: 30%; height: 50px"
              />
            </div>
          </el-col>
          <el-col :span="6" :offset="0" class="left_banner right_banner">
            <dv-decoration-10
              style="
                width: 100%;
                transform: rotateY(180deg);
                align-items: right;
                height: 10px;
              "
            />
            <div class="parent">
              <span style="padding: 0 10px">{{ datetime }}</span>
              <el-button :icon="HomeFilled" @click="navHome">管理页</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="host-body" id="level0">
        <prod />
      </div>
    </div>
  </dv-full-screen-container>
</template>

<script lang="ts" setup>
import screenfull from "screenfull";
import prod from "./pages/prod.vue";
import { reactive, ref, onMounted } from "vue";
import { getmachineids } from "@/api/common";
import { useProdStoreHook } from "@/store/modules/prod";
import { HomeFilled, RefreshLeft } from "@element-plus/icons-vue";
import { useRouter, useRoute } from "vue-router";
import moment from "moment";
import { getShiftByHour } from "@/views/functions/shift";

const datetime = ref(moment().format("YYYY-MM-DD HH:mm:ss"));

const refreshtime = () => {
  setInterval(() => {
    datetime.value = moment().format("YYYY-MM-DD HH:mm:ss");
  }, 1000);
};

const router = useRouter();
console.log(useRoute().params);

const css = reactive({
  titleHeight: 70,
  titelSize: "24px",
  titleTop: "1vh",
  routeTop: "60px",
  tapFont: "20px",
  safetyFont: "40px",
  sty: "-" + 250 * 1 + "px",
  leftpos: 63 * 1 * Math.pow(window.devicePixelRatio, 1 / 6) + "vw"
});

const machineids = ref([]);

const navHome = () => {
  router.push("/welcome");
};

const refreshmachineids = () => {
  getmachineids().then((res: { data: any }) => {
    machineids.value = res.data.machine_list;
    console.log(res.data.machine_list);
  });
};

const querydata = reactive({
  selectedDate: useProdStoreHook().selectedDate,
  shift: useProdStoreHook().shift,
  machineId: useProdStoreHook().machineId
});

const changeData = () => {
  useProdStoreHook().setData(querydata);
};

const init_query = () => {
  querydata.selectedDate = moment().format("YYYY-MM-DD");
  querydata.shift = getShiftByHour();
  changeData();
};

const options = [
  {
    value: "A",
    label: "早班"
  },
  {
    value: "B",
    label: "中班"
  },
  {
    value: "C",
    label: "夜班"
  }
];

onMounted(() => {
  refreshtime();
  // 置暗黑模式
  document.documentElement.classList.add("dark");
  refreshmachineids();
});

const handleFullScreen = () => {
  if (!screenfull.isEnabled) {
    alert("您的浏览器版本过低，不支持全屏浏览");
    return false;
  }
  screenfull.toggle();
};
</script>
<style lang="scss" scoped>
.bg {
  height: 100vh;
  width: 100vw;
  margin: 0rem 0rem 0 0rem;
  padding: 0.2rem 0 0 0;
  background-image: url("@/assets/frontend/pageBg.png");
  background-size: cover;
  color: #fff;
  background-position: center center;

  .host {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    .host-header {
      .left_banner {
        display: flex;
        flex-direction: column;
        .parent {
          display: flex;
          flex: 1;
          flex-direction: row;
          align-items: center;
          padding: 0 10px;
          gap: 10px;
        }
      }
      .right_banner {
        .parent {
          justify-content: flex-end;
        }
      }
    }

    .host-body {
      flex: 1;
      display: flex;
      height: 300px;
    }
  }

  .d-flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }

  .title {
    display: flex;
    top: v-bind("css.titleTop");
    flex-direction: column;
    align-items: center;
    justify-items: center;
    text-align: center;

    .title-text {
      font-size: v-bind("css.titelSize");
      cursor: pointer;
    }
  }

  .row {
    display: flex;
    flex-direction: column;
  }

  .col {
    display: flex;
    flex-direction: column;
  }

  .row2 {
    display: flex;
    transform: translateY(-250%);
    font-size: large;
  }

  .ic {
    z-index: 9999;
    cursor: pointer;
  }

  .corner {
    position: fixed;
    top: 0.5vh;
    margin: 5px 20px;

    .caption {
      display: flex;
      justify-content: space-between;

      .tap {
        margin-left: 4px;
        padding: 4px;
        transform: skew(-30deg);
        cursor: pointer;
        background-color: rgb(4, 151, 205, 0.3);
        filter: alpha(opacity=30);

        .tapin {
          position: relative;
          transform: skew(30deg);
          font-size: v-bind("css.tapFont");
          color: white;
        }
      }
    }
  }
}

.content {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 700px;
  height: 250px;
  padding-left: 15px;
  .item {
    border-radius: 5px;
    border: 1px solid #ccc;
    margin: 3px 5px;
    width: 100px;
    height: 35px;
    text-align: center;
  }
}

.shift_content {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  width: 200px;
  height: 180px;
  padding-left: 15px;
  .item {
    border-radius: 5px;
    border: 1px solid #ccc;
    margin: 10px 5px;
    width: 150px;
    height: 35px;
    text-align: center;
    font-size: 16px;
  }
}
</style>

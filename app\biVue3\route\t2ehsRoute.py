from flask import Blueprint, request
import requests
from config import config, env
from extensions import db
from app.public.model.models_public import Lineinfo
from app.dm.model.models_dm import Lineinfo as linfo
from app.dm.functions import download_img
from app.public.functions import responseError, responseGet

api = Blueprint('bi3/t2ehsAPI', __name__)

# EHS数据还是从原来的PHP文件里获取数据，待重构


@api.route('/getSuggestion', methods=['GET'])  # 获取EHS统计和分类
def getBI():
    rres = request.args
    year = rres.get('year')
    res = requests.get(
        config[env].cloud_url+"welean/bi/safetyAPI/getSummary", params={
            'plant': 'SZ',
            'year': year
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        data = content['data']
        return responseGet('成功', data)
    else:
        return responseError('失败')


@api.route('/getCross', methods=['GET'])  # 获取安全十字数据
def getCross():
    r = request.args
    stime = r.get('stime')
    etime = r.get('etime')
    linename = r.get('linename')
    wxline = ''
    if linename:
        linegroup = db.session.query(linfo).filter(linfo.linename == linename).first()
        ll = db.session.query(Lineinfo).filter(Lineinfo.linename == linegroup.linegroup).first()
        if ll:
            wxline = ll.wxname
    res = requests.get(
        config[env].cloud_url+"welean/bi/safetyAPI/getCross", params={
            'plant': 'SZ',
            'stime': stime,
            'etime': etime,
            'linename': wxline
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        data = content['data']['outArr']
        return responseGet('成功', data)
    else:
        return responseError('失败')


@api.route('/getClose', methods=['GET'])   # 获取按部门的安全问题完成率
def getClose():
    r = request.args
    year = r.get('year')
    res = requests.get(
        config[env].cloud_url+"welean/bi/safetyAPI/getClose", params={
            'plant': 'SZ',
            'year': year
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        data = content['data']['data']
        return responseGet('成功', data)
    else:
        return responseError('失败')


@api.route('/getsafetydays', methods=['GET'])  # 获取安全天数
def getsafetydays():
    res = requests.get(
        config[env].cloud_url+"welean/bi/safetyAPI/getSafetydays", params={
            'plant': 'SZ'
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        data = content['data']
        return responseGet('成功', data)
    else:
        return responseError('失败')


@api.route('/getDetails', methods=['GET'])  # 获取具体的提案信息
def getDetails():
    r = request.args
    idate = r.get('idate')
    res = requests.get(
        config[env].cloud_url+"welean/bi/safetyAPI/getlistbyday", params={
            'plant': 'SZ',
            'dt': idate
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        dts = content['data']['suggests']
        dts = downLoadPics(dts)
        return responseGet('成功', dts)
    else:
        return responseError('失败')


def downLoadPics(dts):
    imgPath = config[env].localPath+"BI/img/"
    for d in dts:
        p = d['beforepic']
        pa = d['afterpic']
        picurl = p[p.rfind('/')+1:].split('.')
        if len(picurl) == 2:
            picName = picurl[0]
            picAdx = picurl[1]
        else:
            picName = ''
            picAdx = ''
        if p:
            download_img(p, imgPath+picName+'.'+picAdx)
            d['beforepic'] = config[env].base_url+'BI/img/'+picName+'.'+picAdx
            d['picsArr'].append(d['beforepic'])
        if pa:
            download_img(pa, imgPath+picName+'after.'+picAdx)
            d['afterpic'] = config[env].base_url+'BI/img/'+picName+'after.'+picAdx
            d['picsArr'].append(d['afterpic'])
    return dts

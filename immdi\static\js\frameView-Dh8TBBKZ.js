import{d as g,B as w,n as i,Y as x,u as n,G as I,q as R,j as k,w as B,o as S,c as V,e as E,H as L,_ as P}from"./index-BnxEuBzx.js";const D=["element-loading-text"],j=["src"],q=g({name:"FrameView",__name:"frameView",props:{frameInfo:{}},setup(v){var m,u,d;const o=v,{t:p}=w(),f=i(!0),t=x(),r=i(""),l=i(null);(m=n(t.meta))!=null&&m.frameSrc&&(r.value=(u=n(t.meta))==null?void 0:u.frameSrc),((d=n(t.meta))==null?void 0:d.frameLoading)===!1&&c();function c(){f.value=!1}function h(){L(()=>{const e=n(l);if(!e)return;const a=e;a.attachEvent?a.attachEvent("onload",()=>{c()}):e.onload=()=>{c()}})}return I(()=>t.fullPath,e=>{var a,s,_;t.name==="Redirect"&&e.includes((a=o.frameInfo)==null?void 0:a.fullPath)&&(r.value=e,f.value=!0),((s=o.frameInfo)==null?void 0:s.fullPath)===e&&(r.value=(_=o.frameInfo)==null?void 0:_.frameSrc)}),R(()=>{h()}),(e,a)=>{const s=k("loading");return B((S(),V("div",{class:"frame","element-loading-text":n(p)("status.pureLoad")},[E("iframe",{ref_key:"frameRef",ref:l,src:r.value,class:"frame-iframe"},null,8,j)],8,D)),[[s,f.value]])}}}),C=P(q,[["__scopeId","data-v-c2878365"]]);export{C as default};

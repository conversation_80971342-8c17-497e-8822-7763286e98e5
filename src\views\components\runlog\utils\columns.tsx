import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";
import { ref, onMounted, reactive } from "vue";
import { watch, toRaw } from "vue";
import { getrunlog } from "@/api/runlog";
import { useProdStoreHook } from "@/store/modules/prod";
import moment from "moment";

export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);

  watch(
    [
      () => useProdStoreHook().selectedDate,
      () => useProdStoreHook().shift,
      () => useProdStoreHook().machineId
    ],
    (newvalue: any, _) => {
      query_param.selecteddate = newvalue[0];
      query_param.shift = newvalue[1];
      query_param.machine = newvalue[2];
      refreshData();
    }
  );

  const query_param = reactive({
    selecteddate: useProdStoreHook().selectedDate,
    machine: useProdStoreHook().machineId,
    shift: useProdStoreHook().shift
  });

  const refreshData = () => {
    console.log(toRaw(query_param));
    getrunlog(toRaw(query_param)).then((res: { data: any }) => {
      dataList.value = res.data;
      loading.value = false;
    });
  };

  onMounted(() => {
    refreshData();
    loading.value = false;
  });

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载不良记录数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  const adaptiveConfig: AdaptiveConfig = {
    offsetBottom: 20,
    fixHeader: true
  };
  const columns: TableColumnList = [
    {
      label: "开始时间",
      prop: "start_time",
      minWidth: 50
    },
    { label: "结束时间", prop: "end_time", minWidth: 50 },
    { label: "生产料号", prop: "pn", minWidth: 50 },
    { label: "状态", prop: "machine_state", minWidth: 30 },
    { label: "状态描述", prop: "machine_state_des", minWidth: 40 },
    {
      label: "持续时间",
      prop: "unit",
      minWidth: 40,
      formatter(row, column, cellValue, index) {
        let starttime = moment(row.start_time, "HH:mm:ss");
        let endtime = moment(row.end_time, "HH:mm:ss");
        return endtime.diff(starttime, "seconds");
      }
    }
  ];

  return {
    dataList,
    columns,
    loadingConfig,
    adaptiveConfig,
    loading,
    refreshData
  };
}

	<?php
include("coon.php");
$year=$_GET['year'];
if(!$year){
  $year=2023;
}
$nextyear=$year+1;

$sql2 ="select stype, count(stype) as qty from wl_suggest where idate between '{$year}-01-01' and '{$nextyear}-01-01' group by stype";
$query=mysqli_query($link, $sql2);
if (mysqli_num_rows($query) >= 1) {
    while ($rs = mysqli_fetch_array($query)) {
        $tp=$rs['stype'];
        if (!$tp) {
            $tp='未批准';
        }
        $output['yearly'][]=array('value'=>$rs['qty'],'name'=>$tp);
    }
}
$sql3 ="select status , count(status) as qty from wl_suggest where idate between '{$year}-01-01' and '{$nextyear}-01-01' group by status order by status desc";
$query=mysqli_query($link, $sql3);
if (mysqli_num_rows($query) >= 1) {
    while ($rs = mysqli_fetch_array($query)) {
        $tp=$rs['status'];
        if (!$tp) {
            $tp='未批准';
        }
        $output['status'][]=array('value'=>$rs['qty'],'name'=>$tp);
    }
}
print_r(json_encode($output, JSON_UNESCAPED_UNICODE));

?>
	
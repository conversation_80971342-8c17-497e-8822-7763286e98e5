from flask import Blueprint, request
from app.welean.model.models_welean import Newspaper, Userinfo, Rates
from extensions import db
import os
import hashlib
from datetime import date, datetime
from sqlalchemy import or_, desc
from app.public.functions import responseError,  responseGet, login_required, responsePost
from app.welean.functions import getServer, createdir, pyMuPDF_fitz, moveFile, moveFolder
api = Blueprint('welean/V309/newsAPI', __name__)


@api.route('/getCourseslist', methods=['GET'])
@login_required
def getCourseslist():
    res = request.args
    plant = res.get('plant')
    keywords = res.get('keywords')
    nb = int(res.get('nb'))
    dataList = []
    news = db.session.query(Newspaper.Id, Newspaper.subcate, Newspaper.reservedate, Newspaper.postdate, Newspaper.piccover, Newspaper.title, Userinfo.dept1, Userinfo.cname
                            ).join(Userinfo, Userinfo.eid == Newspaper.eid).filter(
        or_(Newspaper.plant == plant, Newspaper.plant == 'ALL')).filter(Newspaper.cate == 'training').order_by(desc(Newspaper.reservedate))
    if keywords:
        news = news.filter(Newspaper.title.like('%{0}%'.format(keywords)))
    news = news.limit(nb).all()
    for n in news:
        coverpath = getServer()['newsPath']+str(n.Id)+'/'+n.piccover
        if os.path.exists(coverpath):
            newscover = getServer()['newsUrl']+str(n.Id)+'/'+n.piccover
        else:
            newscover = getServer()['baseUrl']+'nophoto.png'
        dic = {
            'id': n.Id,
            'name': n.title,
            'subcate': n.subcate,
            'updatedate': datetime.strftime(n.postdate, "%Y-%m-%d"),
            'src': newscover,
        }
        dataList.append(dic)
    return responseGet("获取列表成功", {'contents': dataList})


@api.route('/getLikeContents', methods=['GET'])
@login_required
def getLikeContents():
    res = request.args
    eid = res.get('eid')
    nb = int(res.get('nb'))
    keywords = res.get('keywords')
    dataList = []
    news = db.session.query(Newspaper.Id, Newspaper.subcate, Newspaper.reservedate, Newspaper.postdate, Newspaper.piccover, Newspaper.title).join(Rates, Rates.sid == Newspaper.Id).filter(
        Rates.eid == eid).filter(Newspaper.cate == 'news').filter(Rates.cate == 'news').filter(Rates.score == 5)
    if keywords:
        news = news.filter(Newspaper.title.like('%{0}%'.format(keywords)))
    news = news.order_by(desc(Newspaper.reservedate)).limit(nb).all()
    for n in news:
        coverpath = getServer()['newsPath']+str(n.Id)+'/'+n.piccover
        if os.path.exists(coverpath):
            newscover = getServer()['newsUrl']+str(n.Id)+'/'+n.piccover
        else:
            newscover = getServer()['baseUrl']+'nophoto.png'
        dic = {
            'id': n.Id,
            'icon': 'heart-fill',
            'cate': 'news',
            'name': n.title,
            'subcate': n.subcate,
            'updatedate': datetime.strftime(n.postdate, "%Y-%m-%d"),
            'src': newscover,
        }
        dataList.append(dic)
    return responseGet("获取列表成功", {'contents': dataList})


@api.route('/getContents', methods=['GET'])
@login_required
def getContents():
    res = request.args
    plant = res.get('plant')
    keywords = res.get('keywords')
    posttype = res.get('posttype')
    ttype = res.get('ttype')
    nb = int(res.get('nb'))
    dataList = []
    news = db.session.query(Newspaper.Id, Newspaper.plant, Newspaper.subcate,
                            Newspaper.postdate, Newspaper.piccover, Newspaper.title)
    if posttype == 'news':
        news = news.filter(Newspaper.plant == plant).filter(Newspaper.cate == 'news')
    elif posttype == 'training':
        news = news.filter(Newspaper.plant == plant).filter(Newspaper.cate == 'training')
        if ttype:
            news = news.filter(Newspaper.subcate == ttype)
    else:
        news = news.filter(or_(Newspaper.plant == plant, Newspaper.plant == 'ALL')
                           ).filter(Newspaper.cate == 'news')
    if keywords:
        news = news.filter(Newspaper.title.like('%{0}%'.format(keywords)))
    news = news.order_by(desc(Newspaper.reservedate)).limit(nb).all()
    for n in news:
        coverpath = getServer()['newsPath']+str(n.Id)+'/'+n.piccover
        if os.path.exists(coverpath):
            newscover = getServer()['newsUrl']+str(n.Id)+'/'+n.piccover
        else:
            newscover = getServer()['baseUrl']+'nophoto.png'
        dic = {
            'id': n.Id,
            'name': n.title,
            'subcate': n.subcate,
            'updatedate': datetime.strftime(n.postdate, "%Y-%m-%d"),
            'src': newscover,
            'plant': n.plant
        }
        dataList.append(dic)
    return responseGet("获取列表成功", {'contents': dataList})


@ api.route('/getNewsByid', methods=['GET'])
@ login_required
def getNewsByid():
    res = request.args
    id = res.get('id')
    news = db.session.query(Newspaper.Id, Newspaper.videourl, Newspaper.subcate, Newspaper.content, Newspaper.title, Newspaper.postdate, Userinfo.dept1, Userinfo.cname).outerjoin(
        Userinfo, Newspaper.eid == Userinfo.eid).filter(Newspaper.Id == id).first()
    if news:
        picsLocation = getServer()['newsPath']+str(news.Id)+'/pics/'
        directory = os.listdir(picsLocation)
        if directory:
            directory.sort()
        pics = []
        contentDic = {
            'head': []
        }
        if news.content:
            contentArr = news.content.split('$p')
            for c in contentArr:
                index = c[:2]
                content = c
                try:
                    k = str(int(index)-1)
                    content = c[2:]
                    if k == '-1':
                        contentDic['head'].append(content)
                    elif k in contentDic.keys():
                        contentDic[k].append(content)
                    else:
                        contentDic[k] = [content]
                except Exception:
                    contentDic['head'].append(content)
        for dir in directory:
            pics.append(getServer()['newsUrl']+str(news.Id)+'/pics/'+dir)
        h5url = ''
        if news.videourl:
            # r = news.videourl
            # wxurl = r[r.rfind('/')+2:r.rfind('.')]
            h5url = 'https://v.qq.com/iframe/player.html?vid='+news.videourl
        newspaper = {
            'cate': news.subcate,
            'title': news.title,
            'postdate': datetime.strftime(news.postdate, "%Y-%m-%d"),
            'author': news.dept1+'/'+news.cname,
            'pics': pics,
            'videourl': news.videourl,
            'h5url': h5url,
            'content': contentDic
        }
        print(newspaper)
        return responseGet("获取列表成功", {'newspaper': newspaper})
    return responseError('未找到该新闻')


@ api.route('/getNewsById', methods=['GET'])
@ login_required
def getNewsById():
    res = request.args
    id = res.get('id')
    news = db.session.query(Newspaper).filter(Newspaper.Id == id).first()
    picsLocation = getServer()['newsPath']+str(news.Id)+'/pics/'
    directory = os.listdir(picsLocation)
    pics = []
    if directory:
        directory.sort()
    for dir in directory:
        pics.append(getServer()['newsUrl']+str(news.Id)+'/pics/'+dir)
    if news:
        newspaper = {
            'currentId': news.Id,
            'cate': news.cate,
            'subcate': news.subcate,
            'title': news.title,
            'reservedate': datetime.strftime(news.reservedate, "%Y-%m-%d"),
            'postdate': datetime.strftime(news.postdate, "%Y-%m-%d"),
            'plant': news.plant,
            'videourl': news.videourl,
            'currenttask': news.currenttask,
            'piccover': getServer()['newsUrl']+str(news.Id)+'/'+news.piccover,
            'content': news.content,
            'pics': pics,
            'newpics': []
        }
        return responseGet("获取列表成功", {'newspaper': newspaper})
    return responseError('未找到该新闻')


@ api.route('/uploadNewspic', methods=['POST'])
@ login_required
def uploadNewspic():
    file_obj = request.files.get('file')
    mystr = ('newsfolder' + str(datetime.now())).encode('UTF-8')
    pdfname = '/'+hashlib.md5(mystr).hexdigest()[8:-8]
    tpath = '000'+pdfname
    tempFolder = getServer()['newsPath']+tpath
    print(tempFolder)
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        if appendix.lower() != '.pdf':
            return responseError("上传文件失败，请联系管理员")
        try:
            md = createdir(tempFolder+'/pics')
            if md:
                # apath=os.path.abspath(tempFolder)
                file_obj.save(tempFolder+pdfname+appendix)
                count = pyMuPDF_fitz(tempFolder+pdfname+appendix, tempFolder+'/pics')
                return responsePost("上传成功", {'newurl': tempFolder, 'url': getServer()['newsUrl']+tpath+'/pics/', 'count': count})
        except Exception as e:
            print(e)
    return responseError("上传文件失败，请联系管理员")


@ api.route('/uploadNewsCover', methods=['POST'])
@ login_required
def uploadNewsCover():
    file_obj = request.files.get('file')
    mystr = ('newsCover' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        try:
            file_obj.save(getServer()['newsPath']+'000/'+name+appendix)
            return responsePost("上传成功", {'coverurl': getServer()['newsPath']+'000/'+name+appendix})
        except Exception as e:
            print(e)
    return responseError("上传文件失败，请联系管理员")


@ api.route('/newNews', methods=['POST'])
@ login_required
def newNews():
    res = request.json
    Id = int(res.get('currentId')) if res.get('currentId') else 0
    currenttask = int(res.get('currenttask')) if res.get('currenttask') else 0
    cate = res.get('cate')
    subcate = res.get('subcate')
    title = res.get('title')
    postdate = datetime.strftime(date.today(), "%Y-%m-%d")
    reservedate = res.get('reservedate')
    eid = res.get('eid')
    plant = res.get('plant')
    coverurl = res.get('coverurl')
    newurl = res.get('newurl')
    videourl = res.get('videourl')
    content = res.get('content')

    if Id > 0:
        try:
            if coverurl:
                db.session.query(Newspaper).filter(Newspaper.Id == Id).update({
                    'cate': cate,
                    'subcate': subcate,
                    'title': title,
                    'reservedate': reservedate,
                    'eid': eid,
                    'postdate': postdate,
                    'plant': plant,
                    'piccover': coverurl[coverurl.rfind('/')+1:],
                    'currenttask': currenttask,
                    'videourl': videourl,
                    'content': content
                })
            else:
                db.session.query(Newspaper).filter(Newspaper.Id == Id).update({
                    'cate': cate,
                    'subcate': subcate,
                    'title': title,
                    'reservedate': reservedate,
                    'eid': eid,
                    'postdate': postdate,
                    'plant': plant,
                    'currenttask': currenttask,
                    'videourl': videourl,
                    'content': content
                })
            db.session.commit()
            if coverurl:
                print(coverurl[:coverurl.rfind('/')+1], getServer()['newsPath']+str(Id)+'/',
                      coverurl[coverurl.rfind('/')+1:])
                moveFile(coverurl[:coverurl.rfind('/')+1], getServer()['newsPath']+str(Id)+'/',
                         coverurl[coverurl.rfind('/')+1:])
            if newurl:
                moveFolder(newurl+'/pics/', getServer()['newsPath']+str(Id),
                           getServer()['newsPath']+str(Id)+'/pics/', newurl)
            return responsePost("更新成功")
        except Exception:
            db.session.rollback()
    else:
        print('add')
        try:
            newspaper = Newspaper(cate=cate,
                                  subcate=subcate,
                                  title=title,
                                  reservedate=reservedate,
                                  eid=eid,
                                  postdate=postdate,
                                  plant=plant,
                                  piccover=coverurl[coverurl.rfind('/')+1:],
                                  currenttask=currenttask,
                                  videourl=videourl,
                                  content=content)
            db.session.add(newspaper)
            db.session.flush()
            nid = newspaper.Id
            db.session.commit()
            if coverurl:
                createdir(getServer()['newsPath']+str(nid)+'/pics')
                moveFile(coverurl[:coverurl.rfind('/')+1], getServer()['newsPath']+str(nid)+'/',
                         coverurl[coverurl.rfind('/')+1:])
            if newurl:
                moveFolder(newurl+'/pics/', getServer()['newsPath']+str(nid),
                           getServer()['newsPath']+str(nid)+'/pics/', newurl)
            return responsePost("新建成功")
        except Exception:
            db.session.rollback()
    return responseError("绑定问题失败")

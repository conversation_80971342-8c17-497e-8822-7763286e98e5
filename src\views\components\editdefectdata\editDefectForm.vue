<script setup lang="ts">
import { ref, onMounted } from "vue";
import { FormProps } from "./types";
import { getdefecttype } from "@/api/front";
const props = withDefaults(defineProps<FormProps>(), {
  shiftinfo: () => ({
    selecteddate: "",
    shift: "",
    machineid: ""
  }),
  action: "",
  formInline: () => ({
    hourid: 0,
    pn: "",
    output: 0,
    defect_type: 0,
    unit: "KG",
    defect_qty: 0
  })
});

const defecttype = ref([]);
const init_defecttype = () => {
  getdefecttype().then((res: { meta: any; data: any }) => {
    if (res.meta.status == 200) {
      defecttype.value = res.data;
    }
  });
};

onMounted(() => {
  // 初始化不良分类
  init_defecttype();
});

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);
function getRef() {
  return ruleFormRef.value;
}

function getVal() {
  return newFormInline;
}

defineExpose({ getRef, getVal });
</script>

<template>
  <div>
    <el-form ref="ruleFormRef" label-width="80px" style="font-size: 24px">
      <el-form-item label="小时号" prop="hourid">
        {{ newFormInline.hourid + ":00~" + (newFormInline.hourid + 1) + ":00" }}
      </el-form-item>
      <el-form-item label="料号" prop="pn">
        <el-input
          :disabled="true"
          v-model="newFormInline.pn"
          placeholder="料号"
          class="element"
        />
      </el-form-item>
      <el-form-item label="产出" prop="output">
        <el-input-number
          :disabled="true"
          v-model="newFormInline.output"
          placeholder="产出"
        />&nbsp;EA
      </el-form-item>
      <el-form-item label="不良类型"
        ><el-select
          placeholder="选择不良类型"
          v-model="newFormInline.defect_type"
          class="element"
          ><div class="content">
            <el-option
              class="item"
              v-for="item in defecttype"
              :key="item.id"
              :label="item.des"
              :value="item.id"
            /></div
        ></el-select>
      </el-form-item>
      <el-form-item label="单位" prop="unit">
        <el-radio-group v-model="newFormInline.unit" class="element">
          <el-radio value="EA">计数(EA)</el-radio>
          <el-radio value="KG">计重(KG)</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="不良数量" prop="defect_qty">
        <el-input-number
          v-model="newFormInline.defect_qty"
          :placeholder="newFormInline.unit == 'EA' ? '不良数量' : '不良重量'"
          :min="0"
          :max="newFormInline.unit == 'EA' ? newFormInline.output : 99999"
          :precision="newFormInline.unit == 'EA' ? 0 : 3"
          :step="newFormInline.unit == 'EA' ? 1 : 0.1"
        />&nbsp;{{ newFormInline.unit == "EA" ? "EA" : "KG" }}
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.element {
  width: 80%;
}

.content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  width: 450px;
  height: 200px;
  .item {
    margin: 0 10px 5px 0;
  }
}
</style>

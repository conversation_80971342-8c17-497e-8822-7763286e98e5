<script setup lang="ts">
import { onMounted, ref, computed, watch, toRaw } from "vue";
import { useDark, useECharts } from "@pureadmin/utils";
import { get_cycle_by_shift } from "@/api/cycle";
import { getShiftName } from "@/views/functions/shift";

const { isDark } = useDark();

const theme = computed(() => (isDark.value ? "dark" : "light"));

const props = defineProps({ query_data: { type: Object } });
const cycle_data = ref([]);

onMounted(() => {
  get_cycle_by_shift({
    machine_id: props.query_data.machine_id,
    shift_date: props.query_data.shift_date,
    shift: props.query_data.shift
  }).then((res: any) => {
    cycle_data.value = res.data;
  });
});

const chartRef = ref();
const { echarts, setOptions } = useECharts(chartRef, {
  theme,
  renderer: "svg"
});

watch(cycle_data, (newVal: any) => {
  setOptions({
    container: ".line-card",
    tooltip: {
      trigger: "axis",
      position: function (pt) {
        return [pt[0], "5%"];
      }
    },
    title: {
      left: "center",
      text: props.query_data.shift_date + getShiftName(props.query_data.shift)
    },
    xAxis: {
      type: "time",
      show: true,
      min: newVal.shift_start_time,
      max: newVal.shift_end_time,
      axisTick: {
        show: true // 显示刻度
      },
      axisLabel: {
        formatter: function (value) {
          // 自定义时间格式
          var date = new Date(value);
          return echarts.format.formatTime("yyyy-MM-dd HH:mm:ss", date);
        }
      }
    },
    grid: {
      top: "15px",
      bottom: 0,
      left: 0,
      right: 0
    },
    yAxis: {
      show: false,
      type: "value",
      min: 0.5,
      max: 2.2
    },
    dataZoom: [
      {
        type: "inside",
        show: false,
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100
      }
    ],
    series: [
      {
        data: toRaw(newVal.cycle),
        type: "line",
        symbol: "none",
        smooth: true,
        color: "#41b6ff",
        lineStyle: {
          shadowOffsetY: 3,
          shadowBlur: 7,
          shadowColor: "#41b6ff"
        }
      }
    ]
  });
});
</script>

<template>
  <div ref="chartRef" style="width: 800px; height: 200px" />
</template>

<style lang="scss" scoped></style>

from flask import Blueprint, request, render_template_string, Response
import struct
import base64
import pymysql

api = Blueprint('client/tunnel', __name__)

# 定义版本号
version_num = 206

# 设置允许测试菜单
allow_test_menu = True

# 检查 pymysql 是否可用
use_pymysql = True

# 定义一些辅助函数
def phpversion_int():
    version = "5.6.0"  # 假设 PHP 版本为 5.6.0
    parts = version.split('.')
    return int(parts[0]) * 10000 + int(parts[1]) * 100 + int(parts[2])

def get_long_binary(num):
    return struct.pack('!I', num)

def get_short_binary(num):
    return struct.pack('!H', num)

def get_dummy(count):
    return b'\x00' * count

def get_block(val):
    length = len(val)
    if length < 254:
        return bytes([length]) + val.encode('utf-8')
    else:
        return b'\xFE' + get_long_binary(length) + val.encode('utf-8')
    
def echo_header(errno):
    global version_num
    response = get_long_binary(1111)
    response += get_short_binary(version_num)
    response += get_long_binary(errno)
    response += get_dummy(6)
    return response

def echo_conn_info(conn):
    response = get_block(conn.host_info)
    response += get_block(str(conn.protocol_version))
    response += get_block(conn.get_server_info())
    return response

def echo_result_set_header(errno, affect_rows, insert_id, num_fields, num_rows):
    print("+++++++",insert_id)
    print("影响的行",affect_rows)
    response = get_long_binary(errno)
    response += get_long_binary(affect_rows)
    response += get_long_binary(insert_id)
    response += get_long_binary(num_fields)
    response += get_long_binary(num_rows)
    response += get_dummy(12)
    return response

def echo_fields_header(cursor, num_fields):
    response = b''
    for i in range(num_fields):
        field = cursor.description[i]
        response += get_block(field[0])  # name
        response += get_block(field[0])  # table (assuming same as name)
        response += get_long_binary(field[1])  # type
        response += get_long_binary(field[5])  # flags
        response += get_long_binary(field[3])  # length
    return response

def echo_data(cursor, num_fields, num_rows):
    response = b''
    for i in range(num_rows):
        row = cursor.fetchone()
        for j in range(num_fields):
            if row[j] is None:
                response += b'\xFF'
            else:
                response += get_block(str(row[j]))
    return response

@api.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        if 'encodeBase64' in request.form and request.form['encodeBase64'] == '1':
            queries = [base64.b64decode(q).decode('utf-8') for q in request.form.getlist('q')]
        else:
            queries = request.form.getlist('q[]')
            print(request.form)
            print(queries)
        host = request.form['host']
        port = int(request.form['port'])
        user = request.form['login']
        password = request.form['password']
        db = request.form['db']
        action = request.form['actn']

        try:
            conn = pymysql.connect(host=host, port=port, user=user, password=password, db=db, charset='utf8')
            cursor = conn.cursor()

            if action == 'C':
                response = echo_header(0)
                response += echo_conn_info(conn)
                return response

            elif action == 'Q':
                response = b''
                for query in queries:
                    cursor.execute(query)
                    errno = cursor.connection.errno
                    affect_rows = cursor.rowcount
                    insert_id = cursor.lastrowid
                    num_fields = len(cursor.description) if cursor.description else 0
                    num_rows = cursor.rowcount
                    print("查询结果参数",errno, affect_rows, insert_id, num_fields, num_rows)
                    if insert_id is None:
                        insert_id = 0
                    response += echo_result_set_header(errno, affect_rows, insert_id, num_fields, num_rows)
                    if errno > 0:
                        response += get_block(cursor.connection.error())
                    else:
                        if num_fields > 0:
                            response += echo_fields_header(cursor, num_fields)
                            response += echo_data(cursor, num_fields, num_rows)
                        else:
                            response += get_block("")

                    if query != queries[-1]:
                        response += b'\x01'
                    else:
                        response += b'\x00'
                return response

        except pymysql.MySQLError as e:
            response = echo_header(e.args[0])
            response += get_block(e.args[1])
            return response

        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    else:
        test_menu = allow_test_menu
        if not test_menu:
            return echo_header(202) + get_block("invalid parameters")

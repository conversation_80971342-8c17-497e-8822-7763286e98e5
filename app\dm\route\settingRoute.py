from flask import Blueprint, request, send_from_directory
from app.dm.model.models_dm import Ctqinfo, Ctqtemplate, Issuelog, Probleminfo, Scaninfo, Shiftinfo, User, \
    Permission, Role, Lineinfo, Kpi, Restinfo, Skuinfo, Routing, Flexinfo, Scansub, Probleminfooee, Ctqrecord
from extensions import db
from sqlalchemy import func, or_, desc, and_
from openpyxl import load_workbook
import os
import json
import requests
import datetime
import traceback
import hashlib
from config import config, env
from app.public.functions import responseError, responsePost, responseGet, responsePut
from app.dm.functions import login_required, getLinelist, getLinegroup, getServer, mgroups,oeeArr,faiArr,ctqOptions
from app.dm.schemas import skus_schema, ctqs_schema, lines_schema
from app.hro.model.models_hro import Material, WorkflowSetting, Workflow
api = Blueprint('dm/settingAPI', __name__)

# 目前有两种ctq选项，子序列好和数据搜集，验证的字段说明如下

@api.route('/getShiftsetting', methods=['GET'])   # 根据产线获取问题类型列表
def getShiftsetting():
    res=request.args
    mgroup=res.get('mgroup')
    skus = db.session.query(Skuinfo).filter(
        Skuinfo.mgroup == mgroup).order_by(Skuinfo.sku).all()
    outArr = []
    for s in skus:
        outArr.append({
            'label': s.sku,
            'value': s.sku
        })
    return responseGet('成功', {'skus': outArr})


@ api.route("/getHROsku", methods=["get"])  # 获取CTQ列表
# @ login_required
def getHROsku():
    res = request.args
    sn = res.get('sn')
    # 去除sn头部的0
    sn = sn.lstrip('0')
    verifysku=res.get('sku')
    result=db.session.query(Material.SKU,Material.SKUDes).join(WorkflowSetting,WorkflowSetting.WFS_SKU==Material.SKU).join(
        Workflow,Workflow.WF_WONO==WorkflowSetting.WFS_WONO).filter(Workflow.WF_SerialNO==sn).first()
    if not result:
        return responseError('没有找到该序列号的记录')
    sku=result.SKU
    if sku!=verifysku:
        return responseError('序列号和SKU不匹配')
    return responseGet('获取成功', {'sku': sku,'des':result.SKUDes})    

@ api.route('/submitCTQresult', methods=['POST'])
def submitCTQresult():
    res = request.json
    sku = res.get('sku')
    sn = res.get('sn')
    actual1 = res.get('actual1')
    actual2 = res.get('actual2')
    ctqid = res.get('ctqid')
    status = res.get('status')
    recordtime = datetime.datetime.now()
    try:
        newRecord = Ctqrecord(sn=sn, ctqid=ctqid, actual1=actual1, sku=sku,
                              actual2=actual2, status=status, recordtime=recordtime)
        db.session.add(newRecord)
        db.session.commit()
        return responsePost('成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('出错了，请联系管理员')


@ api.route('/uploadSKU', methods=['POST'])
@ login_required
def uploadSKU():
    file_obj = request.files.get('file')
    tp = request.headers["tp"]
    mystr = ('SKUUPLOAD' + str(datetime.datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    appendix = file_obj.filename[file_obj.filename.rfind('.'):]
    file_obj.save(getServer()['uploadPath']+name+appendix)
    if tp == 'sku':
        filetoordersku(getServer()['uploadPath']+name+appendix)
    elif tp == 'flex':
        filetoorderflex(getServer()['uploadPath']+name+appendix)
    elif tp == 'ctq':
        filetoorderctq(getServer()['uploadPath']+name+appendix)
    return responsePost("更新成功", {
        'upload_url': getServer()['uploadUrl']+name+appendix})


def filetoorderflex(file):
    wb = load_workbook(file)
    revision = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d")
    ws = wb.active
    if ws.cell(1, 1).value != 'sku' or ws.cell(1, 2).value != 'linename' or ws.cell(1, 3).value != 'headcount' \
            or ws.cell(1, 4).value != 'hourlyrate':
        return responseError('上传格式不正确')
    flexArr = []
    try:
        for r in range(2, ws.max_row + 1):
            sku = ws.cell(r, 1).value if ws.cell(r, 1).value else ''
            linename = ws.cell(r, 2).value if ws.cell(r, 2).value else ''
            headcount = ws.cell(r, 3).value if ws.cell(r, 3).value else 0
            hourlyrate = ws.cell(r, 4).value if ws.cell(r, 4).value else 0
            item = Flexinfo(sku=sku, linename=linename, headcount=headcount,
                            hourlyrate=hourlyrate, revision=revision)
            flexArr.append(item)
        db.session.add_all(flexArr)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('出错了，请联系管理员')
    wb.close()


def filetoorderctq(file):
    wb = load_workbook(file)
    ws = wb.active
    if ws.cell(1, 1).value != 'sku' or ws.cell(1, 2).value != 'skuctqid' or ws.cell(1, 3).value != 'ctqname' \
            or ws.cell(1, 4).value != 'ctqtype':
        return responseError('上传格式不正确')
    try:
        for r in range(2, ws.max_row + 1):
            sku = ws.cell(r, 1).value if ws.cell(r, 1).value else ''
            skuctqid = ws.cell(r, 2).value if ws.cell(r, 2).value else ''
            ctqname = ws.cell(r, 3).value if ws.cell(r, 3).value else ''
            ctqtype = ws.cell(r, 4).value if ws.cell(r, 4).value else ''
            va1 = ws.cell(r, 5).value if ws.cell(r, 5).value else ''
            va2 = ws.cell(r, 6).value if ws.cell(r, 6).value else ''
            va3 = ws.cell(r, 7).value if ws.cell(r, 7).value else ''
            if sku and skuctqid:
                item = db.session.query(Ctqinfo).filter(
                    Ctqinfo.sku == sku, Ctqinfo.skuctqid == skuctqid).first()
                if item:
                    item.ctqname = ctqname
                    item.ctqtype = ctqtype
                    item.va1 = va1
                    item.va2 = va2
                    item.va3 = va3
                else:
                    newitem = Ctqinfo(sku=sku, skuctqid=skuctqid, ctqname=ctqname,
                                      ctqtype=ctqtype, va1=va1, va2=va2, va3=va3)
                    db.session.add(newitem)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('出错了，请联系管理员')
    wb.close()


def filetoordersku(file):
    wb = load_workbook(file)
    td = datetime.datetime.strftime(
        datetime.datetime.now(), "%Y-%m-%d %H:%M:%S")
    ws = wb.active
    if ws.cell(1, 1).value != 'mgroup' or ws.cell(1, 2).value != 'sku' or ws.cell(1, 3).value != 'des' \
            or ws.cell(1, 4).value != 'lenverify' or ws.cell(1, 5).value != 'snverify':
        return responseError('上传格式不正确')
    routingArr = []
    try:
        for r in range(2, ws.max_row + 1):
            currSKU = ws.cell(r, 2).value
            if currSKU:
                if ws.cell(r, 8).value:
                    routingArr.append(Routing(sku=currSKU, routing=ws.cell(
                        r, 8).value, revision=td))
                currItem = db.session.query(Skuinfo).filter(
                    Skuinfo.sku == currSKU).first()
                mgroup = ws.cell(r, 1).value if ws.cell(r, 1).value else ''
                des = ws.cell(r, 3).value if ws.cell(r, 3).value else ''
                lenverify = ws.cell(r, 4).value if ws.cell(r, 4).value else ''
                snverify = ws.cell(r, 5).value if ws.cell(r, 5).value else ''
                typegroup = ws.cell(r, 6).value if ws.cell(r, 6).value else ''
                color = ws.cell(r, 7).value if ws.cell(r, 7).value else ''
                code69 = ws.cell(r, 9).value if ws.cell(r, 9).value else ''
                code69sn = ws.cell(r, 10).value if ws.cell(r, 10).value else ''
                if currItem:
                    currItem.mgroup = mgroup
                    currItem.des = des
                    currItem.lenverify = lenverify
                    currItem.snverify = snverify
                    currItem.typegroup = typegroup
                    currItem.color = color
                    currItem.code69 = code69
                    currItem.code69sn = code69sn
                else:
                    newItem = Skuinfo(sku=currSKU, mgroup=mgroup, des=des, lenverify=lenverify,
                                      snverify=snverify, typegroup=typegroup, color=color, code69=code69, code69sn= code69sn)
                    db.session.add(newItem)
        print(1111, routingArr)
        db.session.add_all(routingArr)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('出错了，请联系管理员')
    wb.close()


@ api.route("/getLinelistpure", methods=["get"])  # 获取工厂的产线清单，班次选项和物料清单
@login_required
def getlistoure():
    res = request.args
    plant = res.get('plant')
    lineList = getLinelist(plant)
    return responseGet('成功', {'lineList': lineList})


@api.route('/downloadFileissue', methods=['GET'])
@login_required
def downloadFileissue():
    res = request.args
    query = json.loads(res.get('query'))
    sd = query['startdate']
    ed = query['enddate']
    area = query['area'] if 'area' in query.keys() else ''
    path = getServer()['templatePath']
    filename = res.get('url')
    res = requests.get(
        config[env].cloud_url+"welean/bi/issueAPI/getMDIIssues", params={
            'area': area,
            'plant': 'SZ',
            'startdate': sd,
            'enddate': ed
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        for item in content['data']['suggests']:
            if item['auditid'] > 0:
                info = db.session.query(Issuelog).filter(
                    Issuelog.Id == item['auditid']).first()
                if info:
                    item['sqdctype'] = info.sqdctype
                    item['problemtype'] = info.problemtype
                    item['qty'] = info.qty
                    item['issuemin'] = info.issuemin
        newFilename = funnelExcelissue(
            path+'/suggestrecord/', path+filename, content['data']['suggests'], sd, ed)
        if os.path.isfile(os.path.join(path+'/suggestrecord/', newFilename)):
            response = send_from_directory(
                path+'/suggestrecord/', newFilename, as_attachment=True)
            response.headers["Access-Control-Expose-Headers"] = "fname"
            response.headers['fname'] = newFilename
            return response
    return responseError('没有找到下载文件')


def funnelExcelissue(path, file, arr, sd, ed):
    wb = load_workbook(file)
    ws = wb['Suggest']
    ws.cell(1, 1).value = sd+'(含)到'+ed+'(不含)的MDI问题清单'
    for i in range(len(arr)):
        ws.cell(i+3, 1).value = arr[i]['Id']
        ws.cell(i+3, 2).value = arr[i]['scname']
        ws.cell(i+3, 3).value = arr[i]['sdept']
        ws.cell(i+3, 4).value = arr[i]['idate']
        ws.cell(i+3, 5).value = arr[i]['dcname']
        ws.cell(i+3, 6).value = arr[i]['ddept']
        ws.cell(i+3, 7).value = arr[i]['content']
        ws.cell(i+3, 8).value = arr[i]['status']
        ws.cell(i+3, 9).value = arr[i]['duedate']
        ws.cell(i+3, 10).value = arr[i]['cfdate']
        ws.cell(i+3, 11).value = arr[i]['acdate']
        ws.cell(i+3, 12).value = arr[i]['stype']
        ws.cell(i+3, 13).value = arr[i]['type2']
        ws.cell(i+3, 14).value = arr[i]['fifi']
        ws.cell(i+3, 15).value = arr[i]['comments']
        ws.cell(i+3, 16).value = arr[i]['plant']
        ws.cell(i+3, 17).value = arr[i]['area']
        ws.cell(i+3, 18).value = arr[i]['linename']
        ws.cell(i+3, 19).value = arr[i]['mdi']
        ws.cell(i+3, 20).value = arr[i]['sqdctype']
        ws.cell(i+3, 21).value = arr[i]['problemtype']
        ws.cell(i+3, 22).value = arr[i]['qty']
        ws.cell(i+3, 23).value = arr[i]['issuemin']
    newFilename = datetime.datetime.strftime(
        datetime.date.today(), '%Y-%m-%d')+' - '+'Suggest.xlsx'
    wb.save(path+newFilename)
    wb.close()
    return newFilename


@api.route('/downloadFile', methods=['GET'])
@login_required
def downloadFile():
    res = request.args
    query = json.loads(res.get('query'))
    sd = query['startdate']
    ed = query['enddate']
    plant = query['plant']
    area = query['area'] if 'area' in query.keys() else ''
    group = query['group'] if 'group' in query.keys() else ''
    line = query['line'] if 'line' in query.keys() else ''
    path = getServer()['templatePath']
    filename = res.get('url')
    datas = db.session.query(Lineinfo.linegroup, Lineinfo.VSM, Lineinfo.plant, Lineinfo.area, func.sum(Scaninfo.scanqty).label('scanqty')).outerjoin(
        Shiftinfo, Lineinfo.linename == Shiftinfo.linename).outerjoin(Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).filter(
        Lineinfo.isactive == 1).filter(and_(Shiftinfo.starttime >= sd, Shiftinfo.starttime < ed)).filter(Lineinfo.plant == plant).group_by(Lineinfo.linegroup)
    if line:
        datas = datas.filter(Lineinfo.linename == line)
    elif group:
        datas = datas.filter(Lineinfo.linegroup == group)
    elif area:
        datas = datas.filter(Lineinfo.area == area)
    datas = datas.all()
    dic = {}
    for d in datas:
        dic[d.linegroup] = {
            'linegroup': d.linegroup,
            'vsm': d.VSM,
            'area': d.area,
            'plant': d.plant,
            'scanqty': d.scanqty,
            'defectqty': 0,
            'issues': '',
            'ftt': 100
        }
    issues = db.session.query(Lineinfo.linegroup, Issuelog.desc, Lineinfo.VSM, Lineinfo.plant, Lineinfo.area, Issuelog.qty.label('defectqty')).outerjoin(
        Lineinfo, Issuelog.linename == Lineinfo.linename).filter(
        Lineinfo.isactive == 1).filter(and_(Issuelog.shiftdate >= sd, Issuelog.shiftdate < ed)).filter(Issuelog.sqdctype == '质量').all()
    for i in issues:
        if i.linegroup in dic.keys():
            dic[i.linegroup]['defectqty'] = dic[i.linegroup]['defectqty']+i.defectqty
            dic[i.linegroup]['issues'] = dic[i.linegroup]['issues'] + \
                ' '+i.desc+'-('+str(i.defectqty)+')'
    newFilename = funnelExcel(
        path+'/fttrecord/', path+filename, list(dic.values()), sd, ed)
    if os.path.isfile(os.path.join(path+'/fttrecord/', newFilename)):
        response = send_from_directory(
            path+'/fttrecord/', newFilename, as_attachment=True)
        response.headers["Access-Control-Expose-Headers"] = "fname"
        response.headers['fname'] = newFilename
        return response
    return responseError('没有找到下载文件')


def funnelExcel(path, file, arr, sd, ed):
    wb = load_workbook(file)
    ws = wb['FTT']
    ws.cell(1, 1).value = sd+'(含)到'+ed+'(不含)区间的生产和不良数量'
    for i in range(len(arr)):
        ws.cell(i+3, 1).value = arr[i]['plant']
        ws.cell(i+3, 2).value = arr[i]['vsm']
        ws.cell(i+3, 3).value = arr[i]['area']
        ws.cell(i+3, 4).value = arr[i]['linegroup']
        ws.cell(i+3, 5).value = arr[i]['scanqty']
        ws.cell(i+3, 6).value = arr[i]['defectqty']
        ws.cell(i+3, 7).value = str(round(arr[i]['scanqty'] /
                                          (arr[i]['defectqty']+arr[i]['scanqty'])*100, 2)) if arr[i]['scanqty'] else 0
        ws.cell(i+3, 8).value = arr[i]['issues']
    newFilename = datetime.datetime.strftime(
        datetime.date.today(), '%Y-%m-%d')+' - '+'FTT.xlsx'
    wb.save(path+newFilename)
    wb.close()
    return newFilename


@ api.route("/changePhone", methods=["put"])  # 修改用户的电话
@login_required
def changePhone():
    res = request.json
    id = res.get('id')
    phone = res.get('phone')
    user = db.session.query(User).filter(User.Id == id).first()
    if user:
        user.phone = phone
        db.session.commit()
        return responsePut('修改成功')
    else:
        return responseError('修改用户手机号失败，请联系管理员')


@ api.route("/changeActive", methods=["put"])  # 把用户标记未可用或不可用
@login_required
def changeActive():
    res = request.json
    id = res.get('id')
    user = db.session.query(User).filter(User.Id == id).first()
    if user:
        ia = user.isactive
        if ia == 0:
            user.isactive = 1
        else:
            user.isactive = 0
        db.session.commit()
        return responsePut('修改成功')
    else:
        return responseError('修改用户状态失败，请联系管理员')


@ api.route("/getUsers", methods=["get"])  # 获取所有用户信息
@login_required
def getUsers():
    res = request.args
    keywords = res.get('keywords')
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    plant = res.get('plant')
    users = db.session.query(User.Id, User.email, Role.name, User.roleid, User.phone,
                             User.isactive, User.auth).join(Role, User.roleid == Role.Id).filter(User.plant == plant)
    total = db.session.query(func.count(User.Id)).filter(User.plant == plant)

    if keywords:
        users = users.filter(User.email.like('%{0}%'.format(keywords)))
        total = total.filter(User.email.like('%{0}%'.format(keywords)))
    users = users.paginate(pagenum, pagesize, error_out=False).items
    total = total.scalar()
    outArr = []
    for u in users:
        outArr.append({
            'email': u.email,
            'id': u.Id,
            'role': u.name,
            'phone': u.phone,
            'auth': u.auth,
            'isactive': u.isactive
        })
    roles = db.session.query(Role.Id, Role.name).filter(
        Role.plant == plant).all()
    roleArr = []
    for r in roles:
        roleArr.append({
            'id': r.Id,
            'name': r.name
        })
    return responseGet('获取成功', {'users': outArr, 'total': total, 'roleList': roleArr})


@ api.route("/getAuth", methods=["get"])     # 获取权限列表
@login_required
def getAuth():
    auths = db.session.query(Permission).order_by(Permission.pid).all()
    dic = {}
    for m in auths:
        if m.pid == 0:
            dic[m.Id] = {
                'id': m.Id,
                'label': m.name,
                'children': []
            }
        else:
            dic[m.pid]['children'].append({
                'id': m.Id,
                'label': m.name
            })
    outArr = []
    for value in dic.values():
        outArr.append(value)
    return responseGet('获取成功', {'emptyTree': outArr})


@ api.route("/setAuth", methods=["POST"])  # 给用户绑定权限
@login_required
def setAuth():
    res = request.json
    auth = res.get('auth')
    id = res.get('id')
    try:
        db.session.query(User).filter(User.Id == id).update({'auth': auth})
        db.session.commit()
    except Exception:
        db.session.rollback()
        return responseError('设置权限失败，请联系管理员')
    return responsePost('设置权限成功')


@api.route('/addUser', methods=['POST'])  # 添加用户
@login_required
def addUser():
    res = request.json
    email = res.get('email')
    roleid = res.get('roleid')
    plant = res.get('plant')
    phone = res.get('phone')
    eid = res.get('eid')
    auth = db.session.query(Role.defaultauth).filter(
        Role.Id == roleid).scalar()
    try:
        newuser = User(email=email, roleid=roleid, auth=auth,
                       isactive=1, plant=plant, phone=phone)
        db.session.add(newuser)
        r = requests.post(config[env].api_url+"/public/addUser",
                          {'email': email, 'eid': eid, 'plant': plant})
        vdata = r.json()
        if vdata['meta']['status'] != 201:
            return responseError(vdata['meta']['msg'])
        db.session.commit()
        return responsePost('新增员工成功！')
    except Exception:
        db.session.rollback()
        return responseError('新增员工失败，请查看是否已经存在该员工')


@ api.route("/getLines", methods=["get"])     # 获取产线列表
@login_required
def getLines():
    res = request.args
    keywords = res.get('keywords')
    plant = res.get('plant')
    defaultTarget = db.session.query(Kpi).filter(
        Kpi.linegroup == 'all').order_by(desc(Kpi.targetyear)).first()
    lines = db.session.query(Lineinfo.Id, Lineinfo.owner, Lineinfo.manager, Lineinfo.linegroup, Lineinfo.linename, Lineinfo.isactive, Lineinfo.VSM, Lineinfo.area,
                             Lineinfo.space, Lineinfo.area,
                             Kpi.targetyear, Kpi.ctarget, Kpi.qtarget, Kpi.dtarget).outerjoin(Kpi, Lineinfo.linegroup == Kpi.linegroup).filter(Lineinfo.plant == plant)
    total = db.session.query(Lineinfo.linegroup).filter(
        Lineinfo.plant == plant)
    if keywords:
        lines = lines.filter(or_(Lineinfo.linegroup.like('%{0}%'.format(
            keywords)), Lineinfo.linename.like('%{0}%'.format(keywords))))
        total = total.filter(or_(Lineinfo.linegroup.like('%{0}%'.format(
            keywords)), Lineinfo.linename.like('%{0}%'.format(keywords))))
    lines = lines.order_by(Lineinfo.VSM, Lineinfo.area).all()
    total = total.group_by(Lineinfo.linegroup).all()
    totalnum = len(total)
    dic = {}
    for ll in lines:
        if ll.linegroup in dic.keys():
            dic[ll.linegroup]['children'].append(
                {
                    'linename': ll.linename,
                    'isactive': ll.isactive,
                    'owner': ll.owner,
                    'manager': ll.manager,
                    'id': ll.Id,
                    'linegroup': ll.linegroup
                }
            )
        else:
            restArr = []
            resttime = db.session.query(Restinfo).filter(
                Restinfo.linegroup == ll.linegroup).all()
            if not resttime:
                resttime = db.session.query(Restinfo).filter(
                    Restinfo.linegroup == 'all').all()
            for r in resttime:
                restArr.append({
                    'resttime': datetime.time.strftime(r.resttime, "%H:%M"),
                    'restmin': r.restmin
                })
            dic[ll.linegroup] = {
                'linegroup': ll.linegroup,
                'vsm': ll.VSM,
                'area': ll.area,
                'ctarget': round(ll.ctarget if ll.ctarget else defaultTarget.ctarget, 0),
                'dtarget': round(ll.dtarget if ll.dtarget else defaultTarget.dtarget, 0),
                'qtarget': round(ll.qtarget if ll.qtarget else defaultTarget.qtarget, 0),
                'restArr': restArr,
                'children': [{
                    'linename': ll.linename,
                    'isactive': ll.isactive,
                    'owner': ll.owner,
                    'manager': ll.manager,
                    'id': ll.Id,
                    'linegroup': ll.linegroup
                }]
            }
    outArr = list(dic.values())
    users = db.session.query(User).filter(User.isactive == 1).all()
    userArr = []
    for u in users:
        userArr.append({
            'label': u.email.split('@')[0],
            'value': u.email
        })
    return responseGet('获取成功', {'lines': outArr, 'total': totalnum, 'users': userArr})


@ api.route("/getGroupform", methods=["get"])    # 根据工厂，产线组获取该产线组的KPI和休息时间
@login_required
def getGroupform():
    res = request.args
    linegroup = res.get('linegroup')
    plant = res.get('plant')
    r = requests.get(config[env].api_url +
                     "/public/info/getLinenamescascade?plant="+plant)
    print(r)
    kpi = db.session.query(Kpi).filter(
        Kpi.linegroup == linegroup).order_by(desc(Kpi.targetyear)).all()
    rest = db.session.query(Restinfo).filter(Restinfo.linegroup ==
                                             linegroup).order_by(Restinfo.resttime).all()
    kpis = []
    rests = []
    if kpi:
        for kk in kpi:
            kpis.append({
                'id': kk.Id,
                'targetyear': kk.targetyear,
                'qtarget': kk.qtarget,
                'dtarget': kk.dtarget,
                'ctarget': kk.ctarget
            })
    if rest:
        for rr in rest:
            rests.append({
                'id': rr.Id,
                'resttime': datetime.time.strftime(rr.resttime, "%H:%M"),
                'restmin': rr.restmin
            })
    return responseGet('获取成功', {'linegroups': r.json()['data']['linenames'], 'kpis': kpis, 'rests': rests})


@ api.route("/getLineform", methods=["get"])  # 获取产线组的树状结构
@ login_required
def getLineform():
    res = request.args
    plant = res.get('plant')
    lines = db.session.query(Lineinfo).filter(
        Lineinfo.plant == plant).group_by(Lineinfo.linegroup).all()
    linegroups = {}
    for line in lines:
        linegroups[line.linegroup] = {
            'linegroup': line.linegroup,
            'area': line.area,
            'vsm': line.VSM
        }
    return responseGet('获取成功', {'linegroups': linegroups})


@ api.route("/setGroup", methods=["POST"])   # 给产线组配置休息时间和KPI
@ login_required
def setGroup():
    res = request.json
    kpis = res.get('kpis')   # KPI
    rests = res.get('rests')  # 休息时间
    linegroup = res.get('linegroup')  # 产线组
    vsm = res.get('vsm')  # VSM
    area = res.get('area')  # area
    plant = res.get('plant')  # plant
    isupdate = res.get('isupdate')  # 是更新还是新建
    linename = res.get('linename')  # 产线名称
    try:
        if isupdate:
            db.session.query(Kpi).filter(Kpi.linegroup == linegroup).delete()
            db.session.query(Restinfo).filter(
                Restinfo.linegroup == linegroup).delete()
        else:
            newl = Lineinfo(linename=linename, isactive=1, VSM=vsm, currentshift=0,
                            linegroup=linegroup, area=area, space=50, plant=plant)
            db.session.add(newl)
        if kpis:
            for kk in kpis:
                newk = Kpi(linegroup=linegroup, targetyear=kk['targetyear'],
                           qtarget=kk['qtarget'], dtarget=kk['dtarget'], ctarget=kk['ctarget'])
                db.session.add(newk)
        if rests:
            for rr in rests:
                newr = Restinfo(linegroup=linegroup,
                                resttime=rr['resttime']+':00', restmin=rr['restmin'])
                db.session.add(newr)
        db.session.commit()
        return responsePost('设置成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('设置失败，查看是否有重复的产线或产线组，或请联系管理员')


@ api.route("/setLine", methods=["POST"])  # 新建或修改产线信息
@ login_required
def setLine():
    res = request.json
    linegroup = res.get('linegroup')
    vsm = res.get('vsm')
    id = int(res.get('id'))  # 如是修改，则产线id》0
    area = res.get('area')
    plant = res.get('plant')
    isactive = res.get('isactive')
    linename = res.get('linename')
    manager = ','.join(res.get('manager'))
    owner = ','.join(res.get('owner'))
    try:
        if id:
            db.session.query(Lineinfo).filter(Lineinfo.Id == id).update(
                {
                    'VSM': vsm,
                    'area': area,
                    'linegroup': linegroup,
                    'linename': linename,
                    'isactive': isactive,
                    'manager': manager,
                    'owner': owner
                })
        else:
            newl = Lineinfo(linename=linename, isactive=1, VSM=vsm, currentshift=0,
                            linegroup=linegroup, area=area, space=50, plant=plant, manager=manager, owner=owner)
            db.session.add(newl)
        db.session.commit()
        return responsePost('设置成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('设置失败，查看是否有重复的产线或产线组，或请联系管理员')


@ api.route("/getSKUs", methods=["get"])  # 获取料号列表
@ login_required
def getSKUs():
    res = request.args
    keywords = res.get('keywords')
    plant = res.get('plant')
    mgroup=res.get('mgroup')
    pagenum = int(res.get('pagenum'))
    pagesize = int(res.get('pagesize'))
    subq = db.session.query(Routing.routing, Routing.sku).order_by(
        desc(Routing.Id)).limit(100000).subquery()
    skus = db.session.query(Skuinfo.Id, Skuinfo.sku, Skuinfo.des, Skuinfo.snverify, Skuinfo.lenverify, subq.c.routing,Skuinfo.code69,Skuinfo.code69sn,
                            Skuinfo.color, Skuinfo.mgroup, Skuinfo.typegroup, Flexinfo.linename, Flexinfo.linename).outerjoin(
        Skuinfo, Skuinfo.sku == subq.c.sku).outerjoin(Flexinfo, Skuinfo.sku == Flexinfo.sku).group_by(subq.c.sku)
    total = db.session.query(func.count(Skuinfo.sku))
    if keywords:
        skus = skus.filter(Skuinfo.sku.like('%{0}%'.format(keywords)))
        total = total.filter(Skuinfo.sku.like('%{0}%'.format(keywords)))
    if mgroup:
        skus = skus.filter(Skuinfo.mgroup == mgroup)
        total = total.filter(Skuinfo.mgroup == mgroup)
    skus = skus.paginate(pagenum, pagesize, error_out=False).items
    total = total.scalar()
    # outArr = skus_schema.dump(skus)
    # print('outARR', outArr)
    outArr = []
    for s in skus:
        outArr.append(
            {
                'Id': s.Id,
                'sku': s.sku,
                'linename': s.linename,
                'mgroup': s.mgroup,
                'color': s.color,
                'des': s.des,
                'lenverify': s.lenverify,
                'snverify': s.snverify,
                'routing': s.routing,
                'typegroup': s.typegroup,
                'code69': s.code69,
                'code69sn': s.code69sn
            }
        )
    lineList = getLinelist(plant)
    return responseGet('获取成功', {'skus': outArr, 'total': total, 'lineList': lineList, 'mgroups': mgroups,'faiArr':faiArr})


@ api.route("/getRoutings", methods=["get"])  # 获取routing列表
@ login_required
def getRoutings():
    res = request.args
    sku = res.get('sku')
    routings = db.session.query(Routing).filter(
        Routing.sku == sku).order_by(desc(Routing.revision), desc(Routing.Id)).all()
    outArr = skus_schema.dump(routings)
    return responseGet('获取成功', {'routingList': outArr})


@ api.route("/checkSNdupilate", methods=["post"])  # 获取routing列表
@ login_required
def checkSNdupilate():
    res = request.json
    snarr = res.get('snarr')
    print(*********,snarr)
    sns=db.session.query(Scaninfo).filter(Scaninfo.sn.in_(snarr)).all()
    outArr=[]
    for s in sns:
        outArr.append(s.sn)
    return responsePost('获取成功', {'sns': outArr})


@ api.route("/addFlex", methods=["POST"])  # 添加弹性计划
@ login_required
def addFlex():
    res = request.json
    linename = res.get('linename')
    sku = res.get('sku')
    headcount = res.get('headcount')
    hourlyrate = res.get('hourlyrate')
    revision = datetime.date.today()
    try:
        flex = Flexinfo(sku=sku, linename=linename, headcount=headcount,
                        hourlyrate=hourlyrate, revision=revision)
        db.session.add(flex)
        db.session.commit()
    except Exception:
        db.session.rollback()
        return responseError('添加失败，在同一天内，不允许建立同一条产线同样人数的弹性计划，请明天再试试')
    return responsePost('添加成功')


@ api.route("/addSKU", methods=["POST"])  # 添加或修改SKU
@ login_required
def addSKU():
    res = request.json
    id = int(res.get('Id'))  # 根据sku的id判断是否是增加
    des = res.get('des')
    sku = res.get('sku')
    mgroup = res.get('mgroup') if res.get('mgroup') else ''
    lenverify = res.get('lenverify') if res.get('lenverify') else ''
    snverify = res.get('snverify') if res.get('snverify') else ''
    routing = res.get('routing')
    oldRouting = res.get('oldRouting')
    color = res.get('color') if res.get('color') else ''
    typegroup = res.get('typegroup') if res.get('typegroup') else ''
    code69 = res.get('code69') if res.get('code69') else ''
    code69sn = res.get('code69sn') if res.get('code69sn') else ''
    if(id > 0):
        try:
            db.session.query(Skuinfo).filter(Skuinfo.Id == id).update(
                {'sku': sku, 'des': des, 'snverify': snverify, 'lenverify': lenverify, 'mgroup': mgroup, 'typegroup': typegroup, 'color': color,'code69':code69,'code69sn':code69sn})
            if(routing != oldRouting):
                newRouting = Routing(
                    sku=sku, routing=routing, revision=datetime.date.today())
                db.session.add(newRouting)
            db.session.commit()
        except Exception:
            db.session.rollback()
            return responseError('编辑料号'+sku+'信息失败，请联系管理员')
    else:
        try:
            newSku = Skuinfo(sku=sku, des=des, snverify=snverify, mgroup=mgroup,code69sn=code69sn,code69=code69,
                             lenverify=lenverify, typegroup=typegroup, color=color)
            db.session.add(newSku)
            newRouting = Routing(sku=sku, routing=routing,
                                 revision=datetime.date.today())
            db.session.add(newRouting)
            db.session.commit()
        except Exception:
            db.session.rollback()
            traceback.print_exc()
            return responseError('新建料号'+sku+'信息失败，请联系管理员')
    return responsePost('成功')


@ api.route("/getFlexs", methods=["get"])  # 根据SKU获取弹性计划
@ login_required
def getFlexs():
    res = request.args
    sku = res.get('sku')
    flexs = db.session.query(Flexinfo).filter(Flexinfo.sku == sku).order_by(desc(Flexinfo.revision),
                                                                            Flexinfo.linename, Flexinfo.revision, Flexinfo.headcount).all()
    outDic = {}
    for f in flexs:
        if f.revision in outDic.keys():
            outDic[f.revision]['items'].append({
                'linename': f.linename,
                'headcount': f.headcount,
                'hourlyrate': f.hourlyrate
            })
        else:
            outDic[f.revision] = ({
                'timestamp': datetime.datetime.strftime(f.revision, '%Y-%m-%d'),
                'items': [
                    {
                        'linename': f.linename,
                        'headcount': f.headcount,
                        'hourlyrate': f.hourlyrate
                    }
                ]
            })
    return responseGet('获取成功', {'flexList': list(outDic.values())})


@ api.route("/getCates", methods=["get"])  # 根据产线获取问题分类和Trigger point
@ login_required
def getCates():
    res = request.args
    linegroup = res.get('linename') if res.get('linename') else 'ALL'
    plant = res.get('plant')
    cates = db.session.query(Probleminfo).filter(
        Probleminfo.linename == linegroup).all()
    catesoee = db.session.query(Probleminfooee).filter(
        Probleminfooee.linename == linegroup).all()
    cateList = {}
    for cc in cates:
        if cc.sqdctype in cateList.keys():
            cateList[cc.sqdctype]['children'].append({
                'Id': cc.Id,
                'problemtype': cc.problemtype,
                'trigger': cc.trigger
            })
        else:
            cateList[cc.sqdctype] = {
                'name': cc.sqdctype,
                'children': [
                    {
                        'Id': cc.Id,
                        'problemtype': cc.problemtype,
                        'trigger': cc.trigger
                    }
                ]
            }
    for cc in catesoee:
        if cc.sqdctype in cateList.keys():
            cateList[cc.sqdctype]['children'].append({
                'Id': cc.Id,
                'problemtype': cc.problemtype,
                'trigger': cc.trigger
            })
        else:
            cateList[cc.sqdctype] = {
                'name': cc.sqdctype,
                'children': [
                    {
                        'Id': cc.Id,
                        'problemtype': cc.problemtype,
                        'trigger': cc.trigger
                    }
                ]
            }
    if '停机损失' not in cateList.keys():
        cateList['停机损失'] = [{
            'name': '停机损失',
            'children': []
        }]
    if '效率损失' not in cateList.keys():
        cateList['效率损失'] = [{
            'name': '效率损失',
            'children': []
        }]
    if '质量不良' not in cateList.keys():
        cateList['质量不良'] = [{
            'name': '质量不良',
            'children': []
        }]
    if '质量' not in cateList.keys():
        cateList['质量'] = [{
            'name': '质量',
            'children': []
        }]
    if '交货' not in cateList.keys():
        cateList['交货'] = [{
            'name': '交货',
            'children': []
        }]
    if '效率' not in cateList.keys():
        cateList['效率'] = [{
            'name': '效率',
            'children': []
        }]
    lineList = getLinegroup(plant)
    return responseGet('获取成功', {'lineList': lineList, 'cateList': cateList, 'oeeArr': oeeArr})


@ api.route("/addCateoee", methods=["POST"])  # 添加QDC分类
@ login_required
def addCateoee():
    res = request.json
    sqdctype = res.get('sqdctype')
    linegroup = res.get('linename') if res.get('linename') else 'ALL'
    problemtype = '请修改此处内容'
    trigger = 5  # 默认5，添加后进行修改
    try:
        cate = Probleminfooee(sqdctype=sqdctype, linename=linegroup,
                              problemtype=problemtype, trigger=trigger)
        db.session.add(cate)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('新增项目失败，请联系管理员')
    return responsePost('成功')


@ api.route("/editCateoee", methods=["PUT"])  # 编辑QDC的某一个分类
@ login_required
def editCateoee():
    res = request.json
    id = res.get('id')
    problemtype = res.get('problemtype')
    trigger = res.get('trigger')
    try:
        db.session.query(Probleminfooee).filter(Probleminfooee.Id == id).update(
            {'problemtype': problemtype, 'trigger': trigger})
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('修改项目失败，请联系管理员')
    return responsePut('修改成功')


@ api.route("/delCateoee", methods=["PUT"])  # 删除QDC分类
@ login_required
def delCateoee():
    res = request.json
    id = res.get('id')
    try:
        db.session.query(Probleminfooee).filter(
            Probleminfooee.Id == id).delete()
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('删除项目失败，请联系管理员')
    return responsePut('删除成功')


@ api.route("/addCate", methods=["POST"])  # 添加QDC分类
@ login_required
def addCate():
    res = request.json
    sqdctype = res.get('sqdctype')
    linegroup = res.get('linename') if res.get('linename') else 'ALL'
    problemtype = '请修改此处内容'
    trigger = 5  # 默认5，添加后进行修改
    try:
        cate = Probleminfo(sqdctype=sqdctype, linename=linegroup,
                           problemtype=problemtype, trigger=trigger)
        db.session.add(cate)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('新增项目失败，请联系管理员')
    return responsePost('成功')


@ api.route("/editCate", methods=["PUT"])  # 编辑QDC的某一个分类
@ login_required
def editCate():
    res = request.json
    id = res.get('id')
    problemtype = res.get('problemtype')
    trigger = res.get('trigger')
    try:
        db.session.query(Probleminfo).filter(Probleminfo.Id == id).update(
            {'problemtype': problemtype, 'trigger': trigger})
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('修改项目失败，请联系管理员')
    return responsePut('修改成功')


@ api.route("/delCate", methods=["PUT"])  # 删除QDC分类
@ login_required
def delCate():
    res = request.json
    id = res.get('id')
    try:
        db.session.query(Probleminfo).filter(Probleminfo.Id == id).delete()
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('删除项目失败，请联系管理员')
    return responsePut('删除成功')


# 这个是转移老数据库使用，系统里不需要，把原表的下属序列号提取出来放入新表，根据现有数量需运行10分钟左右
@ api.route("/getScans9527", methods=["GET"])
# @login_required
def getScans9527():
    scans = db.session.query(Scaninfo).all()
    for s in scans:
        try:
            if s.sn1:
                mySub = Scansub(sn=s.sn, subname='sn1', subsn=s.sn1)
                db.session.add(mySub)
                if s.sn2:
                    mySub = Scansub(sn=s.sn, subname='sn2', subsn=s.sn2)
                    db.session.add(mySub)
                if s.sn3:
                    mySub = Scansub(sn=s.sn, subname='sn3', subsn=s.sn3)
                    db.session.add(mySub)
                if s.sn4:
                    mySub = Scansub(sn=s.sn, subname='sn4', subsn=s.sn4)
                    db.session.add(mySub)
                if s.sn5:
                    mySub = Scansub(sn=s.sn, subname='sn5', subsn=s.sn5)
                    db.session.add(mySub)
        except Exception:
            traceback.print_exc()
            db.session.rollback()
    db.session.commit()
    return responseGet('添加成功')


@ api.route("/editCTQ", methods=["PUT"])  # 删除QDC分类
@ login_required
def editCTQ():
    res = request.json
    Id = res.get('Id')
    ctqname = res.get('ctqname')
    ctqtype = res.get('ctqtype')
    skuctqid = res.get('skuctqid')
    va1 = res.get('va1')
    va2 = res.get('va2')
    va3 = res.get('va3')
    try:
        db.session.query(Ctqinfo).filter(Ctqinfo.Id == Id).update({
            'ctqname': ctqname,
            'ctqtype': ctqtype,
            'skuctqid': skuctqid,
            'va1': va1,
            'va2': va2,
            'va3': va3
        })
        db.session.commit()
        return responsePut('修改成功')
    except Exception:
        db.session.rollback()
        return responseError('修改失败，请检查是否CTQ号重复或联系管理员')


@ api.route("/getCTQ", methods=["get"])  # 根据SKU获取对应的CTQ
@ login_required
def getCTQ():
    res = request.args
    sku = res.get('sku')
    ctqs = db.session.query(Skuinfo.sku, Skuinfo.des, Ctqinfo.skuctqid, Ctqinfo.ctqname, Ctqinfo.ctqtype,Ctqinfo.ctqstation,Ctqinfo.ctqstationname,
                            Ctqinfo.va1, Ctqinfo.va2, Ctqinfo.va3, Ctqinfo.Id).outerjoin(Ctqinfo,     
                                                                                         Ctqinfo.sku == Skuinfo.sku).order_by(Ctqinfo.ctqstation,Ctqinfo.skuctqid).filter(Skuinfo.sku == sku).all()
    stationsDic={}
    ctqList=[]
    for c in ctqs:
        ctqList.append({
            'sku': c.sku,
            'skuctqid': c.skuctqid,
            'ctqname': c.ctqname,
            'ctqtype': c.ctqtype,
            'ctqstation':c.ctqstation,
            'ctqstationname':c.ctqstationname,
            'va1': c.va1,
            'va2': c.va2,
            'va3': c.va3,
            'Id': c.Id
        })
        if c.Id and c.ctqtype:
            if c.ctqstation in stationsDic.keys():
                stationsDic[c.ctqstation]['ctqs'].append(c.skuctqid)
            else:
                stationsDic[c.ctqstation]={'ctqs':[c.skuctqid],'ctqstation':c.ctqstation,'ctqstationname':c.ctqstationname,'sku':sku}
    return responseGet('成功', {'ctqList': ctqList,'stations':list(stationsDic.values())})


@ api.route("/updateStation", methods=["POST"])  # 根据SKU获取对应的CTQ
@ login_required
def updateStation():
    res = request.json
    stations=res.get('stations')
    if len(stations)==0 or not stations:
        return responseError('至少需要一个站别')
    elif len(stations)==1:
        sku=stations[0]['sku']
        db.session.query(Ctqinfo).filter(Ctqinfo.sku == sku).update({'ctqstation':0,'ctqstationname':stations[0]['ctqstationname']})
    else:
        updates=[]
        print(*********,stations)
        for k,s in enumerate(stations):
            updates.append({
                'sku': s['sku'],
                'ctqs': s['ctqs'],
                'new_ctqstation': k+1,
                'new_ctqstationname': s['ctqstationname']
            })
        for update_data in updates:
            db.session.query(Ctqinfo).filter(
                Ctqinfo.sku == update_data['sku'],
                Ctqinfo.skuctqid.in_(update_data['ctqs'])
            ).update({
                'ctqstation': update_data['new_ctqstation'],
                'ctqstationname': update_data['new_ctqstationname']
            }, synchronize_session=False)
    db.session.commit()
    return responsePost('更新成功')



@ api.route("/getCTQTemplate", methods=["get"])  # 获取所有CTQ模板
@ login_required
def getCTQTemplate():
    ctqTemplate = {}
    ctqs = db.session.query(Ctqtemplate).all()
    for c in ctqs:
        if c.templatename in ctqTemplate.keys():
            ctqTemplate[c.templatename].append(
                {
                    'skuctqid': c.ctqid,
                    'ctqname': c.ctqname,
                    'ctqtype': c.ctqtype,
                    'ctqstation':c.ctqstation,
                    'ctqstationname':c.ctqstationname
                }
            )
        else:
            ctqTemplate[c.templatename] = [(
                {
                    'skuctqid': c.ctqid,
                    'ctqname': c.ctqname,
                    'ctqtype': c.ctqtype,
                    'ctqstation':c.ctqstation,
                    'ctqstationname':c.ctqstationname
                }
            )]
    return responseGet('成功', {'ctqTemplate': ctqTemplate, 'ctqOptions': ctqOptions})


@ api.route('/addCTQ', methods=['POST'])  # 添加CTQ
@ login_required
def addCTQ():
    res = request.json
    sku = res.get('sku')
    ctqs = res.get('ctqs')
    print(ctqs)
    try:
        for c in ctqs:
            ctq = Ctqinfo(sku=sku, skuctqid=c['skuctqid'],
                          ctqname=c['ctqname'], ctqtype=c['ctqtype'])
            db.session.add(ctq)
        db.session.commit()
        return responsePost('插入成功')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('插入失败,请检查是否有重复的CTQ顺序号，模板插入前先把已有的CTQ删除后再插入，新建的CTQ从0开始')


@ api.route("/delCTQ", methods=["PUT"])  # 删除CTQ
@ login_required
def delCTQ():
    res = request.json
    id = res.get('id')
    db.session.query(Ctqinfo).filter(Ctqinfo.Id == id).delete()
    db.session.commit()
    return responsePut('删除成功')


@ api.route("/getAreas", methods=["get"])  # 获取区域列表
@ login_required
def getAreas():
    res = request.args
    plant = res.get('plant')
    areas = db.session.query(Lineinfo.area).filter(Lineinfo.plant == plant).group_by(
        Lineinfo.area).order_by(Lineinfo.area).all()
    outArr = lines_schema.dump(areas)
    return responseGet('成功', {'areas': outArr})

<template>
  <div>
    <div>
      <el-form label-width="80px">
        <el-form-item label="料号">
          <el-select
            v-model="newPN.pn"
            filterable
            remote
            reserve-keyword
            placeholder="输入生产料号"
            remote-show-suffix
            :remote-method="remoteMethod"
            :loading="loading"
            size="large"
            :change="handletitle(newPN.pn)"
          >
            <el-option
              v-for="item in options"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <div>
            <span>{{ pn_des }}</span>
          </div>
        </el-form-item>
        <el-form-item label="最近生产">
          <div class="list_recently">
            <el-card
              v-for="item in recentsku"
              :key="item.pn"
              @click="props.PNData.pn = item.pn"
            >
              <template #header>
                <div class="card-header">
                  <span>{{ item.pn }}</span>
                </div>
              </template>
              <p>{{ item.recorddate }}</p>
            </el-card>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { PNProps } from "./types";
import { getpnlistbyprefix, getdesbypn } from "@/api/dashboard";
import { getrecentsku } from "@/api/prod";

const props = withDefaults(defineProps<PNProps>(), {
  PNData: () => ({
    pn: "",
    machine_id: 0
  })
});

const recentsku = ref([]);

const newPN = ref(props.PNData);

const options = ref([]);
const loading = ref(false);
const pn_des = ref("");

onMounted(() => {
  recentskulist(props.PNData.machine_id.toString());
});

//模糊检索料号
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    getpnlistbyprefix({ prefix: query }).then((res: any) => {
      options.value = res.data;
    });
    loading.value = false;
  } else {
    options.value = [];
  }
};
//调出料号描述
const handletitle = (query: string) => {
  getdesbypn({ pn: query }).then((res: any) => {
    pn_des.value = res.data;
  });
};

//检索最近生产物料列表
const recentskulist = (machineid: string) => {
  getrecentsku({ machineid: machineid }).then((res: any) => {
    recentsku.value = res.data;
  });
};
</script>

<style lang="scss" scoped>
.divchangepn {
  display: flex;
}
.el-card {
  margin: 4px;
}
:deep(.el-card__header) {
  padding: 5px;
  background-color: #409eff;
  color: white;
  text-align: center;
}
:deep(.el-card__body) {
  padding: 5px;
}
.list_recently {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
}
</style>

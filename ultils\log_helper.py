#!/usr/bin/env python3
# coding=utf-8
import datetime
# import logging.handlers
from builtins import object
import logging
import os
from logging import handlers
import traceback
import sys
from ultils.dir import LOGROOT_DIR
# from config import yaml_config


def error_parser():
    """Returns the error message and line number"""
    exc_type, exc_obj, exc_tb = sys.exc_info()
    for result in [stack for stack in traceback.extract_tb(exc_tb)]:
        print(result)
    return exc_obj, exc_tb.tb_lineno, [stack for stack in traceback.extract_tb(exc_tb)]


BACKUP_LOG_FILE_COUNT = 10  # 10 files.
MAX_LOG_FILE_SIZE_BYTES = 10000000  # 10 mb.


class ProjectLogger(object):
    ticket_number = ""
    debug_logger = None
    error_logger = None

    def __init__(self,
                 is_out_to_console=False,
                 when="S", backup_count=30, ticket_number="None", fmt="%(asctime)s - %(pathname)s[line:%(lineno)d] - %"
                 "(levelname)s: %(message)s"):
        # 设置日志输出格式

        self.ticket_number = ticket_number
        # 设置日志在控制台输出
        stream_handler = logging.StreamHandler()  # 设置控制台中输出日志格式
        debug_filename = os.path.join(LOGROOT_DIR, "system.log")
        # os.chmod(debug_filename, 755)
        # os.chmod(error_filename, 755)

        # 设置日志输出到文件（指定间隔时间自动生成文件的处理器  --按日生成）
        # filename：日志文件名，interval：时间间隔，when：间隔的时间单位， backupCount：备份文件个数，若超过这个数就会自动删除

        # debug_file_handler = handlers.TimedRotatingFileHandler(filename=debug_filename, when=when,
        #                                                        interval=5,
        #                                                        backupCount=backup_count,
        #                                                        encoding="utf-8")

        # # file_handler.suffix = "%Y-%m-%d_%H-%M-%S.log"
        # debug_file_handler.suffix = "%Y-%m-%d.log"
        # # error_file_handler.setFormatter(logging.Formatter(fmt))
        # # debug_file_handler.setFormatter(logging.Formatter(fmt))
        debug_file_handler = logging.FileHandler(debug_filename,mode='a')
        debug_file_handler.setLevel(logging.DEBUG)
        # 设置日志输出文件
        self.debug_logger = logging.getLogger(debug_filename)
        self.debug_logger.setLevel(logging.DEBUG)
        # 将输出对象添加到logger中
        self.debug_logger.propagate = False

        if not self.debug_logger.hasHandlers():
            self.debug_logger.addHandler(debug_file_handler)

    def debug(self, msg=""):
        func_name = sys.argv[0]
        job_log = "{}, <{}>, <{}>, <{}>".format(datetime.datetime.now(), "DEBUG", func_name,
                                                msg)
        self.debug_logger.debug(job_log)
        # if yaml_config.get("all").get("LogLevel") == "debug":
        #     self.debug_logger.debug(job_log)

    def error(self, msg=""):
        func_name = sys.argv[0]
        job_log = "{}, <{}>, <{}>, <{}>".format(datetime.datetime.now(), "ERROR", func_name,
                                                msg)
        self.debug_logger.debug(job_log)

    def info(self, msg=""):
        func_name = sys.argv[0]
        job_log = "{}, <{}>, <{}>, <{}>".format(datetime.datetime.now(), "INFO", func_name,
                                                msg)
        self.debug_logger.info(job_log)


logger = ProjectLogger()

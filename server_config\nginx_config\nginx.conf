user root;  # 设置 Nginx 运行的用户身份为 www-data，用于提高安全性。

worker_processes auto;  # 根据可用的 CPU 核心数自动设置工作进程的数量。

pid /run/nginx.pid;  # 指定 Nginx 进程的 PID 文件路径。

include /etc/nginx/modules-enabled/*.conf;  # 包含 /etc/nginx/modules-enabled/ 目录下的所有以 .conf 结尾的模块配置文件。

events {
	worker_connections 768;  # 每个工作进程的最大并发连接数。

	# multi_accept on;  # 启用多个连接的接受，可以提高连接的处理效率。
}

http {
	sendfile on;  # 启用 sendfile 机制，提高文件传输的效率。
	tcp_nopush on;  # 启用 tcp_nopush，将多个数据包合并为一个发送，减少网络包的数量，降低延迟。
	types_hash_max_size 2048;  # MIME 类型哈希表的最大大小。

	# server_tokens off;  # 禁止在响应头中发送服务器版本号信息，增加服务器的安全性。

	# server_names_hash_bucket_size 64;  # 设置虚拟主机名称哈希表的桶大小。
	# server_name_in_redirect off;  # 禁止在重定向中使用主机名。

	include /etc/nginx/mime.types;  # 包含 /etc/nginx/mime.types 文件，定义文件扩展名和 MIME 类型的映射关系。
	default_type application/octet-stream;  # 设置默认的 MIME 类型。

	ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;  # 指定支持的 SSL/TLS 协议版本。
	ssl_prefer_server_ciphers on;  # 优先使用服务器端的密码套件。

	access_log /var/log/nginx/access.log;  # 指定访问日志文件的路径和文件名。
	error_log /var/log/nginx/error.log;  # 指定错误日志文件的路径和文件名。

	gzip on;  # 启用 Gzip 压缩功能。

	# gzip_vary on;  # 在响应头中发送 "Vary: Accept-Encoding"，告知客户端对压缩方式的支持。
	# gzip_proxied any;  # 启用对代理服务器响应的压缩。
	# gzip_comp_level 6;  # 设置压缩级别。
	# gzip_buffers 16 8k;  # 设置压缩缓冲区大小。
	# gzip_http_version 1.1;  # 启用 HTTP 1.1 规范的 Gzip 压缩。
	# gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;  # 设置需要进行 Gzip 压缩的 MIME 类型。

	include /etc/nginx/conf.d/*.conf;  # 包含 /etc/nginx/conf.d/ 目录下的所有配置文件。
	include /etc/nginx/sites-enabled/*;  # 包含 /etc/nginx/sites-enabled/ 目录下的所有配置文件。
}

include /etc/nginx/stream_enabled/*;  # 包含 /etc/nginx/stream-enable/ 目录下的所有配置文件

daemon off;

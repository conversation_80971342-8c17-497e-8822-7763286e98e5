import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";
import { ref, onMounted, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { $t, transformI18n } from "@/plugins/i18n";
import { getDN, releaseDN, updateVolume } from "@/api/order";
import ReleaseForm from "./releaseForm/index.vue";
import pdfForm from "./pdfForm/index.vue";
import volumeForm from "./volumeForm/index.vue";
import feeForm from "./feeForm/index.vue";
import moment from "moment";
import {
  Download,
  Upload,
  InfoFilled,
  CircleCheckFilled,
  ShoppingCartFull,
  Van,
  Finished,
  QuestionFilled
} from "@element-plus/icons-vue";

// 导入类型和 composables
import type { RowData, SearchCondition, OperationFlag, FeeFormData, VolumeFormData, ReleaseFormData } from "../types";
import { useDialogManager } from "../composables/useDialogManager";
import { useFeeData } from "../composables/useFeeData";

export function useColumns() {
  const dataList = ref<RowData[]>([]);
  const loading = ref(true);
  const is_current = ref(true);
  const { t } = useI18n();

  // 使用 composables
  const { openFeeDialog, openVolumeDialog, openReleaseDialog, openPdfDialog, handleApiCall } = useDialogManager();
  const { feeFormData } = useFeeData();

  const operation_flag = reactive<OperationFlag>({
    can_release: false
  });
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left"
    },
    {
      label: "放单时间",
      prop: "release_time",
      width: "140px",
      formatter(row, column, cellValue) {
        return moment(cellValue).format("MM/DD HH:mm:ss");
      }
    },
    {
      label: transformI18n($t("table.DN_NO")),
      prop: "DN_NO",
      width: "140px",
      cellRenderer: ({ row }) => {
        const DN_NO = row.DN_NO.split(",");
        return (
          <div class="dn-no-tags">
            <el-tag
              onClick={() => {
                handlePdfView(row);
              }}
            >
              {DN_NO[0]}
            </el-tag>
            {DN_NO.length > 1 ? (
              <el-tag
                type="info"
                onClick={() => {
                  console.log("点击");
                }}
              >
                +{DN_NO.length - 1}
              </el-tag>
            ) : null}
          </div>
        );
      }
    },
    {
      label: transformI18n($t("table.status")),
      prop: "status",
      width: "100px",
      cellRenderer: ({ row: { status } }) => {
        const { Icon, text, color } = {
          1: { Icon: InfoFilled, text: "DN生成", color: "green" },
          2: { Icon: CircleCheckFilled, text: "DN释放", color: "blue" },
          3: { Icon: ShoppingCartFull, text: "仓库分拣", color: "orange" },
          4: { Icon: Van, text: "物流配送", color: "red" },
          5: { Icon: Finished, text: "工单结束", color: "purple" }
        }[status] || {
          Icon: QuestionFilled,
          text: "未知",
          color: "gray"
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              color
            }}
          >
            <el-icon>
              <Icon />
            </el-icon>
            {text}
          </div>
        );
      }
    },
    {
      label: transformI18n($t("table.business_type")),
      prop: "business_type",
      width: "80px",
      cellRenderer: ({ row }) => {
        const [Icon, text, color] =
          row.business_type === 1
            ? [Download, "提货", "green"]
            : [Upload, "发货", "blue"];
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              color
            }}
          >
            <el-icon>
              <Icon />
            </el-icon>
            {text}
          </div>
        );
      }
    },
    {
      label: "CS",
      width: "150px",
      prop: "cs_specialist"
    },
    {
      label: "发货点",
      width: "80",
      prop: "shipping_point"
    },
    {
      label: "经销商",
      prop: "payer_company",
      width: "250"
    },
    {
      label: "出向地",
      width: "150",
      formatter(row, column, cellValue, index) {
        return row.receiver_province
          ? row.receiver_province + "/" + row.receiver_city
          : "";
      }
    },
    {
      label: "仓库",
      cellRenderer: ({ row }) => {
        return (
          <el-button
            type="primary"
            onClick={() => {
              handleVolume(row);
            }}
          >
            处理
          </el-button>
        );
      }
    },
    {
      label: "物流",
      cellRenderer: ({ row }) => {
        return (
          <el-button
            type="primary"
            v-show={
              (row.volume !== undefined &&
                row.volume !== null &&
                row.volume !== "" &&
                row.volume !== 0) ||
              (row.weight !== undefined &&
                row.weight !== null &&
                row.weight !== "" &&
                row.weight !== 0)
            }
            onClick={() => {
              handleFee(row);
            }}
          >
            处理
          </el-button>
        );
      }
    }
  ];

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载工单数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  /** 撑满内容区自适应高度相关配置 */
  const adaptiveConfig: AdaptiveConfig = {
    /** 表格距离页面底部的偏移量，默认值为 `96` */
    offsetBottom: 12
    /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
    // fixHeader: true
    /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
    // timeout: 60
    /** 表头的 `z-index`，默认值为 `100` */
    // zIndex: 100
  };

  // 处理释放DN
  const handleRelease = (row: RowData) => {
    openReleaseDialog(row, ReleaseForm, async (data: ReleaseFormData) => {
      await handleApiCall(
        releaseDN(data),
        'DN工单释放成功,已发至工厂处理！',
        'DN工单释放失败',
        getList
      );
    });
  };

  // 处理PDF查看
  const handlePdfView = (row: RowData) => {
    openPdfDialog(row, pdfForm);
  };

  // 处理货物量登记
  const handleVolume = (row: RowData) => {
    openVolumeDialog(row, volumeForm, async (data: VolumeFormData) => {
      await handleApiCall(
        updateVolume({
          release_time: row.release_time,
          weight: data.weight,
          volume: data.volume,
          warehouse_remark: data.warehouse_remark
        }),
        '货物量登记成功，待物流处理！',
        '登记货物量失败！',
        getList
      );
    });
  };

  // 处理运费计算
  const handleFee = (row: RowData) => {
    openFeeDialog(row, feeForm, async (data: FeeFormData) => {
      console.log("准备保存的运费数据:", data);

      // TODO: 调用API保存运费数据
      // await handleApiCall(
      //   updateFeeData({
      //     release_time: row.release_time,
      //     ...data
      //   }),
      //   '运费计算成功！',
      //   '保存运费数据失败！',
      //   getList
      // );

      // 暂时直接成功
      getList();
    });
  };

  const search_condition = reactive<SearchCondition>({
    dn_number: "",
    status: "",
    daterange: []
  });

  const getList = () => {
    loading.value = true;
    getDN(search_condition).then((res: { data: RowData[] }) => {
      dataList.value = res.data;
      loading.value = false;
    });
  };

  function onSizeChange(val: number) {
    console.log("onSizeChange", val);
  }

  onMounted(() => {
    getList();
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  });

  return {
    operation_flag,
    is_current,
    search_condition,
    loading,
    columns,
    dataList,
    loadingConfig,
    adaptiveConfig,
    onSizeChange,
    getList,
    handleRelease,
    feeFormData
  };
}

import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";
import { ref, onMounted, reactive, h, toRaw } from "vue";
import { useI18n } from "vue-i18n";
import { $t, transformI18n } from "@/plugins/i18n";
import { getDN, releaseDN } from "@/api/order";
import ReleaseForm from "./releaseForm/index.vue";
import { addDialog } from "@/components/ReDialog";
import { message } from "@/utils/message";
import {
  Download,
  Upload,
  InfoFilled,
  CircleCheckFilled,
  ShoppingCartFull,
  Van,
  Finished,
  QuestionFilled
} from "@element-plus/icons-vue";

export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);
  const is_current = ref(true);
  const { t } = useI18n();
  const operation_flag = reactive({
    can_release: false
  });
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left"
    },
    {
      label: "放单时间",
      width: "150px"
    },
    {
      label: transformI18n($t("table.DN_NO")),
      prop: "DN_NO",
      width: "130px"
    },
    {
      label: transformI18n($t("table.SP_NO")),
      prop: "SP_NO",
      width: "130px",
      cellRenderer: ({ row }) => {
        if (row.SP_NO == "") {
          return <div>未分配</div>;
        } else {
          return <div>{row.SP_NO}</div>;
        }
      }
    },
    {
      label: transformI18n($t("table.business_type")),
      prop: "business_type",
      width: "120px",
      cellRenderer: ({ row }) => {
        const [Icon, text, color] =
          row.business_type === 1
            ? [Download, "提货", "green"]
            : [Upload, "发货", "blue"];
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              color
            }}
          >
            <el-icon>
              <Icon />
            </el-icon>
            {text}
          </div>
        );
      }
    },
    {
      label: "出向地",
      prop: "payer_address"
    },
    {
      label: transformI18n($t("table.payer_name")),
      prop: "payer_name"
    },
    {
      label: transformI18n($t("table.status")),
      prop: "status",
      width: "80px",
      cellRenderer: ({ row: { status } }) => {
        const { Icon, text, color } = {
          1: { Icon: InfoFilled, text: "DN生成", color: "green" },
          2: { Icon: CircleCheckFilled, text: "DN释放", color: "blue" },
          3: { Icon: ShoppingCartFull, text: "仓库分拣", color: "orange" },
          4: { Icon: Van, text: "物流配送", color: "red" },
          5: { Icon: Finished, text: "工单结束", color: "purple" }
        }[status] || {
          Icon: QuestionFilled,
          text: "未知",
          color: "gray"
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              color
            }}
          >
            <el-icon>
              <Icon />
            </el-icon>
            {text}
          </div>
        );
      }
    }
  ];

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载工单数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  /** 撑满内容区自适应高度相关配置 */
  const adaptiveConfig: AdaptiveConfig = {
    /** 表格距离页面底部的偏移量，默认值为 `96` */
    offsetBottom: 12
    /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
    // fixHeader: true
    /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
    // timeout: 60
    /** 表头的 `z-index`，默认值为 `100` */
    // zIndex: 100
  };

  const formRef = ref();
  const openReleaseForm = (row: any) => {
    addDialog({
      title: "释放Delivery Note",
      props: {
        formInline: {
          row: row,
          warehouse: "",
          customer_email: "",
          coments: ""
        }
      },
      width: "60%",
      draggable: true,
      fullscreenIcon: false,
      closeOnClickModal: false,
      contentRenderer: () => h(ReleaseForm, { ref: formRef }),
      beforeSure: async (done, { options }) => {
        function chores() {
          message(`DN工单释放成功,已发至工厂处理！`, {
            type: "success"
          });
          done(); // 关闭弹框
          getList(); // 刷新表格数据
        }
        console.log(options.props.formInline);
        releaseDN(toRaw(options.props.formInline)).then(
          (res: { meta: any }) => {
            console.log(res);
            if (res.meta.status == 201) {
              chores();
            } else {
              message("Routing变更失败", { type: "error" });
            }
          }
        );
      }
    });
  };

  const search_condition = reactive({
    dn_number: "",
    status: "",
    daterange: [,]
  });

  const getList = () => {
    loading.value = true;
    getDN(search_condition).then((res: { data: any }) => {
      dataList.value = res.data;
      loading.value = false;
    });
  };

  function onSizeChange(val) {
    console.log("onSizeChange", val);
  }

  onMounted(() => {
    getList();
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  });

  return {
    operation_flag,
    is_current,
    search_condition,
    loading,
    columns,
    dataList,
    loadingConfig,
    adaptiveConfig,
    onSizeChange,
    getList,
    openReleaseForm
  };
}

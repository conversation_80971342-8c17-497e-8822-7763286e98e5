import type { LoadingConfig, AdaptiveConfig } from "@pureadmin/table";
import { ref, onMounted, reactive, h, toRaw, computed } from "vue";
import { useI18n } from "vue-i18n";
import { $t, transformI18n } from "@/plugins/i18n";
import { getDN, releaseDN, updateVolume } from "@/api/order";
import ReleaseForm from "./releaseForm/index.vue";
import pdfForm from "./pdfForm/index.vue";
import volumeForm from "./volumeForm/index.vue";
import feeForm from "./feeForm/index.vue";
import { addDialog } from "@/components/ReDialog";
import { message } from "@/utils/message";
import moment, { now } from "moment";
import {
  Download,
  Upload,
  InfoFilled,
  CircleCheckFilled,
  ShoppingCartFull,
  Van,
  Finished,
  QuestionFilled
} from "@element-plus/icons-vue";
import { nextTick } from "process";

export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);
  const is_current = ref(true);
  const { t } = useI18n();
  const operation_flag = reactive({
    can_release: false
  });
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left"
    },
    {
      label: "放单时间",
      prop: "release_time",
      width: "140px",
      formatter(row, column, cellValue) {
        return moment(cellValue).format("MM/DD HH:mm:ss");
      }
    },
    {
      label: transformI18n($t("table.DN_NO")),
      prop: "DN_NO",
      width: "140px",
      cellRenderer: ({ row }) => {
        const DN_NO = row.DN_NO.split(",");
        return (
          <div class="dn-no-tags">
            <el-tag
              onClick={() => {
                openPdfView(row);
              }}
            >
              {DN_NO[0]}
            </el-tag>
            {DN_NO.length > 1 ? (
              <el-tag
                type="info"
                onClick={() => {
                  console.log("点击");
                }}
              >
                +{DN_NO.length - 1}
              </el-tag>
            ) : null}
          </div>
        );
      }
    },
    {
      label: transformI18n($t("table.status")),
      prop: "status",
      width: "100px",
      cellRenderer: ({ row: { status } }) => {
        const { Icon, text, color } = {
          1: { Icon: InfoFilled, text: "DN生成", color: "green" },
          2: { Icon: CircleCheckFilled, text: "DN释放", color: "blue" },
          3: { Icon: ShoppingCartFull, text: "仓库分拣", color: "orange" },
          4: { Icon: Van, text: "物流配送", color: "red" },
          5: { Icon: Finished, text: "工单结束", color: "purple" }
        }[status] || {
          Icon: QuestionFilled,
          text: "未知",
          color: "gray"
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              color
            }}
          >
            <el-icon>
              <Icon />
            </el-icon>
            {text}
          </div>
        );
      }
    },
    {
      label: transformI18n($t("table.business_type")),
      prop: "business_type",
      width: "80px",
      cellRenderer: ({ row }) => {
        const [Icon, text, color] =
          row.business_type === 1
            ? [Download, "提货", "green"]
            : [Upload, "发货", "blue"];
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              color
            }}
          >
            <el-icon>
              <Icon />
            </el-icon>
            {text}
          </div>
        );
      }
    },
    {
      label: "CS",
      width: "150px",
      prop: "cs_specialist"
    },
    {
      label: "发货点",
      width: "80",
      prop: "shipping_point"
    },
    {
      label: "经销商",
      prop: "payer_company",
      width: "250"
    },
    {
      label: "出向地",
      width: "150",
      formatter(row, column, cellValue, index) {
        return row.receiver_province
          ? row.receiver_province + "/" + row.receiver_city
          : "";
      }
    },
    {
      label: "仓库",
      cellRenderer: ({ row }) => {
        return (
          <el-button
            type="primary"
            onClick={() => {
              handleVolume(row);
            }}
          >
            处理
          </el-button>
        );
      }
    },
    {
      label: "物流",
      cellRenderer: ({ row }) => {
        return (
          <el-button
            type="primary"
            v-show={
              (row.volume !== undefined &&
                row.volume !== null &&
                row.volume !== "" &&
                row.volume !== 0) ||
              (row.weight !== undefined &&
                row.weight !== null &&
                row.weight !== "" &&
                row.weight !== 0)
            }
            onClick={() => {
              handleFee(row);
            }}
          >
            处理
          </el-button>
        );
      }
    }
  ];

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载工单数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  /** 撑满内容区自适应高度相关配置 */
  const adaptiveConfig: AdaptiveConfig = {
    /** 表格距离页面底部的偏移量，默认值为 `96` */
    offsetBottom: 12
    /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
    // fixHeader: true
    /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
    // timeout: 60
    /** 表头的 `z-index`，默认值为 `100` */
    // zIndex: 100
  };

  const formRef = ref();
  const openReleaseForm = (row: any) => {
    addDialog({
      title: "释放Delivery Note",
      props: {
        formInline: {
          row: row,
          warehouse: "",
          customer_email: "",
          coments: ""
        }
      },
      width: "60%",
      draggable: true,
      fullscreenIcon: false,
      closeOnClickModal: false,
      contentRenderer: () => h(ReleaseForm, { ref: formRef }),
      beforeSure: async (done, { options }) => {
        function chores() {
          message(`DN工单释放成功,已发至工厂处理！`, {
            type: "success"
          });
          done(); // 关闭弹框
          getList(); // 刷新表格数据
        }
        console.log(options.props.formInline);
        releaseDN(toRaw(options.props.formInline)).then(
          (res: { meta: any }) => {
            console.log(res);
            if (res.meta.status == 201) {
              chores();
            } else {
              message("Routing变更失败", { type: "error" });
            }
          }
        );
      }
    });
  };

  const openPdfView = (row: any) => {
    addDialog({
      title: "查看DN附件," + row.DN_NO,
      top: "4vh",
      hideFooter: true,
      fullscreenIcon: true,
      props: {
        pdf_url: row.release_time
      },
      width: "62%",
      draggable: true,
      closeOnClickModal: false,
      contentRenderer: () => h(pdfForm)
    });
  };

  const newdata = reactive({
    weight: 0,
    volume: 0,
    warehouse_remark: ""
  });

  const handleVolume = (row: any) => {
    addDialog({
      title: "登记货物量",
      props: {
        rowdata: row,
        newdata: newdata
      },
      contentRenderer: () => h(volumeForm),
      width: "40%",
      draggable: true,
      closeOnClickModal: false,
      beforeSure: async (done, { options }) => {
        function chores() {
          message(`货物量登记成功，待物流处理！`, {
            type: "success"
          });
          getList(); // 刷新表格数据
          done(); // 关闭弹框
        }
        updateVolume({
          release_time: row.release_time,
          weight: options.props.newdata.weight,
          volume: options.props.newdata.volume,
          warehouse_remark: options.props.newdata.warehouse_remark
        }).then((res: { meta: any }) => {
          if (res.meta.status == 201) {
            chores();
          } else {
            message("登记货物量失败！", { type: "error" });
          }
        });
      }
    });
  };

  const feedata = reactive({
    freight_fee: 0,
    fee_remark: "",
    freight_calc_method: 0,
    freight_unit_price: 0,
    freight_adjust: 0,
    freight_adjust_reason: "",
    logistic_remark: "",
    logistic_company: "",
    freight_total_fee: 0
  });

  const handleFee = (row: any) => {
    addDialog({
      title: "运费计算",
      top: "4vh",
      props: {
        rowdata: row
      },
      contentRenderer: () => h(feeForm, { ref: formRef }),
      width: "65%",
      draggable: true,
      closeOnClickModal: false,
      beforeSure: async (done, { options }) => {
        function chores() {
          message(`运费计算成功！`, {
            type: "success"
          });
          getList(); // 刷新表格数据
          done(); // 关闭弹框
        }

        // 从组件实例中获取最新的 feedata 值
        console.log("当前 feedata 值:", feedata);
        console.log("组件 ref:", formRef.value);

        // 如果需要获取组件内部的最新数据，可以通过组件暴露的方法
        if (formRef.value && typeof formRef.value.getFeedData === 'function') {
          const latestFeedData = formRef.value.getFeedData();
          console.log("组件内最新的 feedata:", latestFeedData);
        }

        // 这里可以调用API保存运费数据
        console.log("准备保存的运费数据:", {
          freight_fee: feedata.freight_fee,
          freight_calc_method: feedata.freight_calc_method,
          freight_unit_price: feedata.freight_unit_price,
          freight_adjust: feedata.freight_adjust,
          freight_adjust_reason: feedata.freight_adjust_reason,
          logistic_company: feedata.logistic_company,
          logistic_remark: feedata.logistic_remark,
          freight_total_fee: feedata.freight_total_fee
        });

        // 暂时直接执行成功回调，实际项目中这里应该调用API
        chores();

        // updateFeeData({
        //   release_time: row.release_time,
        //   freight_fee: feedata.freight_fee,
        //   freight_calc_method: feedata.freight_calc_method,
        //   logistic_company: feedata.logistic_company,
        //   // ... 其他运费相关字段
        // }).then((res: { meta: any }) => {
        //   if (res.meta.status == 201) {
        //     chores();
        //   } else {
        //     message("保存运费数据失败！", { type: "error" });
        //   }
        // });
      }
    });
  };

  const search_condition = reactive({
    dn_number: "",
    status: "",
    daterange: [,]
  });

  const getList = () => {
    loading.value = true;
    getDN(search_condition).then((res: { data: any }) => {
      dataList.value = res.data;
      loading.value = false;
    });
  };

  function onSizeChange(val) {
    console.log("onSizeChange", val);
  }

  onMounted(() => {
    getList();
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  });

  return {
    operation_flag,
    is_current,
    search_condition,
    loading,
    columns,
    dataList,
    loadingConfig,
    adaptiveConfig,
    onSizeChange,
    getList,
    openReleaseForm,
    feedata
  };
}

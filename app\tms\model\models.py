from extensions import db



# 修改时间 2025.04.01
class Costcenter(db.Model):
    __bind_key__ = 'tms'
    __tablename__ = 'tms_costcenter'

    id = db.Column(db.BigInteger, primary_key=True)
    costcenter = db.Column(db.String(12))
    dept = db.Column(db.String(12))



# 修改时间 2025.04.01
class Customer(db.Model):
    __bind_key__ = 'tms'
    __tablename__ = 'tms_customer'

    id = db.Column(db.BigInteger, primary_key=True)
    company = db.Column(db.String(32))           # 公司名称
    code = db.Column(db.String(12))              # 客户代码
    province = db.Column(db.String(16))          # 省份
    city = db.Column(db.String(16))              # 城市
    district = db.Column(db.String(16))          # 区县
    address = db.Column(db.String(64))           # 详细地址
    email = db.Column(db.String(64))             # 邮箱



# 修改时间 2025.04.01
class Discount(db.Model):
    __bind_key__ = 'tms'
    __tablename__ = 'tms_discount'

    id = db.Column(db.BigInteger, primary_key=True)
    template = db.Column(db.String(12))
    discount = db.Column(db.Integer)
    createtime = db.Column(db.BigInteger)



# 修改时间 2025.04.01
class Host(db.Model):
    __bind_key__ = 'tms'
    __tablename__ = 'tms_host'

    id = db.Column(db.BigInteger, primary_key=True)
    company = db.Column(db.String(32))
    province = db.Column(db.String(8))
    city = db.Column(db.String(8))
    district = db.Column(db.String(8))
    address = db.Column(db.String(64))
    contact = db.Column(db.String(16))
    phone = db.Column(db.String(64))
    frequency = db.Column(db.BigInteger)
    createtime = db.Column(db.BigInteger)
    updatetime = db.Column(db.BigInteger)
    deletetime = db.Column(db.BigInteger)



# 修改时间 2025.04.01
class QuotationTruck(db.Model):
    __bind_key__ = 'tms'
    __tablename__ = 'tms_quotation_truck'

    id = db.Column(db.BigInteger, primary_key=True)
    template = db.Column(db.String(12))
    province = db.Column(db.String(16))
    city = db.Column(db.String(16))
    district = db.Column(db.String(16))
    truck = db.Column(db.String(6))
    unitprice = db.Column(db.Numeric(10, 2))



# 修改时间 2025.04.01
class QuotationVw(db.Model):
    __bind_key__ = 'tms'
    __tablename__ = 'tms_quotation_vw'

    id = db.Column(db.BigInteger, primary_key=True)
    template = db.Column(db.String(12))
    province = db.Column(db.String(16))
    city = db.Column(db.String(16))
    district = db.Column(db.String(16))
    unit = db.Column(db.String(4))
    lowerlimit = db.Column(db.Integer)
    upperlimit = db.Column(db.Integer)
    unitprice = db.Column(db.Numeric(10, 2))



# 修改时间 2025.04.01
class Shippinglist(db.Model):
    __bind_key__ = 'tms'
    __tablename__ = 'tms_shippinglist'

    id = db.Column(db.BigInteger, primary_key=True)
    deliverdate = db.Column(db.Date)
    direction = db.Column(db.SmallInteger)
    origin_company = db.Column(db.String(32))
    origin_province = db.Column(db.String(16))
    origin_city = db.Column(db.String(16))
    origin_district = db.Column(db.String(16))
    origin_address = db.Column(db.String(255))
    destination_company = db.Column(db.String(32))
    destination_province = db.Column(db.String(16))
    destination_city = db.Column(db.String(16))
    destination_district = db.Column(db.String(16))
    destination_address = db.Column(db.String(255))
    destination_business = db.Column(db.String(4))
    waybill = db.Column(db.String(255))
    measurement = db.Column(db.Integer)
    template = db.Column(db.String(12))
    unitprice = db.Column(db.Numeric(10, 2))
    quantity = db.Column(db.Float(24))
    std_cost = db.Column(db.Numeric(10, 2))
    extra_cost = db.Column(db.Numeric(10, 2))
    discount = db.Column(db.Float(24))
    business = db.Column(db.SmallInteger)
    costcenter = db.Column(db.String(12))
    comments = db.Column(db.String(collation='Chinese_PRC_CI_AS'))
    recorder = db.Column(db.String(6))
    createtime = db.Column(db.BigInteger)
    updatetime = db.Column(db.BigInteger)
    deletetime = db.Column(db.BigInteger)



# 修改时间 2025.04.01
class Supplier(db.Model):
    __bind_key__ = 'tms'
    __tablename__ = 'tms_supplier'

    id = db.Column(db.BigInteger, primary_key=True)
    company = db.Column(db.String(32))
    province = db.Column(db.String(8))
    city = db.Column(db.String(8))
    district = db.Column(db.String(8))
    address = db.Column(db.String(64))
    contact = db.Column(db.String(16))
    mail=db.Column(db.String(64))
    phone = db.Column(db.String(64))
    frequency = db.Column(db.BigInteger)
    createtime = db.Column(db.BigInteger)
    updatetime = db.Column(db.BigInteger)
    deletetime = db.Column(db.BigInteger)



# 修改时间 2025.04.01
class User(db.Model):
    __bind_key__ = 'tms'
    __tablename__ = 'tms_user'

    Id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(50))
    role_ids = db.Column(db.String(50))
    cn_name = db.Column(db.String(4))
    en_name=db.Column(db.String(16))
    eid = db.Column(db.String(50))
    status = db.Column(db.Integer)
    createtime = db.Column(db.DateTime)


class Menu(db.Model):
    __bind_key__ = "tms"
    __tablename__ = "tms_menu"
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(16))
    node_type = db.Column(db.SmallInteger)
    icon = db.Column(db.String(32))
    extra_icon = db.Column(db.String(32))
    path = db.Column(db.String(64))
    redirect = db.Column(db.String(64))
    parent_id = db.Column(db.SmallInteger)
    show_parent = db.Column(db.SmallInteger)
    show_link = db.Column(db.SmallInteger)
    hidden_tag = db.Column(db.SmallInteger)
    rank = db.Column(db.SmallInteger)
    title = db.Column(db.String(32))
    keep_alive = db.Column(db.SmallInteger)
    frame = db.Column(db.String(64))
    frame_loading = db.Column(db.SmallInteger)
    created_time = db.Column(db.DateTime())
    updated_time = db.Column(db.DateTime())


class Role(db.Model):
    __bind_key__ = "tms"
    __tablename__ = "tms_role"
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(32))
    auth = db.Column(db.String(255))
    init_url = db.Column(db.SmallInteger)
    active = db.Column(db.SmallInteger)
    remark = db.Column(db.String(32))
    create_time = db.Column(db.DateTime())
    update_time = db.Column(db.DateTime())

class Master(db.Model):
    __bind_key__ = "tms"
    __tablename__ = "tms_master"
    id = db.Column(db.Integer, primary_key=True)
    SP_NO = db.Column(db.String(32))
    BU_NO = db.Column(db.String(4))
    DN_NO = db.Column(db.String(255))
    DN_DATE = db.Column(db.Date)
    PO_NO = db.Column(db.String(32))
    PO_DATE = db.Column(db.Date)
    cs_specialist = db.Column(db.String(32))
    payer_company = db.Column(db.String(32))
    receiver_name = db.Column(db.String(32))
    receiver_address = db.Column(db.String(80))
    delivery_terms = db.Column(db.String(3))
    dn_attachment = db.Column(db.String(255))
    goods_value = db.Column(db.Float(53))
    volume = db.Column(db.Float(53))
    weight = db.Column(db.Float(53))
    freight_fee = db.Column(db.Float(53))
    fee_remark = db.Column(db.String(50))
    status = db.Column(db.Integer)
    create_time = db.Column(db.DateTime())
    business_type=db.Column(db.Integer)
    warehouse=db.Column(db.Integer)
    shipping_point=db.Column(db.String(4))
    release_time=db.Column(db.DateTime())
    freight_calc_method=db.Column(db.Integer)
    freight_unit_price=db.Column(db.Float(53))
    freight_adjust=db.Column(db.Float(53))
    freight_adjust_reason=db.Column(db.String(255))
    is_valid=db.Column(db.SmallInteger)


class WorkFlow(db.Model):
    __bind_key__ = "tms"
    __tablename__ = "tms_workflow"
    id = db.Column(db.Integer, primary_key=True)
    SP_NO = db.Column(db.String(32))
    action = db.Column(db.String(16))
    owner = db.Column(db.String(16))
    comment = db.Column(db.Text)
    update_time = db.Column(db.DateTime)

class Products(db.Model):
    __bind_key__ = "tms"
    __tablename__ = "tms_products"
    id = db.Column(db.Integer, primary_key=True)
    DN_NO = db.Column(db.String(32))
    item_number = db.Column(db.String(6))
    material_number = db.Column(db.String(32))
    description = db.Column(db.String(64))
    quatity = db.Column(db.Float(53))
    estimated_shipdate = db.Column(db.Date)

class Province(db.Model):
    __bind_key__ = "tms"
    __tablename__ = "tms_province"
    province_code = db.Column(db.String(2), primary_key=True)
    province_name = db.Column(db.String(8))

class City(db.Model):
    __bind_key__ = "tms"
    __tablename__ = "tms_city"
    city_code = db.Column(db.String(4), primary_key=True)
    city_name = db.Column(db.String(12))
    province_code = db.Column(db.String(2))

class District(db.Model):
    __bind_key__ = "tms"
    __tablename__ = "tms_district"
    district_code = db.Column(db.String(6), primary_key=True)
    district_name = db.Column(db.String(16))
    city_code = db.Column(db.String(4))
import{useColumns as p}from"./columns-AwnhnnJ5.js";import{a as m}from"./shift-DH35BNzV.js";import{d as f,p as u,q as h,r as _,o as g,c as v,e as C,y as b,b as y,u as e}from"./index-BnxEuBzx.js";const q=f({__name:"index",props:{search_condition:{},shift:{}},setup(n){const{query_param:a,loading:t,columns:i,loadingConfig:s,dataList:c,adaptiveConfig:r}=p(),o=n,d=u(()=>m(o.shift));return h(()=>{a.search_condition.selecteddate=o.search_condition.selecteddate,a.search_condition.machine=o.search_condition.machine,a.search_condition.shift=o.shift}),(k,w)=>{const l=_("pure-table");return g(),v("div",null,[C("div",null,b(d.value),1),y(l,{ref:"tableRef",border:"",adaptive:"",adaptiveConfig:e(r),"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:e(t),"loading-config":e(s),data:e(c),columns:e(i)},null,8,["adaptiveConfig","loading","loading-config","data","columns"])])}}});export{q as _};

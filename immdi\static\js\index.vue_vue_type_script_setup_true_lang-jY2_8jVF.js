import{R as d}from"./index-CykdORMA.js";import{a2 as F,b7 as B,b8 as S,am as q,p as _,d as E,n as b,r as i,o as f,g as c,h as a,b as e,u as n,C as g,c as I,F as N,t as z,v as D}from"./index-BnxEuBzx.js";const H=F({nickname:[{required:!0,message:"用户昵称为必填项",trigger:"blur"}],username:[{required:!0,message:"用户名称为必填项",trigger:"blur"}],password:[{required:!0,message:"用户密码为必填项",trigger:"blur"}],phone:[{validator:(u,s,r)=>{s===""||B(s)?r():r(new Error("请输入正确的手机号码格式"))},trigger:"blur"}],email:[{validator:(u,s,r)=>{s===""||S(s)?r():r(new Error("请输入正确的邮箱格式"))},trigger:"blur"}]});function J(){const{isDark:u}=q(),s=_(()=>({"--el-switch-on-color":"#6abe39","--el-switch-off-color":"#e84749"})),r=_(()=>v=>v===1?{"--el-tag-text-color":u.value?"#6abe39":"#389e0d","--el-tag-bg-color":u.value?"#172412":"#f6ffed","--el-tag-border-color":u.value?"#274a17":"#b7eb8f"}:{"--el-tag-text-color":u.value?"#e84749":"#cf1322","--el-tag-bg-color":u.value?"#2b1316":"#fff1f0","--el-tag-border-color":u.value?"#58191c":"#ffa39e"});return{isDark:u,switchStyle:s,tagStyle:r}}const P=E({__name:"index",props:{formInline:{default:()=>({title:"新增",eid:"",email:"",role:"",name:"",cnname:"",auth:"",is_active:"",phone:""})}},setup(u,{expose:s}){const r=u,v=b(),{switchStyle:V}=J(),l=b(r.formInline);function x(){return v.value}return s({getRef:x}),(w,t)=>{const p=i("el-input"),m=i("el-form-item"),k=i("el-option"),y=i("el-select"),U=i("el-switch"),h=i("el-row"),C=i("el-form");return f(),c(C,{ref_key:"ruleFormRef",ref:v,model:l.value,rules:n(H),"label-width":"82px"},{default:a(()=>[e(h,{gutter:30},{default:a(()=>[e(n(d),{value:12,xs:24,sm:24},{default:a(()=>[e(m,{label:"用户昵称",prop:"nickname"},{default:a(()=>[e(p,{modelValue:l.value.nickname,"onUpdate:modelValue":t[0]||(t[0]=o=>l.value.nickname=o),clearable:"",placeholder:"请输入用户昵称"},null,8,["modelValue"])]),_:1})]),_:1}),e(n(d),{value:12,xs:24,sm:24},{default:a(()=>[e(m,{label:"用户名称",prop:"username"},{default:a(()=>[e(p,{modelValue:l.value.username,"onUpdate:modelValue":t[1]||(t[1]=o=>l.value.username=o),clearable:"",placeholder:"请输入用户名称"},null,8,["modelValue"])]),_:1})]),_:1}),l.value.title==="新增"?(f(),c(n(d),{key:0,value:12,xs:24,sm:24},{default:a(()=>[e(m,{label:"用户密码",prop:"password"},{default:a(()=>[e(p,{modelValue:l.value.password,"onUpdate:modelValue":t[2]||(t[2]=o=>l.value.password=o),clearable:"",placeholder:"请输入用户密码"},null,8,["modelValue"])]),_:1})]),_:1})):g("",!0),e(n(d),{value:12,xs:24,sm:24},{default:a(()=>[e(m,{label:"手机号",prop:"phone"},{default:a(()=>[e(p,{modelValue:l.value.phone,"onUpdate:modelValue":t[3]||(t[3]=o=>l.value.phone=o),clearable:"",placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1}),e(n(d),{value:12,xs:24,sm:24},{default:a(()=>[e(m,{label:"邮箱",prop:"email"},{default:a(()=>[e(p,{modelValue:l.value.email,"onUpdate:modelValue":t[4]||(t[4]=o=>l.value.email=o),clearable:"",placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1})]),_:1}),e(n(d),{value:12,xs:24,sm:24},{default:a(()=>[e(m,{label:"用户性别"},{default:a(()=>[e(y,{modelValue:l.value.sex,"onUpdate:modelValue":t[5]||(t[5]=o=>l.value.sex=o),placeholder:"请选择用户性别",class:"w-full",clearable:""},{default:a(()=>[(f(!0),I(N,null,z(w.sexOptions,(o,R)=>(f(),c(k,{key:R,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l.value.title==="新增"?(f(),c(n(d),{key:1,value:12,xs:24,sm:24},{default:a(()=>[e(m,{label:"用户状态"},{default:a(()=>[e(U,{modelValue:l.value.status,"onUpdate:modelValue":t[6]||(t[6]=o=>l.value.status=o),"inline-prompt":"","active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"停用",style:D(n(V))},null,8,["modelValue","style"])]),_:1})]),_:1})):g("",!0),e(n(d),null,{default:a(()=>[e(m,{label:"备注"},{default:a(()=>[e(p,{modelValue:l.value.remark,"onUpdate:modelValue":t[7]||(t[7]=o=>l.value.remark=o),placeholder:"请输入备注信息",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}});export{P as _,J as u};

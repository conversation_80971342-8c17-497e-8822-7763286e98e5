import{l as a,aV as t,s as d}from"./index-BnxEuBzx.js";import{h as n}from"./moment-C3TZ8gAF.js";const f=a({id:"prod-info",state:()=>{var e,s,i,o,r,h;return{selectedDate:(s=(e=t().getItem("prod-info"))==null?void 0:e.selectedDate)!=null?s:"",shift:(o=(i=t().getItem("prod-info"))==null?void 0:i.shift)!=null?o:"",machineId:(h=(r=t().getItem("prod-info"))==null?void 0:r.machineId)!=null?h:""}},getters:{},actions:{setData(e){this.selectedDate=n(e.selectedDate).format("YYYY-MM-DD"),this.shift=e.shift,this.machineId=e.machineId,t().setItem("prod-info",{selectedDate:this.selectedDate,shift:this.shift,machineId:this.machineId})}}});function I(){return f(d)}export{I as u};

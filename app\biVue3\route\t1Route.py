from app.dm.functions import getkpi, getproblems, oeeArr
from flask import Blueprint, request
from sqlalchemy import func, not_, and_, or_, cast, Time
import requests
from config import config, env
from app.public.functions import responseGet, responsePost, responseError
from app.dm.model.models_dm import Scaninfo, Shiftinfo, Planinfo, Lineinfo, Issuelog, Restinfo, Probleminfo, Andon, User, Probleminfooee
from app.dm.functions import getReqQty, getReqQtyByShift, andonMsg, dmIssueSubmit, dmIssueChange
from extensions import db
from dateutil import rrule
import traceback
import datetime
api = Blueprint('bi3/t1API', __name__)


@api.route('/getOneline', methods=['GET'])  # 获取小时记录表
def getOneline():
    res = request.args
    dt = res.get('dt')
    linename = res.get('linename')
    lineInfo = {}
    shift = db.session.query(Shiftinfo.linename, Shiftinfo.Id, Shiftinfo.shifttype, Shiftinfo.routing, Shiftinfo.sku).filter(
        func.date(Shiftinfo.starttime) == func.date(dt)).filter(Shiftinfo.finishtime.is_(None)).filter(Shiftinfo.linename == linename).first()
    if shift:
        lineInfo = {
            'shiftid': shift.Id,
            'shifttype': shift.shifttype,
            'linestatus': 1,
            'linename': shift.linename,
            'routing': shift.routing,
            'sku': shift.sku
        }
    else:
        lineInfo = {
            'shiftid': 0,
            'shifttype': '',
            'linestatus': 0,
            'linename': linename,
            'routing': 0,
            'sku': ''
        }
    return responseGet('成功', {'lineInfo': lineInfo})


@api.route('/checkAndon', methods=['GET'])   # 检查安灯是否被登记了
def checkAndon():
    res = request.args
    linename = res.get('linename')
    andontime = res.get('andontime')
    res = requests.get(
        config[env].cloud_url+"welean/bi/issueAPI/checkAndon", params={
            'andontime': andontime,
            'linename': linename
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        try:
            cname = content['data']['cname']
            suggestid = content['data']['id']
            db.session.query(Andon).filter(and_(Andon.starttime == andontime, Andon.linename == linename)).update(
                {'endtime': datetime.datetime.now(), 'cname': cname, 'suggestid': suggestid, 'isactive': 1})
            db.session.commit()
            return responseGet('问题已经由'+cname+'进行登记，请及时完成该问题的处理')
        except Exception:
            db.session.rollback()
            return responseError('系统错误，请联系管理员')
    else:
        return responseError('未检测到有人扫码登记问题，请扫码登记问题后再试')


@api.route('/getAndon', methods=['GET'])   # Tie1页面加载时确认是否有开启的安灯
def getAndon():
    res = request.args
    linename = res.get('linename')
    andon = db.session.query(Andon).filter(
        Andon.linename == linename).filter(Andon.isactive == 0).first()
    linegroup = db.session.query(Lineinfo.linegroup).filter(Lineinfo.linename == linename).first()[0]
    lineurl = 'https://welean.pentair.cn/flaskserver/static/direct/audit?atype=5S&linename=' + linegroup
    if andon:
        starttime = datetime.datetime.strftime(
            andon.starttime, '%Y-%m-%d %H:%M:%S')
        return responseGet('安灯警报待处理中...', {'linename': linename, 'starttime': starttime, 'lineurl': lineurl})
    return responsePost('跳过', {'lineurl': lineurl})


@ api.route("/andonStart", methods=["POST"])   # 开启安灯并通知相应的人
def andonStart():
    res = request.json
    linename = res.get('linename')
    starttime = res.get('starttime')
    print('aaa', linename, starttime)
    try:
        andon = Andon(linename=linename, starttime=starttime, isactive=0)
        phones = []
        line = db.session.query(Lineinfo).filter(
            Lineinfo.linename == linename).first()
        mails = set(line.owner.split(',')+line.manager.split(','))
        users = db.session.query(User).filter(User.email.in_(mails)).all()
        for u in users:
            phones.append('+86'+u.phone)
        andonMsg(phones, mails, linename)
        db.session.add(andon)
        db.session.commit()
        return responsePost('已拉响安灯警报')
    except Exception:
        traceback.print_exc()
        return responseError('出错了，请联系管理员')


@ api.route("/submitIssue", methods=["POST"])  # 登记QDC问题
def submitIssue():
    res = request.json
    Id = res.get('Id')
    if not Id:
        check = dmIssueSubmit(res)
    else:
        check = dmIssueChange(res)
    if check:
        return responsePost('插入成功')
    else:
        return responseError('插入失败，请联系管理员')


@api.route('/getProblemstype', methods=['GET'])   # 根据产线获取问题类型列表
def getProblemstype():
    res = request.args
    linename = res.get('linename')
    linegroup = db.session.query(Lineinfo.linegroup).filter(
        Lineinfo.linename == linename).scalar()
    if linegroup in oeeArr:
        problemtypes = db.session.query(Probleminfooee).filter(
            Probleminfooee.linename == linegroup).all()
    else:
        problemtypes = db.session.query(Probleminfo).outerjoin(Lineinfo, Probleminfo.linename == Lineinfo.linegroup).filter(
            Probleminfo.sqdctype.in_(['质量', '交货', '效率'])).filter(
            or_(Lineinfo.linename == linename, Probleminfo.linename == 'ALL')).all()
    pdic = {}
    for p in problemtypes:
        if p.sqdctype in pdic.keys():
            pdic[p.sqdctype]['children'].append({
                'label': p.problemtype,
                'value': p.problemtype
            })
        else:
            pdic[p.sqdctype] = {
                'label': p.sqdctype,
                'value': p.sqdctype,
                'children': [{
                    'label': p.problemtype,
                    'value': p.problemtype
                }]
            }
    return responseGet('成功', {'problems': list(pdic.values())})


@api.route('/getHH', methods=['GET'])  # 获取小时记录表
def getHH():
    res = request.args
    linename = res.get('linename')
    linegroup = db.session.query(Lineinfo.linegroup).filter(
        Lineinfo.linename == linename).scalar()
    dt = res.get('dt')
    scans = []
    try:
        scans = db.session.query(Shiftinfo.sku, Shiftinfo.shifttype, Shiftinfo.starttime, Shiftinfo.finishtime, Shiftinfo.headcount,
                                 Shiftinfo.routing, Shiftinfo.linename).filter(Shiftinfo.linename == linename).filter(
            not_(and_(cast(func.time(Shiftinfo.starttime), Time) <= '08:00:00', Shiftinfo.shifttype == '夜班', func.datediff(func.date(Shiftinfo.starttime), dt) == 0))).filter(
            not_(and_(Shiftinfo.shifttype == '白班', func.datediff(func.date(Shiftinfo.starttime), dt) == 1))).filter(
            not_(and_(Shiftinfo.shifttype == '夜班', func.datediff(func.date(Shiftinfo.starttime), dt) == 1, cast(func.time(Shiftinfo.starttime), Time) > '08:00:00'))).filter(
            and_(func.datediff(func.date(Shiftinfo.starttime), dt) >= 0, func.datediff(func.date(Shiftinfo.starttime), dt) <= 1)).order_by(Shiftinfo.starttime).all()
    except Exception:
        traceback.print_exc()
        db.session.rollback()
    if len(scans) == 0:
        return responseError('没有生产记录')
    dic = {}
    rests = []
    resttimes = db.session.query(Restinfo).outerjoin(
        Lineinfo, Restinfo.linegroup == Lineinfo.linegroup).filter(Lineinfo.linename == linename).all()
    if len(resttimes) == 0:
        resttimes = db.session.query(Restinfo).filter(
            Restinfo.linegroup == 'all').all()
    for r in resttimes:
        mydt = dt
        if str(r.resttime) < '08:00:00':
            mydt = (datetime.datetime.strptime(dt, '%Y-%m-%d') +
                    datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        rests.append({
            'reststart': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S'),
            'restfinish': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S')+datetime.timedelta(minutes=r.restmin)
        })
    step = 1
    start = ''
    accumreq = 0
    accumscan = 0
    # print('aaaaaaaaaaaaaaaaaaaaaaaa')
    for s in scans:
        # print(**************, s)
        start = s.starttime
        hfin = s.starttime+datetime.timedelta(hours=step)
        currentTime = datetime.datetime.strftime(
            datetime.datetime.now(), '%H:%M:%S')
        if not s.finishtime:
            finishtime = datetime.datetime.strptime(
                dt + ' '+currentTime, '%Y-%m-%d %H:%M:%S')
        else:
            finishtime = s.finishtime
        rst = getHour(dic, s.sku, s.headcount, s.linename, start,
                      hfin, finishtime, rests, s.routing, s.shifttype, step, accumreq, accumscan)
        dic = rst[0]
        accumreq = rst[1]
        accumscan = rst[2]
    allscan = db.session.query(func.sum(Scaninfo.scanqty)).join(Shiftinfo, Scaninfo.shiftid == Shiftinfo.Id).filter(
        Shiftinfo.linename == linename).filter(func.date(Shiftinfo.starttime) == func.date(dt)).scalar()
    hhArr = list(dic.values())
    if allscan > hhArr[len(hhArr)-1]['accumscan']:
        hhArr[len(hhArr)-1]['scanqty'] = hhArr[len(hhArr)-1]['scanqty'] + \
            (allscan-hhArr[len(hhArr)-1]['accumscan'])
        hhArr[len(hhArr)-1]['accumscan'] = allscan
    for i in range(len(hhArr)):
        if i > 0:
            if hhArr[i]['starttime'] < hhArr[i-1]['finishtime']:
                hhArr[i-1]['finishtime'] = hhArr[i]['starttime']
    try:
        suggestArr = getDailyIssues(linegroup, dt)
        print(**************1111, linename, linegroup, dt, suggestArr)
        for s in suggestArr:
            itime = s['idate']
            qty = 0
            issuemin = 0
            issueid = 0
            if s['auditid']:
                dmissue = db.session.query(Issuelog).filter(
                    Issuelog.Id == s['auditid']).first()
                qty = dmissue.qty
                issuemin = dmissue.issuemin
                issueid = dmissue.Id
            info = {
                'issueid': issueid,
                'sqdctype': 'MDI',
                'problemtype': s['stype'],
                'recorder': s['cname'],
                'desc': s['content'],
                'qty': qty,
                'issuemin': issuemin
            }
            av = 1
            for i in range(len(hhArr)):
                if hhArr[i]['starttime'] <= itime and hhArr[i]['finishtime'] > itime:
                    hhArr[i]['issues'].append(info)
                    av = 0
            if av:
                hhArr[len(hhArr)-1]['issues'].append(info)
    except Exception:
        traceback.print_exc()
    outArr = hhArr[::-1]
    # print('bbbbbbbbbbbbbbbbbbb', outArr)
    return responseGet('success', {'tableData': outArr})


def getDailyIssues(linename, shiftdate):  # 获取当日的问题清单
    res1 = requests.get(
        config[env].cloud_url +
        "welean/bi/issueAPI/getDailyIssues?linename=%s&shiftdate=%s" % (
            linename, shiftdate)
    )
    content = res1.json()
    print(content)
    if content and content["meta"]["status"] == 200:
        return content['data']['suggests']
    return []


def getHour(dic, sku, headcount, linename, st, ft, finishtime, rests, rt, shifttype, step, accumreq, accumscan):  # 深度遍历每个班次，返回计算相应的数据，的到每个小时的记录
    if ft >= finishtime:
        reqqty = getReqQtyByShift(headcount, st, finishtime, rests, rt)
        scanqty = getScanqty(linename, st, finishtime, sku)
        accumreq = reqqty+accumreq
        accumscan = scanqty+accumscan
        dic[sku+datetime.datetime.strftime(st, '%H-%M')] = {
            'sku': sku,
            'shifttype': shifttype,
            'routing': rt,
            'scanqty': scanqty,
            'reqqty': round(reqqty, 0),
            'accumscan': accumscan,
            'accumreq': round(accumreq, 0),
            'starttime': datetime.datetime.strftime(st, '%H:%M'),
            'finishtime': datetime.datetime.strftime(finishtime, '%H:%M'),
            'issues': []
        }
        return [dic, accumreq, accumscan]
    else:
        reqqty = getReqQtyByShift(headcount, st, ft, rests, rt)
        scanqty = getScanqty(linename, st, ft, sku)
        accumreq = reqqty+accumreq
        accumscan = scanqty+accumscan
        dic[sku+datetime.datetime.strftime(st, '%H-%M')] = {
            'sku': sku,
            'shifttype': shifttype,
            'routing': rt,
            'scanqty': scanqty,
            'reqqty': round(reqqty, 0),
            'accumscan': accumscan,
            'accumreq': round(accumreq, 0),
            'starttime': datetime.datetime.strftime(st, '%H:%M'),
            'finishtime': datetime.datetime.strftime(ft, '%H:%M'),
            'issues': []
        }
        newst = ft
        newft = ft+datetime.timedelta(hours=step)
        dtHour = rrule.rrule(rrule.HOURLY, dtstart=newst, until=newft).count()
        dtMin = newft.minute
        if (dtHour > 0 and dtMin != 0):
            newft = datetime.datetime.strptime(datetime.datetime.strftime(
                newft, '%Y-%m-%d %H:00:00'), '%Y-%m-%d %H:00:00')
        return getHour(dic, sku, headcount, linename, newst, newft,
                       finishtime, rests, rt, shifttype,  step, accumreq, accumscan)


def getScanqty(linename, st, ft, sku):   # 获取某个时间段内某条产线某个sku的扫描数量
    scanqty = db.session.query(func.sum(Scaninfo.scanqty)).join(Shiftinfo, Scaninfo.shiftid == Shiftinfo.Id).filter(
        and_(Shiftinfo.linename == linename, Scaninfo.sku == sku)).filter(and_(Scaninfo.scantime >= st, Scaninfo.scantime < ft)).scalar()
    if scanqty:
        return scanqty
    return 0


@ api.route('/getWeekChart', methods=['GET'])  # 获取Tie1里QDC图标的信息
def getWeekChart():
    r = request.args
    stime = r.get('stime')
    etime = r.get('etime')
    linename = r.get('linename')
    linegroup = db.session.query(Lineinfo.linegroup).filter(
        Lineinfo.linename == linename).scalar()
    days = (datetime.datetime.strptime(etime, '%Y-%m-%d') -
            datetime.datetime.strptime(stime, '%Y-%m-%d')).days
    kpi = getkpi(stime.split('-')[0], linegroup)
    kpiq = kpi['qtarget']
    kpid = kpi['dtarget']
    kpic = kpi['ctarget']
    shifts = db.session.query(func.date(Shiftinfo.starttime).label('shiftdate'), func.sum(Scaninfo.scanqty).label('pqty')).join(
        Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).filter(Shiftinfo.linename == linename).filter(
        Shiftinfo.starttime.between(stime, etime)).group_by(func.date(Shiftinfo.starttime)).all()
    qdata = {
        'ftt': [],
        'pd': [],
        'fttmin': 50,
        'fttx': [],
        'green': [],
        'red': [],
        'yellow': [],
        'paretodata': [],
        'paretotarget': [],
        'paretox': []
    }
    ddata = {
        'ftt': [],
        'pd': [],
        'fttmin': 50,
        'fttx': [],
        'green': [],
        'red': [],
        'yellow': [],
        'paretodata': [],
        'paretotarget': [],
        'paretox': []
    }
    cdata = {
        'ftt': [],
        'pd': [],
        'fttmin': 50,
        'fttx': [],
        'green': [],
        'red': [],
        'yellow': [],
        'paretodata': [],
        'paretotarget': [],
        'paretox': []
    }
    for p in shifts:
        sdate = datetime.datetime.strftime(p.shiftdate, '%m-%d')
        qdata['fttx'].append(sdate)
        qdata['pd'].append(p.pqty)
        qdata['ftt'].append(100)
        qdata['green'].append(100-kpiq*100)
        qdata['red'].append(kpiq*95)
        qdata['yellow'].append(kpiq*5)
    problems = getproblems(linegroup, '', days)
    plans = db.session.query(Planinfo.plandate, func.sum(Planinfo.qty).label('qty'), func.sum(func.if_(Planinfo.status >= 2, 1, 0)).label('fail'), func.sum(
        func.if_(Planinfo.status == 1, 1, 0)).label('achieve')).filter(Planinfo.linename == linename).filter(
        Planinfo.plandate.between(stime, etime)).filter(Planinfo.status > 0).group_by(Planinfo.plandate).all()
    for p in plans:
        pdate = datetime.datetime.strftime(p.plandate, '%m-%d')
        ddata['fttx'].append(pdate)
        ttplan = p.achieve+p.fail
        ftt = round(p.achieve/ttplan*100, 0)
        if ftt < 0:
            ftt = 0
        ddata['ftt'].append(ftt)
        ddata['green'].append(100-kpid*100)
        ddata['red'].append(kpid*95)
        ddata['yellow'].append(kpid*5)
    restList = db.session.query(Restinfo).all()
    costs = db.session.query(func.date(Shiftinfo.starttime).label('shiftdate'), Lineinfo.linegroup, func.sum(Scaninfo.scanqty).label('pqty'), Shiftinfo.headcount,
                             Shiftinfo.starttime, Shiftinfo.finishtime, Shiftinfo.routing).join(Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).filter(
        Shiftinfo.linename == linename).filter(Shiftinfo.starttime.between(stime, etime)).join(Lineinfo, Shiftinfo.linename == Lineinfo.linename).filter(
        not_(Shiftinfo.finishtime.is_(None))).group_by(Scaninfo.shiftid).all()
    scanDic = {}
    for s in costs:
        shiftdate = datetime.datetime.strftime(s.starttime, '%Y-%m-%d')
        if shiftdate in scanDic.keys():
            pqty = s.pqty
            rqty = getReqQty(shiftdate, s.headcount, s.linegroup,
                             s.starttime, s.finishtime, restList, s.routing)
            scanDic[shiftdate]['pqty'] = scanDic[shiftdate]['pqty']+pqty
            scanDic[shiftdate]['rqty'] = scanDic[shiftdate]['rqty']+rqty
        else:
            scanDic[shiftdate] = {
                'shiftdate': shiftdate,
                'pqty': s.pqty,
                'rqty': getReqQty(shiftdate, s.headcount, s.linegroup, s.starttime, s.finishtime, restList, s.routing)
            }
    for p in scanDic.values():
        sdate = p['shiftdate'].split('-')
        cdata['fttx'].append(sdate[1]+'-'+sdate[2])
        cdata['pd'].append(p['pqty'])
        ftt = round(int(p['pqty'])/p['rqty']*100, 0)
        if ftt < 0:
            ftt = 0
        cdata['ftt'].append(ftt)
        cdata['green'].append(100-kpic*100)
        cdata['red'].append(kpic*95)
        cdata['yellow'].append(kpic*5)
    for p in problems:
        if p['sqdctype'] == '质量':
            qdata['paretox'].append(p['ptype'])
            qdata['paretotarget'].append(p['trigger'])
            qdata['paretodata'].append(0)
        elif p['sqdctype'] == '交货':
            ddata['paretox'].append(p['ptype'])
            ddata['paretotarget'].append(p['trigger'])
            ddata['paretodata'].append(0)
        elif p['sqdctype'] == '效率':
            cdata['paretox'].append(p['ptype'])
            cdata['paretotarget'].append(p['trigger'])
            cdata['paretodata'].append(0)
    qdata['paretox'].append('未分类')
    qdata['paretotarget'].append(round(5/7*days))
    qdata['paretodata'].append(0)
    ddata['paretox'].append('未分类')
    ddata['paretotarget'].append(round(5/7*days))
    ddata['paretodata'].append(0)
    cdata['paretox'].append('未分类')
    cdata['paretotarget'].append(round(5/7*days))
    cdata['paretodata'].append(0)
    listData = []
    issues = db.session.query(Issuelog).filter(Issuelog.linename == linename).filter(
        Issuelog.shiftdate.between(stime, etime)).order_by(Issuelog.sqdctype).all()
    for i in issues:
        if i.sqdctype == '质量':
            if i.problemtype in qdata['paretox']:
                qdata['paretodata'][qdata['paretox'].index(
                    i.problemtype)] += i.qty
            else:
                qdata['paretodata'][len(qdata['paretodata'])-1] += i.qty
            sftdate = datetime.datetime.strftime(i.shiftdate, '%m-%d')
            if sftdate in qdata['fttx']:
                idx = qdata['fttx'].index(sftdate)
                ftt = round((qdata['pd'][idx]-qdata['pd'][idx] *
                             (100-qdata['ftt'][idx])-i.qty)/qdata['pd'][idx]*100, 0)
                qdata['ftt'][idx] = ftt if ftt > 0 else 0
        elif i.sqdctype == '交货':
            if i.problemtype in ddata['paretox']:
                ddata['paretodata'][ddata['paretox'].index(i.problemtype)] += 1
            else:
                ddata['paretodata'][len(ddata['paretodata'])-1] += 1
        elif i.sqdctype == '效率':
            if i.problemtype in cdata['paretox']:
                cdata['paretodata'][cdata['paretox'].index(
                    i.problemtype)] += i.qty
            else:
                cdata['paretodata'][len(cdata['paretodata'])-1] += i.qty
        listData.append({
            'sqdctype': i.sqdctype,
            'recorddate': datetime.datetime.strftime(i.shiftdate, "%Y-%m-%d"),
            'desc': i.desc,
            'linename': i.linename,
            'qty': i.qty,
            'problemtype': i.problemtype if i.problemtype else '其他'
        })
    maxqty = 100
    if len(cdata['ftt']) > 0:
        cdata['fttmin'] = min(cdata['ftt']) if min(cdata['ftt']) < 100 else 80
        maxqty = max(cdata['ftt']) if max(cdata['ftt']) > 100 else 100
    for j in range(len(cdata['green'])):
        cdata['green'][j] = int(maxqty)-kpic*100 + \
            5 if (kpic*100 < int(maxqty)) else 5
    maxqty = 100
    if len(qdata['ftt']) > 0:
        qdata['fttmin'] = min(qdata['ftt']) if min(qdata['ftt']) < 100 else 80
        maxqty = max(qdata['ftt']) if max(qdata['ftt']) > 100 else 100
    for j in range(len(qdata['green'])):
        qdata['green'][j] = int(maxqty)-kpiq*100 + \
            5 if (kpiq*100 < int(maxqty)) else 5
    maxqty = 100
    if len(ddata['ftt']) > 0:
        ddata['fttmin'] = min(ddata['ftt']) if min(ddata['ftt']) < 100 else 80
        maxqty = max(ddata['ftt']) if max(ddata['ftt']) > 100 else 100
    for j in range(len(ddata['green'])):
        ddata['green'][j] = int(maxqty)-kpid*100 + \
            5 if (kpid*100 < int(maxqty)) else 5
    return responseGet('获取成功', {'qdata': qdata, 'ddata': ddata, 'cdata': cdata, 'listData': listData})


@ api.route('/getMDIplan', methods=['GET'])  # 获取Tie1内的计划信息
def getMDIplan():
    res = request.args
    linename = res.get('linename')
    dt = res.get('dt')
    shifts = db.session.query(Shiftinfo.sku, Shiftinfo.shifttype, Shiftinfo.starttime, Shiftinfo.finishtime, Shiftinfo.routing,
                              func.sum(Scaninfo.scanqty).label('scanqty'), Shiftinfo.headcount).join(
        Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).filter(Shiftinfo.linename == linename).filter(
        func.date(Shiftinfo.starttime) == dt).group_by(Shiftinfo.sku).order_by(Shiftinfo.starttime).all()
    plans = db.session.query(Planinfo).filter(
        Planinfo.plandate == dt).filter(Planinfo.linename == linename).order_by(Planinfo.shifttype, Planinfo.sequence).all()
    pdic = {}
    print(plans)
    for p in plans:
        pdic[p.sku] = {
            'plandate': datetime.datetime.strftime(p.plandate, '%Y-%m-%d'),
            'shifttype': p.shifttype,
            'sku': p.sku,
            'planqty': p.qty,
            'planner': p.planner
        }
    sdic = {}
    for s in shifts:
        if s.sku in pdic.keys():
            sdic[s.sku] = {
                'sku': s.sku,
                'starttime': datetime.datetime.strftime(s.starttime, '%Y-%m-%d %H-%M-%S') if s.starttime else '',
                'finishtime': datetime.datetime.strftime(s.finishtime, '%Y-%m-%d %H-%M-%S') if s.finishtime else '',
                'shifttype': s.shifttype,
                'scanqty': s.scanqty,
                'status': '已完成' if s.finishtime else '生产中'
            }
            sdic[s.sku]['planqty'] = pdic[s.sku]['planqty']
            print(pdic[s.sku]['planqty'], s.scanqty)
            sdic[s.sku]['resttime'] = s.routing * \
                (pdic[s.sku]['planqty']-s.scanqty)/s.headcount*3600
        else:
            sdic[s.sku] = {
                'sku': s.sku,
                'starttime': datetime.datetime.strftime(s.starttime, '%Y-%m-%d %H-%M-%S') if s.starttime else '',
                'finishtime': datetime.datetime.strftime(s.finishtime, '%Y-%m-%d %H-%M-%S') if s.finishtime else '',
                'shifttype': s.shifttype,
                'scanqty': s.scanqty,
                'status': '已完成' if s.finishtime else '生产中',
                'planqty': '无计划',
                'resttime': 0
            }
    outArr = list(sdic.values())
    for p in plans:
        if p.sku not in sdic.keys():
            outArr.append({
                'sku': p.sku,
                'starttime': '',
                'finishtime': '',
                'scanqty': 0,
                'shifttype': p.shifttype,
                'status': '当日计划',
                'planqty': p.qty,
                'resttime': 0
            })
    return responseGet('成功', outArr)

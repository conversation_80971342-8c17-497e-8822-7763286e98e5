from flask import Blueprint, request, send_from_directory

from app.cal.model.models_cal import Dcal, Dcal_instep, Dcal_standcaleq, Dcal_user, Dcal_cal_version
from openpyxl import load_workbook, Workbook
from datetime import datetime
from extensions import db
from sqlalchemy import func, case
import os
import json
from app.public.functions import responseError,  responseGet
# from app.voc.functions import getServer, login_required, getLastyear
from app.cal.functions import login_required, getServer
from ultils.log_helper import ProjectLogger
from copy import copy
import time
# from app.voc.schemas import tasks_schema, task_schema, dvoc_schema, dship_schema
# from app.cal.model.models_cal import Dcal, Dcal_instep, Dcal_standcaleq, Dcal_user, Dcal_cal_version
api = Blueprint('cal/settingAPI', __name__)

mylogger = ProjectLogger()


@api.route('/downloadcalrep', methods=['GET'])
@login_required
def downloadcalrep():
    mylogger.debug('downloadcalrep')
    res = request.args
    mylogger.debug(res.get('url'))
    resdic = json.loads(res.get('url'))
    path = getServer()['templatePath']+'downloads/'
    filename = 'Suzhou-calTemplate.xlsx'
    # drow = len(outData)
    wb = load_workbook(path+filename, keep_vba=True)
    ws = wb['Sheet1']
    # for sheet in ws:
    addr1 = len(resdic.get('checkinstrum'))
    addr2 = len(resdic.get('checktask'))
    if addr1 > 0:
        ws.insert_rows(12, addr1)
    if addr2 > 0:
        ws.insert_rows(16+addr1, addr2)
    # ws.cell(14, 1).border = copy(ws.cell(5, 4).border)
    if addr1 > 0:
        for i in range(addr1):
            ws.cell(12+i, 1).border = copy(ws.cell(5, 4).border)
            ws.cell(12+i, 2).border = copy(ws.cell(5, 4).border)
            ws.cell(12+i, 3).border = copy(ws.cell(5, 4).border)
            ws.cell(12+i, 4).border = copy(ws.cell(5, 4).border)
            ws.cell(12+i, 5).border = copy(ws.cell(5, 4).border)

            ws.cell(12+i, 1).value = resdic.get('checkinstrum')[i]['insno']
            ws.cell(12+i, 2).value = resdic.get('checkinstrum')[i]['sname']
            ws.cell(12+i, 3).value = resdic.get('checkinstrum')[i]['mfg_model']
            ws.cell(12+i, 4).value = resdic.get('checkinstrum')[i]['serno']
            ws.cell(12+i, 5).value = resdic.get('checkinstrum')[i].get('duedate', '')
    if addr2 > 0:
        for i in range(addr2):
            ws.cell(16+addr1+i, 1).border = copy(ws.cell(5, 4).border)
            ws.cell(16+addr1+i, 2).border = copy(ws.cell(5, 4).border)
            ws.cell(16+addr1+i, 3).border = copy(ws.cell(5, 4).border)
            ws.cell(16+addr1+i, 4).border = copy(ws.cell(5, 4).border)
            ws.cell(16+addr1+i, 5).border = copy(ws.cell(5, 4).border)

            ws.cell(16+addr1+i, 1).value = resdic.get('checktask')[i]['checkname']
            ws.cell(16+addr1+i, 2).value = resdic.get('checktask')[i]['standard']
            ws.cell(16+addr1+i, 3).value = resdic.get('checktask')[i]['reading']
            ws.cell(
                16+addr1+i, 4).value = round((resdic.get('checktask')[i]['reading'] - resdic.get('checktask')[i]['standard']), 5)
            ws.cell(16+addr1+i, 5).value = resdic.get('checktask')[i]['spec']
        # mylogger.debug(ws.cell(12, 1).border)
    # mylogger.debug(9998881)
    # sheet.insert_rows(14, 15)
    ws.cell(4, 2).value = resdic.get('instrumentno')
    ws.cell(4, 5).value = resdic.get('locno')
    ws.cell(5, 2).value = resdic.get('instrumentname')
    ws.cell(5, 5).value = resdic.get('caldata')
    ws.cell(6, 2).value = resdic.get('factorysign')
    ws.cell(6, 5).value = resdic.get('duedate')
    ws.cell(7, 2).value = resdic.get('intype')
    ws.cell(7, 5).value = resdic.get('temperature')
    ws.cell(8, 2).value = resdic.get('factoryno')
    ws.cell(8, 5).value = resdic.get('humidity')
    ws.cell(9, 2).value = resdic.get('refdoc')
    ws.cell(9, 5).value = resdic.get('checkresultall')
    #         else:
    #             ws.cell(i, j).value = ''
    newFilename = 'cal111.xlsm'
    wb.save(path+newFilename)
    wb.close()
    # newFilename = 'skubatch_f723dd637c09c270.xlsx'
    # path = getServer()['uploadPath']+'temp/'
    if os.path.isfile(os.path.join(path, newFilename)):
        response = send_from_directory(path, newFilename, as_attachment=True)
        response.headers["Access-Control-Expose-Headers"] = "fname"
        response.headers['fname'] = newFilename
        return response
    return responseError('没有找到下载文件')


@api.route('/downloadcarreport', methods=['GET'])
@login_required
def downloadcarreport():
    mylogger.debug('downloadcarreport')
    res = request.args
    mylogger.debug(res.get('url'))
    resdic = json.loads(res.get('url'))
    path = getServer()['templatePath']+'downloads/'
    filename = 'Suzhou-carflowTemplate.xlsx'
    # drow = len(outData)
    wb = load_workbook(path+filename, keep_vba=True)
    ws = wb['Sheet1']
    # for sheet in ws:
    ws.cell(4, 3).value = resdic.get('deptment')
    ws.cell(4, 5).value = resdic.get('keeper')
    ws.cell(5, 3).value = resdic.get('instrumentno')
    ws.cell(5, 5).value = resdic.get('instrumentname')
    ws.cell(6, 3).value = resdic.get('processre')
    ws.cell(6, 5).value = resdic.get('verdate')
    if resdic.get('checkbox2') == 1:
        ws.cell(7, 2).value = '☑ 量具损坏 Gage Damage'
        ws.cell(7, 4).value = '☐ 量具遗失 Gage Lose'
    else:
        ws.cell(7, 2).value = '☐ 量具损坏 Gage Damage'
        ws.cell(7, 4).value = '☑ 量具遗失 Gage Lose'
    ws.cell(9, 2).value = resdic.get('damagedes')
    ws.cell(16, 2).value = resdic.get('carrootcase')
    ws.cell(23, 2).value = resdic.get('carcorrective')
    ws.cell(29, 2).value = resdic.get('carstandardization')
    if resdic.get('carimprovetag') == 1:
        ws.cell(36, 2).value = '是否有类似的量具需要改善 ？Is there any same Gage need to improve?   ☑ 是    ☐ 否'
    else:
        ws.cell(36, 2).value = '是否有类似的量具需要改善 ？Is there any same Gage need to improve?   ☐ 是    ☑ 否'
    ws.cell(37, 3).value = resdic.get('deptA')
    ws.cell(37, 5).value = resdic.get('deptA')+'@pentair.com'
    ws.cell(40, 2).value = resdic.get('carevidence')
    ws.cell(40, 4).value = resdic.get('caraccepter')
    ws.cell(40, 5).value = resdic.get('carclosedate')
    newFilename = 'car_report.xlsm'
    wb.save(path+newFilename)
    wb.close()
    if os.path.isfile(os.path.join(path, newFilename)):
        response = send_from_directory(path, newFilename, as_attachment=True)
        response.headers["Access-Control-Expose-Headers"] = "fname"
        response.headers['fname'] = newFilename
        return response
    return responseError('没有找到下载文件')


@api.route('/downloadneedcal', methods=['GET'])
@login_required
def downloadneedcal():
    mylogger.debug('downloadneedcal')
    res = request.args
    res = json.loads(res.get('url'))
    # res = request.args.get('url')

    # mylogger.debug(res.get('url'))
    option = res.get('oploption')
    calvalue = res.get('vocvalue')
    calstatus = res.get('calstatus')
    measurestatus = res.get('measurestatus')
    filter1 = res.get('filter1')
    vaildvalue1 = res.get('vaildvalue1')
    vaildvalue2 = res.get('vaildvalue2')

    cal = db.session.query(Dcal_cal_version.measurestatus.label('measurestatus'), Dcal.precal.label('precal'), Dcal.caltype.label('caltype'), Dcal.keeper.label('keeper'), Dcal.deptment.label('deptment'), Dcal.instrumentno.label('instrumentno'), Dcal.instrumentname.label('instrumentname'), Dcal.factorysign.label('factorysign'), Dcal.type.label('type'),  Dcal.factoryno.label(
        'factoryno'), Dcal.measureparameter.label('measureparameter'), Dcal.locno.label('locno'), Dcal.location.label('location'), Dcal.checkmode.label('checkmode'), Dcal_cal_version.calperiod.label('calperiod'), Dcal_cal_version.caldata.label('caldata'), Dcal_cal_version.duedate.label('duedate')).outerjoin(Dcal_cal_version, Dcal.id == Dcal_cal_version.calid)

    if calvalue != '':
        if option == '仪器编号':
            tag = Dcal.instrumentno
        elif option == '仪器名称':
            tag = Dcal.instrumentname
        elif option == 'id':
            tag = Dcal.id
        elif option == '厂牌':
            tag = Dcal.factorysign
        elif option == '型号':
            tag = Dcal.type
        elif option == '校验类别':
            tag = Dcal.checkmode
        # cal = db.session.query(Dcal).filter(tag.like('%{0}%'.format(calvalue)))
        # total = db.session.query(Dcal).filter(tag.like('%{0}%'.format(calvalue)))
        cal = cal.filter(tag.like('%{0}%'.format(calvalue)))

    # else:
    #     cal = db.session.query(Dcal)
    #     total = db.session.query(Dcal)
    if calstatus != '':
        if calstatus == '尚未校验':
            cal = cal.filter(Dcal_cal_version.checkstatus == 1)

        elif calstatus == '校验中':
            cal = cal.filter(Dcal_cal_version.checkstatus == 2)

        else:
            cal = cal.filter(Dcal_cal_version.checkstatus == 3)

    if measurestatus != '':
        cal = cal.filter(Dcal_cal_version.measurestatus == measurestatus)

    mylogger.debug(filter1)
    mylogger.debug(1111)
    if filter1 == 'true':
        mylogger.debug(222)
        times = time.localtime()
    # .filter(func.datediff(text('day'), func.current_date(), Dcal_cal_version.duedate) >= 0)
        cal = cal.filter(func.datediff(text('day'), func.current_date(),
                         Dcal_cal_version.duedate) <= 37).filter(Dcal_cal_version.checkstatus != 3)

    if vaildvalue1 != '':
        cal = cal.filter(Dcal_cal_version.duedate.between(vaildvalue1, vaildvalue2))
    cal = cal.all()
    # mylogger.debug(cal)
    outData = []
    for i in cal:
        outData.append({'measurestatus': i.measurestatus, 'precal': i.precal, 'caltype': i.caltype, 'keeper': i.keeper,
                        'deptment': i.deptment, 'instrumentno': i.instrumentno, 'instrumentname': i.instrumentname, 'factorysign': i.factorysign, 'type': i.type,
                        'factoryno': i.factoryno, 'measureparameter': i.measureparameter, 'locno': i.locno, 'location': i.location, 'checkmode': i.checkmode,
                        'calperiod': i.calperiod, 'caldata': i.caldata, 'duedate': i.duedate})
    path = getServer()['templatePath']+'downloads/'
    filename = 'Suzhou-needcalTemplate.xlsx'
    # mylogger.debug(outData)
    drow = len(outData)
    wb = load_workbook(path+filename, keep_vba=True)
    ws = wb['校验列表']
    for i in range(2, drow+3):
        for j in range(1, 18):
            if i-2 < drow:
                if j == 1:
                    ws.cell(i, j).value = outData[i-2]['measurestatus']
                elif j == 2:
                    ws.cell(i, j).value = outData[i-2]['precal']
                elif j == 3:
                    ws.cell(i, j).value = outData[i-2]['caltype']
                elif j == 4:
                    ws.cell(i, j).value = outData[i-2]['keeper']
                elif j == 5:
                    ws.cell(i, j).value = outData[i-2]['deptment']
                elif j == 6:
                    ws.cell(i, j).value = outData[i-2]['instrumentno']
                elif j == 7:
                    ws.cell(i, j).value = outData[i-2]['instrumentname']
                elif j == 8:
                    ws.cell(i, j).value = outData[i-2]['factorysign']
                elif j == 9:
                    ws.cell(i, j).value = outData[i-2]['type']
                elif j == 10:
                    ws.cell(i, j).value = outData[i-2]['factoryno']
                elif j == 11:
                    ws.cell(i, j).value = outData[i-2]['measureparameter']
                elif j == 12:
                    ws.cell(i, j).value = outData[i-2]['locno']
                elif j == 13:
                    ws.cell(i, j).value = outData[i-2]['location']
                elif j == 14:
                    ws.cell(i, j).value = outData[i-2]['checkmode']
                elif j == 15:
                    ws.cell(i, j).value = outData[i-2]['calperiod']
                elif j == 16:
                    ws.cell(i, j).value = outData[i-2]['caldata']
                elif j == 17:
                    ws.cell(i, j).value = outData[i-2]['duedate']
                # elif j == 18:
                #     ws.cell(i, j).value = outData[i-2]['caltype']
            else:
                ws.cell(i, j).value = ''
    newFilename = 'Suzhou'+' callist.xlsm'
    wb.save(path+newFilename)
    wb.close()
    if os.path.isfile(os.path.join(path, newFilename)):
        response = send_from_directory(path, newFilename, as_attachment=True)
        response.headers["Access-Control-Expose-Headers"] = "fname"
        response.headers['fname'] = newFilename
        return response
    return responseError('没有找到下载文件')

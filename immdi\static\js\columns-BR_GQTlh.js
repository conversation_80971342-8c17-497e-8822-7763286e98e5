import{i as C}from"./dashboard-dtTxmf4X.js";import{c as D}from"./index-CA30dg9C.js";import{c as k}from"./index-Ctm3qPP9.js";import{n as f,a2 as h,G as R,q as j,b as a,f as _,r as c,aZ as F,a_ as O,aX as S,aj as z,a$ as A}from"./index-BnxEuBzx.js";import{show_param as v}from"./index-CII2mH6h.js";import{h as o}from"./moment-C3TZ8gAF.js";import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-kKB7wNFU.js";import"./editStateForm-BQH9QYuy.js";import"./index.vue_vue_type_script_setup_true_lang-DEM1uiK0.js";import"./shift-DH35BNzV.js";import"./editPnForm-mTeSfSrW.js";import"./prod-CfeywgVC.js";import"./param-DuQmBhrx.js";import"./columns-AwnhnnJ5.js";import"./index.vue_vue_type_script_setup_true_lang-CPCUbDea.js";import"./columns-BirzvcTI.js";function H(){return o().hour()}function u(){const i=H();return i>=8&&i<16?"A":i>=16&&i<24?"B":"C"}function q(i){return typeof i=="function"||Object.prototype.toString.call(i)==="[object Object]"&&!A(i)}function w(){const i=f([]),d=f(!0),r=f(!0),y=[{label:"机台",prop:"id",width:"60px",cellRenderer:({row:e})=>a("div",null,[e.id,_("#")])},{label:"吨位",prop:"ton",width:"70px",cellRenderer:({row:e})=>a("div",null,[e.ton,_("T")])},{label:"计划停机时间",prop:"planned_stop",hide:!0,width:"120px"},{label:"异常停机",prop:"downtimecount",hide:!0,width:"120px"},{label:"有效时间",prop:"uptime",hide:!0},{label:"标准时间",prop:"stdtime",hide:!0},{label:"联机",prop:"conn",width:"60px",hide:()=>!r.value,cellRenderer:({row:e})=>e.conn?a("div",{style:"display:flex;justify-content:space-around;color:green;"},[a(c("el-icon"),{onClick:()=>{v(e.id,e.pn)},size:24},{default:()=>[a(F,null,null)]})]):a("div",{style:"display:flex;justify-content:space-around;color:grey;"},[a(c("el-icon"),{size:"24",onClick:()=>{v(e.id,e.pn)}},{default:()=>[a(O,null,null)]})])},{label:"状态",width:"120px",hide:()=>!r.value,cellRenderer:({row:e})=>{let t="",l="",p="";return e.state_type==1?(t="green",p="12px",l="white"):e.state_type==2?t="yellow":e.state_type==3?(t="red",l="white"):e.state_type==4&&(t="grey",l="white"),a("div",{style:{backgroundColor:t,fontSize:p,color:l},onClick:()=>D(e.id,n.selecteddate,n.shift,e.state_id,void 0,void 0,void 0,"add",()=>{s()})},[e.state_name])}},{label:"料号",prop:"pn",width:"310px",cellRenderer:({row:e})=>{if(e.pn){const t=e.pn.split(/[/,@]/);return a("div",{style:"display:flex;justify-content:space-around",onClick:()=>k(e.id,e.pn,()=>{s()})},[t.map((l,p)=>a(c("el-tag"),{key:p},q(l)?l:{default:()=>[l]}))])}}},{label:"班产量",prop:"total_output",cellRenderer:({row:e})=>a("div",{onClick:()=>{x(e.id)}},[e.total_output])},{label:"不良",prop:"defect",hide:!0},{label:"理论产量",prop:"std_output",hide:()=>!r.value,formatter(e){if(e.std_output>0)return e.std_output.toFixed(0)}},{label:"最后动作",prop:"last_time",hide:()=>!r.value,sortable:!0,formatter(e){if(e.last_time)return o(e.last_time).format("MM/DD HH:mm")}},{label:"可用率",prop:"availability",formatter(e,t,l){return r.value||e.stdtime>0&&(l=1),l>0?(l*100).toFixed(1)+"%":""}},{label:"性能",prop:"performance",cellRenderer:({row:e})=>{!r.value&&e.uptime>0&&(e.performance=e.stdtime/e.uptime);const t=(e.performance*100).toFixed(1)+"%";return e.performance>1.2?a("span",{style:"color: red;"},[t]):e.performance==null?a("span",null,null):a("span",null,[t])}},{label:"良率",prop:"quality",formatter(e,t,l){if(!r.value&&e.total_output>0&&(l=1-e.defect/e.total_output),l>0)return(l*100).toFixed(1)+"%"}},{label:"OEE",prop:"OEE",formatter(e){let t=0;return!r.value&&e.total_output>0?t=e.stdtime/e.uptime*(1-e.defect/e.total_output):t=e.availability*e.performance*e.quality,t>0?(t*100).toFixed(1)+"%":""}}],b=h({text:"正在加载生产采集数据...",viewBox:"-10, -10, 50, 50",spinner:`
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `}),Y={offsetBottom:12},x=e=>{S({title:"查看"+e+"#机小时记录表",props:{search_condition:{selecteddate:n.selecteddate,machine:e},shift:n.shift},width:"60%",draggable:!0,fullscreenIcon:!1,closeOnClickModal:!0,contentRenderer:()=>z(E)})};function g(e){}const n=h({selecteddate:u()=="C"?o().subtract(1,"days").format("YYYY-MM-DD"):o().format("YYYY-MM-DD"),shift:u(),status:1}),m=()=>{n.selecteddate=u()=="C"?o().subtract(1,"days").format("YYYY-MM-DD"):o().format("YYYY-MM-DD"),n.shift=u(),n.status=2},M=()=>{setInterval(()=>{m(),s()},12e4)};R(n,e=>{d.value=!0,i.value=[],r.value=!0,e.shift!=u()?r.value=!1:e.shift=="C"?e.selecteddate!=o().subtract(1,"days").format("YYYY-MM-DD")&&(r.value=!1):e.selecteddate!=o().format("YYYY-MM-DD")&&(r.value=!1),s()});const s=()=>{d.value=!0,C(n).then(e=>{i.value=e.data,d.value=!1})};return j(()=>{s(),M()}),{refreshData:s,search_condition:n,reset_condition:m,is_current:r,loading:d,columns:y,dataList:i,loadingConfig:b,adaptiveConfig:Y,onSizeChange:g}}export{w as useColumns};

import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export const gethourtable = (querydata?: object) => {
  return http.request("get", baseUrlApi("prodinfo/testapi"), {
    params: querydata
  });
  // return http.get(baseUrlApi("prodinfo/testapi"), data);
};

export const getrecentsku = (querydata: object) => {
  return http.request("get", baseUrlApi("shift/getrecentsku"), {
    params: querydata
  });
};

// 返回模具动作节拍
export const getCycle = (data: object) => {
  return http.request("get", baseUrlApi("cycle/getlistbyhour"), {
    params: data
  });
};


from flask import Blueprint, request
from functools import wraps
from app.public.functions import responseError,  responseExpire, responsePost
import traceback
import openai
api = Blueprint('welean/bi/shingoAPI', __name__)


def login_shingo(view_func):  # 通过一个装饰函数来实现登录验证
    @wraps(view_func)
    def verify_token(*args, **kwargs):
        try:
            token = request.headers["token"]
            print('token', token)
        except Exception:
            traceback.print_exc()
            return responseError('登录失败！')
        if token != "sdfjio23hi@ihj3kd90lk23@#lkj23jkhsjkEJKH":
            return responseExpire('登录失败！请重新登录！')
        return view_func(*args, **kwargs)
    return verify_token


@ api.route('/openAIGPTfinal', methods=['POST'])
@ login_shingo
def openAIGPTfinal():
    res = request.json
    answer = res.get('answer') if res.get('answer') else ''
    question = res.get('question')
    ctype = res.get('ctype')
    lang = res.get('lang')
    interviewcomment = res.get('interviewcomment')
    firstcomment = res.get('firstcomment')
    if not (question and ctype and firstcomment and interviewcomment):
        return responseError('参数不完整')
    result = ''
    if lang == '中文':
        prompt = f"""身份:{ctype}\n问题:{question}\n回答:{answer}"""
        prompt2 = f"结合审核员现场审核时的发现内容:{interviewcomment},请给出一个最终的评分并给出理由"
        content = """你是一个Shingo Prize的专家,根据Shingo Prize的评分规则,
        会根据leader,manager,associate这三个身份的问答结果给出1~5分的整数评分,
        请根据Shingo Prize的10个指导原则对下面身份的问答来打分"""
    else:
        prompt = f"""Identity:{ctype}\nQuestion:{question}\nAnswer:{answer}"""
        prompt2 = f"Combining the actual findings of the auditor during the audit:{interviewcomment}, please give a final score and give reasons"
        content = """You are an expert of Shingo Prize. According to the scoring rules of Shingo Prize,
        you will give a score of 1~5 according to the question and answer results of the three identities of leader, manager and associate.
        Please score and give reasons according to the questions and answers of the following positions.
        It is best to combine the 10 guiding principles of Shingo Prize."""
    try:
        response = openai.ChatCompletion.create(
            deployment_id="gpt35",
            messages=[
                {"role": "system", "content": content},
                {"role": "user", "content": prompt},
                {"role": "assistant", "content": firstcomment},
                {"role": "user", "content": prompt2}
            ]
        )
        result = response.choices[0]['message']['content']
        return responsePost('成功,请查看下方结果', {'result': result})
    except Exception as e:
        result = str(e)
    return responseError(result+prompt)


@ api.route('/openAIGPTsummary', methods=['POST'])
@ login_shingo
def openAIGPTsummary():
    res = request.json
    answer = res.get('answer') if res.get('answer') else ''
    question = res.get('question')
    ctype = res.get('ctype')
    lang = res.get('lang')
    interviewcomment = res.get('interviewcomment') if res.get('interviewcomment') else '无额外特殊发现'
    if not (question and ctype and interviewcomment):
        return responseError('参数不完整')
    result = ''
    if lang == '中文':
        prompt = f"""身份:{ctype}\n问题:{question}\n回答:{answer}\n审核员发现:{interviewcomment}"""
        content = """你是一个Shingo Prize的专家,运用Shingo Prize的精神和基本原则对于员工采访的问题以及现场评审员的发现进行评判,
        员工的身份分为：leader,manager和associate三类，请基于Shingo Prize要求给出分析和建议，回复格式为：
        分析：（不超过100字）建议：（不超过100字）"""
    else:
        prompt = f"""Identity:{ctype}\nQuestion:{question}\nAnswer:{answer}\nAuditor's findings:{interviewcomment}"""
        content = """You are an expert of Shingo Prize. According to the spirit and basic principles of Shingo Prize,
        you will judge the problems of employees' interviews and the findings of on-site auditors.
        The identities of employees are divided into: leader, manager and associate.
        Please analyze and put forward improvement suggestions based on the requirements of Shingo Prize, no more than 150 words."""
    try:
        response = openai.ChatCompletion.create(
            deployment_id="gpt35",
            messages=[
                {"role": "system", "content": content},
                {"role": "user", "content": prompt}
            ]
        )
        result = response.choices[0]['message']['content']
        return responsePost('成功,请查看下方结果', {'result': result})
    except Exception as e:
        result = str(e)
    return responseError(result+prompt)


@ api.route('/openAIGPT', methods=['POST'])
@ login_shingo
def openAIGPT():
    res = request.json
    answer = res.get('answer')
    question = res.get('question')
    ctype = res.get('ctype')
    lang = res.get('lang')
    if not (answer and question and ctype):
        return responseError('参数不完整')
    result = ''
    if lang == '中文':
        prompt = f"""身份:{ctype}\n问题:{question}\n回答:{answer}"""
        content = """你是一个Shingo Prize的专家,根据Shingo Prize的评分规则,
        会根据leader,manager,associate这三个身份的问答结果给出1~5分的整数评分,
        请根据下面职位的问答来打分并给出理由,最好能够结合Shingo Prize的10个指导原则,
        当回答表达不清楚或不知道等的意思时,可以打1分,理由是受访者不了解问题,
        以下是一个评分例子: <article>'得分':1,'理由':'打分理由','追问':'追问问题'</article>
"""
    else:
        prompt = f"""Identity:{ctype}\nQuestion:{question}\nAnswer:{answer}"""
        content = """You are an expert of Shingo Prize. According to the scoring rules of Shingo Prize,
        you will give a score of 1~5 according to the question and answer results of the three identities of leader, manager and associate.
        Please score and give reasons according to the questions and answers of the following positions.
        It is best to combine the 10 guiding principles of Shingo Prize.
        When the answer is unclear or the meaning of not knowing, you can score 1 point, the reason is that the interviewee does not understand the problem,
        Here is a scoring example: <article>'score':1,'reason':'reason for scoring','followup':'followup question'</article>
"""
    try:
        response = openai.ChatCompletion.create(
            deployment_id="gpt35",
            messages=[
                {"role": "system", "content": content},
                {"role": "user", "content": prompt}
            ]
        )
        result = response.choices[0]['message']['content']
        return responsePost('成功,请查看下方结果', {'result': result})
    except Exception as e:
        result = str(e)
    return responseError(result+prompt)


@ api.route('/openAIGPTculture', methods=['POST'])
@ login_shingo
def openAIGPTculture():
    res = request.json
    cultureDic = res.get('cultureDic')
    v = res.get('v')
    lang = res.get('lang')
    if not (cultureDic and v):
        return responseError('参数不完整')
    result = ''
    typeArr = []
    prompt = ''
    for k, v in cultureDic.items():
        prompt += f"{k}:<article>{v}</article>\n"
        typeArr.append(k)
    if lang == '中文':
        content = """你是一个Shingo Prize的专家,并且善于总结内容,
        你对一个工厂进行了审核,现在有三条文化被识别出是{v},分别是{typeArr[0]},{typeArr[1]},{typeArr[2]},
        下面有一些对于每一条{v}的评判内容,被<article>标签包裹,请对于这三条{v},基于已知的审核内容和你的经验,
        提取关键信息并作出不超过100字的简短总结,不需要打分,结果对这三条只要一小段话总结即可"""
    elif lang == 'English':
        content = """You are an expert of Shingo Prize. And you are good at summarizing content.
        you have audited a factory. Now there are three cultures identified as {v}, which are {typeArr[0]}, {typeArr[1]} and {typeArr[2]}.
        There are some evaluation contents for each {v}, which are wrapped in <article> tags.
        Please give a short summary for each {v} based on the known evaluation contents and your experience, no need to score."""
    try:
        response = openai.ChatCompletion.create(
            deployment_id="gpt35",
            messages=[
                {"role": "system", "content": content},
                {"role": "user", "content": prompt}
            ]
        )
        result = response.choices[0]['message']['content']
        return responsePost('成功,请查看下方结果', {'result': result})
    except Exception as e:
        result = str(e)
    return responseError(result+prompt)

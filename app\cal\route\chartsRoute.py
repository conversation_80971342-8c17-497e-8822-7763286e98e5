from distutils.log import debug
import pstats
from telnetlib import DM
from flask import Blueprint, request
from sqlalchemy import func, text, and_, case, distinct, or_
from app.cal.model.models_cal import Dcal, Dcal_instep, Dcal_standcaleq, Dcal_user, Dcal_cal_version
from app.tp.model.models_tp import Funnel, Funnellog, Target
from extensions import db
from datetime import datetime, date, timedelta
import time
from app.public.functions import responseGet
from ultils.log_helper import ProjectLogger
mylogger = ProjectLogger()
api = Blueprint('cal/chartsAPI', __name__)


@ api.route('/getIssuetype', methods=['GET'])
# @ login_required
def getIssuetype():
    mylogger.info("*"*10+"getIssuetype start"+"*"*10)
    res = request.args
    mylogger.debug(res)
    y = res.get('annual')
    plant = res.get('plant')
    quarter = res.get('quarter')
    # sql = "select deptment,SUM(calcost) as cost from (select dcal_cal2.id,dcal_cal2.instrumentno,dcal_cal_version.version,dcal_cal_version.calcost," + \
    #     "dcal_cal2.deptment," + \
    #     "row_number() over(partition by dcal_cal2.id order by dcal_cal_version.version desc) as n " + \
    #     " from dcal_cal2 left join dcal_cal_version on dcal_cal_version.calid = dcal_cal2.id " + \
    #     "where dcal_cal_version.checkresultall = 'fail' and deptment is not null and deptment!='') as table1 " + \
    #     "where table1.n = 1 " + \
    #     "group by deptment"
    # cost = db.session.execute(sql)
    if quarter == '按月':
        cost = db.session.query(func.sum(Dcal_cal_version.calcost).label('cost'),
            func.month(Dcal_cal_version.caldata).label('mon')).filter(Dcal_cal_version.caldata.between(y+'-01-01', y+'-12-31')).filter(Dcal_cal_version.checkmode != '内校').group_by(func.
            month(Dcal_cal_version.caldata)).order_by('mon').all()
    else:
        cost = db.session.query(func.sum(Dcal_cal_version.calcost).label('cost'),
            case(whens=[(or_(func.month(Dcal_cal_version.caldata) == 1, func.month(Dcal_cal_version.caldata) == 2, func.month(Dcal_cal_version.caldata) == 3), 1),
            (or_(func.month(Dcal_cal_version.caldata) == 4, func.month(Dcal_cal_version.caldata) == 5, func.month(Dcal_cal_version.caldata) == 6), 2),
            (or_(func.month(Dcal_cal_version.caldata) == 7, func.month(Dcal_cal_version.caldata) == 8, func.month(Dcal_cal_version.caldata) == 9), 3)],else_=4).label('mon')).filter(Dcal_cal_version.checkmode != '内校').filter(Dcal_cal_version.caldata.between(y+'-01-01', 
            y+'-12-31')).group_by(case(whens=[(or_(func.month(Dcal_cal_version.caldata) == 1, func.month(Dcal_cal_version.caldata) == 2, func.month(Dcal_cal_version.caldata) == 3), 1),
            (or_(func.month(Dcal_cal_version.caldata) == 4, func.month(Dcal_cal_version.caldata) == 5, func.month(Dcal_cal_version.caldata) == 6), 2),
            (or_(func.month(Dcal_cal_version.caldata) == 7, func.month(Dcal_cal_version.caldata) == 8, func.month(Dcal_cal_version.caldata) == 9), 3)],else_=4)).order_by('mon').all()
    outputdic = {
        'xdata':[],
        'ydata':[]
    }
    for i in cost:
        if quarter == '按月':
            outputdic['xdata'].append(str(i[1])+'月')
        else:
            outputdic['xdata'].append('第'+str(i[1])+'季度')
        outputdic['ydata'].append(i[0])
    # issuetype1 = db.session.query(Dvoc.issuetype, func.sum(case(whens=[(Dvoc.eppmtype == '3rd Party', Dvoc.eppmqty)])).label('partyQty'), func.sum(case(whens=[(Dvoc.eppmtype == 'IC', Dvoc.eppmqty)])).label(
    #     'icQty')).filter(Dvoc.issuetype.isnot(None)).filter(~Dvoc.etype.in_(['电商退货', 'Case pool'])).filter(func.year(Dvoc.receivedate) == y).group_by(Dvoc.issuetype).all()
    # issuetype0 = db.session.query(Dvoc.issuetype.label('iss'), func.avg(Dvoc.eppmqty).label('eppm'), Dvoc.Id.label('vid'), func.sum(case(whens=[(Dvoc.dateeppm == None, Dsku.skuqty)])).label(
    #     'qty')).outerjoin(Dsku, Dvoc.Id == Dsku.vocid).filter(~Dvoc.etype.in_(['电商退货', 'Case pool'])).filter(func.year(Dvoc.receivedate) == y).group_by(Dvoc.Id, Dvoc.issuetype).subquery()
    # issuetype01 = db.session.query(issuetype0.c.iss, func.sum(issuetype0.c.eppm).label('cqty'), func.sum(
    #     issuetype0.c.qty).label('ncqty'), func.count(issuetype0.c.vid).label('nb')).group_by(issuetype0.c.iss).order_by(func.sum(issuetype0.c.eppm).label('cqty').desc()).all()
    # mylogger.debug(issuetype01)
    # outputissuetypea = {
    #     'issuetype': [],
    #     '3pq': [],
    #     'ic': []
    # }
    # outputissuetypeb = {
    #     'issuetype': [],
    #     'coq': [],
    #     'nc': [],
    #     'cq': []
    # }
    # for i in issuetype1:
    #     outputissuetypea.get('issuetype').append(i[0])
    #     outputissuetypea.get('3pq').append(i[1])
    #     if i[2]:
    #         outputissuetypea.get('ic').append(i[2])
    #     else:
    #         outputissuetypea.get('ic').append(0)
    # for i in issuetype01:
    #     if i[0]:
    #         outputissuetypeb.get('issuetype').append(i[0])
    #     else:
    #         outputissuetypeb.get('issuetype').append('None')
    #     if i[1]:
    #         outputissuetypeb.get('coq').append(i[1])
    #     else:
    #         outputissuetypeb.get('coq').append(0)
    #     if i[2]:
    #         outputissuetypeb.get('nc').append(i[2])
    #     else:
    #         outputissuetypeb.get('nc').append(0)
    #     if i[3]:
    #         outputissuetypeb.get('cq').append(i[3])
    #     else:
    #         outputissuetypeb.get('cq').append(0)
    mylogger.debug(5555)
    mylogger.debug(outputdic)

    return responseGet('成功', {'outputissuetypea': outputdic, 'outputissuetypeb': outputdic})



@ api.route('/abbad', methods=['GET'])
# @ login_required
def abbad():
    mylogger.info("*"*10+"abbad start"+"*"*10)
    res = request.args
    mylogger.debug(res)
    y = res.get('annual')
    plant = res.get('plant')
    quarter = res.get('quarter')
    # sql = "select deptment,SUM(calcost) as cost from (select dcal_cal2.id,dcal_cal2.instrumentno,dcal_cal_version.version,dcal_cal_version.calcost," + \
    #     "dcal_cal2.deptment," + \
    #     "row_number() over(partition by dcal_cal2.id order by dcal_cal_version.version desc) as n " + \
    #     " from dcal_cal2 left join dcal_cal_version on dcal_cal_version.calid = dcal_cal2.id " + \
    #     "where dcal_cal_version.checkresultall = 'fail' and deptment is not null and deptment!='') as table1 " + \
    #     "where table1.n = 1 " + \
    #     "group by deptment"
    # cost = db.session.execute(sql)
    if quarter == '按数量':
        cost = db.session.query(func.count(Dcal_cal_version.id).label('qty'),
            case(whens=[(or_(func.month(Dcal_cal_version.verdate) == 1, func.month(Dcal_cal_version.verdate) == 2, func.month(Dcal_cal_version.verdate) == 3), 1),
            (or_(func.month(Dcal_cal_version.verdate) == 4, func.month(Dcal_cal_version.verdate) == 5, func.month(Dcal_cal_version.verdate) == 6), 2),
            (or_(func.month(Dcal_cal_version.verdate) == 7, func.month(Dcal_cal_version.verdate) == 8, func.month(Dcal_cal_version.verdate) == 9), 3)],else_=4).label('mon')
            ,Dcal.deptment).outerjoin(Dcal,Dcal_cal_version.calid == Dcal.id).filter(Dcal_cal_version.verdate.between(y+'-01-01', 
            y+'-12-31')).filter(Dcal_cal_version.classify == '非正常损坏').group_by(case(whens=[(or_(func.month(Dcal_cal_version.verdate) == 1, func.month(Dcal_cal_version.verdate) == 2, func.month(Dcal_cal_version.verdate) == 3), 1),
            (or_(func.month(Dcal_cal_version.verdate) == 4, func.month(Dcal_cal_version.verdate) == 5, func.month(Dcal_cal_version.verdate) == 6), 2),
            (or_(func.month(Dcal_cal_version.verdate) == 7, func.month(Dcal_cal_version.verdate) == 8, func.month(Dcal_cal_version.verdate) == 9), 3)],else_=4),Dcal.deptment).order_by('mon').all()
    else:
        cost = db.session.query(func.sum(case(whens=[(or_(Dcal_cal_version.repaircost == 0,Dcal_cal_version.repaircost.is_(None)), 
            Dcal_cal_version.newbuycost)],else_=Dcal_cal_version.repaircost)),
            case(whens=[(or_(func.month(Dcal_cal_version.verdate) == 1, func.month(Dcal_cal_version.verdate) == 2, func.month(Dcal_cal_version.verdate) == 3), 1),
            (or_(func.month(Dcal_cal_version.verdate) == 4, func.month(Dcal_cal_version.verdate) == 5, func.month(Dcal_cal_version.verdate) == 6), 2),
            (or_(func.month(Dcal_cal_version.verdate) == 7, func.month(Dcal_cal_version.verdate) == 8, func.month(Dcal_cal_version.verdate) == 9), 3)],else_=4).label('mon')
            ,Dcal.deptment).outerjoin(Dcal,Dcal_cal_version.calid == Dcal.id).filter(Dcal_cal_version.verdate.between(y+'-01-01', 
            y+'-12-31')).filter(Dcal_cal_version.classify == '非正常损坏').group_by(case(whens=[(or_(func.month(Dcal_cal_version.verdate) == 1, func.month(Dcal_cal_version.verdate) == 2, func.month(Dcal_cal_version.verdate) == 3), 1),
            (or_(func.month(Dcal_cal_version.verdate) == 4, func.month(Dcal_cal_version.verdate) == 5, func.month(Dcal_cal_version.verdate) == 6), 2),
            (or_(func.month(Dcal_cal_version.verdate) == 7, func.month(Dcal_cal_version.verdate) == 8, func.month(Dcal_cal_version.verdate) == 9), 3)],else_=4),Dcal.deptment).order_by('mon').all() 
    outputdic = {
        'xdata':[],
        'ydata1':[],
        'ydata2':[],
        'ydata3':[],
        'ydata4':[],
    }
    for i in cost:
        outputdic['xdata'].append(i[2])
    outputdic['xdata'] = list(set(outputdic['xdata']))
    outputdic['ydata1'] = [0 for i in range(len(outputdic['xdata']))]
    outputdic['ydata2'] = [0 for i in range(len(outputdic['xdata']))]
    outputdic['ydata3'] = [0 for i in range(len(outputdic['xdata']))]
    outputdic['ydata4'] = [0 for i in range(len(outputdic['xdata']))]
    for i in cost:
        if i[1] == 1:
            outputdic['ydata1'][outputdic['xdata'].index(i[2])] = i[0]
        elif i[1] == 2:
            outputdic['ydata2'][outputdic['xdata'].index(i[2])] = i[0]
        elif i[1] == 3:
            outputdic['ydata3'][outputdic['xdata'].index(i[2])] = i[0]
        elif i[1] == 4:
            outputdic['ydata4'][outputdic['xdata'].index(i[2])] = i[0]
    mylogger.debug(cost)
    mylogger.debug(outputdic)

    return responseGet('成功', {'outputissuetypea': outputdic, 'outputissuetypeb': outputdic})


@ api.route('/getsavecost', methods=['GET'])
# @ login_required
def getsavecost():
    mylogger.info("*"*10+"getsavecost start"+"*"*10)
    res = request.args
    mylogger.debug(res)
    y = res.get('annual')
    plant = res.get('plant')
    quarter = res.get('quarter')

    if quarter == '按月':
        cost = db.session.query(func.sum(Dcal_cal_version.calcost).label('cost'),
            func.month(Dcal_cal_version.verdate).label('mon')).filter(or_(Dcal_cal_version.checkbox2 == 4,Dcal_cal_version.checkbox2 == 5)).filter(Dcal_cal_version.verdate.between(y+'-01-01', y+'-12-31')).group_by(func.
            month(Dcal_cal_version.verdate)).order_by('mon').all()
    else:
        cost = db.session.query(func.sum(Dcal_cal_version.calcost).label('cost'),
            case(whens=[(or_(func.month(Dcal_cal_version.verdate) == 1, func.month(Dcal_cal_version.verdate) == 2, func.month(Dcal_cal_version.verdate) == 3), 1),
            (or_(func.month(Dcal_cal_version.verdate) == 4, func.month(Dcal_cal_version.verdate) == 5, func.month(Dcal_cal_version.verdate) == 6), 2),
            (or_(func.month(Dcal_cal_version.verdate) == 7, func.month(Dcal_cal_version.verdate) == 8, func.month(Dcal_cal_version.verdate) == 9), 3)],else_=4).label('mon')).filter(or_(Dcal_cal_version.checkbox2 == 4,Dcal_cal_version.checkbox2 == 5)).filter(Dcal_cal_version.verdate.between(y+'-01-01', 
            y+'-12-31')).group_by(case(whens=[(or_(func.month(Dcal_cal_version.verdate) == 1, func.month(Dcal_cal_version.verdate) == 2, func.month(Dcal_cal_version.verdate) == 3), 1),
            (or_(func.month(Dcal_cal_version.verdate) == 4, func.month(Dcal_cal_version.verdate) == 5, func.month(Dcal_cal_version.verdate) == 6), 2),
            (or_(func.month(Dcal_cal_version.verdate) == 7, func.month(Dcal_cal_version.verdate) == 8, func.month(Dcal_cal_version.verdate) == 9), 3)],else_=4)).order_by('mon').all()
    outputdic = {
        'xdata':[],
        'ydata':[]
    }
    for i in cost:
        if quarter == '按月':
            outputdic['xdata'].append(str(i[1])+'月')
        else:
            outputdic['xdata'].append('第'+str(i[1])+'季度')
        outputdic['ydata'].append(i[0])
    mylogger.debug(5555)
    mylogger.debug(outputdic)

    return responseGet('成功', {'outputissuetypea': outputdic, 'outputissuetypeb': outputdic})


@ api.route('/getcalnumber', methods=['GET'])
# @ login_required
def getcalnumber():
    mylogger.info("*"*10+"getcalnumber start"+"*"*10)
    res = request.args
    mylogger.debug(res)
    y = res.get('annual')
    plant = res.get('plant')
    quarter = res.get('quarter')

    if quarter == '按校验方式':
        cost = db.session.query(func.count(Dcal_cal_version.id).label('count'),Dcal_cal_version.checkmode.label('checkmode'),
            func.month(Dcal_cal_version.caldata).label('mon')).filter(Dcal_cal_version.caldata.between(y+'-01-01', y+'-12-31')).group_by(func.
            month(Dcal_cal_version.caldata),Dcal_cal_version.checkmode).order_by('mon').all()
    elif quarter == '按量具状态':
        cost = db.session.query(func.count(Dcal_cal_version.id).label('count'),Dcal_cal_version.measurestatus.label('measurestatus'),
            func.month(Dcal_cal_version.caldata).label('mon')).filter(Dcal_cal_version.caldata.between(y+'-01-01', y+'-12-31')).group_by(func.
            month(Dcal_cal_version.caldata),Dcal_cal_version.measurestatus).order_by('mon').all()
    else:
        cost = db.session.query(func.count(Dcal_cal_version.id).label('count'),Dcal_cal_version.checkresultall.label('checkresultall'),
            func.month(Dcal_cal_version.caldata).label('mon')).filter(Dcal_cal_version.caldata.between(y+'-01-01', y+'-12-31')).group_by(func.
            month(Dcal_cal_version.caldata),Dcal_cal_version.checkresultall).order_by('mon').all()
    outputdic = {
        'xdata':[],
        'ccount':[],
        'ncount':[],
        'shcount':[],
        'szcount':[],
        'yccount':[],
    }
    for i in cost:
        if str(i[2])+'月' not in outputdic['xdata']:
            outputdic['xdata'].append(str(i[2])+'月')
        if i[1] == '厂校' or i[1] == 'True' or i[1] == 'pass':
            outputdic['ccount'].append(i[0])
        elif i[1] == '内校' or i[1] == 'ZT' or i[1] == 'fail':
            outputdic['ncount'].append(i[0])
        elif i[1] == '上海计量' or i[1] == 'WX' or i[1] == None:
            outputdic['shcount'].append(i[0])
        elif i[1] == '苏州计量' or i[1] == 'MX':
            outputdic['szcount'].append(i[0])
        elif i[1] == '原厂' or i[1] == 'BF':
            outputdic['yccount'].append(i[0])
    mylogger.debug(5555)
    mylogger.debug(outputdic)

    return responseGet('成功', {'outputissuetypea': outputdic, 'outputissuetypeb': outputdic})
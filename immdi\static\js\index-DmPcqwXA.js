import{useSysMenuManagement as $}from"./hook-pzVMp2q7.js";import{d as z,n as B,r as s,j as M,o as a,c as h,b as n,h as t,e as N,u as l,b3 as V,f as i,w as E,g as c,y as S,z as F,a3 as O,C as _,b4 as R,b5 as j}from"./index-BnxEuBzx.js";import{_ as L}from"./edit.vue_vue_type_script_setup_true_lang-yIcXLRFv.js";import"./system-DzNitOCO.js";import"./index-8CyAUJH-.js";const T={class:"main-content"},q={class:"mb-2"},A={key:0,class:"inline-block"},W=z({name:"SysMenuManagement",__name:"index",setup(G){const p=B(),{dataList:b,loading:k,handleEdit:u,handleEditChild:g,handleDelete:w,fetchData:v}=$(p);return(H,f)=>{const r=s("el-button"),o=s("el-table-column"),m=s("IconifyIconOnline"),d=s("el-tag"),C=s("el-popconfirm"),x=s("el-table"),D=s("el-card"),I=M("loading");return a(),h("div",T,[n(D,{shadow:"never"},{default:t(()=>[N("div",q,[n(r,{icon:l(V),type:"primary",onClick:f[0]||(f[0]=e=>l(u)(e))},{default:t(()=>[i(" 添加 ")]),_:1},8,["icon"])]),E((a(),c(x,{border:"",data:l(b),"default-expand-all":"","row-key":"id","tree-props":{children:"children"}},{default:t(()=>[n(o,{label:"菜单名称",prop:"title","min-width":"120"},{default:t(({row:e})=>[i(S(l(F)(l(O)(e.title))),1)]),_:1}),n(o,{label:"图标",width:"55",align:"center"},{default:t(({row:e})=>[e.icon?(a(),h("div",A,[n(m,{icon:e.icon},null,8,["icon"])])):_("",!0)]),_:1}),n(o,{label:"路由地址",prop:"path"}),n(o,{label:"组件路径",prop:"component","min-width":"120"}),n(o,{label:"唯一标识",prop:"name","min-width":"120"}),n(o,{label:"排序",prop:"rank",width:"60"}),n(o,{align:"center",label:"类型",width:"90"},{default:t(({row:e})=>[e.node_type==1?(a(),c(d,{key:0},{default:t(()=>[i("目录")]),_:1})):_("",!0),e.node_type==2?(a(),c(d,{key:1,type:"warning"},{default:t(()=>[i("菜单")]),_:1})):_("",!0),e.node_type==3?(a(),c(d,{key:2,type:"danger"},{default:t(()=>[i("按钮")]),_:1})):_("",!0)]),_:1}),n(o,{align:"center",label:"显示父级",width:"90"},{default:t(({row:e})=>[e.show_parent?(a(),c(d,{key:0,type:"success"},{default:t(()=>[i("是")]),_:1})):(a(),c(d,{key:1,type:"warning"},{default:t(()=>[i("否")]),_:1}))]),_:1}),n(o,{align:"center",label:"隐藏",prop:"hidden",width:"60"},{default:t(({row:e})=>[e.show_link?(a(),c(d,{key:0,type:"success"},{default:t(()=>[i("否")]),_:1})):(a(),c(d,{key:1,type:"warning"},{default:t(()=>[i("是")]),_:1}))]),_:1}),n(o,{align:"center",label:"缓存",width:"60"},{default:t(({row:e})=>[e.keep_alive?(a(),c(d,{key:0,type:"success"},{default:t(()=>[i("是")]),_:1})):(a(),c(d,{key:1,type:"warning"},{default:t(()=>[i("否")]),_:1}))]),_:1}),n(o,{align:"center",fixed:"right",label:"操作",width:"160"},{default:t(({row:e})=>[n(r,{size:"small",type:"success",title:"添加下级菜单",onClick:y=>l(g)(e),disabled:e.node_type!=1},{default:t(()=>[n(m,{icon:"ci:arrow-sub-down-left"})]),_:2},1032,["onClick","disabled"]),n(r,{title:"编辑菜单",icon:l(R),size:"small",type:"primary",onClick:y=>l(u)(e)},null,8,["icon","onClick"]),n(C,{title:"是否确认删除?",onConfirm:y=>l(w)(e)},{reference:t(()=>[n(r,{title:"删除菜单",icon:l(j),size:"small",type:"danger"},null,8,["icon"])]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[I,l(k)]]),n(L,{ref_key:"editRef",ref:p,onFetchData:l(v)},null,8,["onFetchData"])]),_:1})])}}});export{W as default};

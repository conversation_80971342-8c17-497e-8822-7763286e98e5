from flask import Blueprint, jsonify, request
import requests
import pymssql
import datetime
from config import config, env
from extensions import db
from sqlalchemy import func, and_, or_, not_, cast, Time
from app.public.functions import responseGet,  responseError, responsePost, responsePut
from app.dm.functions import download_img, getReqQtyByShift, getRouting
from app.dm.model.models_dm import Restinfo, Lineinfo, Shiftinfo, Planinfo, Scaninfo, Issuelog, Temprecord, Otd, Otdmiss, Kpi,Skuinfo,Routing
from app.public.model.models_public import Lineinfo as Publine
from app.public.functions.sap import getMaterialDes
from app.biVue3.route.t2ehsRoute import downLoadPics
import traceback

api = Blueprint('bi3/t3productionAPI', __name__)

# EHS数据还是从原来的PHP文件里获取数据，待重构
plantDic = {
    'Suzhou': 'SZ',
    'Wuxi': 'WX',
    'Ningbo': 'NB'
}


@api.route('/getLayeredDetail', methods=['GET'])  # 获取EHS统计和分类
def getLayeredDetail():
    res = request.args
    eid = res.get('eid')
    weeknum = res.get('weeknum')
    year = res.get('year')
    res = requests.get(
        config[env].cloud_url+"welean/bi/taskAPI/getLayeredDetail", params={
            'eid': eid,
            'weeknum': weeknum,
            'year' :year
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        outArr = content['data']['outArr']
        outArr = downLoadPics(outArr)
        return responseGet('成功', {'outArr': outArr})
    return responseError('获取问题列表失败，请尝试刷新或联系管理员')


@api.route('/getLayeredaudit', methods=['GET'])  # 获取EHS统计和分类
def getLayeredaudit():
    res = request.args
    dept = res.get('dept')
    plant = res.get('plant')
    year = res.get('year')
    res = requests.get(
        config[env].cloud_url+"welean/bi/taskAPI/getLayeredresult", params={
            'plant': plantDic[plant],
            'year': year,
            'dept': dept
        })
    content = res.json()
    print(content)
    if content["meta"]["status"] == 200:
        outDic = content['data']['outDic']
        weeknumArr = content['data']['weeknumArr']
        return responseGet('成功', {'outDic': outDic, 'weeknumArr': weeknumArr})
    else:
        return responseError('获取问题列表失败，请尝试刷新或联系管理员')


@api.route('/closeOtdmiss', methods=['PUT'])   # 从VOC系统里获取最近的客诉信息列表
def closeOtdmiss():
    res = request.json
    id = res.get('id')
    dt = res.get('dt')
    db.session.query(Otdmiss).filter(
        Otdmiss.Id == id).update({'solvedate': dt})
    db.session.commit()
    return responsePut('success')


@api.route('/setOtdmiss', methods=['POST'])  # 获取EHS统计和分类
def setOtdmiss():
    res = request.json
    id = res.get('Id')
    try:
        if id:
            db.session.query(Otdmiss).filter(Otdmiss.Id == id).update({
                'otddate':  res.get('otddate'),
                'area': res.get('area'),
                'misslines': res.get('misslines'),
                'comments': res.get('comments')
            })
        else:
            newotd = Otdmiss(otddate=res.get('otddate'), area=res.get('area'), misslines=res.get('misslines'),
                             comments=res.get('comments'))
            db.session.add(newotd)
        db.session.commit()
        return responsePost('success')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('更新失败，请联系管理员')


@api.route('/setOtd', methods=['POST'])  # 获取EHS统计和分类
def setOtd():
    res = request.json
    id = res.get('Id')
    print('aaaaaaaaaaaa', res)
    try:
        if id:
            db.session.query(Otd).filter(Otd.Id == id).update({
                'promissnew':  res.get('promissnew'),
                'requestnew': res.get('requestnew'),
                'promisstotal': res.get('promisstotal'),
                'requesttotal': res.get('requesttotal'),
                'promissmiss': res.get('promissmiss'),
                'requestmiss': res.get('requestmiss'),
                'otddate': res.get('dt'),
            })
        else:
            newotd = Otd(promissnew=res.get('promissnew'), requestnew=res.get('requestnew'), promisstotal=res.get('promisstotal'),
                         requesttotal=res.get('requesttotal'), promissmiss=res.get('promissmiss'), requestmiss=res.get('requestmiss'), otddate=res.get('dt'))
            db.session.add(newotd)
        db.session.commit()
        return responsePost('success')
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('更新失败，请联系管理员')


@api.route('/getOtd', methods=['GET'])  # 获取EHS统计和分类
def getOtd():
    res = request.args
    dt = res.get('dt')
    print('ddddddddddddt', dt)
    otd = db.session.query(Otd).filter(Otd.otddate == dt).first()
    otdInfo = {}
    promisstarget = 1
    requesttarget = 1
    otdtarget = db.session.query(Kpi).filter(
        Kpi.targetyear == dt[:4]).filter(Kpi.linegroup == 't3otd').first()
    if otdtarget:
        promisstarget = otdtarget.dtarget
        requesttarget = otdtarget.qtarget
    if otd:
        otdInfo = {
            'Id': otd.Id,
            'requesttype': 'danger' if otd.requestmiss/otd.requesttotal < requesttarget else 'success',
            'promisstype': 'danger' if otd.promissmiss/otd.promisstotal < promisstarget else 'success',
            'promissnew': otd.promissnew,
            'requestnew': otd.requestnew,
            'promisstotal': otd.promisstotal,
            'requesttotal': otd.requesttotal,
            'promissmiss': otd.promissmiss,
            'requestmiss': otd.requestmiss
        }
    otdmiss = db.session.query(Otdmiss).filter(
        or_(Otdmiss.solvedate > dt, Otdmiss.solvedate.is_(None))).filter(Otdmiss.otddate <= dt).all()
    missArr = []
    for o in otdmiss:
        missArr.append({
            'otddate': datetime.datetime.strftime(o.otddate, '%Y-%m-%d'),
            'area': o.area,
            'comments': o.comments,
            'misslines': o.misslines,
            'Id': o.Id
        })
    return responseGet('dd', {'otdInfo': otdInfo, 'missArr': missArr})


@api.route('/recordtemp', methods=['POST'])   # 从VOC系统里获取最近的客诉信息列表
def recordtemp():
    res = request.json
    dt = res.get('dt')
    linegroup = res.get('linegroup')
    issues = res.get('issues')
    planqty = res.get('planqty')
    actqty = res.get('actqty')
    try:
        re = db.session.query(Temprecord).filter(Temprecord.linegroup ==
                                                 linegroup).filter(Temprecord.recorddate == dt).first()
        if re:
            re.issues = issues
            re.planqty = planqty
            re.actqty = actqty
        else:
            record = Temprecord(recorddate=dt, linegroup=linegroup,
                                issues=issues, planqty=planqty, actqty=actqty)
            db.session.add(record)
        db.session.commit()
        return responsePost('录入成功')
    except Exception:
        db.session.rollback()
    return responseError('输入失败，请联系管理员解决问题')


@api.route('/getEHS', methods=['GET'])  # 获取EHS统计和分类
def getEHS():
    res = request.args
    plant = res.get('plant')
    year = res.get('year')
    res = requests.get(
        config[env].cloud_url+"welean/bi/safetyAPI/gett3ehs", params={
            'plant': plantDic[plant],
            'year': year
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        pyramid = content['data']['pyramid']
        word = content['data']['word']
        return responseGet('成功', {'pyramid': pyramid, 'word': word})
    else:
        return responseError('获取问题列表失败，请尝试刷新或联系管理员')


@api.route('/getlistbyday', methods=['GET'])  # 获取EHS统计和分类
def getlistbyday():
    res = request.args
    plant = res.get('plant')
    dt = res.get('dt')
    imgPath = config[env].localPath+"BI/img/"
    res = requests.get(
        config[env].cloud_url+"welean/bi/safetyAPI/getlistbyday", params={
            'plant': plantDic[plant],
            'dt': dt,
            'tier': 3
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        dts = content['data']['suggests']
        print('aaaaaaaaaa', dts)
        for d in dts:
            p = d['beforepic']
            pa = d['afterpic']
            picurl = p[p.rfind('/')+1:].split('.')
            if len(picurl) == 2:
                picName = picurl[0]
                picAdx = picurl[1]
            else:
                picName = ''
                picAdx = ''
            if p:
                download_img(p, imgPath+picName+'.'+picAdx)
            if pa:
                download_img(pa, imgPath+picName+'after.'+picAdx)
            d['beforepic'] = config[env].base_url+'BI/img/'+picName+'.'+picAdx
            d['afterpic'] = config[env].base_url + \
                'BI/img/'+picName+'after.'+picAdx
        return responseGet('成功', {'detailList': dts})
    else:
        return responseError('失败')


@api.route('/getEHSDetail', methods=['GET'])  # 获取EHS统计和分类
def getEHSDetail():
    res = request.args
    plant = res.get('plant')
    year = res.get('year')
    type2 = res.get('type2')
    res = requests.get(
        config[env].cloud_url+"welean/bi/safetyAPI/gett3ehsdetail", params={
            'plant': plant,
            'year': year,
            'type2': type2
        })
    content = res.json()
    if content["meta"]["status"] == 200:
        dts = content['data']['suggests']
        dts = downLoadPics(dts)
        return responseGet('成功', {'detailList': dts})
    else:
        return responseError('失败')


@api.route('/getvoc', methods=['GET'])   # 从VOC系统里获取最近的客诉信息列表
def getvoc():
    res = request.args
    dt = res.get('dt')
    print(11111111, config[env].vocdb)
    connect = pymssql.connect(config[env].vocdb['host'],
                              config[env].vocdb['user'], config[env].vocdb['password'], config[env].vocdb['database'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    sql1 = """select  status,receivedate, dvoc.vsm, fg, vendor, vocdesc,dsku.sku,"from",dvoc.id,skuqty from dvoc left join dline on
    dvoc.mgroupnumber = dline.mgroup left join dsku on dsku.vocid=dvoc.id where CONVERT(varchar(100), receivedate, 23) = '%s'""" % dt
    sql1 = sql1+" order by receivedate desc "
    print(sql1)
    cursor.execute(sql1)
    r1 = cursor.fetchall()
    outArr = []
    dic = {}
    skuArr = []
    for r in r1:
        if r[6]:
            skuArr.append(r[6])
        if r[8] is not None and r[8] in dic.keys():
            dic[r[8]] = {
                'status': r[0],
                'receivedate': datetime.datetime.strftime(r[1], '%Y-%m-%d') if r[1] else '',
                'vsm': r[2],
                'fg': r[3],
                'vendor': r[4],
                'vocdesc': r[5],
                'sku': r[6],
                'vfrom': r[7],
                'qty': r[9]
            }
        else:
            dic[r[8]] = {
                'status': r[0],
                'receivedate': datetime.datetime.strftime(r[1], '%Y-%m-%d') if r[1] else '',
                'vsm': r[2],
                'fg': r[3],
                'vendor': r[4],
                'vocdesc': r[5],
                'sku': r[6],
                'vfrom': r[7],
                'qty': r[9],
            }
    outArr = list(dic.values())
    print('aaaaa', skuArr)
    desDic = getMaterialDes(skuArr)
    print('bbbb', desDic)
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data={'voc': outArr, 'des': desDic})


@api.route('/getDay', methods=['GET'])
def getDay():
    res = request.args
    dt = res.get('dt')
    plant = res.get('plant')
    restList = db.session.query(Restinfo).all()
    rests = {}
    for r in restList:
        mydt = dt
        if str(r.resttime) < '08:00:00':
            mydt = (datetime.datetime.strptime(dt, '%Y-%m-%d') +
                    datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        if r.linegroup in rests.keys():
            rests[r.linegroup].append({
                'reststart': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S'),
                'restfinish': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S')+datetime.timedelta(minutes=r.restmin)
            })
        else:
            rests[r.linegroup] = [{
                'reststart': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S'),
                'restfinish': datetime.datetime.strptime(mydt+' ' + str(r.resttime), '%Y-%m-%d %H:%M:%S')+datetime.timedelta(minutes=r.restmin)
            }]
    shifts = db.session.query(func.date(Shiftinfo.starttime).label('shiftdate'), Lineinfo.VSM, Lineinfo.linegroup, Shiftinfo.linename, Lineinfo.linegroup,
                              func.sum(Scaninfo.scanqty).label(
                                  'scanQty'), Shiftinfo.headcount, Lineinfo.area,
                              Shiftinfo.starttime, Shiftinfo.finishtime, Shiftinfo.routing, Shiftinfo.sku).outerjoin(Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(
        Lineinfo, Lineinfo.linename == Shiftinfo.linename).filter(not_(Lineinfo.area.in_(['B4Meltblown','B1Tank', 'temp','B3']))).filter(
            not_(and_(Shiftinfo.shifttype.in_(['白班','中班']), func.datediff(func.date(Shiftinfo.starttime), dt) == 1))).filter(
            not_(and_(Shiftinfo.shifttype == '夜班', func.datediff(func.date(Shiftinfo.starttime), dt) == 1, cast(func.time(Shiftinfo.starttime), Time) > '08:00:00'))).filter(
            not_(and_(cast(func.time(Shiftinfo.starttime), Time) <= '08:00:00', Shiftinfo.shifttype == '夜班', func.datediff(func.date(Shiftinfo.starttime), dt) == 0))).filter(
            and_(func.datediff(func.date(Shiftinfo.starttime), dt) >= 0, func.datediff(func.date(Shiftinfo.starttime), dt) <= 1)).group_by(Shiftinfo.Id).all()
    scanDic = {}
    for s in shifts:
        linename = s.linename+'-'+s.sku
        linegroup=s.linegroup
        if  s.linename=='CAN包装':
            linegroup= s.linename
        if linegroup in rests.keys():
            lg = linegroup
        else:
            lg = 'all'
        if s.finishtime:
            ftime = s.finishtime
        else:
            ftime = datetime.datetime.now()
        # shiftdate = datetime.datetime.strftime(s.starttime, '%Y-%m-%d')
        if linename in scanDic.keys():
            scanQty = s.scanQty if s.scanQty else 0
            # requireQty = getReqQty(shiftdate, s.headcount, s.linegroup,
            #                        s.starttime, s.finishtime, restList, s.routing)
            requireQty = getReqQtyByShift(s.headcount, s.starttime,
                                          ftime, rests[lg], s.routing)
            scanDic[linename]['scanQty'] = scanDic[linename]['scanQty']+scanQty
            scanDic[linename]['requireQty'] = scanDic[linename]['requireQty']+requireQty
        else:
            scanDic[linename] = {
                'vsm': s.VSM,
                'area': s.area,
                'linegroup': linegroup,
                'routing': round(1/s.routing, 1),
                'sku': s.sku,
                'linename': s.linename,
                'scanQty': s.scanQty if s.scanQty else 0,
                'requireQty': getReqQtyByShift(s.headcount, s.starttime, ftime, rests[lg], s.routing),
                'planQty': 0,
                'finishQty': 0,
                'desc': []
            }
    plans = db.session.query(Planinfo.linename, Planinfo.mo, Lineinfo.VSM, Lineinfo.area, Lineinfo.linegroup, Planinfo.qty, Planinfo.sku,
                             func.sum(Scaninfo.scanqty).label('finishQty')).outerjoin(
        Shiftinfo, and_(Shiftinfo.shifttype == Planinfo.shifttype, Shiftinfo.linename == Planinfo.linename, Shiftinfo.sku == Planinfo.sku,
                        or_(and_(func.date(Shiftinfo.starttime) == Planinfo.plandate,
                                 cast(func.time(Shiftinfo.starttime), Time) >= '08:00:00'),
                            and_(cast(func.time(Shiftinfo.starttime), Time) < '08:00:00', Shiftinfo.shifttype == '夜班',
                                 func.datediff(func.date(Shiftinfo.starttime), Planinfo.plandate) == 1)))).outerjoin(
        Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).join(Lineinfo, Lineinfo.linename == Planinfo.linename).filter(Planinfo.plandate == dt).group_by(Planinfo.Id).all()
    planDic = {}
    for p in plans:
        linename = p.linename+'-'+p.sku
        if linename in planDic.keys():
            planDic[linename]['finishQty'] = planDic[linename]['finishQty'] + \
                (p.finishQty if p.finishQty else 0)
            planDic[linename]['planQty'] = planDic[linename]['planQty'] + \
                (p.qty if p.qty else 0)
        else:
            planDic[linename] = {
                'vsm': p.VSM,
                'area': p.area,
                'mo': p.mo,
                'linegroup': p.linegroup,
                'routing': round(1/getRouting(p.sku, p.linename, 1), 1),
                'sku': p.sku,
                'linename': p.linename,
                'planQty': p.qty if p.qty else 0,
                'finishQty': p.finishQty if p.finishQty else 0,
                'desc': []
            }

    for k in scanDic.keys():
        if k in planDic.keys():
            scanDic[k]['planQty'] = planDic[k]['planQty']
            scanDic[k]['finishQty'] = planDic[k]['finishQty']
            scanDic[k]['mo'] = planDic[k]['mo']
    for k in planDic.keys():
        if k not in scanDic.keys():
            scanDic[k] = planDic[k]
            scanDic[k]['scanQty'] = 0
            scanDic[k]['requireQty'] = 0
    tanks = getOEEdaily(dt,'B1Tank')
    meltblowns=getOEEdaily(dt,'B4Meltblown',rests['Meltblown'])
    for k, v in tanks.items():
        scanDic[k] = v
    for k,v in meltblowns.items():
        scanDic[k] = v
    problems = db.session.query(Issuelog.linename,Issuelog.shifttype,Issuelog.sqdctype,Issuelog.problemtype,Issuelog.recordtime,
                                Issuelog.desc,func.sum(Issuelog.qty).label('qty'),
                                func.sum(Issuelog.issuemin).label('issuemin')).join(Lineinfo,Issuelog.linename==Lineinfo.linename).filter(
        Issuelog.shiftdate == dt).group_by(Issuelog.problemtype,Issuelog.shifttype,Issuelog.shiftdate,Lineinfo.linegroup).all()
    pDic = {}
    for p in problems:
        if p.qty and p.qty >= 1 and p.sqdctype!='停机损失':
            log =f'({p.linename+"|"+p.shifttype[0]})'+p.problemtype+'-'+p.desc+'('+str(p.qty)+')'
        elif p.issuemin and p.issuemin > 0:
            log = f'({p.linename+"|"+p.shifttype[0]})'+p.problemtype+'-'+p.desc+'<'+str(p.issuemin)+'>'
        else:
            log = f'({p.linename+"|"+p.shifttype[0]})'+p.problemtype+'-'+p.desc
        if p.linename in pDic.keys():
            pDic[p.linename].append(log)
        else:
            pDic[p.linename] = [log]
    # for k, v in pDic.items():
    #     print(v)
        # v=sorted(v, key=lambda item: (item['shifttype'], item['problemtype']))
    arr = list(scanDic.values())
    for i in range(len(arr)):
        if arr[i]['linename'] in pDic.keys():
            arr[i]['desc'] = pDic[arr[i]['linename']]
    outArr = sorted(arr, key=lambda item: (item['vsm'], item['area']))
    groupDic = {}
    for oo in outArr:
        if oo['linegroup'] in groupDic.keys():
            groupDic[oo['linegroup']]['planQty'] = groupDic[oo['linegroup']
                                                            ]['planQty']+oo['planQty']
            groupDic[oo['linegroup']]['scanQty'] = groupDic[oo['linegroup']
                                                            ]['scanQty']+oo['scanQty']
            groupDic[oo['linegroup']]['requireQty'] = groupDic[oo['linegroup']
                                                               ]['requireQty']+oo['requireQty']
            groupDic[oo['linegroup']]['finishQty'] = groupDic[oo['linegroup']
                                                              ]['finishQty']+oo['finishQty']
            for des in oo['desc']:
                if des not in groupDic[oo['linegroup']]['desc']:
                    groupDic[oo['linegroup']]['desc'].append(des)
        else:
            groupDic[oo['linegroup']] = {
                'vsm': oo['vsm'],
                'area': oo['area'],
                'linegroup': oo['linegroup'],
                'scanQty': oo['scanQty'],
                'requireQty': oo['requireQty'],
                'planQty': oo['planQty'],
                'finishQty': oo['finishQty'],
                'desc': oo['desc']
            }
    actlines = db.session.query(Lineinfo).filter(
        Lineinfo.plant == plant).filter(Lineinfo.isactive == 1).filter(not_(Lineinfo.area.in_(['temp']))).all()
    actArr = []
    for aa in actlines:
        actArr.append(aa.linegroup)
    publines = db.session.query(Publine).filter(
        Publine.plant == plant).filter(Publine.isactive == 1).all()
    tps = db.session.query(Temprecord).filter(
        Temprecord.recorddate == dt).all()
    tpDic = {}
    imDic={}
    dts=[]
    if env=='production':
        res = requests.get(
                "http://10.76.1.234:8080/im/proxy/im/external/getoutput", params={
                    'query_date': dt,
                })
        content = res.json()
        if content["meta"]["status"] == 200:
            dts = content['data']
    for i in range(30):
        imDic['IM'+str(i)]={
            'std_output':0,
            'total_output':0
        }
    for d in dts:
        imDic['IM'+str(d['machine_id'])]={
            'std_output':d['std_output'],
            'total_output':d['total_output']
        }
    for tt in tps:
        tpDic[tt.linegroup] = {
            'planqty': tt.planqty,
            'actqty': tt.actqty,
            'issues': tt.issues,
            'stdqty':0,
            'totalqty':0
        }
    for pp in publines:
        if pp.linename not in groupDic.keys():
            if pp.linename not in actArr:
                planqty = 0   #计划数量
                actqty = 0    #实际数量
                stdqty=0      #抓取的标准产出
                totalqty=0    #抓取的实际产出
                if pp.linename in imDic.keys():
                    stdqty=imDic[pp.linename]['std_output']
                    totalqty=imDic[pp.linename]['total_output']
                issues = ''
                if pp.linename in tpDic.keys():
                    planqty = tpDic[pp.linename]['planqty']
                    actqty = tpDic[pp.linename]['actqty']
                    issues = tpDic[pp.linename]['issues']
                groupDic[pp.linename] = {
                    'vsm': pp.vsm,
                    'area': pp.area,
                    'linegroup': pp.linename,
                    'scanQty': actqty,
                    'requireQty': planqty,
                    'planQty': planqty,
                    'finishQty': actqty,
                    'stdqty':stdqty,
                    'totalqty':totalqty,
                    'desc': [issues],
                    'hand': 1
                }
            else:
                groupDic[pp.linename] = {
                    'vsm': pp.vsm,
                    'area': pp.area,
                    'linegroup': pp.linename,
                    'scanQty': 0,
                    'requireQty': 0,
                    'planQty': 0,
                    'finishQty': 0,
                    'desc': [],
                    'hand': 0
                }
    brr = list(groupDic.values())
    outBrr = sorted(brr, key=lambda item: (item['vsm'], item['area']))
    # print(222,outBrr)
    return responseGet('成功', outBrr)


def getOEEdaily(dt,area,rests=[]):
    mbDic={}
    if area=='B4Meltblown':
        mbs=db.session.query(Skuinfo).join(Routing,Skuinfo.sku==Routing.sku).filter(Skuinfo.mgroup=='Meltblown').all()
        for m in mbs:
            if m.sku and m.color:
                mbDic[m.sku]=float(m.color)
    tm = datetime.datetime.strptime(dt, '%Y-%m-%d')+datetime.timedelta(days=1)
    dtime = datetime.datetime.strptime(dt+' 08:00:00', '%Y-%m-%d %H:%M:%S')
    ttime = tm+datetime.timedelta(hours=8)
    if area=='B4Meltblown':
        shifts=db.session.query(Shiftinfo.linename, Shiftinfo.Id, Shiftinfo.starttime, Shiftinfo.finishtime, Shiftinfo.routing, Shiftinfo.lineleader, Lineinfo.area,
                              Shiftinfo.team, Lineinfo.VSM, Lineinfo.linegroup, Shiftinfo.sku, Shiftinfo.shifttype, func.sum(Scaninfo.scanqty).label('scanqty')).join(
        Lineinfo, Shiftinfo.linename == Lineinfo.linename).outerjoin(Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).filter(func.date(Shiftinfo.starttime)==dt).filter(
        Lineinfo.area == area).group_by(Shiftinfo.Id).all()
    else:
        shifts = db.session.query(Shiftinfo.linename, Shiftinfo.Id, Shiftinfo.starttime, Shiftinfo.finishtime, Shiftinfo.routing, Shiftinfo.lineleader, Lineinfo.area,
                                Shiftinfo.team, Lineinfo.VSM, Lineinfo.linegroup, Shiftinfo.sku, Shiftinfo.shifttype, func.sum(Scaninfo.scanqty).label('scanqty')).join(
            Lineinfo, Shiftinfo.linename == Lineinfo.linename).outerjoin(Scaninfo, Shiftinfo.Id == Scaninfo.shiftid).filter(
            or_(and_(func.date(Shiftinfo.starttime) == dt, Shiftinfo.finishtime > dtime),
                and_(func.date(Shiftinfo.starttime) == tm, Shiftinfo.starttime <= ttime, Shiftinfo.finishtime <= ttime))).filter(
            Lineinfo.area == area).group_by(Shiftinfo.Id).all()
    restDic = {}
    resttime = db.session.query(Restinfo.resttime, Restinfo.restmin, Lineinfo.linename).join(
        Lineinfo, Restinfo.linegroup == Lineinfo.linegroup).all()
    for r in resttime:
        if r.linename not in restDic.keys():
            restDic[r.linename] = {
                r.resttime: r.restmin
            }
        else:
            restDic[r.linename][r.resttime] = r.restmin
    shiftDic = {}
    for s in shifts:
        scanqty=s.scanqty
        rpscanqty=s.scanqty
        requireQty=0
        if area=='B4Meltblown':
            if s.sku in mbDic.keys():
                ftime=s.finishtime if s.finishtime else datetime.datetime.now()
                requireQty = int(getReqQtyByShift(1, s.starttime,
                                          ftime, rests, s.routing)*mbDic[s.sku])
                rpscanqty=int(float(rpscanqty)*mbDic[s.sku])
        ttrest = 0
        for k, v in restDic[s.linename].items():
            ss = datetime.datetime.strftime(s.starttime, '%H:%M:%S')
            ff = datetime.datetime.strftime(s.finishtime, '%H:%M:%S')
            kk = datetime.time.strftime(k, '%H:%M:%S')
            if (ss > ff):
                if ss <= kk < '24:00:00' or '00:00:00' <= kk < ff:
                    ttrest += v
            else:
                if ss <= kk < ff:
                    ttrest += v
        if s.linename in shiftDic.keys():
            if s.shifttype in shiftDic[s.linename].keys():
                shiftDic[s.linename][s.shifttype]['totalactmin'] += s.routing*scanqty*60
                shiftDic[s.linename][s.shifttype]['scanQty'] += rpscanqty
                shiftDic[s.linename][s.shifttype]['requireQty']+=requireQty
                if area=='B4Meltblown':
                    shiftDic[s.linename][s.shifttype]['planQty'] += (s.finishtime-s.starttime).seconds/60-ttrest
                    shiftDic[s.linename][s.shifttype]['finishQty'] += (s.finishtime-s.starttime).seconds/60-ttrest
            else:
                shiftDic[s.linename][s.shifttype] = {
                    'sku':s.sku,
                    'defectQty': 0,
                    'stopmin': 0,
                    'routing':s.routing,
                    'totalactmin': s.routing*scanqty*60,
                    'vsm': s.VSM,
                    'area': s.area,
                    'linename': s.linename,
                    'linegroup': s.linegroup,
                    'scanQty': rpscanqty,
                    'requireQty': requireQty,
                    'planQty': (s.finishtime-s.starttime).seconds/60-ttrest,
                    'finishQty': (s.finishtime-s.starttime).seconds/60-ttrest,
                    'desc': [],
                    'hand': 0
                }
        else:
            shiftDic[s.linename] = {
                s.shifttype: {
                    'sku':s.sku,
                    'defectQty': 0,
                    'stopmin': 0,
                    'routing':s.routing,
                    'totalactmin': s.routing*scanqty*60,
                    'vsm': s.VSM,
                    'area': s.area,
                    'linename': s.linename,
                    'linegroup': s.linegroup,
                    'scanQty': rpscanqty,
                    'requireQty': requireQty,
                    'planQty': (s.finishtime-s.starttime).seconds/60-ttrest,
                    'finishQty': (s.finishtime-s.starttime).seconds/60-ttrest,
                    'desc': [],
                    'hand': 0
                }
            }
    lineDic = {}
    # print('shiftDic',shiftDic)
    for k, v in shiftDic.items():
        lineDic[k] = {
            'sku':'',
            'defectQty': 0,
            'stopmin': 0,
            'totalactmin': 0,
            'vsm': '',
            'area': '',
            'linename': '',
            'linegroup': '',
            'scanQty': 0,
            'requireQty': 0,
            'planQty': 0,
            'finishQty': 0,
            'routing':0,
            'desc': [],
            'hand': 0
        }
        for kk, vv in shiftDic[k].items():
            lineDic[k]['planQty'] += vv['planQty']
            lineDic[k]['requireQty']+=vv['requireQty']
            lineDic[k]['finishQty'] += vv['finishQty']
            lineDic[k]['routing'] += vv['routing']
            lineDic[k]['totalactmin'] += vv['totalactmin']
            lineDic[k]['scanQty'] += vv['scanQty']
            lineDic[k]['sku'] = vv['sku']   #估算sku，实际不准确，以实际当个班次的某个sku作为代表来计算mb的折算数量
            lineDic[k]['vsm'] = vv['vsm']
            lineDic[k]['area'] = vv['area']
            lineDic[k]['linegroup'] = vv['linegroup']
            lineDic[k]['linename'] = vv['linename']
    # print('linedic0',lineDic)
    problems = db.session.query(Issuelog.sqdctype, Issuelog.linename,Issuelog.sku, func.sum(Issuelog.qty).label('qty'), func.sum(Issuelog.issuemin).label('issuemin')).filter(
        Issuelog.shiftdate == dt).filter(not_(Issuelog.problemtype.in_(['换喷头','开机调试','开机']))).group_by(Issuelog.linename,Issuelog.sqdctype).all()
    problemDic = {}
    for p in problems:
        # print(**************,p.linename,p.qty,p.issuemin)
        if p.linename in problemDic.keys():
            problemDic[p.linename]['defect'] += p.qty if p.sqdctype=='质量不良' else 0
            problemDic[p.linename]['issuemin'] += p.issuemin if  p.sqdctype=='停机损失' else 0
        else:
            problemDic[p.linename] = {
                'defect': p.qty if p.sqdctype=='质量不良' else 0,
                'issuemin': p.issuemin if p.sqdctype=='停机损失' else 0,
                'repqty':p.issuemin
            }
    # print('problemDic',problemDic)
    for k in lineDic.keys():
        replenishqty=0
        if k in problemDic.keys():
            lineDic[k]['finishQty'] -= float(problemDic[k]['issuemin'])
            replenishqty=float(problemDic[k]['issuemin']/lineDic[k]['routing'])/(mbDic[lineDic[k]['sku']]) if mbDic.get(lineDic[k]['sku']) else 0
        if area=='B4Meltblown':
            kpi=db.session.query(Kpi).filter(Kpi.linegroup=='Meltblown').order_by(Kpi.targetyear).first()
            rate=kpi.ctarget if kpi else 1
            lineDic[k]['requireQty'] =lineDic[k]['requireQty'] *rate-int(replenishqty)
        else:
            lineDic[k]['requireQty'] = float(lineDic[k]['scanQty']) * \
                (lineDic[k]['finishQty']/float(lineDic[k]['planQty']))
    return lineDic

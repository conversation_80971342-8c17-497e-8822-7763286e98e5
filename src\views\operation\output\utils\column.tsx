import type { AdaptiveConfig, LoadingConfig } from "@pureadmin/table";
import { getdayoutput } from "@/api/operation";
import { ref, onMounted, reactive } from "vue";
import moment from "moment";
import { toRaw } from "vue";
import { message } from "@/utils/message";

export function useColumns() {
  const dataList = ref([]);
  const loading = ref(true);
  const search_condition = reactive({
    selected_date: moment().format("YYYY-MM-DD")
  });

  onMounted(() => {});

  const search = () => {
    loading.value = true;
    getdayoutput(toRaw(search_condition))
      .then((res: { data: any; meta: any }) => {
        if (res.meta.status != 200) {
          message(res.meta.msg, { type: "error" });
          loading.value = false;
          return;
        }
        dataList.value = res.data;
        loading.value = false;
      })
      .catch(() => {
        message("系统请求数据错误！", { type: "error" });
        loading.value = false;
      });
  };

  /** 加载动画配置 */
  const loadingConfig = reactive<LoadingConfig>({
    text: "正在加载产量数据...",
    viewBox: "-10, -10, 50, 50",
    spinner: `
          <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
          " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
  });

  /** 撑满内容区自适应高度相关配置 */
  const adaptiveConfig: AdaptiveConfig = {
    /** 表格距离页面底部的偏移量，默认值为 `96` */
    offsetBottom: 30
    /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
    // fixHeader: true
    /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
    // timeout: 60
    /** 表头的 `z-index`，默认值为 `100` */
    // zIndex: 100
  };

  const columns: TableColumnList = [
    {
      label: "成品料号",
      prop: "pn",
      minWidth: "100"
    },
    {
      label: "描述",
      prop: "pn_des",
      minWidth: "160"
    },
    {
      label: "产量(EA)",
      children: [
        {
          label: "早班",
          prop: "shiftA_output",
          width: "90"
        },
        {
          label: "中班",
          prop: "shiftB_output",
          width: "90"
        },
        { label: "夜班", prop: "shiftC_output", width: "90" }
      ]
    },

    {
      label: "汇总(EA)",
      prop: "total_output"
    },
    {
      label: "不良(EA)",
      prop: "defect_qty",
      width: "80"
    },
    {
      label: "良品数量(EA)",
      formatter(row) {
        return (row.total_output - row.defect_qty).toLocaleString();
      }
    }
  ];

  return {
    search_condition,
    search,
    loading,
    columns,
    dataList,
    loadingConfig,
    adaptiveConfig
  };
}

from flask import Blueprint, request
from app.dm.model.models_dm import Ctqinfo, Ctqrecord, Palletinfo, Routing, Scaninfo, Scanpack, Shiftinfo, Skuinfo,  Lineinfo, Scansub
from extensions import db
import datetime
from sqlalchemy import func, desc
import traceback
from app.public.functions import responseError, responsePost, responsePut, responseGet
from app.dm.functions import getServer, getRouting, checkSubunique
from app.dm.schemas import shift_schema, shifts_schema
api = Blueprint('dm/app/V101/dmAPI', __name__)

lineShort = {
    'B4WR': '洁净房',
    'B4UF': 'UF'
}


@api.route('/getCtqrecords', methods=['GET'])  # 输入：序列号，输出：获取CTQ的结果，通常是由外挂程序获得
def getCtqrecords():
    res = request.args
    sn = res.get('sn')
    records = db.session.query(Ctqrecord).filter(
        Ctqrecord.sn == sn).filter(Ctqrecord.status == 'PASS').group_by(Ctqrecord.status).all()
    dic = {}
    for r in records:
        dic[r.ctqid] = {
            'actual1': r.actual1,
            'actual2': r.actual2,
            'status': r.status
        }
    print(dic)
    return responseGet('获取成功', {'ctqrecords': dic})


@api.route('/getCTQInfo', methods=['GET'])  # 输入：料号，输出：CTQ及其验证要求
def getCTQInfo():
    res = request.args
    sku = res.get('sku').upper()
    skuinfo = db.session.query(Skuinfo).filter(Skuinfo.sku == sku).first()
    ctqs = db.session.query(Ctqinfo).filter(
        Ctqinfo.sku == sku).order_by(Ctqinfo.ctqtype, Ctqinfo.skuctqid).all()
    dic = {}
    cbox = []  # 返回checkbox初始化
    for c in ctqs:
        if c.ctqtype in dic.keys():
            dic[c.ctqtype].append({
                'ctqid': c.skuctqid,
                'ctqname': c.ctqname,
                'va1': c.va1,
                'va2': c.va2,
                'va3': c.va3
            })
        else:
            dic[c.ctqtype] = [{
                'ctqid': c.skuctqid,
                'ctqname': c.ctqname,
                'va1': c.va1,
                'va2': c.va2,
                'va3': c.va3
            }]
        cbox.append([])
    return responseGet('获取成功', {'snverify': skuinfo.snverify, 'lenverify': skuinfo.lenverify, 'ctqs': dic, 'cbox': cbox})


@api.route('/getAllsn', methods=['GET'])  # 输入：班次号，搜索关键字，输出：料号列表和总数
def getAllsn():
    res = request.args
    shiftid = res.get('shiftid')
    keyword = res.get('keyword')
    dic = {}
    skus = db.session.query(Scaninfo.sn, Scanpack.palletsn, Scanpack.boxsn, Scaninfo.scanqty, Scaninfo.Id).outerjoin(Scanpack, Scaninfo.sn == Scanpack.sn).filter(
        Scaninfo.shiftid == shiftid)
    total = db.session.query(func.sum(Scaninfo.scanqty)).filter(
        Scaninfo.shiftid == shiftid)
    if keyword:
        skus = skus.filter(Scaninfo.sn.like('%{0}%'.format(keyword)))
        total = total.filter(Scaninfo.sn.like('%{0}%'.format(keyword)))
    skus = skus.order_by(desc(Scanpack.palletsn), desc(Scanpack.boxsn), desc(Scaninfo.Id)).all()
    total = total.scalar()
    for sku in skus:
        psn = sku.palletsn if sku.palletsn else '散托'
        bsn = sku.boxsn if sku.boxsn else '散箱'
        if psn in dic.keys():
            if bsn in dic[psn].keys():
                dic[psn][bsn].append({
                    'sn': sku.sn,
                    'scanqty': sku.scanqty
                })
            else:
                dic[psn][bsn] = [{
                    'sn': sku.sn,
                    'scanqty': sku.scanqty
                }]
        else:
            dic[psn] = {
                bsn: [{
                    'sn': sku.sn,
                    'scanqty': sku.scanqty
                }]
            }
    return responseGet('获取成功', {'snList': dic, 'total': total})


@api.route('/getScanInfo', methods=['GET'])  # 输入：班次ID，输出：扫描列表和总数
def getScanInfo():
    res = request.args
    shiftid = res.get('shiftid')
    skuList = []
    skus = db.session.query(Scaninfo).filter(
        Scaninfo.shiftid == shiftid).order_by(desc(Scaninfo.Id)).limit(6)
    for sku in skus:
        tm = datetime.datetime.strftime(sku.scantime, '%Y-%m-%d %H:%M:%S')
        skuList.append({
            'sn': sku.sn,
            'scanqty': sku.scanqty,
            'Id': sku.Id,
            'scantime': tm
        })
    total = db.session.query(func.sum(Scaninfo.scanqty)).filter(
        Scaninfo.shiftid == shiftid).scalar()
    return responseGet('获取成功', {'skuList': skuList, 'total': total})


@api.route('/newPack', methods=['POST'])  # 输入：料号，序列号，包装类型（箱或托），默认总数，输出：新建一个包装记录
def newPack():
    res = request.json
    sku = res.get('sku').upper()
    sn = res.get('sn')
    ptype = res.get('ptype')
    setqty = res.get('setqty')
    try:
        pack = Palletinfo(palletsn=sn, starttime=datetime.datetime.now(),
                          sku=sku, ptype=ptype, setqty=setqty)
        db.session.add(pack)
        db.session.commit()
        return responsePost('建立成功')
    except Exception:
        db.session.rollback()
        return responseError('箱号或托号不允许重复，该号码已存在系统中，请更换号码后再重新录入，或尝试退出到产线选择画面重新进入产线，然后选择未结箱号或托号')


@api.route('/getSkuInfo', methods=['GET'])  # 输入：料号，输出：SKU的验证信息，长度和头几位
def getSkuInfo():
    res = request.args
    sku = res.get('sku').upper()
    skuinfo = db.session.query(Skuinfo).filter(Skuinfo.sku == sku).first()
    return responseGet('获取成功', {'snverify': skuinfo.snverify, 'lenverify': skuinfo.lenverify})


@api.route('/submitCTQ', methods=['POST'])  # 根据CTQ模板录入扫描的序列号并判断是否要关闭箱子或托盘
def submitCTQ():
    res = request.json
    shiftid = res.get('shiftid')  # 班次号
    sn = res.get('sn')  # 录入的sn
    sns = res.get('sns')  # 子序列号列表
    names = res.get('names')  # 子序列号名称
    isleader = res.get('isleader')
    if not isleader:
        ckchange = db.session.query(Lineinfo).filter(Lineinfo.currentshift == shiftid).first()
        if not ckchange:
            return responseError('该产线已经在别的设备换型，请联系领班并退出到产线选择画面后再进,如果你就是领班并且在补录未扫描过的序列号，请结束当日班次后再补录')
        curshift = db.session.query(Shiftinfo).filter(Shiftinfo.Id == shiftid).first()
        datediff = round((datetime.datetime.now()-curshift.starttime).days*24 +
                         (datetime.datetime.now()-curshift.starttime).seconds/3600, 2)
        if datediff > 12:
            return responseError('本次扫描距离当前班次开始已经过了'+str(datediff)+'小时,请通知领班结束班次后，退出到产线选择画面重新开启新的班次')
    cksub = checkSubunique(sns, names)
    if cksub != 'pass':
        return responseError('%s的子序列号%s存在重复，无法提交，请通知管理员' % (cksub[0], cksub[1]))
    sku = res.get('sku').upper()  # 料号
    currentBox = res.get('currentBox')  # 当前箱
    currentPallet = res.get('currentPallet')  # 当前托盘号
    boxnum = 0
    palletnum = 0
    boxtotal = 0
    pallettotal = 0
    if currentBox:
        box = getPackInfo(currentBox, 'BZ')
        boxnum = box[1]
        boxtotal = box[0]
        restBox = boxtotal-boxnum
        if 1 > restBox:
            return responseError('扫描的数量超过了托盘或者包装箱的数量，请减少扫描批量后再试')
    if currentPallet:
        pallet = getPackInfo(currentPallet, 'TP')
        palletnum = pallet[1]
        pallettotal = pallet[0]
        restPallet = pallettotal-palletnum
        if 1 > restPallet:
            return responseError('扫描的数量超过了托盘或者包装箱的数量，请减少扫描批量后再试')
    try:
        allsn = []
        mysn = Scaninfo(sn=sn, shiftid=shiftid,
                        scantime=datetime.datetime.now(), sku=sku, scanqty=1)
        db.session.add(mysn)
        if currentPallet or currentBox:
            mypack = Scanpack(sn=sn, palletsn=currentPallet, boxsn=currentBox)
            db.session.add(mypack)
        for i in range(len(sns)):
            if sns[i]:
                allsn.append(Scansub(sn=sn, subsn=sns[i], subname=names[i]))
        db.session.add_all(allsn)
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('序列号有重复，请检查标签是否正确或已用过')
    if currentBox and boxnum+1 == boxtotal:
        state = finishPack(currentBox)
        if not state:
            return responseError('序列号已经扫入，但是没有成功关闭已满的包装箱，请尝试手动关闭或联系管理员')
    if currentPallet and palletnum+1 == pallettotal:
        state = finishPack(currentPallet)
        if not state:
            return responseError('序列号已经扫入，但是没有成功关闭已满的托盘，请尝试手动关闭或联系管理员')
    # 返回成功录入后的新的箱子数量和托盘数量
    return responsePost('提交成功', {'realbox': boxnum+1, 'realpallet': palletnum+len(sns)+1})


@ api.route('/submitStandard', methods=['POST'])  # 标准模板录入序列号
def submitStandard():
    res = request.json
    shiftid = res.get('shiftid')  # 班次id
    sns = res.get('sns')  # 扫描的序列号，可以多个一起提交，所以是列表
    sku = res.get('sku').upper()  # 扫描的料号
    currentBox = res.get('currentBox')  # 箱号
    currentPallet = res.get('currentPallet')  # 托号
    isleader = res.get('isleader')
    print(isleader, 'aaaaaaaaaaaaaaaaaaaaaa')
    if not isleader:
        ckchange = db.session.query(Lineinfo).filter(Lineinfo.currentshift == shiftid).first()
        if not ckchange:
            return responseError('该产线已经在别的设备换型，请返回班次界面查看当前换型后的生产信息并通知领班,如果你就是领班并且在补录未扫描过的序列号，请结束当日班次后再补录')
        curshift = db.session.query(Shiftinfo).filter(Shiftinfo.Id == shiftid).first()
        datediff = round((datetime.datetime.now()-curshift.starttime).days*24 +
                         (datetime.datetime.now()-curshift.starttime).seconds/3600, 2)
        if datediff > 12:
            return responseError('本次扫描距离当前班次开始已经过了'+str(datediff)+'小时,请通知领班结束班次后，退出到产线选择画面重新开启新的班次')
    boxnum = 0
    palletnum = 0
    boxtotal = 0
    pallettotal = 0
    print(currentBox, currentPallet)
    if currentBox:
        box = getPackInfo(currentBox, 'BZ')
        boxnum = box[1]
        boxtotal = box[0]
        restBox = boxtotal-boxnum
        if len(sns) > restBox:
            return responseError('扫描的数量超过了托盘或者包装箱的数量，请减少扫描批量后再试')
    if currentPallet:
        pallet = getPackInfo(currentPallet, 'TP')
        palletnum = pallet[1]
        pallettotal = pallet[0]
        restPallet = pallettotal-palletnum
        if len(sns) > restPallet:
            return responseError('扫描的数量超过了托盘或者包装箱的数量，请减少扫描批量后再试')
    try:
        allsn = []
        allpack = []
        for i in range(len(sns)):
            allsn.append(Scaninfo(sn=sns[i], shiftid=shiftid,
                                  scantime=datetime.datetime.now(), sku=sku, scanqty=1))
            if currentPallet or currentBox:
                allpack.append(Scanpack(sn=sns[i], palletsn=currentPallet, boxsn=currentBox))
        db.session.add_all(allsn)
        db.session.add_all(allpack)
        db.session.commit()
    except Exception:
        db.session.rollback()
        return responseError('序列号有重复，请检查标签是否正确或已用过')
    if currentBox and boxnum+len(sns) == boxtotal:
        state = finishPack(currentBox)
        if not state:
            return responseError('序列号已经扫入，但是没有成功关闭已满的包装箱，请尝试手动关闭或联系管理员')
    if currentPallet and palletnum+len(sns) == pallettotal:
        state = finishPack(currentPallet)
        if not state:
            return responseError('序列号已经扫入，但是没有成功关闭已满的托盘，请尝试手动关闭或联系管理员')
    # 返回成功录入后的新的箱子数量和托盘数量
    return responsePost('提交成功', {'realbox': boxnum+len(sns), 'realpallet': palletnum+len(sns)})


@ api.route('/submitSimple', methods=['POST'])  # 简单模板的扫码录入
def submitSimple():
    res = request.json
    shiftid = res.get('shiftid')  # 班次号
    sn = res.get('sn')  # 序列号
    mode = res.get('mode')  # 模式，有直接输数量和拆分序列号两个选项
    scanqty = res.get('scanqty')  # 扫码数量
    sku = res.get('sku').upper()  # 料号
    currentBox = res.get('currentBox')  # 箱号
    currentPallet = res.get('currentPallet')  # 托号
    isleader = res.get('isleader')
    print('isleader', isleader)
    if not isleader:
        ckchange = db.session.query(Lineinfo).filter(Lineinfo.currentshift == shiftid).first()
        if not ckchange:
            return responseError('该产线已经在别的设备换型，请返回班次界面查看当前换型后的生产信息并通知领班,如果你就是领班并且在补录未扫描过的序列号，请结束当日班次后再补录')
        curshift = db.session.query(Shiftinfo).filter(Shiftinfo.Id == shiftid).first()
        datediff = round((datetime.datetime.now()-curshift.starttime).days*24 +
                         (datetime.datetime.now()-curshift.starttime).seconds/3600, 2)
        if datediff > 12:
            return responseError('本次扫描距离当前班次开始已经过了'+str(datediff)+'小时,请通知领班结束班次后，退出到产线选择画面重新开启新的班次')
    boxnum = 0
    palletnum = 0
    boxtotal = 0
    pallettotal = 0
    if currentBox:
        box = getPackInfo(currentBox, 'BZ')
        boxnum = box[1]
        boxtotal = box[0]
        restBox = boxtotal-boxnum
        if scanqty > restBox:
            return responseError('扫描的数量超过了托盘或者包装箱的数量，请减少扫描批量后再试')
    if currentPallet:
        pallet = getPackInfo(currentPallet, 'TP')
        palletnum = pallet[1]
        pallettotal = pallet[0]
        restPallet = pallettotal-palletnum
        if scanqty > restPallet:
            return responseError('扫描的数量超过了托盘或者包装箱的数量，请减少扫描批量后再试')
    try:
        if mode:
            sns = []
            allsn = []
            allpack = []
            prefix = sn[:len(sn)-4]
            nb = int(sn[len(sn)-4:])
            for j in range(nb, nb+scanqty):
                sns.append(prefix+str(j).zfill(4))
            for i in range(len(sns)):
                allsn.append(Scaninfo(sn=sns[i], shiftid=shiftid,
                                      scantime=datetime.datetime.now(), sku=sku, scanqty=1))
                if currentPallet or currentBox:
                    allpack.append(Scanpack(sn=sns[i], palletsn=currentPallet, boxsn=currentBox))
            db.session.add_all(allsn)
            db.session.add_all(allpack)
        else:
            mysn = Scaninfo(sn=sn, shiftid=shiftid,
                            scantime=datetime.datetime.now(), sku=sku, scanqty=scanqty)
            if currentPallet or currentBox:
                mypack = Scanpack(sn=sn, palletsn=currentPallet, boxsn=currentBox)
                db.session.add(mypack)
            db.session.add(mysn)
        db.session.commit()
    except Exception:
        db.session.rollback()
        return responseError('序列号有重复，请检查标签是否正确或已用过')
    if currentBox and boxnum+scanqty == boxtotal:
        state = finishPack(currentBox)
        if not state:
            return responseError('序列号已经扫入，但是没有成功关闭已满的包装箱，请尝试手动关闭或联系管理员')
    if currentPallet and palletnum+scanqty == pallettotal:
        state = finishPack(currentPallet)
        if not state:
            return responseError('序列号已经扫入，但是没有成功关闭已满的托盘，请尝试手动关闭或联系管理员')
    # 返回成功录入后的新的箱子数量和托盘数量
    return responsePost('提交成功', {'realbox': boxnum+scanqty, 'realpallet': palletnum+scanqty})


@ api.route('/closePallet', methods=['PUT'])  # 根据序列号关闭托盘
def closePallet():
    res = request.json
    sn = res.get('sn')
    now = datetime.datetime.now()
    try:
        db.session.query(Palletinfo).filter(Palletinfo.palletsn == sn).update({'endtime': now})
        db.session.commit()
        return responsePut('关闭成功')
    except Exception:
        db.session.rollback()
        return responseError('手工关闭失败，请联系管理员')


@ api.route('/getPalletInfo', methods=['GET'])  # 获取托盘信息
def getPalletInfo():
    res = request.args
    sn = res.get('sn')  # 序列号
    ptype = res.get('ptype')  # 托盘类型，箱子还是托盘
    result = getPackInfo(sn, ptype)
    return responseGet('获取成功', {'total': result[0], 'real': result[1]})


def finishPack(sn):  # 完成箱子
    try:
        db.session.query(Palletinfo).filter(Palletinfo.palletsn ==
                                            sn).update({'endtime': datetime.datetime.now()})
        db.session.commit()
        return True
    except Exception:
        db.session.rollbak()
        traceback.print_exc()
        return False


def getPackInfo(sn, ptype):  # 根据序列号，类型（箱还是托）获取包装的信息，输出总数和已包数量
    total = db.session.query(Palletinfo.setqty).filter(Palletinfo.palletsn ==
                                                       sn).filter(Palletinfo.ptype == ptype).scalar()
    if ptype == 'TP':
        real = db.session.query(func.sum(Scaninfo.scanqty)).join(
            Scanpack, Scaninfo.sn == Scanpack.sn).filter(Scanpack.palletsn == sn).scalar()
    elif ptype == 'BZ':
        real = db.session.query(func.sum(Scaninfo.scanqty)).join(
            Scanpack, Scaninfo.sn == Scanpack.sn).filter(Scanpack.boxsn == sn).scalar()
    if not real:
        real = 0
    return [total, real]


@api.route('/getLines', methods=['GET'])  # 获取产线的树状结构
def getLines():
    res = request.args
    plant = res.get('plant')
    if not plant:
        plant = 'Suzhou'
    lines = db.session.query(Lineinfo).filter(
        Lineinfo.isactive == 1).filter(Lineinfo.plant == plant).order_by(Lineinfo.linename).all()
    dic = {}
    for ll in lines:
        if ll.area in lineShort.keys():
            area = lineShort[ll.area]
        else:
            area = ll.area
        if area in dic.keys():
            dic[area].append({
                'name': ll.linename,
                'icon': getServer()['iconUrl']+ll.linename+'.jpg'
            })
        else:
            dic[area] = [{
                'name': ll.linename,
                'icon': getServer()['iconUrl']+ll.linename+'.jpg'
            }]
    return responseGet('成功获取信息', {'lines': dic})


@api.route('/getShiftInfo', methods=['GET'])  # 获取当前可用未结的班次信息，如果当日之前有未结，则不允许新开班次
def getShiftInfo():
    res = request.args
    linename = res.get('linename')  # 产线
    shiftdate = res.get('shiftdate')  # 日期
    shifttype = res.get('shifttype')  # 班次类型，白，中，夜
    shiftInfo = db.session.query(Shiftinfo).filter(Shiftinfo.linename == linename).filter(Shiftinfo.shifttype == shifttype).filter(
        func.date_format(Shiftinfo.starttime, '%Y-%m-%d') == shiftdate).filter(Shiftinfo.finishtime.is_(None)).first()
    shiftInfo = shift_schema.dump(shiftInfo)
    lastshift = db.session.query(Shiftinfo).filter(Shiftinfo.linename == linename).filter(Shiftinfo.shifttype == shifttype).filter(
        func.date_format(Shiftinfo.starttime, '%Y-%m-%d') < shiftdate).filter(Shiftinfo.finishtime.is_(None)).first()
    if lastshift:
        return responseError(lastshift.linename+'产线由于上个'+lastshift.shifttype+'没有关闭导致不允许开启新的'+lastshift.shifttype+'班次，请通知领班关闭上个班次后再试')
    return responseGet('成功获取信息', {'shiftInfo': shiftInfo, 'serverDate': datetime.datetime.strftime(datetime.datetime.today(), '%Y-%m-%d')})


@api.route('/getShiftsInfo', methods=['GET'])  # 获得产线当日某个白班，夜班的所有班次
def getShiftsInfo():
    res = request.args
    linename = res.get('linename')
    shiftdate = res.get('shiftdate')
    shifttype = res.get('shifttype')
    shiftsInfo = db.session.query(Shiftinfo).filter(Shiftinfo.linename == linename).filter(Shiftinfo.shifttype == shifttype).filter(
        func.date_format(Shiftinfo.starttime, '%Y-%m-%d') == shiftdate).all()
    shiftsInfo = shifts_schema.dump(shiftsInfo)
    return responseGet('成功获取信息', {'shiftsInfo': shiftsInfo})


@api.route('/searchSKU', methods=['GET'])  # 通过SN获取SKU信息便于扫码是录入
def searchSKU():
    res = request.args
    query = res.get('query')
    skuArr = []
    if len(query) > 3:
        sku = db.session.query(Skuinfo).filter(Skuinfo.sku.like('%{0}%'.format(query))).all()
        for s in sku:
            skuArr.append({'name': s.sku, 'label': s.sku})
        print(skuArr)
    return responseGet('成功获取信息', {'skuArr': skuArr})


@api.route('/getSKUbySN', methods=['GET'])  # 通过SN获取SKU信息便于扫码是录入
def getSKUbySN():
    res = request.args
    sn = res.get('sn')[:5]
    sku = db.session.query(Skuinfo).filter(Skuinfo.snverify == sn).all()
    skuArr = []
    for s in sku:
        skuArr.append({'name': s.sku, 'label': s.sku})
    print(skuArr)
    return responseGet('成功获取信息', {'skuArr': skuArr})


@api.route('/getTPbySKU', methods=['GET'])  # 通过料号找到存在的扫码模板，如果没有则默认未标准模板
def getTPbySKU():
    res = request.args
    sku = res.get('sku').upper()
    skuinfo = db.session.query(Skuinfo).join(Routing, Skuinfo.sku ==
                                             Routing.sku).filter(Skuinfo.sku == sku).first()
    if not skuinfo:
        return responseError('没有找到料号'+sku+'或标准工时的信息，请联系领班添加')
    tp = db.session.query(Ctqinfo).filter(Ctqinfo.sku == sku).first()
    if tp:
        return responseGet('成功获取CTQ模板', {'tp': 'CTQ模板'})
    return responseGet('没有找到CTQ模板', {'tp': '标准模板'})


@api.route('/addnewShift', methods=['POST'])  # 补录并且新开班次
def addnewShift():
    res = request.json
    shiftdate = res.get('shiftdate')  # 日期
    fdate = res.get('fdate')  # 日期
    lineleader = res.get('lineleader')  # 日期
    shifttype = res.get('shifttype')  # 白，中，夜班
    headcount = float(res.get('headcount'))  # 人数
    sku = res.get('sku').upper()  # 料号
    scantemplate = res.get('scantemplate')  # 扫码模板
    starttime = shiftdate+' '+res.get('starttime')+':00'  # 开始时间
    finishtime = fdate+' '+res.get('finishtime')+':00'  # 开始时间
    linename = res.get('linename')  # 产线
    curshift = db.session.query(Shiftinfo).filter(Shiftinfo.linename == linename).filter(Shiftinfo.shifttype == shifttype).filter(
        Shiftinfo.sku == sku).filter(func.date(Shiftinfo.starttime) == shiftdate).first()
    if curshift:
        return responseError('无法新建班次补录，由于当班已经建立了此料号的班次，请使用正常补录搜索日期选择该物料进行补录')
    try:
        routing = getRouting(sku, linename, headcount)
        newShift = Shiftinfo(shifttype=shifttype, scantemplate=scantemplate, finishtime=finishtime, lineleader=lineleader,
                             headcount=headcount, starttime=starttime, linename=linename, sku=sku, routing=routing)
        db.session.add(newShift)
        db.session.flush()
        shiftid = newShift.Id
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('新建班次失败，请退出重试或联系管理员')
    return responsePost('成功', {'shiftid': shiftid})  # 成功返回班次号


@api.route('/newShift', methods=['POST'])  # 新开班次
def newShift():
    res = request.json
    shiftdate = res.get('shiftdate')  # 日期
    today = datetime.datetime.strftime(datetime.date.today(), '%Y-%m-%d')
    shiftdate = today
    shifttype = res.get('shifttype')  # 白，中，夜班
    headcount = float(res.get('headcount'))  # 人数
    sku = res.get('sku').upper()  # 料号
    scantemplate = res.get('scantemplate')  # 扫码模板
    starttime = shiftdate+' '+res.get('starttime')+':00'  # 开始时间
    linename = res.get('linename')  # 产线
    curshift = db.session.query(Shiftinfo).filter(Shiftinfo.linename == linename).filter(Shiftinfo.shifttype == shifttype).filter(
        func.date_format(Shiftinfo.starttime, '%Y-%m-%d') <= shiftdate).filter(Shiftinfo.finishtime.is_(None)).first()
    if curshift:
        return responseError('无法新建班次，可能由于在其他设备已经建立了班次，请退出到产线选择界面后重新进入查看')
    try:
        routing = getRouting(sku, linename, headcount)
        newShift = Shiftinfo(shifttype=shifttype, scantemplate=scantemplate,
                             headcount=headcount, starttime=starttime, linename=linename, sku=sku, routing=routing)
        db.session.add(newShift)
        db.session.flush()
        shiftid = newShift.Id
        db.session.query(Lineinfo).filter(Lineinfo.linename ==
                                          linename).update({'currentshift': shiftid})
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('新建班次失败，请退出重试或联系管理员')
    return responsePost('成功', {'shiftid': shiftid})  # 成功返回班次号


@api.route('/changeShift', methods=['POST'])  # 换型操作
def changeShift():
    res = request.json
    Id = res.get('Id')
    shifttype = res.get('shifttype')  # 白，中，夜班
    headcount = float(res.get('headcount'))  # 人数
    sku = res.get('sku').upper()  # 料号
    scantemplate = res.get('scantemplate')  # 扫码模板
    starttime = datetime.datetime.now()  # 开始时间
    linename = res.get('linename')  # 产线
    curshift = db.session.query(Shiftinfo).filter(Shiftinfo.linename == linename).filter(Shiftinfo.shifttype == shifttype).filter(
        Shiftinfo.Id > Id).filter(Shiftinfo.finishtime.is_(None)).first()
    if curshift:
        return responseError('无法换型，可能由于在其他设备已经换型，请退出到产线选择界面后重新进入查看')
    thisshift = db.session.query(Shiftinfo).filter(Shiftinfo.Id == Id).first()
    datediff = round((datetime.datetime.now()-thisshift.starttime).days*24 +
                     (datetime.datetime.now()-thisshift.starttime).seconds/3600, 2)
    if datediff > 12:
        return responseError('本次换型距离上个班次开始已经过了'+str(datediff)+'小时,请通知领班结束之前的班次后，退出到产线选择画面重新开启今天新的班次')
    try:
        print('aaaaaaaaaaaa', Id)
        db.session.query(Shiftinfo).filter(Shiftinfo.Id == Id).update(
            {'finishtime': datetime.datetime.now()})
        routing = getRouting(sku, linename, headcount)
        newShift = Shiftinfo(shifttype=shifttype, scantemplate=scantemplate,
                             headcount=headcount, starttime=starttime, linename=linename, sku=sku, routing=routing)
        db.session.add(newShift)
        db.session.flush()
        shiftid = newShift.Id
        db.session.query(Lineinfo).filter(Lineinfo.linename ==
                                          linename).update({'currentshift': shiftid})
        db.session.commit()
    except Exception:
        db.session.rollback()
        traceback.print_exc()
        return responseError('新建班次失败，请退出重试或联系管理员')
    return responsePost('成功', {'shiftid': shiftid})


@api.route('/getOpenPallet', methods=['GET'])  # 获取可用的托盘和箱子列表，同时返回上次的托盘和箱子总数做为默认值返回
def getOpenPallet():
    res = request.args
    sku = res.get('sku').upper()
    lastPallet = 0
    lastBox = 0
    pallets = db.session.query(Palletinfo).filter(
        Palletinfo.sku == sku).filter(Palletinfo.endtime.is_(None)).all()
    lastP = db.session.query(Palletinfo).filter(
        Palletinfo.sku == sku).filter(Palletinfo.setqty > 0).filter(Palletinfo.ptype == 'TP').order_by(desc(Palletinfo.Id)).first()
    if lastP:
        lastPallet = lastP.setqty
    lastB = db.session.query(Palletinfo).filter(
        Palletinfo.sku == sku).filter(Palletinfo.setqty > 0).filter(Palletinfo.ptype == 'BZ').order_by(desc(Palletinfo.Id)).first()
    if lastB:
        lastBox = lastB.setqty
    boxArr = []
    palletArr = []
    for p in pallets:
        if p.ptype == 'TP':
            palletArr.append(p.palletsn)
        elif p.ptype == 'BZ':
            boxArr.append(p.palletsn)
    print('aaa', lastPallet, lastBox)
    return responseGet('获取空箱成功', {'boxArr': boxArr, 'palletArr': palletArr, 'lastPallet': lastPallet, 'lastBox': lastBox})

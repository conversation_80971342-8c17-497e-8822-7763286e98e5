from flask import jsonify, request
from config import config, myEnv
import traceback
from itsdangerous import TimedSerializer
from functools import wraps


def create_token(api_user):  # 创建token
    s = TimedSerializer(config[myEnv].SECRET_KEY)
    token = s.dumps(api_user)
    return token


def login_required(view_func):  # 通过一个装饰函数来实现登录验证
    @wraps(view_func)
    def verify_token(*args, **kwargs):
        try:
            # email = request.headers["email"]
            # auth = request.headers["auth"]
            # # print("ddd", auth, email)
            # user = db.session.query(User).filter(User.email == email).first()
            # if(user.auth != auth):
            #     return responseError('登录校验失败，请重新登陆！')
            # 在请求头上拿到token
            token = request.headers["token"]
            # print('token', token)
        except Exception:
            traceback.print_exc()
            return responseError('登录失败！')

        s = TimedSerializer(config[myEnv].SECRET_KEY)
        try:
            s.loads(token, max_age=7200)
        except Exception:
            return responseExpire('登录已过期！请重新登录！')

        return view_func(*args, **kwargs)

    return verify_token


def responseGet(msg, data={}):  # get方法返回成功格式化
    meta = {'status': 200, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responsePost(msg, data={}):  # post方法返回成功格式化
    meta = {'status': 201, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responsePut(msg, data={}):  # put方法返回成功格式化
    meta = {'status': 202, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responseDelete(msg, data={}):  # delete方法返回成功格式化
    meta = {'status': 203, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responseError(msg, data={}):  # request失败返回格式化
    meta = {'status': 204, 'msg': msg}
    return jsonify(meta=meta, data=data)


def responseExpire(msg, data={}):  # request超时返回格式化
    meta = {'status': 205, 'msg': msg}
    return jsonify(meta=meta, data=data)

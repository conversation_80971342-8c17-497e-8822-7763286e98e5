# FeedData 数据同步问题分析与解决方案

## 📋 问题描述

在 `columns.tsx` 的 `beforeSure` 回调中，`console.log(feedata)` 打印的是默认值，而不是在 `feeForm` 组件中修改后的值。

## 🔍 问题分析

### 1. **数据流向**
```
columns.tsx (定义 feedata) 
    ↓ 
useColumns() 返回 feedata
    ↓
feeForm 组件通过 useColumns() 获取 feedata
    ↓
feeForm 组件修改 feedata 的值
    ↓
columns.tsx 的 beforeSure 中访问 feedata (期望获取修改后的值)
```

### 2. **问题根源**

#### 可能原因1：响应式对象引用问题
```typescript
// columns.tsx 中
const feedata = reactive({
  freight_fee: 0,
  logistic_company: "",
  // ...
});

// feeForm 中
const { feedata } = useColumns();
feedata.logistic_company = "新值"; // 修改
```

#### 可能原因2：组件实例隔离
- `feeForm` 组件可能创建了新的 `useColumns()` 实例
- 导致 `feedata` 对象不是同一个引用

#### 可能原因3：时机问题
- `beforeSure` 执行时，组件内的修改可能还没有同步

## 🔧 解决方案

### 方案1：通过组件 ref 获取数据（推荐）

**1. 在 feeForm 组件中暴露获取数据的方法：**
```typescript
// feeForm/index.vue
const getFeedData = () => {
  return {
    freight_fee: feedata.freight_fee,
    freight_calc_method: feedata.freight_calc_method,
    freight_unit_price: feedata.freight_unit_price,
    freight_adjust: feedata.freight_adjust,
    freight_adjust_reason: feedata.freight_adjust_reason,
    logistic_company: feedata.logistic_company,
    logistic_remark: feedata.logistic_remark,
    freight_total_fee: feedata.freight_fee + feedata.freight_adjust
  };
};

defineExpose({
  getFeedData
});
```

**2. 在 columns.tsx 中通过 ref 获取：**
```typescript
beforeSure: async (done, { options }) => {
  // 从组件实例中获取最新数据
  if (formRef.value && typeof formRef.value.getFeedData === 'function') {
    const latestFeedData = formRef.value.getFeedData();
    console.log("组件内最新的 feedata:", latestFeedData);
    
    // 使用最新数据进行后续操作
    // 调用API保存数据等
  }
}
```

### 方案2：使用事件通信

**1. 在 feeForm 组件中发射事件：**
```typescript
// feeForm/index.vue
const emit = defineEmits(['update:feedata']);

const showdata = (obj: any, type: string, index: number) => {
  // 修改数据
  feedata.logistic_company = obj.template;
  feedata.freight_fee = obj.freight_fee;
  // ...
  
  // 发射更新事件
  emit('update:feedata', feedata);
};
```

**2. 在父组件中监听事件：**
```typescript
// columns.tsx
let latestFeedData = null;

const handleFeedataUpdate = (data) => {
  latestFeedData = data;
};

// 在 contentRenderer 中
contentRenderer: () => h(feeForm, { 
  ref: formRef,
  'onUpdate:feedata': handleFeedataUpdate
});
```

### 方案3：使用 Pinia 状态管理

**1. 创建 store：**
```typescript
// stores/fee.ts
export const useFeeStore = defineStore('fee', () => {
  const feedata = reactive({
    freight_fee: 0,
    logistic_company: "",
    // ...
  });
  
  return { feedata };
});
```

**2. 在组件中使用：**
```typescript
// feeForm/index.vue 和 columns.tsx 中
const feeStore = useFeeStore();
const { feedata } = feeStore;
```

## 🎯 推荐实现

基于当前代码结构，推荐使用 **方案1（组件 ref）**：

### 1. **修改后的 columns.tsx**
```typescript
beforeSure: async (done, { options }) => {
  function chores() {
    message(`运费计算成功！`, { type: "success" });
    getList();
    done();
  }
  
  // 获取组件内最新数据
  let currentFeedData = feedata; // 默认使用原始数据
  
  if (formRef.value && typeof formRef.value.getFeedData === 'function') {
    currentFeedData = formRef.value.getFeedData();
    console.log("最新的运费数据:", currentFeedData);
  }
  
  // 使用最新数据调用API
  // await saveFeeData(currentFeedData);
  
  chores();
}
```

### 2. **修改后的 feeForm 组件**
```typescript
// 暴露获取数据的方法
const getFeedData = () => {
  return {
    freight_fee: feedata.freight_fee,
    freight_calc_method: feedata.freight_calc_method,
    freight_unit_price: feedata.freight_unit_price,
    freight_adjust: feedata.freight_adjust,
    freight_adjust_reason: feedata.freight_adjust_reason,
    logistic_company: feedata.logistic_company,
    logistic_remark: feedata.logistic_remark,
    freight_total_fee: feedata.freight_fee + feedata.freight_adjust
  };
};

defineExpose({
  getFeedData
});
```

## 🔍 调试方法

### 1. **验证数据引用**
```typescript
// 在 feeForm 组件中
console.log("feeForm 中的 feedata 引用:", feedata);
console.log("feedata 是否为 reactive:", isReactive(feedata));

// 在 columns.tsx 中
console.log("columns 中的 feedata 引用:", feedata);
console.log("两个引用是否相同:", feedata === formRef.value?.feedata);
```

### 2. **监听数据变化**
```typescript
// 在 columns.tsx 中
watch(feedata, (newVal) => {
  console.log("feedata 发生变化:", newVal);
}, { deep: true });
```

### 3. **检查组件生命周期**
```typescript
// 在 feeForm 组件中
onMounted(() => {
  console.log("feeForm 组件挂载，feedata:", feedata);
});

onUnmounted(() => {
  console.log("feeForm 组件卸载，最终 feedata:", feedata);
});
```

## ✅ 验证步骤

1. **打开运费计算弹窗**
2. **选择一个运费方案**
3. **在控制台查看日志**：
   - `feeForm 中修改后的 feedata:` - 应该显示修改后的值
   - `组件内最新的 feedata:` - 应该显示相同的修改后的值
4. **点击确定按钮**
5. **验证数据是否正确传递**

## 🎉 总结

通过使用组件 ref 和 `defineExpose`，我们可以确保在 `beforeSure` 回调中获取到组件内最新的数据状态，解决了数据同步问题。

**关键点：**
- ✅ 使用 `defineExpose` 暴露组件内部方法
- ✅ 通过 `formRef.value.getFeedData()` 获取最新数据
- ✅ 在 `beforeSure` 中使用最新数据进行后续操作
- ✅ 保持原有的响应式特性

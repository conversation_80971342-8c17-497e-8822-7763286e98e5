<script setup lang="ts">
import { onMounted, ref } from "vue";
import Guage from "./controls/guage.vue";

onMounted(() => {
  console.log("temp mounted");
});
</script>

<template>
  <div style="text-align: right">
    <span>采集时间：2025-01-01 12:00:00</span>
  </div>
  <div class="guage_panel">
    <Guage />
    <Guage />
    <Guage />
    <Guage />
    <Guage />
    <Guage />
    <Guage />
  </div>
</template>

<style lang="scss" scoped>
/* 默认明亮模式颜色 */
:root {
  --el-color-primary: #409eff;
  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
  --el-color-info: #909399;
  --el-background-color: #ffffff;
  --el-text-color: #303133;
}

/* 黑暗模式颜色 */
@media (prefers-color-scheme: dark) {
  :root {
    --el-color-primary: #79bbff;
    --el-color-success: #85ce61;
    --el-color-warning: #eebe77;
    --el-color-danger: #f78989;
    --el-color-info: #a6a9ad;
    --el-background-color: #1f1f1f;
    --el-text-color: #e5e5e5;
  }
}

.guage_panel {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
}
</style>

from extensions import db
from werkzeug.security import generate_password_hash, check_password_hash


class Publicuser(db.Model):
    __bind_key__ = "pentair_public"
    __tablename__ = "userinfo"
    Id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(64))
    eid = db.Column(db.String(7))
    password_hash = db.Column(db.String(255))

    def __repr__(self):
        return '<User %r>' % self.Id

    @property
    def password(self):
        raise AttributeError("密码不允许读取")

    # 转换密码为hash存入数据库
    @password.setter
    def password(self, password):
        self.password_hash = generate_password_hash(password)

    # 检查密码
    def check_password_hash(self, password):
        return check_password_hash(self.password_hash, password)

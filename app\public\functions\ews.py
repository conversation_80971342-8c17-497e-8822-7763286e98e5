from exchangelib import (
    Credentials,
    Account,
    DELEGATE,
    Configuration,
    FileAttachment,
    Message,
    HTMLBody
)
import threading
from config import config, myEnv
import smtplib
import traceback
from email.mime.text import MIMEText
from email.header import Header
from email.mime.base import MIMEBase
from email import encoders
from email.mime.multipart import MIMEMultipart


class EmailLoader:
    def __init__(self, username, password, mailserver, address):
        self.username = username
        self.password = password
        self.mailserver = mailserver
        self.address = address
        self.account = self.connect_to_mailbox()

    def connect_to_mailbox(self):
        cred = Credentials(self.username, self.password)
        mailconfig = Configuration(server=self.mailserver, credentials=cred)
        account = Account(
            primary_smtp_address=self.address,
            config=mailconfig,
            autodiscover=False,
            access_type=DELEGATE,


        )
        return account

    def send_mail(self, toArr, subject, body, *args):
        m = Message(account=self.account, subject=subject, body=HTMLBody(body),
                    to_recipients=toArr)
        if len(args) == 1:
            for f in args[0]:
                filename = f[f.rfind('/')+1:]
                # print(filename)
                myfile = FileAttachment(name=filename, content=open(
                    f, 'rb').read())
                m.attach(myfile)
        m.send_and_save()

    def send_mailcc(self, toArr, ccArr, subject, body, *args):
        m = Message(account=self.account, subject=subject, body=HTMLBody(body),
                    to_recipients=toArr, cc_recipients=ccArr)
        if len(args) == 1:
            for f in args[0]:
                filename = f[f.rfind('/')+1:]
                # print(filename)
                myfile = FileAttachment(name=filename, content=open(
                    f, 'rb').read())
                m.attach(myfile)
        m.send_and_save()


def sendMailSMTPcc(to, cc, title, content, *args):
    server = config[myEnv].smtpLoader['server']  # 设置服务器
    sender = config[myEnv].smtpLoader['sender']
    user = config[myEnv].smtpLoader['user']
    password = config[myEnv].smtpLoader['password']
    port = config[myEnv].smtpLoader['port']
    message = MIMEMultipart()
    msgtext = MIMEText(content, _subtype='html', _charset='utf-8')  # _subtype有plain,html等格式，避免使用错误
    message.attach(msgtext)
    message['From'] = Header("Pentair-Suzhou IQC")
    message['To'] = Header(';'.join(to))
    # message['Bcc'] = Header('<EMAIL>', 'utf-8')
    # receivers = to+['<EMAIL>']
    receivers = to
    print(receivers)
    if len(cc) >= 1:
        message['cc'] = Header(';'.join(cc))
        receivers = receivers+cc
    subject = title
    message['Subject'] = Header(subject, 'utf-8')
    if len(args) == 1:
        for f in args[0]:
            att1 = MIMEBase("application", "octet-stream")
            att1.set_payload(open(f, "rb").read())
            hd = f[f.rfind('/')+1:]
            att1.add_header("Content-Disposition", "attachment",
                            filename=Header(hd, "utf-8").encode())
            encoders.encode_base64(att1)
            message.attach(att1)
    try:
        if user:
            smtpObj = smtplib.SMTP_SSL(server, port)
            smtpObj.login(user, password)
        else:
            smtpObj = smtplib.SMTP()
            smtpObj.connect(server, port)
        smtpObj.sendmail(sender, receivers, message.as_string())
        print("邮件发送成功")
    except smtplib.SMTPException:
        traceback.print_exc()
        print('失败')


def sendMailSMTP(to, title, content, *args):
    server = config[myEnv].smtpLoader['server']  # 设置服务器
    sender = config[myEnv].smtpLoader['sender']
    user = config[myEnv].smtpLoader['user']
    password = config[myEnv].smtpLoader['password']
    port = config[myEnv].smtpLoader['port']
    print(server, sender, user, password, port)
    message = MIMEMultipart()
    msgtext = MIMEText(content, _subtype='html', _charset='utf-8')  # _subtype有plain,html等格式，避免使用错误
    message.attach(msgtext)
    message['From'] = Header("Pentair-Suzhou IQC")
    message['To'] = Header(';'.join(to))
    # message['Bcc'] = Header('<EMAIL>', 'utf-8')
    # receivers = to+['<EMAIL>']
    receivers = to
    subject = title
    message['Subject'] = Header(subject, 'utf-8')
    if len(args) == 1:
        for f in args[0]:
            att1 = MIMEBase("application", "octet-stream")
            att1.set_payload(open(f, "rb").read())
            hd = f[f.rfind('/')+1:]
            att1.add_header("Content-Disposition", "attachment",
                            filename=Header(hd, "utf-8").encode())
            encoders.encode_base64(att1)
            message.attach(att1)
    try:
        if user:
            smtpObj = smtplib.SMTP_SSL(server, port)
            smtpObj.login(user, password)
        else:
            smtpObj = smtplib.SMTP()
            smtpObj.connect(server, port)
        smtpObj.sendmail(sender, receivers, message.as_string())
        print("邮件发送成功")
    except smtplib.SMTPException:
        traceback.print_exc()
        print('失败')


def sendMail(to, title, content, *args):
    # e = EmailLoader(config[myEnv].mailLoader[0], config[myEnv].mailLoader[1],
    #                 config[myEnv].mailLoader[2], config[myEnv].mailLoader[3])
    if len(args) == 1:
        ccArr = args[0]
        sendMailSMTPcc(to, ccArr, title, content)
        # e.send_mailcc(to, ccArr, title, content)
    else:
        sendMailSMTP(to, title, content)
        # e.send_mailcc(to, title, content)


def sendMailwithAttachments(to, title, content, attachments, *args):
    # e = EmailLoader(config[myEnv].mailLoader[0], config[myEnv].mailLoader[1],
    #                 config[myEnv].mailLoader[2], config[myEnv].mailLoader[3])
    if len(args) == 1:
        ccArr = args[0]
        sendMailSMTPcc(to, ccArr, title, content, attachments)
        # e.send_mailcc(to, ccArr, title, content, attachments)
    else:
        sendMailSMTP(to, title, content, attachments)
        # e.send_mailcc(to, title, content, attachments)


def sendMailTread(to, title, content, *args):
    t = threading.Thread(target=sendMail, args=(to, title, content, *args))
    t.setDaemon(True)
    t.start()


def sendMailwithAttachmentsTread(to, title, content, attachments, *args):
    t = threading.Thread(target=sendMailwithAttachments,
                         args=(to, title, content, attachments, *args))
    t.setDaemon(True)
    t.start()


if __name__ == "__main__":
    # sendMailwithAttachmentsTread(['<EMAIL>'], 'test', 'test',
    #                              ['E:/phpstudy_pro/WWW/receiving/uploads/CARS/9.xlsm'])
    sendMailSMTP(['<EMAIL>'],  'test', 'testcontnt',
                 [r'C:/test.txt'])

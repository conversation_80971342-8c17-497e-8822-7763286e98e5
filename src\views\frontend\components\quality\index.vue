<script setup lang="ts">
import { useColumns } from "./utils/columns";
import { ref } from "vue";
const { dataList, columns, loadingConfig, adaptiveConfig, loading } =
  useColumns();
</script>

<template>
  <div class="main">
    <div class="header">
      <span class="lightfont">质量信息</span>
    </div>
    <div class="content">
      <pure-table
        ref="tableRef"
        border
        stripe
        adaptive
        :adaptiveConfig="adaptiveConfig"
        row-key="id"
        alignWhole="center"
        showOverflowTooltip
        :loading="loading"
        :loading-config="loadingConfig"
        :data="dataList"
        :columns="columns"
        style="width: 100%"
        max-height="100%"
        height="100%"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 100%;
  flex: 1;
  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    .lightfont {
      font-size: 18px;
      text-shadow:
        0 0 10px red,
        0 0 20px red,
        0 0 30px red,
        0 0 40px red;
    }
  }
  .content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    color: grey;
    height: calc(100% - 40px);
  }
}

:deep(.el-table__row) {
  background-color: transparent;
}

.pure-table {
  height: 100% !important;
  :deep(.el-table--border) {
    // height: 100% !important;
  }
}
</style>
./utils/columns

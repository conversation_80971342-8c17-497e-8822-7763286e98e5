import functools
from apscheduler.schedulers.background import BlockingScheduler
import requests
from sqlalchemy.ext.declarative import declarative_base
import sqlalchemy as db
from o365pentairnew import O365
from sendmail import Sendmail
from localscript import startDaily, startMonthly, dmDailyTask, clearFiles
from backupScript import backupMysql,  backupMssqlVOC, update_libs
from utils.mylog import logger
from datetime import datetime
import multiprocessing
import pandas as pd
from sqlalchemy import create_engine,distinct,func,inspect,engine,or_
from sqlalchemy.orm import sessionmaker, scoped_session
from typing import List

Base = declarative_base()
api_url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
authorization_token = '787047b374d542bfa042972686f15889.gzWbGhg07gjzSjBE'

class ZSDDN(Base):
    __tablename__ = "ZSDDN"
    TimeSN=db.Column(db.String(50), primary_key=True)
    DN=db.Column(db.String(50))
    SalesOrg=db.Column(db.String(50))
    CreatedBy=db.Column(db.String(50))
    CreatedOn=db.Column(db.Date)
    ShippingPoint=db.Column(db.String(50))
    CustomerPO=db.Column(db.String(50))
    CustomerPODate=db.Column(db.Date)
    SoldTo=db.Column(db.String(50))
    ShipTo=db.Column(db.String(50))
    City=db.Column(db.String(50))
    Street=db.Column(db.String(50))
    Street2=db.Column(db.String(50))
    Remark=db.Column(db.String(50))
    PlannedDate=db.Column(db.Date)
    Incoterms1=db.Column(db.String(50))
    Incoterms2=db.Column(db.String(50))
    ShippingCondition=db.Column(db.String(50))
    ShippingConditionDes=db.Column(db.String(50))
    Amount=db.Column(db.Float)
    Currency=db.Column(db.String(50))
    SoldToName=db.Column(db.String(50))
    DeleteMark=db.Column(db.String(50))

class Master(Base):
    __tablename__ = "tms_master"
    id = db.Column(db.Integer, primary_key=True)
    BU_NO = db.Column(db.String(4))
    DN_NO = db.Column(db.String(255))
    cs_specialist = db.Column(db.String(32))
    payer_company = db.Column(db.String(32))
    receiver_name = db.Column(db.String(32))
    receiver_address = db.Column(db.String(80))
    delivery_terms = db.Column(db.String(3))
    goods_value = db.Column(db.Float)
    volume = db.Column(db.Float)
    weight = db.Column(db.Float)
    freight_fee = db.Column(db.Float)
    fee_remark = db.Column(db.String(50))
    status = db.Column(db.Integer)
    import_time = db.Column(db.DateTime)
    business_type = db.Column(db.Integer)
    warehouse = db.Column(db.Integer)
    shipping_point = db.Column(db.String(4))
    release_time = db.Column(db.DateTime)
    freight_calc_method = db.Column(db.Integer)
    freight_unit_price = db.Column(db.Float)
    freight_adjust = db.Column(db.Float)
    freight_adjust_reason = db.Column(db.String(255))
    is_valid = db.Column(db.Boolean)
    receiver_province = db.Column(db.String(16))
    receiver_city = db.Column(db.String(16))
    receiver_district = db.Column(db.String(16))
    customer_mail = db.Column(db.String(32))
    warehouse_remark = db.Column(db.String(255))
    logistic_remark = db.Column(db.String(255))
    receiver_phone = db.Column(db.String(32))

class Connection_LeanServer:
    def __init__(self):
        self.db_url = 'mssql+pymssql://sa:xiaobo*748180@localhost/EMDI_TMS?charset=utf8'
        self.__engine = None
        self.__Session = None

    def __enter__(self):
        self.__engine = create_engine(
            self.db_url,
            pool_size=5,            # 连接池大小
            max_overflow=10,        # 最大溢出连接数
            pool_timeout=30,        # 获取连接超时时间(秒)
            echo=False,            # 是否输出SQL日志
            connect_args={
                'login_timeout': 10,  # 登录超时时间(秒)
                'charset': 'utf8'     # 字符集设置
            }
        )
        self.__Session = scoped_session(sessionmaker(bind=self.__engine))
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.__Session is not None:
            self.__Session.remove()
        if self.__engine is not None:
            self.__engine.dispose()

    def get_session(self):
        return self.__Session()

class Connection_SAP:
    def __init__(self):
        self.db_url = 'mssql+pymssql://sa:xiaobo*748180@localhost:1433/SAP_TEST'
        self.__engine = None
        self.__Session = None

    def __enter__(self):
        self.__engine = create_engine(
            self.db_url,
            pool_size=5,            # 连接池大小
            max_overflow=10,        # 最大溢出连接数
            pool_timeout=30,        # 获取连接超时时间(秒)
            echo=False,            # 是否输出SQL日志
            connect_args={
                'login_timeout': 10,  # 登录超时时间(秒)
                'charset': 'utf8'     # 字符集设置
            }
        )
        self.__Session = scoped_session(sessionmaker(bind=self.__engine))
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.__Session is not None:
            self.__Session.remove()
        if self.__engine is not None:
            self.__engine.dispose()

    def get_session(self):
        return self.__Session()

def run_with_timeout(func, timeout):
    process = multiprocessing.Process(target=func)  # 使用 Process
    process.start()
    process.join(timeout)  # 设置超时
    if process.is_alive():
        logger.critical("收件任务超时，强制结束。")
        process.terminate()  # 强制终止进程
        process.join()  # 等待进程结束


class SyncTmsData():
    def __init__(self):
        self.sched = BlockingScheduler(timezone='Asia/Shanghai')
        self.sched.add_job(lambda: run_with_timeout(handleData, 250), 'cron', second='*/20')
        logger.info('tms.py - scheduler started======'+datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        self.sched.start()


def just_one_instance(func):
    @functools.wraps(func)
    def f(*args, **kwargs):
        import socket
        try:
            global s
            s = socket.socket()
            host = socket.gethostname()
            s.bind((host, 60334))
        except Exception:
            print('already has an instance')
            return None
        return func(*args, **kwargs)
    return f


@just_one_instance
def mainProgram():
    SyncTmsData()

def handleData():
    with Connection_LeanServer() as conn_lean,Connection_SAP()as conn_sap:
        # try:
        lean=conn_lean.get_session()
        sap=conn_sap.get_session()
        # 同步无效工单
        invalid_items=sap.query(distinct(ZSDDN.TimeSN)).filter(ZSDDN.DeleteMark=='X').all()
        invalid_items=[item[0] for item in invalid_items]
        ExistMasterItem:List[Master]=lean.query(Master).filter(Master.release_time.in_(invalid_items)).filter(Master.is_valid==True).all()
        for item in ExistMasterItem:
            item.is_valid=False
        lean.commit()
        
        # 同步有效工单
        # 将sap中存在的有效订单，并且在lean中不存在的，同步到lean中，并且将sap中同一个TimeSN的DN字段以逗号分隔的形式，存在lean中DN_NO字段中
        sap_valid_items = sap.query(ZSDDN.TimeSN,func.string_agg(ZSDDN.DN,',').label('dn'),
                func.sum(ZSDDN.Amount).label('Amount')  # 使用解包操作符
            ).filter(or_(ZSDDN.DeleteMark!='X', ZSDDN.DeleteMark.is_(None))).group_by(ZSDDN.TimeSN).all()
        sap_valid_keys=[item[0] for item in sap_valid_items]
        exist_lean=lean.query(Master.release_time).filter(Master.release_time.in_(sap_valid_keys)).all()
        exist_lean_keys=[item[0] for item in exist_lean]
        import_items=[item for item in sap_valid_items if item[0] not in exist_lean_keys]
        for item in import_items:
            # 将主要的值工单释放时间、DN列表、货值及有效位先写入表中
            lean.add(Master(
                release_time=item[0],
                DN_NO=item[1],                
                goods_value=float(item[2]),
                is_valid=True
            ))
        lean.commit()
        import_keys=[item[0] for item in import_items]
        subquery=sap.query(
            ZSDDN.TimeSN,
            ZSDDN.CreatedBy,
            ZSDDN.SoldToName,
            ZSDDN.Remark,
            ZSDDN.SalesOrg,
            ZSDDN.Street,
            ZSDDN.Incoterms1,
            ZSDDN.ShippingPoint,
            func.row_number().over(partition_by=ZSDDN.TimeSN,order_by=ZSDDN.CreatedOn).label('row_num')).filter(ZSDDN.TimeSN.in_(import_keys)).subquery()
        full_data=sap.query(subquery).filter(subquery.c.row_num==1).all()
        new_add_items=lean.query(Master).filter(Master.release_time.in_(import_keys)).filter(Master.is_valid==True).all()
        for item in new_add_items:
            for data in full_data:
                print(data[0],item.release_time)
                if data[0]==item.release_time:
                    item.cs_specialist=data[1]
                    item.payer_company=data[2]
                    item.receiver_name=data[3]
                    item.BU_NO=data[4]
                    item.receiver_address=data[5]
                    item.delivery_terms=data[6]
                    item.import_time=datetime.now()
                    item.business_type=1
                    item.shipping_point=data[7]
                    item.status=1
                    break
        lean.commit()

        # 完善地址信息
        order_list=lean.query(Master).filter(Master.is_valid==True).filter(
            or_(
                Master.receiver_province.is_(None),
                Master.receiver_province=='',
                func.length(Master.receiver_name)>3
                )
        ).all()
        for item in order_list:
            if item.receiver_province is None or item.receiver_province=='':
                res=chat(item.receiver_address+",将这个地址解析为省市区，以json格式返回，key分别为province,city,district")
                if res['status']==1:
                    result=res['content']
                    result=eval(result)
                    item.receiver_province=result['province']
                    item.receiver_city=result['city']
                    item.receiver_district=result['district']
                else:
                    logger.error(f"解析地址失败，订单号：{item.release_time}，地址：{item.receiver_address}")

            if len(item.receiver_name)>3:
                res=chat(item.receiver_name+",将这个名字解析为姓名和电话，以json格式返回，key为name,phone")
                print(res)
                if res['status']==1:
                    result=res['content']
                    result=eval(result)
                    item.receiver_name=result['name']
                    item.receiver_phone=result['phone']
                else:
                    logger.error(f"解析名字失败，订单号：{item.release_time}，名字：{item.receiver_name}")
        lean.commit()

        return

        # except Exception as e:
        #     print(e)

def chat(chat_str):
    headers = {
        'Authorization': f'Bearer {authorization_token}',
        'Content-Type': 'application/json'
    }    
    data = {
    'model': 'glm-4-flash',
    'messages':[
            {"role": "user", "content": chat_str}
        ],
    'response_format':{
        'type': 'json_object'
    }
    }
    response = requests.post(api_url, json=data,headers=headers)
    if response.status_code == 200:
        result = response.json()
        return {'status':1,'content':result['choices'][0]['message']['content']}
    else:
        return {'status':0,'content':response.text}


if __name__ == '__main__':
    # mainProgram()
    handleData()
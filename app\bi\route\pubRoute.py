from flask import Blueprint, jsonify, request
import requests
import json
import pymysql
import pymssql
import datetime
from extensions import db
from sqlalchemy import func
from config import config, env
from app.receiving.functions import getParams, responseGet
from app.receiving.model.models_receiving import Ncmr, Inspect
api = Blueprint('bi/pubAPI', __name__)


@api.route('/')
def index():
    return 'ok等等'


@api.route('/getWR', methods=['GET'])
def getWR():
    connect = pymysql.connect(host=config[env].wrdb['host'], port=config[env].wrdb['port'], user=config[env].wrdb['user'],
                              password=config[env].wrdb['password'], database=config[env].wrdb['database'], charset=config[env].wrdb['charset'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    sql1 = '''select sum(qty) as planQty from mdi_planinfo where mdi_planinfo.plandate=current_date() and shifttype="白班"'''
    cursor.execute(sql1)
    r1 = cursor.fetchone()[0]
    planQty = int(r1) if r1 else 0
    sql2 = '''select sum(actualout) as produceQty,sum(requireout) as requireQty from mdi_hourinfo where date(mdi_hourinfo.hourstart)=current_date()'''
    cursor.execute(sql2)
    r2 = cursor.fetchone()
    r20 = r2[0]
    r21 = r2[1]
    produceQty = int(r20) if r20 else 0
    requireQty = int(r21) if r21 else 0
    sql3 = '''select sum(qty) as defectQty from mdi_issuelog where date(mdi_issuelog.recordtime)=current_date()'''
    cursor.execute(sql3)
    r3 = cursor.fetchone()[0]
    defectQty = int(r3) if r3 else 0
    sql4 = '''select count(id) as num, sum(if(currentshift>0,1,0)) as act from mdi_lineinfo'''
    cursor.execute(sql4)
    r4 = cursor.fetchone()
    r41 = r4[1]
    r41 = r41 if r41 else 0
    aRate = round(r41/int(r4[0])*100, 0)
    sql5 = '''select linename,sum(actualout) as produceQty,sum(requireout) as requireQty from mdi_hourinfo
            left join mdi_shiftinfo on mdi_shiftinfo.id=mdi_hourinfo.shiftid
            left join mdi_lineinfo on mdi_lineinfo.id = mdi_shiftinfo.lineid
            where date(mdi_hourinfo.hourstart)=current_date() group by linename'''
    cursor.execute(sql5)
    rlist = cursor.fetchall()
    linesdata = []
    if rlist:
        for r in rlist:
            if r[2] > 0:
                dic = {}
                dic['name'] = r[0]
                dic['value'] = round(int(r[1])/int(r[2])*100, 1)
                linesdata.append(dic)

    dRate = round(produceQty/planQty*100, 1) if planQty else 0
    qRate = round((produceQty-defectQty)/produceQty*100, 1) if produceQty else 0
    cRate = round(produceQty/requireQty*100, 2) if requireQty else 0
    print(planQty, produceQty, defectQty, dRate, qRate, cRate, aRate)
    return responseGet("获取WR-EMDI数据成功", {'planQty': str(planQty), 'produceQty': str(produceQty), 'defectQty': str(defectQty),
                                         'dRate': str(dRate), 'qRate': str(qRate), 'cRate': str(cRate), 'aRate': str(aRate), 'linesdata': linesdata})


@api.route('/getEPPM', methods=['GET'])
def getEPPM():
    connect = pymssql.connect(config[env].vocdb['host'],
                              config[env].vocdb['user'], config[env].vocdb['password'], config[env].vocdb['database'])
    if not connect:
        return jsonify(meta={'status': 204, 'msg': '失败'})
    cursor = connect.cursor()
    sql1 = '''
    SELECT    dmgroup.vsm, AVG(dfmonth.month) AS month, SUM(dvoc.eppmqty) AS dppmqty,SUM(case when dvoc.eppmtype='3rd Party' then dvoc.eppmqty else 0 end) as eppmqty
        FROM         dfmonth LEFT OUTER JOIN
                              dvoc ON dvoc.dateeppm >= dfmonth.startday AND dvoc.dateeppm < DATEADD(DAY, 1, dfmonth.endday)
                    LEFT OUTER JOIN
                              dline ON dvoc.line=dline.linename
                    LEFT OUTER JOIN
                              dmgroup ON dline.mgroup=dmgroup.mgroup
        WHERE     (dfmonth.year = 2021)  and (etype not in ('电商退货','Case pool'))
        GROUP BY dmgroup.vsm,dfmonth.month
        UNION
        SELECT     dmgroup.vsm,YEAR(getdate()) AS month, SUM(eppmqty) AS dppmqty, SUM(case when eppmtype='3rd Party' then eppmqty else 0 end) as eppmqty
        FROM         dvoc
                    LEFT OUTER JOIN
                              dline ON dvoc.line=dline.linename
                    LEFT OUTER JOIN
                              dmgroup ON dline.mgroup=dmgroup.mgroup
        WHERE     (YEAR(dateeppm) = YEAR(getdate()))  and (etype not in ('电商退货','Case pool')) group by dmgroup.vsm
    '''
    cursor.execute(sql1)
    r1 = cursor.fetchall()
    sql2 = """
    select vsm,issuetype,SUM(eppm) as cqty,COUNT(vid) as nb from
    (select dvoc.vsm vsm, issuetype,avg(eppmqty) as eppm,dvoc.id as vid
    from dvoc left join dsku on dvoc.id=dsku.vocid
    where Year(dvoc.receivedate)=YEAR(getdate()) and issuetype!=''  group by dvoc.vsm,dvoc.id,issuetype) as A
    group by issuetype,vsm  order by issuetype,vsm desc
    """
    cursor.execute(sql2)
    r2 = cursor.fetchall()
    bdic = {
        'title': 'BIBO',
        'radarValue': [],
        'radarCount': [],
        'radarData': [],
        'lineData': [['Month'], ['EPPM'], ['DPPM']]
    }
    vsm1dic = {
        'title': 'VSM1',
        'radarValue': [],
        'radarCount': [],
        'radarData': [],
        'lineData': [['Month'], ['EPPM'], ['DPPM']]
    }
    vsm2dic = {
        'title': 'VSM2',
        'radarValue': [],
        'radarCount': [],
        'radarData': [],
        'lineData': [['Month'], ['EPPM'], ['DPPM']]
    }
    for r in r1:
        if r[0] == 'BIBO':
            bdic['lineData'][0].append(r[1])
            bdic['lineData'][1].append(r[2])
            bdic['lineData'][2].append(r[3])
        elif r[0] == 'VSM1':
            vsm1dic['lineData'][0].append(r[1])
            vsm1dic['lineData'][1].append(r[2])
            vsm1dic['lineData'][2].append(r[3])
        elif r[0] == 'VSM2':
            vsm2dic['lineData'][0].append(r[1])
            vsm2dic['lineData'][1].append(r[2])
            vsm2dic['lineData'][2].append(r[3])
    maxb = 0
    max1 = 0
    max2 = 0
    for r in r2:
        if r[0] == 'BIBO':
            if r[2] > maxb:
                maxb = r[2]
        elif r[0] == 'VSM1':
            if r[2] > max1:
                max1 = r[2]
        elif r[0] == 'VSM2':
            if r[2] > max2:
                max2 = r[2]
    for r in r2:
        if r[0] == 'BIBO':
            bdic['radarValue'].append(r[2])
            bdic['radarCount'].append(r[3])
            bdic['radarData'].append({'text': r[1], 'max': maxb})
        elif r[0] == 'VSM1':
            vsm1dic['radarValue'].append(r[2])
            vsm1dic['radarCount'].append(r[3])
            vsm1dic['radarData'].append({'text': r[1], 'max': max1})
        elif r[0] == 'VSM2':
            vsm2dic['radarValue'].append(r[2])
            vsm2dic['radarCount'].append(r[3])
            vsm2dic['radarData'].append({'text': r[1], 'max': max2})
    myData = [bdic, vsm1dic, vsm2dic]
    meta = {'status': 200, 'msg': '成功'}
    return jsonify(meta=meta, data=myData)


@api.route('/getSuggestion', methods=['GET'])
def getBI():
    res = requests.get('https://welean.pentair.cn/xcx/Code/BI/getsummary.php')
    # res = requests.get('http://localhost/BI/getsummary.php')
    content = res.text
    if content.startswith(u'\ufeff'):
        content = content.encode('utf8')[3:].decode('utf8')
    if res.status_code == 200:
        meta = {'status': 200, 'msg': '成功'}
        return jsonify(meta=meta, data=json.loads(content))
    else:
        meta2 = {'status': 204, 'msg': '失败'}
        return jsonify(meta=meta2)


@ api.route('/getFTT', methods=['GET'])
def getFTT():
    res = request.args
    isThermal = res.get('isThermal')
    isVendor = res.get('isVendor')
    isHistory = res.get('isHistory')
    thermalArr = ['B103-FH', 'B103-QC', 'B103-T2B', 'B427-FH', 'B428-CON',
                  ' B429-RTU', 'B430-RAYC', 'B431-T2B', ' B432-Q70', 'B432-Q80']
    dateRange = res.get('dateRange')
    dataX = []
    startyear = int(dateRange.split(',')[0].split('-')[0])
    startmonth = int(dateRange.split(',')[0].split('-')[1])
    startday = dateRange.split(',')[0].split('-')[2]
    endyear = int(dateRange.split(',')[1].split('-')[0])
    endmonth = int(dateRange.split(',')[1].split('-')[1])
    endday = dateRange.split(',')[1].split('-')[2]
    ftarget = getParams()['fttTarget']
    useArr = []
    sortArr = []
    pendArr = []
    reworkArr = []
    returnArr = []
    fttArr = []
    acceptArr = []
    fttTargetArr = []
    for i in range(startmonth, endmonth+(endyear-startyear)*12+1):
        if i == startmonth:
            sd = '-'+startday
        else:
            sd = '-01'
        if i == endmonth+(endyear-startyear)*12:
            ed = '-'+endday
            em = str(endmonth)
            xmonth = 0
        else:
            ed = '-01'
            xmonth = 1
            em = str(i-12*int(i/12)+xmonth) if i-12*int(i/12) + \
                xmonth > 9 else '0'+str(i-12*int(i/12)+1)
        startdate = str(startyear+int(i/13))+'-'+(str(i-12*int(i/13))
                                                  if i-12*int(i/13) > 9 else '0'+str(i-12*int(i/13)))+sd
        if i % 12 == 0:
            enddate = str(startyear+int(i/13)+xmonth)+'-'+em+ed
        else:
            enddate = str(startyear+int(i/13))+'-'+em+ed
        dataX.append(startdate[: 7])
        fttdata = getFttdata(startdate, enddate, isThermal, isVendor, thermalArr)
        pendArr.append(fttdata[0])
        useArr.append(fttdata[1])
        reworkArr.append(fttdata[2])
        sortArr.append(fttdata[3])
        returnArr.append(fttdata[4])
        acceptArr.append(fttdata[5])
        fttArr.append(fttdata[6])
        fttTargetArr.append(ftarget)
    wend = datetime.datetime.strptime(dateRange.split(',')[1], "%Y-%m-%d").date()
    wstart = datetime.datetime(wend.year, wend.month, 1).date()
    currentDay = wstart
    currentEnd = currentDay
    i = 1
    while currentDay <= wend:
        wk = currentEnd.isoweekday()
        if wk == 6 and (currentEnd-currentDay).days > 1:
            fttdata = getFttdata(currentDay, currentEnd,
                                 isThermal, isVendor, thermalArr)
            pendArr.append(fttdata[0])
            useArr.append(fttdata[1])
            reworkArr.append(fttdata[2])
            sortArr.append(fttdata[3])
            returnArr.append(fttdata[4])
            acceptArr.append(fttdata[5])
            fttArr.append(fttdata[6])
            fttTargetArr.append(ftarget)
            dataX.append(str(endmonth)+'-WK'+str(i))
            currentDay = currentEnd
            i += 1
        currentEnd = currentEnd+datetime.timedelta(days=1)

    history = {
        'dataX': ['Y2019', 'Y2020', 'Y2021', '2021-01', '2021-02', '2021-03', '2021-04'],
        'sorting': [93, 44, 15, 3, 6, 6, 0],
        'rt': [121, 134, 56, 13, 7, 13, 23],
        'pend': [0, 0, 0, 0, 0, 0, 0],
        'use': [137, 129, 68, 13, 16, 13, 26],
        'rework': [0, 0, 0, 0, 0, 0, 0],
        'accept': [20268, 20403, 8141, 2125, 1287, 2646, 2083],
        'ftt': [98.4, 98.5, 98.2, 98.7, 98.8, 98.8, 97.3],
        'target': [97, 5, 98, 98.5, 98.5, 98.5, 98.5, 98.5]
    }
    print(history['dataX'])
    if isHistory == 'true':
        for i in range(6, -1, -1):
            dataX.insert(0, history['dataX'][i])
            pendArr.insert(0, history['pend'][i])
            useArr.insert(0, history['use'][i])
            reworkArr.insert(0, history['rework'][i])
            sortArr.insert(0, history['sorting'][i])
            returnArr.insert(0, history['rt'][i])
            acceptArr.insert(0, history['accept'][i])
            fttArr.insert(0, history['ftt'][i])
            fttTargetArr.insert(0, history['target'][i])
    date = {'dataX': dataX, 'pendArr': pendArr, 'useArr': useArr, 'reworkArr': reworkArr, 'sortArr': sortArr,
            'returnArr': returnArr, 'acceptArr': acceptArr, 'fttArr': fttArr, 'fttTargetArr': fttTargetArr}
    return responseGet("获取FTT数据成功", date)


def getFttdata(startdate, enddate, isThermal, isVendor, thermalArr):
    query = db.session.query(func.sum(func.if_(Ncmr.dealType.is_(None), 1, 0)),
                             func.sum(func.if_(Ncmr.dealType == 'Use as is', 1, 0)),
                             func.sum(func.if_(Ncmr.dealType == 'Rework', 1, 0)),
                             func.sum(func.if_(Ncmr.dealType.like('Sorting%'), 1, 0)),
                             func.sum(func.if_(Ncmr.dealType == 'return', 1, 0)),
                             ).outerjoin(Inspect, Inspect.Id == Ncmr.inspectid).filter(Ncmr.ncmrtype == 'IQC').filter(
        Inspect.receive_date.between(startdate, enddate))
    nmr = db.session.query(func.count(Inspect.Id)).filter(
        Inspect.receive_date.between(startdate, enddate))

    if isThermal == 'false':
        query = query.filter(Inspect.mgroup.notin_(thermalArr))
        nmr = nmr.filter(Inspect.mgroup.notin_(thermalArr))
    if isVendor == 'false':
        query = query.filter(Ncmr.nogoodType == '供应商问题')
    query = query.all()
    nmr = nmr.scalar()
    pend = 0 if query[0][0] is None else int(query[0][0])
    use = 0 if query[0][1] is None else int(query[0][1])
    rework = 0 if query[0][2] is None else int(query[0][2])
    sort = 0 if query[0][3] is None else int(query[0][3])
    returna = 0 if query[0][4] is None else int(query[0][4])
    accept = 0
    ftt = ''
    if nmr:
        accept = nmr-pend-use-rework-sort-returna
        ftt = round((nmr-pend-use-rework-sort-returna)/nmr*100, 1)
    return [pend, use, rework, sort, returna, accept, ftt]

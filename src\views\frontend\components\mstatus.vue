<script setup lang="ts">
import { onMounted, reactive, watch } from "vue";
import { message } from "@/utils/message";
import { getbasicinfo } from "@/api/front";
import { useProdStoreHook } from "@/store/modules/prod";
import { isCurrentShift } from "@/views/functions/shift";
import { changeState } from "@/views/components/editstate/index";
import { changePn } from "@/views/components/editpn/index";
import { ReNormalCountTo, ReboundCountTo } from "@/components/ReCountTo";

const state_data = reactive({
  state: "",
  state_id: "",
  state_des: "",
  pn: ""
});

watch(
  [
    () => useProdStoreHook().selectedDate,
    () => useProdStoreHook().shift,
    () => useProdStoreHook().machineId
  ],
  (newvalue: any, _) => {
    if (isCurrentShift(newvalue[0], newvalue[1])) {
      updateInfo({
        selecteddate: newvalue[0],
        shift: newvalue[1],
        machine: newvalue[2]
      });
    }
  }
);

const updateInfo = (querydata: any) => {
  getbasicinfo(querydata).then((res: { data: any; meta: any }) => {
    if (res.meta.status == 200) {
      console.log(res.data);
      state_data.state = res.data.state;
      state_data.state_des = res.data.state_des;
      state_data.state_id = res.data.state_id;
      state_data.pn = res.data.pn;
    } else {
      message("查询状态失败", { customClass: "el", type: "error" });
    }
  });
};

onMounted(() => {
  updateInfo({
    selecteddate: useProdStoreHook().selectedDate,
    shift: useProdStoreHook().shift,
    machine: useProdStoreHook().machineId
  });
});
</script>

<template>
  <div class="basic">
    <div class="basic_header">
      <span class="lightfont">生产基本信息</span>
      <el-button round type="danger"
        ><el-icon :size="24" style="margin-right: 5px"
          ><WarningFilled /></el-icon
        >安灯报警</el-button
      >
    </div>
    <div class="basic_body">
      <div class="border-box-content">
        <el-card
          shadow="always"
          :body-style="{ padding: '0px' }"
          class="status"
        >
          <div class="title">设备状态</div>
          <div
            class="body_content"
            @click="
              changeState(
                useProdStoreHook().machineId,
                state_data.state_id,
                () => {
                  updateInfo({
                    selecteddate: useProdStoreHook().selectedDate,
                    shift: useProdStoreHook().shift,
                    machine: useProdStoreHook().machineId
                  });
                }
              )
            "
          >
            <el-icon
              v-if="state_data.state == '1'"
              color="#00ff00"
              :size="48"
              class="is-loading"
              ><Tools
            /></el-icon>
            <el-icon v-if="state_data.state == '2'" color="yellow" :size="48"
              ><WarnTriangleFilled
            /></el-icon>
            <el-icon v-if="state_data.state == '3'" color="red" :size="48"
              ><CircleCloseFilled
            /></el-icon>
            <el-icon v-if="state_data.state == '4'" color="grey" :size="48"
              ><Tools
            /></el-icon>

            <div>{{ state_data.state_des }}</div>
          </div>
        </el-card>
        <el-card shadow="always" :body-style="{ padding: '0px' }" class="pn">
          <div class="title">
            <span>实时生产料号</span>
          </div>
          <div class="body_content">
            <div
              class="pn_content"
              @click="
                changePn(useProdStoreHook().machineId, state_data.pn, () => {
                  updateInfo({
                    selecteddate: useProdStoreHook().selectedDate,
                    shift: useProdStoreHook().shift,
                    machine: useProdStoreHook().machineId
                  });
                })
              "
            >
              <el-tag
                v-for="item in state_data.pn.split('@')"
                :key="item"
                type="success"
                size="large"
                style="font-size: 16px"
                >{{ item }}</el-tag
              >
            </div>
          </div>
        </el-card>
        <!-- <el-card shadow="always" :body-style="{ padding: '0px' }" class="oee">
          <div class="title">
            <span>OEE效率</span>
          </div>
          <div class="body_content">
            <ReNormalCountTo
              suffix="%"
              :duration="3000"
              :color="'#409EFF'"
              :fontSize="'2em'"
              :startVal="1"
              :endVal="100"
            />
          </div>
        </el-card> -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.lightfont {
  font-size: 18px;
  text-shadow:
    0 0 10px red,
    0 0 20px red,
    0 0 30px red,
    0 0 40px red;
}
.basic {
  display: flex;
  flex-direction: column;
  padding: 0 !important;
  height: 100%;

  .basic_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 10px;
  }
  .basic_body {
    display: flex;
    flex: 1;
    :deep(.border-box-content) {
      padding: 5px 5px 10px 0;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 0 5px 0;
      }
      .footer {
        text-align: center;
      }
      .el-card {
        width: 48%;

        .title {
          text-align: center;
          margin-top: 5px;
          height: 20%;
        }

        .body_content {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          align-items: center;
          text-align: center;
          height: 70%;
        }
        .body_content > * {
          margin: 3px 0;
        }

        .status {
          display: flex;
          flex-direction: column;
          flex-grow: 1;
          justify-content: space-between;
        }
      }
      .pn {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .el-card__body {
          height: 85%;
          .body_content {
            display: flex;
            padding: 0;
            flex: 1;
            height: 70%;

            .pn_content {
              padding: 0;
              height: 100%;
              width: 90%;
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              align-items: center;
              .el-tag {
                width: 90%;
              }
            }
          }
        }
      }
    }
  }
}
</style>

import{d as O,n as i,q as Q,r as n,o as _,c as v,b as o,h as t,u as e,F as U,t as W,g as Y,f as r,y as w,w as $,D as j,_ as A}from"./index-BnxEuBzx.js";import{useColumns as G}from"./routingColumns-k2Rcj7nX.js";import{f as H}from"./dashboard-dtTxmf4X.js";import"./moment-C3TZ8gAF.js";import"./item-CclCd0Fm.js";const J=O({__name:"index",setup(K){const{search_condition:p,sql_count:f,redis_count:m,asyncRouting:h,openItem:k,loading:g,columns:x,loadingConfig:R,getRoutingList:z,pagination:s,dataList:S,adaptiveConfig:V,onCurrentChange:L}=G(),P=i(),B=i(),c=i([]);i([]),i("");const D=u=>{u?H({prefix:u}).then(l=>{c.value=l.data}):c.value=[]};return Q(()=>{}),(u,l)=>{const M=n("el-option"),F=n("el-select"),d=n("el-form-item"),C=n("el-button"),I=n("el-dropdown-item"),N=n("el-dropdown-menu"),T=n("el-dropdown"),y=n("el-text"),b=n("el-tag"),q=n("el-form"),E=n("pure-table");return _(),v("div",null,[o(q,{ref_key:"formRef",ref:B,inline:!0,model:e(p),class:"search-form bg-bg_color"},{default:t(()=>[o(d,{label:"料号：",prop:"selecteddate"},{default:t(()=>[o(F,{class:"select_sku",modelValue:e(p).pn,"onUpdate:modelValue":l[0]||(l[0]=a=>e(p).pn=a),filterable:"",clearable:"",remote:"","reserve-keyword":"",placeholder:"输入生产料号","remote-show-suffix":"","remote-method":D,loading:e(g),onChange:e(z)},{default:t(()=>[(_(!0),v(U,null,W(c.value,a=>(_(),Y(M,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","onChange"])]),_:1}),o(d,{prop:"status"},{default:t(()=>[o(C,{type:"primary",onClick:l[1]||(l[1]=a=>e(k)("add",{}))},{default:t(()=>[r("+ 添加料号")]),_:1})]),_:1}),o(d,null,{default:t(()=>[o(T,{"split-button":"",type:"primary"},{dropdown:t(()=>[o(N,null,{default:t(()=>[o(I,null,{default:t(()=>[r("下载格式")]),_:1})]),_:1})]),default:t(()=>[r(" 批量上传 ")]),_:1})]),_:1}),o(d,null,{default:t(()=>[o(b,{class:"ml-2",type:"success",size:"large"},{default:t(()=>[o(y,{size:"default",type:"primary"},{default:t(()=>[r("MYSQL记录条数："+w(e(f)),1)]),_:1})]),_:1}),o(b,{class:"ml-2",type:"success",size:"large"},{default:t(()=>[o(y,{type:"warning"},{default:t(()=>[r("缓存记录条数："+w(e(m)),1)]),_:1})]),_:1})]),_:1}),o(d,null,{default:t(()=>[$(o(C,{type:"danger",onClick:e(h)},{default:t(()=>[r("同步缓存")]),_:1},8,["onClick"]),[[j,e(f)!=e(m)]])]),_:1})]),_:1},8,["model"]),o(E,{ref_key:"tableRef",ref:P,border:"",adaptive:"",adaptiveConfig:e(V),"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:e(g),pagination:e(s),"loading-config":e(R),data:e(S).slice((e(s).currentPage-1)*e(s).pageSize,e(s).currentPage*e(s).pageSize),columns:e(x),onPageCurrentChange:e(L)},null,8,["adaptiveConfig","loading","pagination","loading-config","data","columns","onPageCurrentChange"])])}}}),ne=A(J,[["__scopeId","data-v-e846a4de"]]);export{ne as default};

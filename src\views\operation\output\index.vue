<script setup lang="ts">
import { onMounted } from "vue";
import { useColumns } from "./utils/column";
import Papa from "papaparse";
import moment from "moment";

defineOptions({
  name: "OutputQty"
});

const {
  search_condition,
  search,
  loading,
  columns,
  loadingConfig,
  dataList,
  adaptiveConfig
} = useColumns();

const exportToCSV = () => {
  const exportdata = dataList.value.map(item => {
    const { pn, defect_qty, total_output } = item;
    return {
      "Material Number": pn,
      Quantity: Number(total_output) - (defect_qty || 0),
      "Basic start date": moment(search_condition.selected_date).format(
        "YYYY-MM-DD"
      ),
      "Basic finish date": moment(search_condition.selected_date)
        .add(1, "days")
        .format("YYYY-MM-DD")
    };
  });

  const csv = Papa.unparse(exportdata, {
    delimiter: "\t" // 使用制表符作为分隔符
  });
  const blob = new Blob([csv], { type: "text/csv" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute(
    "download",
    moment(search_condition.selected_date).format("YYYY-MM-DD") +
      "注塑生产数据.txt"
  );
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

onMounted(() => {
  loading.value = false;
});
</script>

<template>
  <div>
    <el-form :inline="true" class="search-form bg-bg_color">
      <el-form-item label="生产日期" />
      <el-date-picker
        type="date"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
        v-model="search_condition.selected_date"
        placeholder="包含早/中/晚班次"
      />
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" v-show="dataList.length" @click="exportToCSV"
          >导出SAP格式文本</el-button
        >
      </el-form-item>
    </el-form>

    <pure-table
      class="bg-bg_color"
      ref="tableRef"
      border
      :adaptiveConfig="adaptiveConfig"
      row-key="id"
      alignWhole="center"
      showOverflowTooltip
      :loading="loading"
      :loading-config="loadingConfig"
      :data="dataList"
      :columns="columns"
    />
  </div>
</template>

<style lang="scss" scoped>
.search-form {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .extra_info {
    font-size: 14px;
  }
  .el-form-item {
    display: flex;
    margin: 8px 10px;
  }
}

:deep(.el-table thead.is-group th.el-table__cell) {
  background: var(--el-table-header-bg-color);
}
</style>

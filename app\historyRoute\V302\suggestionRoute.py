
from flask import Blueprint, request, json
from extensions import db
from openpyxl import load_workbook
import hashlib
import os
from datetime import datetime, date, timedelta
from sqlalchemy import func, or_, desc, and_, not_
from sqlalchemy.orm import aliased
from app.public.functions import responseError, responsePost, responseGet,  responsePut, login_required
from app.welean.functions import getServer, TASKthread, PROGRESSthread, getScore
from app.welean.model.models_welean import Getscore, Userinfo, Account, Suggest, Tasks, Appvowners, Rates, Newspaper, Options, Settings
from app.welean.schemas import bills_schema


api = Blueprint('welean/V302/suggestionAPI', __name__)
Userinfo1 = aliased(Userinfo)
Userinfo2 = aliased(Userinfo)
sDic = {
    '待批准': 'open',
    '进行中': 'ongoing',
    '已完成': 'closed',
    '被取消': 'cancel'
}
srDic = {
    'open': '待批准',
    'ongoing': '进行中',
    'closed': '已完成',
    'cancel': '被取消'
}
auditTypes = ['提案任务', '稽核任务', '管理层稽核', '安全问题']


@ api.route('/downloadSug', methods=['GET'])
@ login_required
def downloadSug():
    res = request.args
    plant = res.get('plant')
    year = res.get('year')
    dept = res.get('dept')
    suggests = db.session.query(Suggest.Id, Suggest.fifi, Suggest.idate, Suggest.cfdate, Suggest.acdate, Suggest.afterpic, Suggest.content,
                                Userinfo1.cname.label('scname'), Userinfo1.dept1.label(
                                    'sdept'), Suggest.status, Suggest.stype, Suggest.type2, Userinfo2.cname.label('dcname'),
                                Userinfo2.dept1.label('ddept'), Suggest.comments, Suggest.duedate).join(Userinfo1, Suggest.eid == Userinfo1.eid).join(
        Userinfo2, Suggest.exeid == Userinfo2.eid).filter(Suggest.plant == plant).filter(
        func.year(Suggest.idate) == year).order_by(desc(Userinfo2.cname), desc(Suggest.acdate))
    if dept:
        suggests = suggests.filter(Userinfo2.dept1 == dept)
    if suggests:
        path = getServer()['downloadsPath']
        excelFile = str(year)+'suggestion' + datetime.now().strftime("%Y-%m-%d %H%M%S") + '.xlsx'
        createExcel(suggests, path, excelFile)
        return responseGet("获取列表成功", {'url': getServer()['downloadsUrl']+excelFile})
    else:
        return responseError("没有任何数据可以下载")


def createExcel(query, path, excelFile):
    wb = load_workbook(path+'templates/suggestsTemplate.xlsx')
    sht = wb.worksheets[0]
    i = 2
    for q in query:
        sht.cell(row=i, column=1).value = q.Id
        sht.cell(row=i, column=2).value = q.scname
        sht.cell(row=i, column=3).value = q.sdept
        sht.cell(row=i, column=4).value = datetime.strftime(q.idate, "%Y-%m-%d")
        sht.cell(row=i, column=5).value = q.dcname
        sht.cell(row=i, column=6).value = q.ddept
        sht.cell(row=i, column=7).value = q.content
        sht.cell(row=i, column=8).value = q.status
        sht.cell(row=i, column=9).value = q.duedate
        sht.cell(row=i, column=10).value = q.cfdate
        sht.cell(row=i, column=11).value = q.acdate
        sht.cell(row=i, column=12).value = q.stype
        sht.cell(row=i, column=13).value = q.type2
        sht.cell(row=i, column=14).value = q.fifi
        sht.cell(row=i, column=15).value = q.comments
        i += 1
    wb.save(path + excelFile)


@ api.route('/getBills', methods=['GET'])
@ login_required
def getBills():
    res = request.args
    plant = res.get('plant')
    year = res.get('year')
    current = int(res.get('current'))  # 0积分榜，1等级榜，2部门榜
    limit = 5 if current == 2 else 10
    bills = db.session.query(Getscore.eid, Userinfo.cname, Userinfo.dept1, Account.avatar, Userinfo.dept2, func.sum(Getscore.getscore).label('score')).join(
        Userinfo, Getscore.eid == Userinfo.eid).join(Account, Account.eid == Getscore.eid).filter(Userinfo.active == 1).filter(Userinfo.plant == plant)
    if current == 0:
        bills = bills.filter(func.year(Getscore.getdate) == year).group_by(Getscore.eid)
    elif current == 1:
        bills = bills.group_by(Getscore.eid)
    elif current == 2:
        bills = bills.filter(func.year(Getscore.getdate) == year).group_by(Userinfo.dept1)
    bills = bills.order_by(desc(func.sum(Getscore.getscore))).limit(limit).all()
    pie = []
    if current == 2:
        for b in bills:
            dic = {
                'name': b.dept1,
                'value': b.score
            }
            pie.append(dic)
        abills = [{
            'data': pie
        }]
    else:
        abills = bills_schema.dump(bills)
    return responseGet("获取列表成功", {'bills': abills})


@ api.route('/getSugCharts', methods=['GET'])
@ login_required
def getSugCharts():
    res = request.args
    plant = res.get('plant')
    year = res.get('year')
    charts = getCharts(plant, year)
    return responseGet("获取列表成功", {'charts': charts})


@ api.route('/getEHSCharts', methods=['GET'])
@ login_required
def getEHSCharts():
    res = request.args
    plant = res.get('plant')
    year = res.get('year')
    charts = getCharts(plant, year, 'ehs')
    return responseGet("获取列表成功", {'charts': charts})


def getCharts(plant, year, tp=''):
    charts = []
    # 获取部门积分榜单图
    bills = db.session.query(func.count(Suggest.Id).label('qty'), Userinfo.dept1).join(Userinfo, Suggest.eid == Userinfo.eid).filter(
        Suggest.plant == plant).filter(Suggest.audittype == 0).filter(func.year(Suggest.idate) == year)
    if tp == 'ehs':
        bills = bills.filter(Suggest.stype.in_(['安全', '安全检查']))
    bills = bills.group_by(Userinfo.dept1).order_by(desc(func.count(Suggest.Id)))
    pie = []
    for b in bills:
        dic = {
            'name': b.dept1,
            'value': b.qty
        }
        pie.append(dic)

    charts.append({
        'title': '部门提案数量',
        'type': 'pie',
        'chartData': {
            'categories': [],
            'series': [
                {
                    'data': pie
                }
            ]
        }
    })
    # 获取提案类型词云
    words = db.session.query(Suggest.type2, func.count(Suggest.Id).label('qty')).filter(not_(Suggest.type2.is_(None))).filter(
        Suggest.plant == plant).filter(func.year(Suggest.idate) == year)
    if tp == 'ehs':
        words = words.filter(Suggest.stype == '安全')
    words = words.group_by(Suggest.type2).all()
    total = 0
    for w in words:
        total += w.qty
    wArr = []
    for w in words:
        wArr.append({
            'name': w.type2,
            'textSize': getTextsize(int((w.qty/total)*100))
        })
    charts.append({
        'title': '提案类型分布',
        'type': 'word',
        'chartData': {
            "series": wArr
        }
    })
    # 获取部门完成率
    suggestions = db.session.query(Suggest.status, Suggest.duedate, Suggest.cfdate, Userinfo.dept1).join(Userinfo, Suggest.exeid == Userinfo.eid).filter(
        Userinfo.plant == plant).filter(or_(Suggest.status == 'ongoing', Suggest.status == 'closed')).filter(func.year(Suggest.idate) == year)
    if tp == 'ehs':
        suggestions = suggestions.filter(Suggest.stype.in_(['安全', '安全检查']))
    suggestions = suggestions.all()
    sDic = {}
    deptArr = []
    totalArr = []
    ontimeArr = []
    for s in suggestions:
        duedate = s.cfdate if s.cfdate else s.duedate
        if s.dept1 in sDic.keys():
            sDic[s.dept1]['total'] += 1
            if (not duedate) or duedate <= date.today():
                sDic[s.dept1]['ontime'] += 1
        else:
            sDic[s.dept1] = {
                'total': 1,
                'ontime': 1
            }
    for k, v in sDic.items():
        totalArr.append(v['total'])
        ontimeArr.append(
            {
                "value": round(v['ontime']/v['total']*100, 1),
                "color": "#f04864"
            })
        deptArr.append(k)
    charts.append({
        'title': '提案执行部门按时完成率',
        'type': 'mix',
        'opts': {
            'xAxis': {
                'rotateLabel': True
            },
            'yAxis': {
                'data': [
                     {'position': 'left', 'title': '百分比'},
                     {'position': 'right', 'title': '数量', 'textAlign': 'left'}
                ]
            }
        },
        'chartData': {
            'categories': deptArr,
            'series': [
                {
                    "name": "任务数量",
                    "data": totalArr,
                    'type': 'column',
                    "index": 1
                },
                {
                    "name": "完成率",
                    "data": ontimeArr,
                    'type': 'line'
                }
            ]
        }
    })
    return charts


def getTextsize(q):
    if q >= 20:
        return 40
    elif q >= 10 and q < 20:
        return 30
    elif q >= 5 and q < 10:
        return 20
    elif q >= 1 and q <= 5:
        return 20
    else:
        return 10


@ api.route('/getMyOpenActions', methods=['GET'])
@ login_required
def getMyOpenActions():
    res = request.args
    eid = res.get('eid')
    all = db.session.query(func.count(Suggest.Id)).filter(
        Suggest.exeid == eid).filter(Suggest.status == 'ongoing').scalar()
    notcf = db.session.query(func.count(Suggest.Id)).filter(
        Suggest.exeid == eid).filter(Suggest.status == 'ongoing').filter(Suggest.cfdate.is_(None)).scalar()
    myList = [
        {
            'name': '待确认',
            'count': notcf if notcf else 0
        },
        {
            'name': '待完成',
            'count': (all-notcf) if all-notcf else 0
        },
        {
            'name': '已完成'
        }
    ]
    return responseGet("获取列表成功", {'list': myList})


@ api.route('/searchName', methods=['GET'])
@ login_required
def searchName():
    res = request.args
    keywords = res.get('keywords')
    plant = res.get('plant')
    users = db.session.query(Userinfo.eid, Userinfo.dept1, Userinfo.dept2, Userinfo.dept3, Userinfo.cname, Userinfo.ename
                             ).join(Account, Account.eid == Userinfo.eid).filter(or_(Userinfo.cname.like('%{0}%'.format(keywords)), Userinfo.ename.like('%{0}%'.format(keywords)))
                                                                                 ).filter(Userinfo.plant == plant).filter(Userinfo.active == 1).filter(Account.isactive == 1).all()
    outArr = []
    for u in users:
        dept1 = u.dept1 if u.dept1 else ''
        dept2 = u.dept2 if u.dept2 else ''
        dept3 = u.dept3 if u.dept3 else ''
        cname = u.cname if u.cname else ''
        ename = u.ename if u.ename else ''
        outArr.append({
            'eid': u.eid+'/'+cname,
            'name': dept1+'/'+dept2+'/'+dept3+'/'+cname+'/'+ename
        })
    return responseGet("获取列表成功", {'nameList': outArr})


@ api.route('/getSettings', methods=['GET'])
@ login_required
def getSettings():
    res = request.args
    plant = res.get('plant')
    audittype = int(res.get('audittype')) if res.get('audittype') else 0
    if audittype == 3:
        tps = 'ehstypes'
    else:
        tps = 'sugtypes'
    sets = db.session.query(Settings).filter(Settings.plant == plant).filter(
        or_(Settings.cate == tps, Settings.cate == 'areas')).filter(Settings.isactive == 1).all()
    areaDic = {}
    sugDic = {}
    for s in sets:
        if s.cate == 'areas':
            if s.name1 in areaDic.keys():
                if s.name2 in areaDic[s.name1]['children'].keys():
                    areaDic[s.name1]['children'][s.name2]['children'].append({
                        'value': s.name3,
                        'label': s.name3
                    })
                else:
                    areaDic[s.name1]['children'][s.name2] = {
                        'value': s.name2,
                        'label': s.name2,
                        'children': [{
                            'value': s.name3,
                            'label': s.name3
                        }]
                    }
            else:
                areaDic[s.name1] = {}
                areaDic[s.name1] = {
                    'value': s.name1,
                    'label': s.name1,
                    'children': {
                        s.name2: {
                            'value': s.name2,
                            'label': s.name2,
                            'children': [{
                                'value': s.name3,
                                'label': s.name3
                            }]
                        }
                    }
                }
        elif s.cate == tps:
            if s.name1 in sugDic.keys():
                sugDic[s.name1]['children'].append(
                    {
                        'value': s.name2,
                        'label': s.name2
                    }
                )
            else:
                sugDic[s.name1] = {}
                sugDic[s.name1] = (
                    {
                        'value': s.name1,
                        'label': s.name1,
                        'children': [{
                            'value': s.name2,
                            'label': s.name2
                        }]
                    }
                )

    areaArr = []
    for k, v in areaDic.items():
        v['children'] = list(v['children'].values())
        areaArr.append(v)
    sugArr = list(sugDic.values())
    return responseGet("获取列表成功", {'areaList': areaArr, 'sugTypes': sugArr})


@ api.route('/getDepts', methods=['GET'])
@ login_required
def getDepts():
    res = request.args
    plant = res.get('plant')
    depts = db.session.query(Userinfo.dept1).filter(Userinfo.plant == plant).filter(
        not_(Userinfo.dept1.is_(None))).group_by(Userinfo.dept1).order_by(desc(Userinfo.dept1)).all()
    outArr = []
    for dp in depts:
        dic = {
            'value': dp.dept1,
            'label': dp.dept1
        }
        outArr.append(dic)
    return responseGet("获取列表成功", {'deptList': outArr})


@ api.route('/getMyAudits', methods=['GET'])
@ login_required
def getMyAudits():
    res = request.args
    eid = res.get('eid')
    tasks = db.session.query(func.count(Suggest.Id).label('scount'), Tasks.Id, Tasks.content, Tasks.month, Tasks.ttype).outerjoin(
        Suggest, Tasks.Id == Suggest.auditid).filter(or_(Tasks.owner1 == eid, Tasks.owner2 == eid)).filter(
        Tasks.status == 0).filter(Tasks.month <= date.today()).group_by(Tasks.Id, Suggest.auditid).all()
    outArr = []
    for t in tasks:
        dic = {
            'id': t.Id,
            'content': t.content,
            'startdate': datetime.strftime(t.month, '%Y-%m-%d'),
            'title': t.ttype,
            'show': 0,
            'count': t.scount
        }
        outArr.append(dic)
    return responseGet("获取列表成功", {'myTasks': outArr})


@ api.route('/getMyAppvs', methods=['GET'])
@ login_required
def getMyAppvs():
    res = request.args
    eid = res.get('eid')
    dept = res.get('dept')
    plant = res.get('plant')
    tag = res.get('tag')
    nb = res.get('nb')
    suggests = db.session.query(Suggest.Id, Suggest.idate, Suggest.beforepic,  Suggest.status, Suggest.linename, Suggest.type2, Suggest.content,
                                Userinfo2.cname.label('scname'), Userinfo2.dept1.label('sdept')).join(
        Userinfo2, Suggest.eid == Userinfo2.eid).join(Appvowners, Appvowners.dept == Userinfo2.dept1).filter(
                                    Suggest.plant == plant).filter(Suggest.status == 'open')
    if tag == 'my':
        suggests = suggests.filter(Appvowners.eid == eid)
    else:
        suggests = suggests.group_by(Appvowners.dept)
        if dept:
            suggests = suggests.filter(Userinfo2.dept1 == dept)
    suggests = suggests.order_by(Suggest.idate).limit(nb).all()
    cardsData = []
    for s in suggests:
        dic = {
            'id': s.Id,
            'title': srDic[s.status]+'|No.'+str(s.Id),
            'subTitle': '提案日期:'+datetime.strftime(s.idate, "%Y-%m-%d"),
            'thumb': '/static/icon/'+s.status+'.png',
            'content': s.content,
            'url': getServer()['suggestionUrl']+s.beforepic.split(',')[0] if s.beforepic else 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png',
            'suggester': s.sdept+'/'+s.scname,
            'linename': s.linename
        }
        cardsData.append(dic)
    return responseGet("获取列表成功", {'cardsData': cardsData})


@ api.route('/getCancels', methods=['GET'])
@ login_required
def getCancels():
    res = request.args
    plant = res.get('plant')
    nb = res.get('nb')
    suggests = db.session.query(Suggest.Id, Suggest.idate, Suggest.beforepic, Suggest.comments, Suggest.status, Suggest.linename, Suggest.type2, Suggest.content,
                                Userinfo2.cname.label('scname'), Userinfo2.dept1.label('sdept')).join(
        Userinfo2, Suggest.eid == Userinfo2.eid).join(Appvowners, Appvowners.dept == Userinfo2.dept1).filter(
                                    Suggest.plant == plant).filter(Suggest.status == 'cancel').filter(func.year(Suggest.idate) >= 2022).filter(Suggest.acdate.is_(None))
    suggests = suggests.group_by(Appvowners.dept).order_by(Suggest.idate).limit(nb).all()
    cardsData = []
    for s in suggests:
        dic = {
            'id': s.Id,
            'title': srDic[s.status]+'|No.'+str(s.Id),
            'subTitle': '提案日期:'+datetime.strftime(s.idate, "%Y-%m-%d"),
            'thumb': '/static/icon/'+s.status+'.png',
            'content': s.content,
            'url': getServer()['suggestionUrl']+s.beforepic.split(',')[0] if s.beforepic else 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png',
            'suggester': s.sdept+'/'+s.scname,
            'linename': s.linename,
            'comments': s.comments
        }
        cardsData.append(dic)
    return responseGet("获取列表成功", {'cardsData': cardsData})


@ api.route('/getMyActions', methods=['GET'])
@ login_required
def getMyActions():
    res = request.args
    eid = res.get('eid')
    exeid = res.get('exeid')
    dept = res.get('linename')
    onlymdi = res.get('onlymdi')
    plant = res.get('plant')
    keywords = res.get('keywords')
    astatus = int(res.get('astatus'))  # 0待确认=cfdate为空且ongoing，1待完成=有cfdate且ongoing，2已完成closed,3为all
    nb = res.get('nb')
    suggests = db.session.query(Suggest.Id, Suggest.duedate, Suggest.cfdate, Suggest.beforepic,  Suggest.status, Suggest.linename, Suggest.type2, Suggest.content,
                                Userinfo2.cname.label('scname'), Userinfo2.dept1.label('sdept'), Userinfo1.cname.label('dcname'), Userinfo1.dept1.label('ddept')).join(
        Userinfo2, Suggest.eid == Userinfo2.eid).join(Userinfo1, Suggest.exeid == Userinfo1.eid)
    if exeid:
        suggests = suggests.filter(Suggest.exeid == exeid)
    if plant:
        suggests = suggests.filter(Suggest.plant == plant)
    if onlymdi == 'true':
        suggests = suggests.filter(Suggest.mdi == '是')
    if dept:
        suggests = suggests.filter(Userinfo1.dept1 == dept)
    if eid:
        suggests = suggests.filter(Suggest.exeid == eid)
    if astatus == 0:
        suggests = suggests.filter(Suggest.status == 'ongoing').filter(Suggest.cfdate.is_(None))
    elif astatus == 1:
        suggests = suggests.filter(Suggest.status == 'ongoing').filter(
            not_(Suggest.cfdate.is_(None)))
    elif astatus == 2:
        if keywords != '':
            suggests = suggests.filter(Suggest.content.like('%{0}%'.format(keywords)))
        suggests = suggests.filter(Suggest.status == 'closed')
    elif astatus == 3:
        suggests = suggests.filter(Suggest.status == 'ongoing')
    suggests = suggests.order_by(Suggest.idate).limit(nb).all()
    cardsData = []
    for s in suggests:
        dic = {
            'status': s.status,
            'dealer': s.ddept+'/'+s.dcname,
            'id': s.Id,
            'title': srDic[s.status]+'|No.'+str(s.Id),
            'subTitle': '承诺日期:'+datetime.strftime(s.cfdate, "%Y-%m-%d") if astatus == 1 else '要求日期:'+datetime.strftime(s.duedate, "%Y-%m-%d"),
            'thumb': '/static/icon/'+s.status+'.png',
            'content': s.content,
            'url': getServer()['suggestionUrl'] + s.beforepic.split(',')[0] if s.beforepic else 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png',
            'suggester': s.sdept+'/'+s.scname,
            'linename': s.linename
        }
        cardsData.append(dic)
    return responseGet("获取列表成功", {'cardsData': cardsData})


@ api.route('/getAuditSuggestions', methods=['GET'])
@ login_required
def getAuditSuggestions():
    res = request.args
    auditid = res.get('auditid')
    suggests = db.session.query(Suggest.Id, Suggest.idate, Suggest.beforepic,  Suggest.status, Suggest.linename, Suggest.type2, Suggest.content,
                                Userinfo1.cname.label('scname'), Userinfo1.dept1.label('sdept')).join(
        Userinfo1, Suggest.eid == Userinfo1.eid).filter(Suggest.auditid == auditid).order_by(desc(Suggest.idate)).all()
    cardsData = []
    for s in suggests:
        print(s)
        dic = {
            'id': s.Id,
            'title': srDic[s.status]+'|No.'+str(s.Id),
            'subTitle': '提案日期:'+datetime.strftime(s.idate, "%Y-%m-%d"),
            'thumb': '/static/icon/'+s.status+'.png',
            'content': s.content,
            'url': getServer()['suggestionUrl'] + s.beforepic.split(',')[0] if s.beforepic else 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png',
            'suggester': s.sdept+'/'+s.scname,
            'linename': s.linename
        }
        cardsData.append(dic)
    return responseGet("获取列表成功", {'cardsData': cardsData})


@ api.route('/getMySuggestions', methods=['GET'])
@ login_required
def getMySuggestions():
    res = request.args
    keywords = res.get('keywords')
    eid = res.get('eid')
    statusArr = res.get('statusArr')
    nb = res.get('nb')
    sArr = []
    if statusArr:
        for s in statusArr.split(','):
            sArr.append(sDic[s])
    suggests = db.session.query(Suggest.Id, Suggest.idate, Suggest.beforepic,  Suggest.status, Suggest.linename, Suggest.type2, Suggest.content,
                                Userinfo1.cname.label('scname'), Userinfo1.dept1.label('sdept')).join(
        Userinfo1, Suggest.eid == Userinfo1.eid).filter(Suggest.status.in_(sArr))
    if keywords:
        suggests = suggests.filter(Suggest.content.like('%{0}%'.format(keywords)))
    if eid:
        suggests = suggests.filter(Suggest.eid == eid)
    suggests = suggests.order_by(desc(Suggest.idate)).limit(nb).all()
    cardsData = []
    for s in suggests:
        print(s)
        dic = {
            'status': s.status,
            'id': s.Id,
            'title': srDic[s.status]+'|No.'+str(s.Id),
            'subTitle': '提案日期:'+datetime.strftime(s.idate, "%Y-%m-%d"),
            'thumb': '/static/icon/'+s.status+'.png',
            'content': s.content,
            'url': getServer()['suggestionUrl'] + s.beforepic.split(',')[0] if s.beforepic else 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png',
            'suggester': s.sdept+'/'+s.scname,
            'linename': s.linename
        }
        cardsData.append(dic)
    return responseGet("获取列表成功", {'cardsData': cardsData})


@ api.route('/getLikeSuggestions', methods=['GET'])
@ login_required
def getLikeSuggestions():
    res = request.args
    keywords = res.get('keywords')
    eid = res.get('eid')
    nb = res.get('nb')
    suggests = db.session.query(Suggest.Id, Suggest.idate, Suggest.beforepic,  Suggest.status, Suggest.linename, Suggest.type2, Suggest.content,
                                Userinfo1.cname.label('scname'), Userinfo1.dept1.label('sdept')).join(Userinfo1, Suggest.eid == Userinfo1.eid).join(
        Rates, Rates.sid == Suggest.Id).filter(Rates.eid == eid).filter(Rates.cate == 'sug').filter(Rates.score == 5)
    if keywords:
        suggests = suggests.filter(Suggest.content.like('%{0}%'.format(keywords)))
    suggests = suggests.order_by(desc(Suggest.acdate)).limit(nb).all()
    cardsData = []
    for s in suggests:
        print(s)
        dic = {
            'id': s.Id,
            'icon': 'heart-fill',
            'cate': 'sug',
            'title': srDic[s.status]+'|No.'+str(s.Id),
            'subTitle': '提案日期:'+datetime.strftime(s.idate, "%Y-%m-%d"),
            'thumb': '/static/icon/'+s.status+'.png',
            'content': s.content,
            'url': getServer()['suggestionUrl'] + s.beforepic.split(',')[0] if s.beforepic else 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png',
            'suggester': s.sdept+'/'+s.scname,
            'linename': s.linename
        }
        cardsData.append(dic)
    return responseGet("获取列表成功", {'cardsData': cardsData})


@ api.route('/getAllSuggestions', methods=['GET'])
@ login_required
def getAllSuggestions():
    res = request.args
    keywords = res.get('keywords').split('|')
    plant = res.get('plant')
    nb = res.get('nb')
    suggests = db.session.query(Suggest.Id, Suggest.idate, Suggest.beforepic,  Suggest.status, Suggest.linename, Suggest.type2, Suggest.content,
                                Userinfo1.cname.label('scname'), Userinfo1.dept1.label('sdept'), Userinfo2.cname.label('dcname')).join(
        Userinfo1, Suggest.eid == Userinfo1.eid).join(
        Userinfo2, Suggest.exeid == Userinfo2.eid).filter(Suggest.plant == plant)

    if keywords:
        kwords = keywords[0]
        suggests = suggests.filter(or_(Suggest.content.like(
            '%{0}%'.format(kwords)), Userinfo2.cname.like('%{0}%'.format(kwords)), Suggest.Id.like('%{0}%'.format(kwords))))
        if len(keywords) > 1:
            status = sDic[keywords[1]]
            suggests = suggests.filter(Suggest.status == status)
    suggests = suggests.order_by(desc(Suggest.acdate)).limit(nb).all()
    cardsData = []
    for s in suggests:
        print(s)
        dic = {
            'id': s.Id,
            'icon': 'heart-fill',
            'cate': 'sug',
            'title': srDic[s.status]+'|No.'+str(s.Id),
            'subTitle': '提案日期:'+datetime.strftime(s.idate, "%Y-%m-%d"),
            'thumb': '/static/icon/'+s.status+'.png',
            'content': s.content,
            'url': getServer()['suggestionUrl'] + s.beforepic.split(',')[0] if s.beforepic else 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png',
            'suggester': s.sdept+'/'+s.scname,
            'linename': s.linename
        }
        cardsData.append(dic)
    return responseGet("获取列表成功", {'cardsData': cardsData})


@ api.route('/getSuggestByid', methods=['GET'])
# @login_required
def getSuggestByid():
    res = request.args
    id = res.get('id')
    outDic = {}
    s = db.session.query(Suggest.Id, Suggest.idate, Suggest.acdate, Suggest.beforepic, Suggest.afterpic, Suggest.status, Suggest.stype, Suggest.type2, Suggest.comments, Suggest.content,
                         Suggest.cfdate, Userinfo1.cname.label(
                             'scname'), Userinfo1.dept1.label('sdept'),
                         Userinfo2.cname.label('dcname'), Userinfo2.dept1.label('ddept'), Suggest.comments).join(
        Userinfo1, Suggest.eid == Userinfo1.eid).outerjoin(Userinfo2, Suggest.exeid == Userinfo2.eid).filter(Suggest.Id == id).first()
    if s:
        status = s.status
        if s.beforepic:
            newArr = []
            for ss in s.beforepic.split(','):
                newArr.append(getServer()['suggestionUrl']+ss)
            outbeforepic = ','.join(newArr)
        else:
            outbeforepic = 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png'
        if s.afterpic:
            newArr = []
            for ss in s.afterpic.split(','):
                newArr.append(getServer()['suggestionUrl']+ss)
            outafterpic = ','.join(newArr)
        else:
            outafterpic = 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png'
        if status == 'open':
            outDic = {
                '01_提案编号/状态/类型': str(s.Id)+'/待批准中...',
                '02_提案日期': datetime.strftime(s.idate, "%Y-%m-%d"),
                '04_提案人': s.sdept+'/'+s.scname,
                '05_提案内容': s.content,
                '10_改善前图片': outbeforepic,
            }
        elif status == 'ongoing':
            outDic = {
                '01_提案编号/状态/类型': str(s.Id)+'/已批准,等待执行.../'+((s.stype+'/'+s.type2) if s.type2 else s.stype),
                '02_提案日期': datetime.strftime(s.idate, "%Y-%m-%d"),
                '04_提案人': s.sdept+'/'+s.scname,
                '05_提案内容': s.content,
                '06_执行人': s.ddept+'/'+s.dcname,
                '08_预计完成日期': datetime.strftime(s.cfdate, "%Y-%m-%d") if s.cfdate else '未确认完成日期',
                '10_改善前图片': outbeforepic,
            }
        elif status == 'closed':
            outDic = {
                '01_提案编号/状态/类型': str(s.Id)+'/已完成/'+((s.stype+'/'+s.type2) if s.type2 else s.stype),
                '02_提案日期': datetime.strftime(s.idate, "%Y-%m-%d"),
                '03_完成日期': datetime.strftime(s.acdate, "%Y-%m-%d"),
                '04_提案人': s.sdept+'/'+s.scname,
                '05_提案内容': s.content,
                '06_执行人': s.ddept+'/'+s.dcname,
                '07_完成说明': s.comments,
                '10_改善前图片': outbeforepic,
                '11_改善后图片': outafterpic,
            }
        elif status == 'cancel':
            outDic = {
                '01_提案编号/状态/类型': str(s.Id)+'/被取消',
                '02_提案日期': datetime.strftime(s.idate, "%Y-%m-%d"),
                '04_提案人': s.sdept+'/'+s.scname,
                '05_提案内容': s.content,
                '07_取消原因': s.comments,
                '10_改善前图片': outbeforepic,
            }
    return responseGet("获取列表成功", {'suggest': outDic})


@ api.route('/getShares', methods=['GET'])
@ login_required
def getShares():
    res = request.args
    plant = res.get('plant')
    nb = int(res.get('nb'))
    if nb > 30:
        nb = 30
    eid = res.get('eid')
    dataList = []
    td = date.today()
    suggests = db.session.query(Suggest.Id, Suggest.acdate, Suggest.afterpic, Suggest.content, Userinfo1.cname.label('scname'), Userinfo1.dept1.label('sdept'),
                                Userinfo2.cname.label('dcname'), Userinfo2.dept1.label('ddept'), Suggest.comments, Rates.score).join(
        Userinfo1, Suggest.eid == Userinfo1.eid).join(Userinfo2, Suggest.exeid == Userinfo2.eid).outerjoin(Rates, and_(
            Rates.eid == eid, Rates.sid == Suggest.Id)).filter(Suggest.status == 'closed').filter(Suggest.plant == plant).order_by(desc(Suggest.acdate)).limit(nb).all()
    for s in suggests:
        item = {
            'id': s.Id,
            'tp': 'share',
            'rate': s.score if s.score else 0,
            'title': '提案分享',
            'closeDate': datetime.strftime(s.acdate, "%Y-%m-%d"),
            'picUrl': getServer()['suggestionUrl']+s.afterpic.split(',')[0] if s.afterpic else 'https://welean.pentair.cn/flaskserver/static/welean/nophoto.png',
            'suggestion': s.content,
            'dealer': s.ddept+'/'+s.dcname,
            'result': s.comments,
            'suggester': s.sdept+'/'+s.scname
        }
        dataList.append(item)
        if s.acdate < td:
            td = s.acdate
    news = db.session.query(Newspaper.Id, Rates.score, Newspaper.subcate, Newspaper.reservedate, Newspaper.piccover, Newspaper.title, Userinfo.dept1, Userinfo.cname
                            ).join(Userinfo, Userinfo.eid == Newspaper.eid).outerjoin(Rates, and_(Rates.eid == eid, Rates.sid == Newspaper.Id)).filter(
        or_(Newspaper.plant == plant, Newspaper.plant == 'ALL')).filter(Newspaper.reservedate >=
                                                                        td).filter(Newspaper.cate == 'news').order_by(desc(Newspaper.reservedate)).all()
    for n in news:
        coverpath = getServer()['newsPath']+str(n.Id)+'/'+n.piccover
        print('coverpath', coverpath)
        if os.path.exists(coverpath):
            newscover = getServer()['newsUrl']+str(n.Id)+'/'+n.piccover
        else:
            newscover = getServer()['baseUrl']+'nophoto.png'
        item = {
            'id': n.Id,
            'rate': n.score,
            'tp': 'news',
            'title': n.subcate,
            'closeDate': datetime.strftime(n.reservedate, "%Y-%m-%d"),
            'picUrl': newscover,  # 此处正式部署记得加上getServer()['newsUrl']+n.Id+'/',
            'newsTitle': n.title,
            'newsAuthor': n.dept1+'/'+n.cname
        }
        dataList.append(item)
    dataList = sorted(dataList, key=lambda dt: dt['closeDate'], reverse=True)
    return responseGet("获取列表成功", {'dataList': dataList})


@ api.route('/getMenus', methods=['GET'])
@ login_required
def getMenus():
    res = request.args
    user = json.loads(res.get('user'))
    eid = user['eid']
    plant = user['plant']
    menu = [
        {
            'id': 1,
            'src': '/static/menu/suggest.png',
            'des': '我要提案',
            'count': 0
        },
        {
            'id': 2,
            'src': '/static/menu/mysuggestion.png',
            'des': '我的提案',
            'count': 0
        }
    ]
    myTasks = db.session.query(func.count(Suggest.Id)).filter(
        Suggest.exeid == eid).filter(Suggest.status == 'ongoing').scalar()
    taskItem = {
        'id': 3,
        'src': '/static/menu/tasks.png',
        'des': '我的任务',
        'count': myTasks
    }
    myAudits = db.session.query(func.count(Tasks.Id)).filter(or_(Tasks.owner1 == eid, Tasks.owner2 == eid)).filter(
        Tasks.status == 0).filter(Tasks.month <= date.today()).scalar()
    auditItem = {
        'id': 4,
        'src': '/static/menu/audit.png' if myAudits else '/static/menu/approve-disable.png',
        'des': '我的稽核',
        'count': myAudits
    }
    myAppvs = db.session.query(func.count(Suggest.Id)).join(Userinfo, Suggest.eid == Userinfo.eid).join(
        Appvowners, Userinfo.dept1 == Appvowners.dept).filter(Userinfo.plant == plant).filter(Appvowners.eid == eid).filter(Suggest.status == 'open').scalar()
    appvItem = {
        'id': 5,
        'src': '/static/menu/approve.png' if myAppvs else '/static/menu/approve-disable.png',
        'des': '需我批准',
        'count': myAppvs
    }
    rateItem = {
        'id': 6,
        'src': '/static/menu/top.png',
        'des': '排行榜',
        'count': 0
    }
    menu = menu+[taskItem, auditItem, appvItem, rateItem]
    return responseGet("获取列表成功", {'menu': menu})


@ api.route('/uploadPics', methods=['POST'])
@ login_required
def uploadFile():
    file_obj = request.files.get('file')
    sid = request.form.get('sid')
    print('aaaaaaaaaaa', sid)
    mystr = ('suggestion' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        sql = "update wl_suggest set beforepic=(case when beforepic is null then '%s' else concat(beforepic,',','%s') end) where Id=%d" % (
            name+appendix, name+appendix, int(sid))
        db.session.execute(sql)
        db.session.commit()
        file_obj.save(getServer()['suggestionPath']+name+appendix)
        return responsePost("更新成功", {'upload_url': getServer()['suggestionUrl']+name+appendix, 'img': name+appendix})
    return responseError("上传文件失败，请联系管理员")


@ api.route('/uploadClosePics', methods=['POST'])
@ login_required
def uploadCloseFile():
    file_obj = request.files.get('file')
    sid = request.form.get('sid')
    print('bbbbbbbbbb', sid)
    mystr = ('suggestionclose' + str(datetime.now())).encode('UTF-8')
    name = hashlib.md5(mystr).hexdigest()[8:-8]
    if file_obj:
        print(name)
        appendix = file_obj.filename[file_obj.filename.rfind('.'):]
        sql = "update wl_suggest set afterpic=(case when afterpic is null then '%s' else concat(afterpic,',','%s') end) where Id=%d" % (
            name+appendix, name+appendix, int(sid))
        db.session.execute(sql)
        db.session.commit()
        file_obj.save(getServer()['suggestionPath']+name+appendix)
        return responsePost("更新成功", {'upload_url': getServer()['suggestionUrl']+name+appendix, 'img': name+appendix})
    return responseError("上传文件失败，请联系管理员")


@ api.route('/initSuggest', methods=['POST'])
@ login_required
def initSuggest():
    res = request.json
    content = res.get('content')
    special = res.get('special')
    eid = res.get('eid')
    plant = res.get('plant')
    if special:
        content = '['+special+']'+content
    if content:
        try:
            suggest = Suggest(audittype=0, auditid=0, eid=eid, plant=plant, content=content,
                              status='open', idate=date.today())
            db.session.add(suggest)
            db.session.flush()
            sid = suggest.Id
            db.session.commit()
            return responsePost("提案成功", {'sid': sid})
        except Exception as e:
            db.session.rollback()
            print(e)
            return responseError('提案失败，请联系精益部门反馈该问题')


@ api.route('/newSuggest', methods=['POST'])
@ login_required
def newSuggest():
    res = request.json
    content = res.get('content')
    eid = res.get('eid')
    plant = res.get('plant')
    duedate = res.get('duedate')
    exeid = res.get('exeid').split('/')[0]
    fifi = res.get('fifi')
    mdi = res.get('mdi')
    linename = res.get('linename').split('/')[2]
    stype = res.get('stype').split('/')[0]
    type2 = res.get('stype').split('/')[1]
    appvdate = datetime.strftime(date.today(), '%Y-%m-%d')
    status = 'ongoing'
    audittype = int(res.get('audittype'))
    auditid = res.get('auditid')
    idate = res.get('idate')
    if not idate:
        idate = date.today()
    if fifi == '是':
        duedate = datetime.now() + timedelta(days=10)
    try:
        suggest = Suggest(content=content, eid=eid, plant=plant, duedate=duedate, exeid=exeid, fifi=fifi, mdi=mdi, linename=linename,
                          stype=stype, type2=type2, appvdate=appvdate, status=status, audittype=audittype, auditid=auditid, idate=idate)
        db.session.add(suggest)
        db.session.flush()
        sid = suggest.Id
        db.session.commit()
        suggester = db.session.query(Account.wxid, Userinfo.cname).join(
            Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == eid).first()
        dealer = db.session.query(Account.wxid, Userinfo.cname).join(
            Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == exeid).first()
        if suggester and dealer:
            taskBody = {
                'idate': datetime.strftime(date.today(), '%Y-%m-%d'),
                'linename': linename[:20],
                'suggester': suggester.cname[:10],
                'jobtype': auditTypes[audittype],
                'content': content[:20]
            }
            dwxid = dealer.wxid
            dhref = 'pages/login/login'
            TASKthread(taskBody, dwxid, dhref)
        return responsePost("提案成功", {'sid': sid})
    except Exception as e:
        db.session.rollback()
        print(e)
        return responseError('提案失败，请联系精益部门反馈该问题')


@ api.route('/getSpecial', methods=['GET'])
@ login_required
def getSpecial():
    sp = db.session.query(Options).filter(
        Options.cate == 'sptopics').filter(Options.isactive == 1).first()
    if sp:
        tips = {
            'title': sp.title,
            'description': sp.content
        }
        return responseGet("获取列表成功", {'tips': tips})
    return responseError('不存在特殊提案')


@ api.route('/submitRate', methods=['POST'])
@ login_required
def submitRate():
    res = request.json
    sid = res.get('sid')
    eid = res.get('eid')
    cate = res.get('cate')
    score = res.get('score')
    print('res', res)
    myRate = db.session.query(Rates).filter(
        and_(Rates.sid == sid, Rates.eid == eid, Rates.cate == cate)).first()
    if myRate:
        myRate.score = score
    else:
        try:
            r = Rates(eid=eid, sid=sid, cate=cate, score=score)
            db.session.add(r)
        except Exception as e:
            db.session.rollback()
            print(e)
    db.session.commit()
    return responsePost("打分成功")


@ api.route('/killSug', methods=['PUT'])
@ login_required
def killSug():
    res = request.json
    comments = res.get('comments')
    sid = res.get('sid')
    s = db.session.query(Suggest.content, Suggest.idate,  Suggest.status, Account.wxid, Userinfo.cname
                         ).join(Userinfo, Userinfo.eid == Suggest.eid).join(Account, Account.eid == Suggest.eid).filter(Suggest.Id == sid).first()
    if s:
        db.session.query(Suggest).filter(Suggest.Id == sid).update(
            {'status': 'cancel', 'comments': comments, 'acdate': date.today()})
        db.session.commit()
        otBody = {
            'content': s.content[:20],
            'idate': datetime.strftime(s.idate, "%Y-%m-%d"),
            'owner': s.cname[:10],
            'status': '被取消',
            'comments': comments[:20]
        }
        wxid = s.wxid
        href = 'pages/suggestion/suggestinfo/suggestinfo?id='+str(sid)
        PROGRESSthread(otBody, wxid, href)
        return responsePut("否决成功")
    else:
        return responseError('没有找到提案')


@ api.route('/negSug', methods=['PUT'])
@ login_required
def negSug():
    res = request.json
    comments = res.get('comments')
    sid = res.get('sid')
    s = db.session.query(Suggest.content, Suggest.idate,  Suggest.status, Account.wxid, Userinfo.cname
                         ).join(Userinfo, Userinfo.eid == Suggest.eid).join(Account, Account.eid == Suggest.eid).filter(Suggest.Id == sid).first()
    if s:
        db.session.query(Suggest).filter(Suggest.Id == sid).update(
            {'status': 'cancel', 'comments': comments})
        db.session.commit()
        return responsePut("否决成功")
    else:
        return responseError('没有找到提案')


@ api.route('/transSug', methods=['PUT'])
@ login_required
def transSug():
    res = request.json
    exeid = res.get('exeid').split('/')[0]
    sid = res.get('sid')
    s = db.session.query(Suggest).filter(Suggest.Id == sid).first()
    if s:
        eid = s.exeid
        s.exeid = exeid
        db.session.commit()
        suggester = db.session.query(Account.wxid, Userinfo.cname).join(
            Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == eid).first()
        dealer = db.session.query(Account.wxid, Userinfo.cname).join(
            Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == exeid).first()
        if dealer and suggester:
            taskBody = {
                'idate': datetime.strftime(date.today(), '%Y-%m-%d'),
                'linename': s.linename[:20],
                'suggester': suggester.cname[:10],
                'jobtype': '转移提案',
                'content': s.content[:20]
            }
            dwxid = dealer.wxid
            href = 'pages/login/login'
            TASKthread(taskBody, dwxid, href)
        return responsePut("转移成功")
    else:
        return responseError('没有找到提案')


@ api.route('/confirmSug', methods=['PUT'])
@ login_required
def confirmSug():
    res = request.json
    sid = res.get('sid')
    cfdate = res.get('cfdate')
    print(sid, cfdate)
    try:
        db.session.query(Suggest).filter(Suggest.Id == sid).update({'cfdate': cfdate})
        db.session.commit()
        return responsePut("确认日期成功")
    except Exception:
        return responseError("确认失败，请通知管理员")


@ api.route('/submitAppv', methods=['PUT'])
@ login_required
def submitAppv():
    res = request.json
    duedate = res.get('duedate')
    exeid = res.get('exeid').split('/')[0]
    fifi = res.get('fifi')
    mdi = res.get('mdi')
    linename = res.get('linename')
    stype = res.get('stype')
    sid = int(res.get('sid'))
    if fifi == '是':
        duedate = datetime.now() + timedelta(days=10)
    s = db.session.query(Suggest).filter(Suggest.Id == sid).first()
    if s:
        try:
            eid = s.eid
            s.duedate = duedate
            s.exeid = exeid
            s.fifi = fifi
            s.mdi = mdi
            s.linename = linename.split('/')[2]
            s.stype = stype.split('/')[0]
            s.type2 = stype.split('/')[1]
            s.appvdate = datetime.strftime(date.today(), '%Y-%m-%d')
            s.status = 'ongoing'
            db.session.commit()
            getScore(eid, '批准', sid)
            suggester = db.session.query(Account.wxid, Userinfo.cname).join(
                Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == eid).first()
            dealer = db.session.query(Account.wxid, Userinfo.cname).join(
                Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == exeid).first()
            if suggester and dealer:
                otBody = {
                    'content': s.content[:20],
                    'idate': datetime.strftime(s.idate, "%Y-%m-%d"),
                    'owner': dealer.cname[:10],
                    'status': '已批准',
                    'comments': '提案已批准并获得积分，可参与抽奖'
                }
                wxid = suggester.wxid
                href = 'pages/suggestion/suggestinfo/suggestinfo?id='+str(sid)
                PROGRESSthread(otBody, wxid, href)
                taskBody = {
                    'idate': datetime.strftime(date.today(), '%Y-%m-%d'),
                    'linename': s.linename[:20],
                    'suggester': suggester.cname[:10],
                    'jobtype': '提案任务',
                    'content': s.content[:20]
                }
                dwxid = dealer.wxid
                dhref = 'pages/login/login'
                TASKthread(taskBody, dwxid, dhref)
            return responsePut("提案批准成功")
        except Exception:
            return responseError('出错了')
    else:
        return responseError('没有找到提案或用户')


@ api.route('/submitClose', methods=['PUT'])
@ login_required
def submitClose():
    res = request.json
    comments = res.get('comments')
    sid = int(res.get('sid'))
    s = db.session.query(Suggest).filter(Suggest.Id == sid).first()
    if s:
        try:
            eid = s.eid
            exeid = s.exeid
            audittype = s.audittype
            s.status = 'closed'
            s.comments = comments
            s.acdate = datetime.strftime(date.today(), '%Y-%m-%d')
            db.session.commit()
            getScore(eid, '结束', sid, audittype)
            getScore(exeid, '帮助', sid, audittype)
            suggester = db.session.query(Account.wxid, Userinfo.cname).join(
                Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == eid).first()
            dealer = db.session.query(Account.wxid, Userinfo.cname).join(
                Userinfo, Userinfo.eid == Account.eid).filter(Account.eid == exeid).first()
            if suggester and dealer:
                otBody = {
                    'content': s.content[:20],
                    'idate': datetime.strftime(s.idate, "%Y-%m-%d"),
                    'owner': dealer.cname[:10],
                    'status': '已完成',
                    'comments': comments
                }
                wxid = suggester.wxid
                href = 'pages/suggestion/suggestinfo/suggestinfo?id='+str(sid)
                PROGRESSthread(otBody, wxid, href)
            return responsePut("提案完成成功")
        except Exception:
            return responseError('出错了')
    else:
        return responseError('没有找到提案或用户')


@ api.route('/finishAudit', methods=['PUT'])
@ login_required
def finishAudit():
    res = request.json
    auditid = res.get('auditid')
    db.session.query(Tasks).filter(Tasks.Id == auditid).update({'status': 1})
    db.session.commit()
    return responsePut("稽核完成成功!")

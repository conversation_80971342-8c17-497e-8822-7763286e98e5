import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export const getpnbyprefix = (querydata?: object) => {
  return http.request("get", baseUrlApi("resinmix/getpnbyprefix"), {
    params: querydata
  });
};

export const handlemix = (querydata?: object) => {
  return http.request("get", baseUrlApi("resinmix/handlemix"), {
    params: querydata
  });
};

export const getexchangematerial = (querydata?: object) => {
  return http.request("get", baseUrlApi("exchange/getdata"), {
    params: querydata
  });
};

//获取指定日期产量API
export const getdayoutput = (querydata?: object) => {
  return http.request("get", baseUrlApi("dayoutput/getlist"), {
    params: querydata
  });
};

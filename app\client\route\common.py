from flask import Blueprint, request, json
from app.public.functions import responseError, responseGet, responsePost
from extensions import db
from app.client.model.models_client import ClientInfo
from app.public.model.models_public import Lineinfo
from extensions import ma
from sqlalchemy import or_
import datetime
import re
import os
from app.client.functions import get_static_path

api = Blueprint('client/commonAPI', __name__)

# 验证MAC地址是否合法


def is_valid_mac_address(mac_address: str) -> bool:
    mac_address = mac_address.replace("-", ":").upper()
    pattern = r'^([0-9A-Fa-f]{2}:){5}[0-9A-Fa-f]{2}$'
    return bool(re.match(pattern, mac_address))

# 验证IP地址是否合法


def is_valid_ip_address(ip):
    pattern = re.compile(
        r'^((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])$')
    if pattern.match(ip):
        return True
    else:
        return False


class client(ma.Schema):
    class Meta:
        fields = (
            'mac_addr', 'hostname', 'model', 'unique_id', 'ip_addr', 'area', 'linename', 'location',
            'client_type', 'app_version', 'display_type', 'remark', 'keeper', 'nav_url', 'boot_count',
            'connect_count', 'last_boot_time', 'boot_duration', 'is_active', 'os_name', 'plantform',
            'os_version', 'cpu_model', 'cpu_load', 'memory_capacity', 'memory_usage_percent', 'disk_name',
            'disk_type', 'disk_capacity', 'disk_temperature', 'disk_usage_percent', 'battery_status',
            'graphics', 'monitor_resolution', 'is_touchscreen'
        )


@api.route('/get_client_list', methods=['GET'])
def get_client_list():
    res=db.session.query(ClientInfo)
    args=request.args
    if args.get('area'):
        res=res.filter(or_(ClientInfo.area==args.get('area'),ClientInfo.linename==args.get('area')))
    if args.get('client_type'):
        res=res.filter(ClientInfo.client_type==args.get('client_type'))
    if args.get('search_field'):
        # 使用or_组合所有模糊搜索条件
        search_pattern = f"%{args.get('search_field')}%"
        res = res.filter(or_(
            ClientInfo.mac_addr.like(search_pattern),
            ClientInfo.hostname.like(search_pattern),
            ClientInfo.model.like(search_pattern),
            ClientInfo.ip_addr.like(search_pattern),
            ClientInfo.area.like(search_pattern),
            ClientInfo.linename.like(search_pattern),
            ClientInfo.location.like(search_pattern),
            ClientInfo.client_type.like(search_pattern),
            ClientInfo.remark.like(search_pattern),
            ClientInfo.keeper.like(search_pattern),
            ClientInfo.nav_url.like(search_pattern)
        ))
    res=res.order_by(ClientInfo.area.asc()).all()
    res = client(many=True).dump(res)
    return responseGet("返回列表成功", res)


@api.route('/get_client', methods=['GET'])
def geturl():
    params = request.args
    mac = params.get('mac').replace("-", ":").upper()
    if not mac:
        return responseError("MAC 地址参数缺失!")
    if not is_valid_mac_address(mac):
        return responseError("提交的MAC地址格式错误!")
    res = db.session.query(ClientInfo).filter(
        ClientInfo.mac_addr == mac).scalar()
    if res:
        res = client().dump(res)
        return responseGet("获取设备信息成功!", res)
    else:
        return responseError("设备信息不存在!")


@api.route('/update_client', methods=['POST'])
def update_ClientInfo():
    params = json.loads(request.get_data(as_text=True))
    print(params)
    print("*"*20)
    if 'mac_addr' in params:
        mac = params.get('mac_addr').replace("-", ":").upper()
    else:
        return responseError("MAC 地址参数缺失!")
    if not is_valid_mac_address(mac):
        return responseError("提交的MAC地址格式错误!")
    # ip查重
    if 'ip_addr' in params:
        if is_valid_ip_address(params['ip_addr']):
            res = db.session.query(ClientInfo).filter(
                ClientInfo.ip_addr == params['ip_addr'], ClientInfo.mac_addr != mac).scalar()
            if res:
                return responseError("IP地址已存在,请确认!")
    res = db.session.query(ClientInfo).filter(
        ClientInfo.mac_addr == mac).scalar()
    # 如果根据MAC地址未找到设备信息，则创建新设备信息
    if not res:
        res = ClientInfo(mac_addr=mac)
        db.session.add(res)
    # 更新设备信息
    for key, value in params.items():
        setattr(res, key, value)
        print(key, value)
    try:
        db.session.commit()
    except Exception:
        return responseError("更新设备信息失败!")
    return responsePost("更新设备信息成功!", {})


@api.route('/delete_client', methods=['POST'])
def delete_ClientInfo():
    params = json.loads(request.get_data(as_text=True))
    if 'mac_addr' in params:
        mac = params.get('mac_addr').replace("-", ":").upper()
    else:
        return responseError("MAC 地址参数缺失!")
    if not is_valid_mac_address(mac):
        return responseError("提交的MAC地址格式错误!")
    res = db.session.query(ClientInfo).filter(
        ClientInfo.mac_addr == mac).scalar()
    if not res:
        return responseError("设备信息不存在!")
    try:
        db.session.delete(res)
        db.session.commit()
    except Exception:
        return responseError("删除设备信息失败!")
    return responsePost("删除设备信息成功!", {})

# 查询返回Lineinfo中area与linename,将相同的area下的linename放到该area的children中,结果中的每个area与linename都使用value与label进行封装


@api.route('/get_area_list', methods=['GET'])
def get_line_list():
    res = db.session.query(Lineinfo).all()
    res = client(many=True).dump(res)
    area_list = []
    for i in res:
        if i['area'] not in area_list:
            area_list.append(i['area'])
    for i in area_list:
        area_dict = {}
        area_dict['value'] = i
        area_dict['label'] = i
        area_dict['children'] = []
        for j in res:
            if j['area'] == i:
                area_dict['children'].append(
                    {"value": j['linename'], "label": j['linename']})
        area_dict['children'].append(
            {"value": "备用", "label": "备用"})
        area_list[area_list.index(i)] = area_dict
    return responseGet("返回列表成功", area_list)


# 根据指定的查询关键字key，返回ClientInfo中多列与key匹配的结果
@api.route('/search_client', methods=['GET'])
def search_client():
    params = request.args
    key = params.get('key')
    if not key:
        return responseError("关键字参数缺失!")
    search_fields = [ClientInfo.mac_addr,
                     ClientInfo.hostname, ClientInfo.ip_addr, ClientInfo.location]
    # 构建查询条件
    conditions = [field.like(f'%{key}%') for field in search_fields]

    # 构建查询
    query = db.session.query(ClientInfo).filter(or_(*conditions))

    # 执行查询
    res = query.all()
    res = client(many=True).dump(res)
    return responseGet("查询成功", res)

# 接收图片文件并保存
@api.route('/upload_img', methods=['POST'])
def upload_img():
    params = request.files
    img = params.get('img')
    if not img:
        return responseError("图片参数缺失!")
    img_name = img.filename
    img_path=os.path.join(get_static_path()['screen_capture'], img_name)  
    os.makedirs(os.path.dirname(img_path), exist_ok=True)
    img.save(img_path)
    return responsePost("图片上传成功!", {"img_path": img_path})


# 获取***********-***********的IP地址段中除去client表中已经分配的ip_addr,返回可用的ip地址列表
@api.route('/get_available_ip', methods=['GET'])
def get_available_ip():
    res = db.session.query(ClientInfo).all()
    used_ip_list = [i.ip_addr for i in res]
    ip_list = []
    for i in range(128, 255):
        ip = f"10.76.4.{i}"
        if ip not in used_ip_list:
            ip_list.append(ip)
    return responseGet("返回可用IP地址列表成功", ip_list)

# 按client_type分组取数据
# 取类似这样的数据
#  [{client_type:'一体机',data:{total:10,online:5}}]
# 其中data中的total表示该client_type总数,online表示在线数online表示refresh_time到目前为止时间间隔在10分钟以内的客户端数
@api.route('/get_client_type_list', methods=['GET'])
def get_client_type_list():
    res = db.session.query(ClientInfo).all()
    client_type_list = []
    for i in res:
        if i.client_type not in client_type_list:
            client_type_list.append(i.client_type)
    client_type_dict_list = []
    for i in client_type_list:
        client_type_dict = {}
        client_type_dict['client_type'] = i
        client_type_dict['data'] = {}
        client_type_dict['data']['total'] = 0
        client_type_dict['data']['online'] = 0
        client_type_dict_list.append(client_type_dict)
    for i in res:
        for j in client_type_dict_list:
            if i.client_type == j['client_type']:
                j['data']['total'] += 1
                if i.refresh_time is not None:
                    if (datetime.datetime.now() - i.refresh_time).seconds < 600:
                        j['data']['online'] += 1
    return responseGet("返回客户端类型列表成功", client_type_dict_list)



# @api.route('/getconfiginfo', methods=['GET'])
# def getconfiginfo():
#     res = db.session.query(webview_config).filter(
#         webview_config.sname == 'winapp').scalar()
#     version = res.version
#     usedinwebviewlist = res.usedinwebviewlist.split(',')
#     wifischeduletime = res.wifischeduletime
#     configinfodic = {}
#     configinfodic['version'] = version
#     configinfodic['wifischeduletime'] = wifischeduletime
#     configinfodic['usedinwebviewlist'] = usedinwebviewlist
#     return responseGet('查询成功', {"configinfodic": configinfodic})


# @api.route('/RefreshClient', methods=['POST'])
# def RefreshClient():
#     params = json.loads(request.get_data(as_text=True))
#     params['refresh_time'] = datetime.datetime.now()
#     res = db.session.query(ClientInfo).filter(
#         ClientInfo.client_id == params['client_id']).scalar()
#     if not res:
#         new_client = ClientInfo(
#             client_id=params['client_id'],
#             refresh_time=datetime.datetime.now(),
#             boot_time=params['boot_time'],
#             ops_name=params['ops_name'],
#             ops_type=params['ops_type'],
#             ops_version=params['ops_version'],
#             cpu_model=params['cpu_model'],
#             cpu_load=params['cpu_load'],
#             mem_size=params['mem_size'],
#             mem_usedpercent=params['mem_usedpercent'],
#             hdd_name=params['hdd_name'],
#             hdd_temp=params['hdd_temp'],
#             hdd_size=params['hdd_size'],
#             hdd_usedpercent=params['hdd_usedpercent']
#         )
#         db.session.add(new_client)
#         try:
#             db.session.commit()
#         except Exception:
#             print("refresh error")
#             return responseError("新建客户端信息错误")
#     else:
#         res.client_id = params['client_id'],
#         res.refresh_time = datetime.datetime.now(),
#         res.boot_time = params['boot_time'],
#         res.ops_name = params['ops_name'],
#         res.ops_type = params['ops_type'],
#         res.ops_version = params['ops_version'],
#         res.cpu_model = params['cpu_model'],
#         res.cpu_load = params['cpu_load'],
#         res.mem_size = params['mem_size'],
#         res.mem_usedpercent = params['mem_usedpercent'],
#         res.hdd_name = params['hdd_name'],
#         res.hdd_temp = params['hdd_temp'],
#         res.hdd_size = params['hdd_size'],
#         res.hdd_usedpercent = params['hdd_usedpercent']
#         try:
#             db.session.commit()
#         except Exception:
#             print("refresh error")
#             responseError("刷新客户端信息错误")
#     return responsePost("信息刷新成功", {})


@api.route('/test', methods=['GET'])
def test():
    return "ok"
    ...
